package com.ruoyi.ffsafe.component;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.3.0_dev
 * @description:
 * @date 2025-03-16 16:16
 */
@Component
@Slf4j
public class FFSafeRequestComponent {
    //添加阻断IP
    public static final String ADD_BLOCK_IP_URL = "/v2/flow-add-block-ip";
    //解封阻断IP
    public static final String BYPASS_UNBLOCK_IP_URL = "/v2/flow-bypass-unblock";
    //正在阻断事件
    public static final String BYPASS_FILTER_BLOCKING_LOG_URL = "/v2/flow-bypass-filter-blocking-log";
    //流量告警数据
    public static final String FLOW_ALARM_URL = "/v2/flow-alarm";
    //主机安全事件
    public static final String HOST_EVENTS_URL = "/v2/host-security-event";
    //主机安全事件详情
    public static final String HOST_EVENTS_DETAIL_URL = "/v2/host-security-event/detail";

    @Autowired
    private Environment env;
    @Resource
    private ISysConfigService sysConfigService;
    //为当前线程绑定一个TblDeviceConfig
    public static ThreadLocal<TblDeviceConfig> deviceConfigThreadLocal = new ThreadLocal<>();
    @Resource
    private ITblDeviceConfigService deviceConfigService;

    public String sendPostRequest(String url, Map<String, Object> params, String contentType){
        HttpRequest postRequest = getPostRequest(url, params, contentType);
        return execRequest(postRequest);
    }

    public String sendGetRequest(String url, Map<String, Object> params, String contentType){
        HttpRequest getRequest = getGetRequest(url, params, null);
        return execRequest(getRequest);
    }

    public HttpRequest getPostRequest(String url, Map<String, Object> params,String contentType){
        if(StrUtil.isBlank(contentType)){
            contentType = "application/x-www-from-urlencoded";
        }
        if(params == null){
            params = new LinkedHashMap<>();
        }
        url = handleParamsAndGetUrl(url,params);
        return HttpRequest.post(url).contentType(contentType);
    }

    public HttpRequest getGetRequest(String url, Map<String, Object> params,String contentType){
        if(StrUtil.isBlank(contentType)){
            contentType = "application/x-www-from-urlencoded";
        }
        if(params == null){
            params = new LinkedHashMap<>();
        }
        url = handleParamsAndGetUrl(url,params);
        return HttpRequest.get(url).contentType(contentType);
    }

    private String execRequest(HttpRequest request){
        String body = request.execute().body();
        if(StrUtil.isNotBlank(body)){
            log.info("非凡请求响应: {}", UnicodeUtil.toString(body));
        }else {
            log.info("非凡请求响应为空");
            return "";
        }

        return UnicodeUtil.toString(body);
    }

    private String handleParamsAndGetUrl(String url,Map<String, Object> params){
        FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(FFSafeRequestComponent.deviceConfigThreadLocal.get());
        if(ffsafeApiConfig == null){
            throw new ServiceException("平台参数未配置");
        }
        if(!ffsafeApiConfig.isEnable()){
            throw new ServiceException("ffsafe未启用");
        }
        params.put("access_token",ffsafeApiConfig.getToken());
        String paramsStr = HttpUtil.toParams(params, StandardCharsets.UTF_8, true);
        return StrUtil.format("{}{}?{}",ffsafeApiConfig.getUrl(),url,paramsStr);
    }
}
