package cn.anmte.aqsoc.work.service.impl;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.mapper.WorkHwTaskMapper;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.monitor2.domain.TblOperateWork;
import com.ruoyi.monitor2.service.ITblOperateWorkService;
import com.ruoyi.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-25 16:47
 */
@Service
@Slf4j
public class WorkHwTaskServiceImpl extends ServiceImpl<WorkHwTaskMapper, WorkHwTask> implements IWorkHwTaskService {
    @Resource
    private WorkHwTaskMapper workHwTaskMapper;
    @Resource
    private ISysDictDataService sysDictDataService;
    @Resource(name = "delayExecutorService")
    private ScheduledExecutorService delayExecutorService;
    @Resource(name = "delayTaskMap")
    private Map<String, ScheduledFuture<?>> delayTaskMap;

    private static final String DELAY_TASK_KEY = "hwTask_";
    @Resource
    private ITblOperateWorkService operateWorkService;

    /**
     * 查询HW事务任务
     *
     * @param id HW事务任务主键
     * @return HW事务任务
     */
    @Override
    public WorkHwTask selectWorkHwTaskById(Long id)
    {
        return workHwTaskMapper.selectWorkHwTaskById(id);
    }

    /**
     * 批量查询HW事务任务
     *
     * @param ids HW事务任务主键集合
     * @return HW事务任务集合
     */
    @Override
    public List<WorkHwTask> selectWorkHwTaskByIds(Long[] ids)
    {
        return workHwTaskMapper.selectWorkHwTaskByIds(ids);
    }

    /**
     * 查询HW事务任务列表
     *
     * @param workHwTask HW事务任务
     * @return HW事务任务
     */
    @Override
    public List<WorkHwTask> selectWorkHwTaskList(WorkHwTask workHwTask)
    {
        return workHwTaskMapper.selectWorkHwTaskList(workHwTask);
    }

    /**
     * 新增HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    @Override
    public int insertWorkHwTask(WorkHwTask workHwTask)
    {
        workHwTask.setCreateTime(DateUtils.getNowDate());
        int i = workHwTaskMapper.insertWorkHwTask(workHwTask);
        if(i > 0){
            //启动定时任务
            this.startDelayTask(workHwTask);
        }
        return i;
    }

    /**
     * 修改HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    @Override
    public int updateWorkHwTask(WorkHwTask workHwTask)
    {
        workHwTask.setUpdateTime(DateUtils.getNowDate());
        int i = workHwTaskMapper.updateWorkHwTask(workHwTask);
        if(i > 0){
            if(workHwTask.getStartTime() != null){
                //先移除任务
                this.removeDelayTask(workHwTask.getId());
                //启动定时任务
                this.startDelayTask(workHwTask);
            }
        }
        return i;
    }

    /**
     * 删除HW事务任务信息
     *
     * @param id HW事务任务主键
     * @return 结果
     */
    @Override
    public int deleteWorkHwTaskById(Long id)
    {
        int i = workHwTaskMapper.deleteWorkHwTaskById(id);
        //移除定时任务
        this.removeDelayTask(id);
        return i;
    }

    /**
     * 批量删除HW事务任务
     *
     * @param ids 需要删除的HW事务任务主键
     * @return 结果
     */
    @Override
    public int deleteWorkHwTaskByIds(Long[] ids)
    {
        int i = workHwTaskMapper.deleteWorkHwTaskByIds(ids);
        for (Long id : ids) {
            //移除定时任务
            this.removeDelayTask(id);
        }
        return i;
    }

    /**
     * 获取阶段树
     * @param workHwTask
     * @return
     */
    @Override
    public List<JSONObject> getStageTree(WorkHwTask workHwTask) {
        List<JSONObject> result = new ArrayList<>();
        //获取阶段字典
        SysDictData queryDictData = new SysDictData();
        queryDictData.setDictType("hw_stage_class");
        List<SysDictData> dictDataList = sysDictDataService.selectDictDataList(queryDictData);
        if(CollUtil.isNotEmpty(dictDataList)){
            //按阶段统计
            List<WorkHwTask> taskList = baseMapper.selectWorkHwTaskList(workHwTask);
            dictDataList.forEach(dictData -> {
                JSONObject resultItem = new JSONObject();
                resultItem.put("value", dictData.getDictValue());
                resultItem.put("label", dictData.getDictLabel());
                resultItem.put("sort", dictData.getDictSort());
                resultItem.put("count", taskList.stream().filter(task -> task.getStageClass().equals(dictData.getDictValue())).count());
                result.add(resultItem);
            });
        }
        return result;
    }

    @Override
    public void startDelayTask(WorkHwTask workHwTask){
        if(workHwTask != null && workHwTask.getWorkId() != null && workHwTask.getStartTime() != null){
            DateTime nowDate = DateUtil.date();
            long time = workHwTask.getStartTime().getTime() - nowDate.getTime();
            if(time > 0){
                //启动定时任务
                log.info("创建HW定时任务..." + workHwTask.getTaskName());
                ScheduledFuture<?> schedule = delayExecutorService.schedule(() -> {
                    TblOperateWork operateWork = operateWorkService.selectTblOperateWorkById(Long.valueOf(workHwTask.getOperateWorkId()));
                    operateWork.setWorkName(workHwTask.getTaskName());
                    operateWork.setPersonId(String.valueOf(workHwTask.getManageUser()));
                    String flowTaskId = operateWorkService.createWorkFlowTask(operateWork);
                    WorkHwTask workHwTaskInDB = this.selectWorkHwTaskById(workHwTask.getId());
                    if(workHwTaskInDB != null){
                        workHwTaskInDB.setFlowTaskId(flowTaskId);
                        this.updateById(workHwTaskInDB);
                    }
                }, time, TimeUnit.MILLISECONDS);
                delayTaskMap.put(DELAY_TASK_KEY + workHwTask.getId(), schedule);
            }
        }
    }

    private void removeDelayTask(Long taskId){
        ScheduledFuture<?> scheduledFuture = delayTaskMap.get(DELAY_TASK_KEY + taskId);
        if(scheduledFuture != null){
            scheduledFuture.cancel(true);
        }
        delayTaskMap.remove(DELAY_TASK_KEY + taskId);
    }
}
