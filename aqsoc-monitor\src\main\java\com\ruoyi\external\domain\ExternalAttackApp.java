package com.ruoyi.external.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP应用程序对象 external_attack_app
 * 
 * <AUTHOR>
 * @date 2024-10-12
 */
@Data
public class ExternalAttackApp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** APP名称 */
    @Excel(name = "APP名称")
    private String appName;

    /** APP最新版本 */
    @Excel(name = "APP最新版本")
    private String appLatestVersion;

    /** APP下载链接 */
    @Excel(name = "APP下载链接")
    private String appDownloadLink;

    /** APP包名 */
    @Excel(name = "APP包名")
    private String appPackageName;

    /** 所属部门 */
    private Long deptId;

    @Excel(name = "所属部门")
    private String deptName;

    /** APP开发者 */
    @Excel(name = "APP开发者")
    private String appDeveloper;

    /** APP商店ID */
    @Excel(name = "商店ID")
    private String appStoreId;

    /** APP ID */
    @Excel(name = "APP ID")
    private String appId;

    /** 责任人及联系方式 */
    private String responsiblePerson;

    @Excel(name = "责任人及联系方式")
    private String responsiblePersonName;

    /** APP更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "APP更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date appUpdateTime;

    /** 发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date discoveryTime;

    /** 数据录入类型：1数据同步，2手动 */
    private Integer entryType;

    /** 平台类型(IOS/Android等)，关联sys_dict_data.dict_value */
    @Excel(name = "平台类型", dictType = "app_platform_type")
    private String platformType;

    /** 平台类型标签（字典翻译后的显示值） */
    private String platformTypeLabel;

    /** 服务端地址(IP/URL)(IP/URL) */
    @Excel(name = "服务端地址(IP/URL)")
    private String serverAddress;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 设备配置ID */
    private Long deviceConfigId;

}
