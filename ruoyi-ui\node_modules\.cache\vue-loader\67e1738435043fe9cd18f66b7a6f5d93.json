{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\vue-simple-uploader\\fileUploader.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\vue-simple-uploader\\fileUploader.vue", "mtime": 1756794279925}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["fileUploader.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fileUploader.vue", "sourceRoot": "src/components/Generator/components/Upload/vue-simple-uploader", "sourcesContent": ["<template>\n  <div id=\"common-file-uploader\" :class=\"{ hasDefault: value && !!value.length }\">\n    <uploader\n      class=\"uploader-app\"\n      ref=\"uploader\"\n      :options=\"options\"\n      :autoStart=\"false\"\n      :file-status-text=\"statusText\"\n      @file-added=\"onFileAdded\"\n      @file-success=\"onFileSuccess\"\n      @file-progress=\"onFileProgress\"\n      @file-error=\"onFileError\"\n      @complete=\"onComplete\"\n    >\n      <uploader-unsupport></uploader-unsupport>\n      <uploader-btn id=\"file-uploader-btn\" ref=\"uploadBtn\" :attrs=\"attrs\"\n        >选择文件</uploader-btn\n      >\n      <uploader-list>\n        <template slot-scope=\"{ fileList }\">\n          <ul class=\"el-upload-list el-upload-list el-upload-list--text\">\n            <li\n              class=\"el-upload-list__item\"\n              v-for=\"file in fileList\"\n              :key=\"file.id\"\n            >\n              <uploader-file\n                :class=\"'file_' + file.id\"\n                ref=\"files\"\n                :file=\"file\"\n                :list=\"true\"\n              >\n                <template slot-scope=\"props\">\n                  <FileItem :file=\"props.file\" :list=\"props.list\" />\n                </template>\n              </uploader-file>\n            </li>\n          </ul>\n        </template>\n      </uploader-list>\n    </uploader>\n  </div>\n</template>\n\n<script>\nimport { chunkMerge } from \"@/api/lowCode/common\";\nimport uploadMixin from \"@/components/Generator/components/Upload/vue-simple-uploader/mixin\";\n\nconst units = {\n  KB: 1024,\n  MB: 1024 * 1024,\n  GB: 1024 * 1024 * 1024,\n};\n\nexport default {\n  props: {\n    value: {\n      type: Array,\n      default: () => [],\n    },\n    type: {\n      type: String,\n      default: \"annex\",\n    },\n    limit: {\n      type: Number,\n      default: 0,\n    },\n    accept: {\n      type: String,\n      default: \"*\",\n    },\n    sizeUnit: {\n      type: String,\n      default: \"MB\",\n    },\n    pathType: {\n      type: String,\n      default: \"defaultPath\",\n    },\n    isAccount: {\n      type: Number,\n      default: \"0\",\n    },\n    folder: {\n      type: String,\n      default: \"\",\n    },\n    fileSize: {\n      default: 5,\n    },\n  },\n  mixins: [uploadMixin],\n  data() {\n    return {};\n  },\n  computed: {\n    acceptText() {\n      let txt = \"\";\n      if (this.accept.includes(\"image/*\")) txt += \"、图片\";\n      if (this.accept.includes(\"video/*\")) txt += \"、视频\";\n      if (this.accept.includes(\"audio/*\")) txt += \"、音频\";\n      if (this.accept.includes(\".xls,.xlsx\")) txt += \"、excel\";\n      if (this.accept.includes(\".doc,.docx\")) txt += \"、word\";\n      if (this.accept.includes(\".pdf\")) txt += \"、pdf\";\n      if (this.accept.includes(\".txt\")) txt += \"、txt\";\n      return txt.slice(1);\n    },\n  },\n  methods: {\n    beforeUpload(file) {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      const unitNum = units[this.sizeUnit];\n      let isRightSize = this.fileSize\n        ? file.size / unitNum < this.fileSize\n        : true;\n      if (!isRightSize) {\n        this.$message.error(`文件大小超过${this.fileSize}${this.sizeUnit}`);\n        return isRightSize;\n      }\n      const isAccept = this.checkAccept(file);\n      if (!isAccept) {\n        this.$message.error(`请选择${this.acceptText}类型的文件`);\n        return isAccept;\n      }\n      return isRightSize && isAccept;\n    },\n    // 校验格式\n    checkAccept(file) {\n      if (!this.accept || this.accept === \"*\") return true;\n      const extension = file.getExtension();\n      const fileType = file.fileType;\n      if (this.accept.indexOf(extension) > -1) return true;\n      if (\n        this.accept.includes(\"image/*\") &&\n        new RegExp(\"image/*\").test(fileType)\n      )\n        return true;\n      if (\n        this.accept.includes(\"video/*\") &&\n        new RegExp(\"video/*\").test(fileType)\n      )\n        return true;\n      if (\n        this.accept.includes(\"audio/*\") &&\n        new RegExp(\"audio/*\").test(fileType)\n      )\n        return true;\n      return false;\n    },\n    handelSuccess(file) {\n      const form = new FormData();\n      form.append(\"identifier\", file.uniqueIdentifier);\n      form.append(\"fileName\", file.name.replaceAll(\"#\", \"\"));\n      form.append(\"fileSize\", file.size);\n      form.append(\"fileType\", file.getType());\n      form.append(\"extension\", file.getExtension());\n      form.append(\"type\", this.type);\n      form.append(\"pathType\", this.pathType);\n      form.append(\"isAccount\", this.isAccount);\n      form.append(\"folder\", this.folder);\n      chunkMerge(form).then((res) => {\n        // 自定义完成状态\n        this.$set(file, \"customCompleted\", true);\n        let data = {\n          name: file.name.replaceAll(\"#\", \"\"),\n          fileId: res.data.name,\n          fileSize: res.data.fileSize,\n          fileExtension: res.data.fileExtension,\n          fileVersionId: res.data.fileVersionId,\n          url: res.data.url,\n          fileType: file.fileType\n        };\n        this.$emit(\"fileSuccess\", data);\n        file.cancel();\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n#common-file-uploader {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  &.hasDefault {\n    .el-upload-list__item:first-child {\n      margin-top: 5px;\n    }\n  }\n  .el-upload-list {\n    ::v-deep .uploader-file {\n      border-bottom: none;\n      height: 25px !important;\n      line-height: 25px;\n      &:hover {\n        background-color: #f5f7fa;\n      }\n    }\n  }\n  ::v-deep .uploader-file-icon {\n    &:before {\n      content: \"\" !important;\n    }\n  }\n  ::v-deep .uploader-file-actions > span {\n    margin-right: 6px;\n  }\n}\n/* 隐藏上传按钮 */\n#file-uploader-btn {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n}\n</style>\n"]}]}