package com.ruoyi.external.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.validation.Validator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.external.domain.ExternalAttackApp;
import com.ruoyi.external.mapper.ExternalAttackAppMapper;
import com.ruoyi.external.model.ExternalAttackAppExcelForm;
import com.ruoyi.external.service.IExternalAttackAppService;
import com.ruoyi.external.util.ExternalAttackValidationUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * APP应用程序Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
@Slf4j
@Service
public class ExternalAttackAppServiceImpl implements IExternalAttackAppService
{
    // 每批次查询的最大元素数量
    private static final int BATCH_SIZE = 500;

    @Autowired
    private ExternalAttackAppMapper externalAttackAppMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper userMapper;


    private Long defaultDeptId = 100L;

    @PostConstruct
    public void initDefaultDeptId() {
        SysDept defaultDept = deptMapper.selectDeptByParentId(0L);
        if (Objects.nonNull(defaultDept)) {
            this.defaultDeptId = Objects.nonNull(defaultDept.getDeptId()) ? defaultDept.getDeptId() : 100L;
        }
    }

    /**
     * 查询APP应用程序
     *
     * @param id APP应用程序主键
     * @return APP应用程序
     */
    @Override
    public ExternalAttackApp selectExternalAttackAppById(Long id)
    {
        return externalAttackAppMapper.selectExternalAttackAppById(id);
    }

    /**
     * 批量查询APP应用程序
     *
     * @param ids APP应用程序主键集合
     * @return APP应用程序集合
     */
    @Override
    public List<ExternalAttackApp> selectExternalAttackAppByIds(Long[] ids)
    {
        return externalAttackAppMapper.selectExternalAttackAppByIds(ids);
    }

    /**
     * 查询APP应用程序列表
     *
     * @param externalAttackApp APP应用程序
     * @return APP应用程序
     */
    @Override
    public List<ExternalAttackApp> selectExternalAttackAppList(ExternalAttackApp externalAttackApp)
    {
        return externalAttackAppMapper.selectExternalAttackAppList(externalAttackApp);
    }

    /**
     * 新增APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    @Override
    public int insertExternalAttackApp(ExternalAttackApp externalAttackApp)
    {
        // 录入类型为手动时，才校验，因为数据同步时，已有去重逻辑
        if (Objects.equals(externalAttackApp.getEntryType(),2)) {
            validateAppUniqueness(externalAttackApp, null);
        }


        externalAttackApp.setCreateTime(DateUtils.getNowDate());
        // 如果没有deptId，则设置为默认部门id(定级部门)
        if (Objects.isNull(externalAttackApp.getDeptId())) {
            externalAttackApp.setDeptId(defaultDeptId);
        }
        return externalAttackAppMapper.insertExternalAttackApp(externalAttackApp);
    }

    /**
     * 修改APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    @Override
    public int updateExternalAttackApp(ExternalAttackApp externalAttackApp)
    {
        validateAppUniqueness(externalAttackApp, externalAttackApp.getId());


        externalAttackApp.setUpdateTime(DateUtils.getNowDate());
        return externalAttackAppMapper.updateExternalAttackApp(externalAttackApp);
    }

    /**
     * 检查 APP应用程序 是否已存在（排除指定 id）
     * xxAppId非空的校验唯一性,空的就不校验唯一
     * @param record
     * @param excludeId
     */
    private void validateAppUniqueness(ExternalAttackApp record, Long excludeId) {
        String appId = record.getAppId();
        String appName = record.getAppName();
        if (externalAttackAppMapper.checkAppExistence(appId, appName, excludeId) > 0) {
            throw new ServiceException("APP应用程序已存在");
        }
    }

    /**
     * 校验APP应用程序是否存在
     *
     * @param appId
     * @param appName
     * @return
     */
    private boolean validateApp(String appId, String appName) {
        return externalAttackAppMapper.checkAppExistence(appId, appName,null) > 0;
    }

    /**
     * 删除APP应用程序信息
     *
     * @param id APP应用程序主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackAppById(Long id)
    {
        return externalAttackAppMapper.deleteExternalAttackAppById(id);
    }

    /**
     * 批量删除APP应用程序
     *
     * @param ids 需要删除的APP应用程序主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackAppByIds(Long[] ids)
    {
        return externalAttackAppMapper.deleteExternalAttackAppByIds(ids);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExternalAttackApp(List<ExternalAttackAppExcelForm> externalAttackAppExcelFormList, boolean updateSupport, String operName) {
        if (CollUtil.isEmpty(externalAttackAppExcelFormList)){
            throw new ServiceException("导入APP应用程序数据不能为空！");
        }
        // 检查导入数据条数是否超过500条
        if (externalAttackAppExcelFormList.size() > 500) {
            throw new ServiceException("单次导入数据条数不能超过500条！");
        }
        // 查询部门列表
        List<SysDept> queryDeptList = deptMapper.selectDeptList(new SysDept());
        Map<String, SysDept> deptMap = queryDeptList.stream().collect(Collectors.toMap(
                SysDept::getDeptName,
                Function.identity(),
                (dept1, dept2) -> { // mergeFunction, 解决键冲突的方法
                    // 按ID大小比较
                    return dept1.getDeptId() < dept2.getDeptId() ? dept1 : dept2;
                }));
        // 查询用户列表
        List<SysUser> sysUsers = userMapper.selectUserList(new SysUser());
        Map<String, Long> userMap = sysUsers.stream().collect(Collectors.toMap(
                user -> user.getNickName() + "-" + user.getPhonenumber(), // 新的键生成方式
                SysUser::getUserId, // 值为 SysUser 的 userId
                (id1, id2) -> { // 解决键冲突的方法
                    // 如果两个用户有相同的键，则选择一个值，这里简单地返回第一个值
                    return id1;
                }
        ));

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        //记录新增和更新域名
        List<ExternalAttackApp> insertAppList = new ArrayList<>();
        List<ExternalAttackApp> updateAppList = new ArrayList<>();
        //检查新增APP应用程序的App ID否有重复的
        HashSet<String> attackAppIds = new HashSet<>();
        //先检查APP ID是否存在和校验数据是否正确
        for (ExternalAttackAppExcelForm attackAppExcelForm : externalAttackAppExcelFormList)
        {
            try
            {
                if (!attackAppIds.contains(StrUtil.format("{}-{}", attackAppExcelForm.getAppName(), attackAppExcelForm.getAppId())))
                {
                    BeanValidators.validateWithException(validator, attackAppExcelForm);
                    // 域名重复
                    if (this.validateApp(attackAppExcelForm.getAppId(), attackAppExcelForm.getAppName())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、APP名称 "+attackAppExcelForm.getAppName()+" APP ID " + attackAppExcelForm.getAppId() + " 的数据已存在");
                    }else if(!deptMap.containsKey(attackAppExcelForm.getDeptName())){
                        // 校验部门
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、APP ID " + attackAppExcelForm.getAppId() + " 输入的所属部门不正确");
                    }else {
                        // 校验负责人可以为空
                        String responsiblePerson = attackAppExcelForm.getResponsiblePersonName();
                        Pair<Boolean, String> booleanStringPair = Pair.of(true, "默认校验通过");
                        if(StrUtil.isNotBlank(responsiblePerson)){
                            booleanStringPair = ExternalAttackValidationUtils.checkResponsibilityExists(sysUsers, responsiblePerson);
                        }
                        if (!booleanStringPair.getKey()) {
                            // 校验负责人输入格式是否正确/负责人是否存在
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、APP ID " + attackAppExcelForm.getAppId() + booleanStringPair.getValue());
                        } else {
                            ExternalAttackApp externalAttackApp = new ExternalAttackApp();
                            BeanUtils.copyBeanProp(externalAttackApp, attackAppExcelForm);
                            externalAttackApp.setDeptId(deptMap.get(attackAppExcelForm.getDeptName()).getDeptId());
                            String userIds = ExternalAttackValidationUtils.getUserIds(responsiblePerson, userMap);
                            externalAttackApp.setResponsiblePerson(userIds);
                            externalAttackApp.setCreateBy(operName);
                            // 设置数据录入类型为2手动
                            externalAttackApp.setEntryType(2);
                            insertAppList.add(externalAttackApp);
                            successMsg.append("<br/>" + successNum + "、APP ID " + externalAttackApp.getAppId() + " 导入成功");
                            attackAppIds.add(StrUtil.format("{}-{}", attackAppExcelForm.getAppName(), attackAppExcelForm.getAppId()));
                        }
                    }
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、APP名称 "+attackAppExcelForm.getAppName()+" APP ID " + attackAppExcelForm.getAppId() + " 已存在或重复添加");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、APP ID " + attackAppExcelForm.getAppId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            //插入域名数据
            for (ExternalAttackApp externalAttackApp : insertAppList) {
                this.insertExternalAttackApp(externalAttackApp);
                successNum++;
            }
            if (updateSupport){
                for (ExternalAttackApp externalAttackApp : updateAppList) {
                    this.updateExternalAttackApp(externalAttackApp);
                    successNum++;
                }
            }
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据appId批量查询App应用程序,每500条查一次并最终汇总
     * @param appIds
     * @return
     */
    public List<ExternalAttackApp> selectByAppIds(Set<String> appIds) {
        List<ExternalAttackApp> result = new ArrayList<>();
        int totalSize = appIds.size();
        int batchSize = BATCH_SIZE;
        // 将 Set 转换为 List，以便支持 subList 操作
        List<String> appIdsList = new ArrayList<>(appIds);
        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<String> batchAppIds = appIdsList.subList(i, end);
            result.addAll(externalAttackAppMapper.selectByAppIds(batchAppIds));
        }
        return result;
    }

    @Override
    public int countNum() {
        return externalAttackAppMapper.countNum();
    }

    @Override
    public void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId) {
        if (CollUtil.isEmpty(uniqueKeys)) {
            return;
        }
        externalAttackAppMapper.deleteByEntryTypeAndAppId(uniqueKeys, deviceConfigId);
    }

}
