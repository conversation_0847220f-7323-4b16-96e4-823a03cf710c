package com.ruoyi.external.service;

import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackOfficialAccount;
import com.ruoyi.external.model.ExternalAttackOfficialAccountExcelForm;

/**
 * 微信公众号Service接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface IExternalAttackOfficialAccountService
{
    /**
     * 查询微信公众号
     *
     * @param id 微信公众号主键
     * @return 微信公众号
     */
    public ExternalAttackOfficialAccount selectExternalAttackOfficialAccountById(Long id);

    /**
     * 批量查询微信公众号
     *
     * @param ids 微信公众号主键集合
     * @return 微信公众号集合
     */
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountByIds(Long[] ids);

    /**
     * 查询微信公众号列表
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 微信公众号集合
     */
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountList(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 新增微信公众号
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    public int insertExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 修改微信公众号
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    public int updateExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 删除微信公众号信息
     *
     * @param id 微信公众号主键
     * @return 结果
     */
    public int deleteExternalAttackOfficialAccountById(Long id);

    /**
     * 批量删除微信公众号
     *
     * @param ids 需要删除的微信公众号主键集合
     * @return 结果
     */
    public int deleteExternalAttackOfficialAccountByIds(Long[] ids);

    String importExternalAttackOfficialAccount(List<ExternalAttackOfficialAccountExcelForm> officialAccountExcelFormList, boolean updateSupport, String operName);

    List<ExternalAttackOfficialAccount> selectByOfficialAccountAppIds(Set<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId);

}
