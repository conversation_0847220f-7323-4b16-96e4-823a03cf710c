<template>
  <div class="main">
    <div class="head-box">
      <div class="left">
        {{workInfo.year}}HW计划【{{workInfo.hwStart}} 至 {{workInfo.hwEnd}}】
      </div>
      <div class="right">
        <el-button class="btn2" size="small" @click="back">返回</el-button>
      </div>
    </div>
    <div class="custom-container">
      <div class="custom-tree-container">
        <div class="head-container">
          <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            size="medium"
            prefix-icon="el-icon-search"
            style="margin-bottom: 15px"
            @input="search"
          />
        </div>
        <div class="head-container">
          <el-tabs tab-position="left" style="height: 100%;" class="work-tabs" @tab-click="tabClick">
            <el-tab-pane :label="tabItem.label" v-for="tabItem in stageList"><div slot="label" class="work-tabs-label">{{`${tabItem.label}（${tabItem.count}）`}}</div></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="custom-content-container-right">
        <div class="custom-content-container">
          <div class="common-header">
            <div><span class="common-head-title">任务列表</span></div>
            <div class="common-head-right">
              <el-row :gutter="10">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleAdd"
                    :disabled="editBtnDisabled()"
                  >新增</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
          <el-table
            v-loading="loading"
            :data="dataList"
            ref="table"
            height="100%">
            <el-table-column label="任务名称" align="center" prop="taskName"/>
            <el-table-column label="所属阶段" align="center" prop="stageClass">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.hw_stage_class" :value="scope.row.stageClass"/>
              </template>
            </el-table-column>
            <el-table-column label="任务内容" align="center" prop="content" />
            <el-table-column label="任务开始时间" align="center" prop="startTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="任务结束时间" align="center" prop="endTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责人" align="center" prop="manageUserName"/>
            <el-table-column label="关联事务" align="center" prop="operateWorkName" />
            <el-table-column
              label="操作"
              fixed="right"
              :show-overflow-tooltip="false"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button size="mini" type="text" @click="handleUpdate(scope.row)" :disabled="editDisabled(scope.row) || editBtnDisabled()">编辑</el-button>
                <el-button size="mini" type="text" @click="handleDelete(scope.row)" :disabled="editBtnDisabled()">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 添加或修改事务管理对话框! -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-row>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-col :span="12">
            <el-form-item label="HW阶段" prop="stageClass">
              <el-select v-model="form.stageClass" clearable placeholder="请选择">
                <el-option v-for="dict in dict.type.hw_stage_class"
                           :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" maxlength="20" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="任务内容" prop="content">
              <el-input type="textarea" v-model="form.content" placeholder="请输入任务内容" maxlength="500" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务开始时间" prop="startTime">
              <el-date-picker type="datetime" placeholder="请选择" v-model="form.startTime" :picker-options="startPickerOptions" style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" :default-value="defaultStartTime"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务完成时间" prop="endTime">
              <el-date-picker type="datetime" placeholder="请选择" v-model="form.endTime" :picker-options="endPickerOptions" style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" :default-value="defaultStartTime"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人" prop="manageUser">
              <user-select v-model="form.manageUser"></user-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联事务" prop="operateWorkId">
              <el-select v-model="form.operateWorkId" clearable filterable placeholder="请选择">
                <el-option v-for="item in operateWorkList"
                           :key="item.id" :label="item.workName"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWorkHwTask, getWorkHwTask, delWorkHwTask, addWorkHwTask, updateWorkHwTask,getStageTree } from "@/api/aqsoc/work-hw/workHwTask";
import {listOperateWork} from "@/api/operateWork/operateWork"
import UserSelect from '@/views/hhlCode/component/userSelect';
export default {
  name: "Plan",
  components: {UserSelect},
  dicts: ['hw_stage_class'],
  props:{
    workInfo: {
      type: Object,
      required: true,
      default: {}
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: null,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" },
          { max: 20, message: "长度不能超过20个字符", trigger: "blur" }
        ],
        content: [
          { required: true, message: "任务内容不能为空", trigger: "blur" },
          { max: 500, message: "长度不能超过500个字符", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "任务开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "任务完成时间不能为空", trigger: "blur" }
        ],
        manageUser: [
          { required: true, message: "责任人不能为空", trigger: "blur" }
        ],
        operateWorkId: [
          { required: true, message: "关联事务不能为空", trigger: "blur" }
        ],
        stageClass: [
          { required: true, message: "HW阶段不能为空", trigger: "blur" }
        ]
      },
      form: {},
      stageList: [],
      operateWorkList: [],
      title: '',
      btnLoading: false,
      startPickerOptions: {
        disabledDate: (time) => {
          return new Date(time).getTime() < new Date(this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.form.endTime?this.form.endTime : this.workInfo.hwEnd).getTime();
        },
      },
      endPickerOptions: {
        disabledDate: (time) => {
          return new Date(time).getTime() < new Date(this.form.startTime?this.form.startTime : this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.workInfo.hwEnd).getTime();
        },
      },
      defaultStartTime: new Date(this.workInfo.hwStart),
    };
  },
  watch: {
  },
  created() {
    this.queryParams.workId = this.workInfo.id;
    this.getList();
    this.getOperateWorkList();
  },
  methods: {
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询HW事务任务列表 */
    getList() {
      this.loading = true;
      this.getStageList();
      listWorkHwTask(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getStageList(){
      getStageTree({workId: this.workInfo.id}).then(res => {
        let arr = res.data;
        let first = {
          value: null,
          label: '全部阶段',
          sort: 0,
          count: 0
        };
        if(arr && arr.length>0){
          arr.forEach(item => {
            first.count += item.count;
          })
        }
        arr.unshift(first);
        this.stageList = arr;
      })
    },
    getOperateWorkList(){
      listOperateWork({
        queryAllData: true,
        workType: 2
      }).then(res => {
        this.operateWorkList = res.rows;
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      let match = this.operateWorkList.find(item => item.workName === 'HW专项');
      if(match != null){
        this.form.operateWorkId = match.id;
      }
      this.open = true;
      this.title = "添加HW事务任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWorkHwTask(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改HW事务任务";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除HW事务任务编号为"' + ids + '"的数据项？').then(function () {
        return delWorkHwTask(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 表单重置
    reset() {
      this.form = {
        workId: this.workInfo.id
      };
      this.resetForm("form");
    },
    back(){
      this.$emit('update:show',false);
    },
    submitForm(){
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.btnLoading = true;
          this.form.workId = this.workInfo.id;
          let currentForm = {...this.form};
          if (this.form.id != null) {
            updateWorkHwTask(currentForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.btnLoading = false;
            });
          } else {
            addWorkHwTask(currentForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.btnLoading = false;
            });
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    tabClick(tab){
      if(tab.paneName !== '0'){
        this.queryParams.stageClass = tab.paneName;
      }else {
        this.queryParams.stageClass = null;
      }
      this.search();
    },
    editDisabled(row){
      return row.flowTaskId;
    },
    editBtnDisabled(){
      return this.workInfo.isStart;
    },
  }
};
</script>
<style lang="scss" scoped>
.main{
  width: 100%;

  .head-box{
    background: #fff;
    flex-shrink: 0;
    margin-bottom: 10px;
    padding: 15px 10px 15px;
    position: relative;
    display: flex;

    .left{
      align-content: center;
      font-size: 16px;
      font-weight: 700;
    }
    .right{
      flex: 1;
      text-align: right;
    }

    .btn2{
      height: 32px;
      color: #656C75;
      font-size: 14px;
      border: 1px solid #dbdbdb;
      background: #f2f7f7;
    }
  }

  .work-tabs{
    ::v-deep .el-tabs__item {
      height: 48px !important;
      line-height: 48px !important;
      text-align: left !important;
      width: 285px !important;
      padding: 0 10px;
    }
    /*设置第一个标签项不使用通用的悬停和选中效果*/
    ::v-deep .el-tabs__item:not(#tab-search):hover {
      color: #333 !important;
      background-color: #f5f5f5 !important;
    }
    ::v-deep .el-tabs__item:not(#tab-search).is-active {
      line-height: 48px;
      color: #333333;
      font-weight: bold;
      background-color: #f5f5f5;
    }
    .work-tabs-label {
      padding: 0 10px;
    }
  }
}
</style>
