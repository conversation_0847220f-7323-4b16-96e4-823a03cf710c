{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue", "mtime": 1756706031905}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7YWRkQXBwbGljYXRpb25JbmZvLCB1cGRhdGVBcHBsaWNhdGlvbkluZm8sIGdldEFwcGxpY2F0aW9ufSBmcm9tICJAL2FwaS9zYWZlL2FwcGxpY2F0aW9uIjsKaW1wb3J0IEFwcGxpY2F0aW9uTGluayBmcm9tICdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uTGluayc7CmltcG9ydCBBcHBsaWNhdGlvblNpdGUgZnJvbSAnQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvblNpdGUnOwppbXBvcnQgVXNlclNlbGVjdCBmcm9tICdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L3VzZXJTZWxlY3QnOwppbXBvcnQgRGVwdFNlbGVjdCBmcm9tICdAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L2RlcHRTZWxlY3QnOwppbXBvcnQgTmV0d29ya1NlbGVjdCBmcm9tICdAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L25ldHdvcmtTZWxlY3QnOwppbXBvcnQgRHluYW1pY1RhZyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhZyc7CmltcG9ydCBWZW5kb3JTZWxlY3QyIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvdmVuZG9yU2VsZWN0Mic7CmltcG9ydCBEaWN0U2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGljdFNlbGVjdCc7CmltcG9ydCB7Z2V0VmFsRnJvbU9iamVjdH0gZnJvbSAiQC91dGlscyI7CmltcG9ydCB7Z2VuZXJhdGVTZWN1cmVVVUlELCB3YWl0Rm9yVmFsdWV9IGZyb20gIkAvdXRpbHMvcnVveWkiOwppbXBvcnQge2xpc3RWZW5kb3JCeUFwcGxpY2F0aW9ufSBmcm9tICJAL2FwaS9zYWZlL3ZlbmRvciI7CmltcG9ydCBzZXJ2ZXJFViBmcm9tICJAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uSGFyZHdhcmUvc2VydmVyRVYudnVlIjsKaW1wb3J0IGRhdGVFViBmcm9tICJAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uSGFyZHdhcmUvZGF0ZUVWLnZ1ZSI7CmltcG9ydCBuZXR3b3JrRVYgZnJvbSAiQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvbkhhcmR3YXJlL25ldHdvcmtFVi52dWUiOwppbXBvcnQgc2FmZUVWIGZyb20gIkAvdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25IYXJkd2FyZS9zYWZlRVYudnVlIjsKaW1wb3J0IG92ZXJWaWV3U2VsZWN0IGZyb20gIkAvdmlld3MvY29tcG9uZW50cy9zZWxlY3Qvb3ZlclZpZXdTZWxlY3QudnVlIjsKaW1wb3J0IHtsaXN0QWxsT3ZlcnZpZXd9IGZyb20gIkAvYXBpL3NhZmUvb3ZlcnZpZXciOwppbXBvcnQgRWRpdFNlcnZlciBmcm9tICJAL3ZpZXdzL3NhZmUvc2VydmVyL2VkaXRTZXJ2ZXIudnVlIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAic3lzdGVtRGV0YWlscyIsCiAgY29tcG9uZW50czogewogICAgRWRpdFNlcnZlciwKICAgIG92ZXJWaWV3U2VsZWN0LAogICAgc2FmZUVWLAogICAgbmV0d29ya0VWLAogICAgZGF0ZUVWLAogICAgc2VydmVyRVYsCiAgICBBcHBsaWNhdGlvbkxpbmssCiAgICBBcHBsaWNhdGlvblNpdGUsCiAgICBVc2VyU2VsZWN0LAogICAgRGVwdFNlbGVjdCwKICAgIE5ldHdvcmtTZWxlY3QsCiAgICBEaWN0U2VsZWN0LAogICAgRHluYW1pY1RhZywKICAgIFZlbmRvclNlbGVjdDIsCiAgfSwKICBkaWN0czogWwogICAgJ3NlcnZlX2dyb3VwJywKICAgICdjb3Zlcl9hcmVhJywKICAgICdzeXNfeWVzX25vJywKICAgICdhcHBfbmV0X3NjYWxlJywKICAgICdjb25zdHJ1Y3RfdHlwZScsCiAgICAnc3lzdGVtX3R5cGUnLAogICAgJ3Byb3RlY3Rpb25fZ3JhZGUnLAogICAgJ2Fzc2V0X3N0YXRlJywKICAgICdhcHBfbG9naW5fdHlwZScsCiAgICAnYXBwX3RlY2huaWNhbCcsCiAgICAnYXBwX2RlcGxveScsCiAgICAnYXBwX3N0b3JhZ2UnLAogICAgJ2V2YWx1YXRpb25fcmVzdWx0cycsCiAgICAnZXZhbHVhdGlvbl9zdGF0dXMnLAogICAgJ2lzX29wZW5fbmV0d29yaycsCiAgICAnaHdfaXNfdHJ1ZV9zaHV0X2Rvd24nCiAgXSwKICBpbmplY3Q6IHsKICAgICRlZGl0YWJsZTogewogICAgICBkZWZhdWx0OiB7dmFsdWU6IHRydWV9LAogICAgfQogIH0sCiAgcHJvcHM6IHsKICAgIGFzc2V0SWQ6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICBkZWZhdWx0OiBudWxsLAogICAgfSwKICAgIGNoYW5nZUlkOiBGdW5jdGlvbiwKICAgIHJlYWRvbmx5OiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIGRpc2FibGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIGFzc2V0TGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNvbGxhcHNlTmFtZXM6IFsnMScsICcyJywgJzMnLCAnNCcsICc1J10sCiAgICAgIHZlbmRvcnNkYXRhOiAnMScsCiAgICAgIHVzZXJkYXRhOiAnMScsCiAgICAgIGZ1bmN0aW9uU3RhdGVMaXN0OiBbe30sIHt9LCB7fV0sCiAgICAgIC8vIOWfuuacrOS/oeaBr+ihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5Lia5Yqh5L+h5oGv6KGo5Y2V5Y+C5pWwCiAgICAgIGJ1c2luZXNzRm9ybTogewogICAgICAgIGRlbExpc3Q6IFtdCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGFzc2V0TmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bqU55So5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAgIHttaW46IDAsIG1heDogNjQsIG1lc3NhZ2U6ICflupTnlKjlkI3np7DkuI3og73otoXov4cgNjQg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGFzc2V0Q2xhc3M6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOS6p+WIhuexu+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBkb21haW5JZCA6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4u+mDqOe9sue9kee7nOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBkb21haW5Vcmw6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMTI4LCBtZXNzYWdlOiAn5Z+f5ZCN5LiN6IO96LaF6L+HIDEyOCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAgewogICAgICAgICAgICBwYXR0ZXJuOiAvXig/PV4uezMsMjU1fSQpKGh0dHAocyk/OlwvXC8pPyh3d3dcLik/W2EtekEtWjAtOV1bLWEtekEtWjAtOV17MCw2Mn0oXC5bYS16QS1aMC05XVstYS16QS1aMC05XXswLDYyfSkrKDpcZCspKihcL1x3K1wuXHcrKSokLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOWfn+WQjSIsCiAgICAgICAgICAgIHRyaWdnZXI6IFsnYmx1cicsICdjaGFuZ2UnXQogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIG1hbmFnZXI6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui0n+i0o+S6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICB1c2VySWQ6IFsKICAgICAgICAgIC8vIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLdJROS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZXB0SWQ6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBwaG9uZTogWwogICAgICAgICAge21pbjogMCwgbWF4OiAxMSwgbWVzc2FnZTogJ+iBlOezu+eUteivneS4jeiDvei2hei/hyAxMSDkvY0nLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3BhdHRlcm46IC9eMVsxfDJ8M3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTogZTns7vnlLXor50iLCB0cmlnZ2VyOiBbJ2JsdXInLCAnY2hhbmdlJ119LAogICAgICAgIF0sCiAgICAgICAgdXJsOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnmbvlvZXlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgICAge21pbjogMCwgbWF4OiAxMjgsIG1lc3NhZ2U6ICfnmbvlvZXlnLDlnYDkuI3og73otoXov4cgMTI4IOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7CiAgICAgICAgICAgIC8vIOato+WImeihqOi+vuW8j+eUqOS6jumqjOivgSBVUkwg5qC85byPCiAgICAgICAgICAgIC8vIOaUr+aMgSBodHRwL2h0dHBzIOWNj+iuru+8jOWFgeiuuCBJUCDlnLDlnYDmiJbln5/lkI3vvIzmlK/mjIHnq6/lj6Plj7flkozot6/lvoQKICAgICAgICAgICAgLy9wYXR0ZXJuOiAvXihodHRwcz86XC9cLyk/KChbYS16QS1aMC05LV0rXC4pK1thLXpBLVpdezIsfXwoKFxkezEsM31cLil7M31cZHsxLDN9KSkoOlxkKyk/KFwvW1x3Li1dKikqJC8sCiAgICAgICAgICAgIC8vcGF0dGVybjogL14oPzojXC8/W15ccyNdK3woaHR0cHM/fGZ0cCk6XC9cLyhbXHcuLV0rfFxbW1xkYS1mQS1GOl0rXF0pKDpcZCspPyhcL1teP1xzI10qKT8oXD9bXlxzI10qKT8oIy4qKT8pJC8sCiAgICAgICAgICAgIHBhdHRlcm46IC9eKGh0dHBzPzpcL1wvKT8oKChbYS16QS1aMC05LV0rXC4pK1thLXpBLVpdezIsfSl8KChcZHsxLDN9XC4pezN9XGR7MSwzfSkpKDpcZCspPyhcL1teXHM/I10qKT8oXD9bXlxzI10qKT8oIy4qKT8kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOeZu+W9leWcsOWdgCIsCiAgICAgICAgICAgIHRyaWdnZXI6IFsnYmx1cicsICdjaGFuZ2UnXQogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgaXBkOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICJJcOWcsOWdgOauteS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDMyMCwgbWVzc2FnZTogJ0lQ5Zyw5Z2A5q615aGr5YaZ5bey5LiK6ZmQJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcGF0dGVybjogL14oKDIoNVswLTVdfFswLTRdXGQpKXxbMC0xXT9cZHsxLDJ9KShcLigoMig1WzAtNV18WzAtNF1cZCkpfFswLTFdP1xkezEsMn0pKXszfSgsKCgyKDVbMC01XXxbMC00XVxkKSl8WzAtMV0/XGR7MSwyfSkoXC4oKDIoNVswLTVdfFswLTRdXGQpKXxbMC0xXT9cZHsxLDJ9KSl7M30pKiQvLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qESVDlnLDlnYDmrrXvvIzlpJrkuKpJUOWcsOWdgOeUqOmAl+WPt+malOW8gCIsCiAgICAgICAgICAgIHRyaWdnZXI6IFsnYmx1cicsICdjaGFuZ2UnXQogICAgICAgICAgfSwKICAgICAgICBdLAoKICAgICAgICBuZXRNZW1vOiBbCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDI1NSwgbWVzc2FnZTogJ+aLk+aJkeWbvuivtOaYjuS4jeiDvei2hei/hyAyNTUg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBidXNpbmVzc1J1bGVzOiB7CiAgICAgICAgc3lzQnVzaW5lc3NTdGF0ZTogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDgwMCwgbWVzc2FnZTogJ+aLk+aJkeWbvuivtOaYjuS4jeiDvei2hei/hyA4MDAg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIHVzZXJOdW1zOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtYXg6IDEyLCBtZXNzYWdlOiAn55So5oi35pWw6YeP5LiN6IO96LaF6L+HIDEyIOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBwYXR0ZXJuOiAvXlswLTldKiQvLCBtZXNzYWdlOiAn6K+36L6T5YWl5aSn5LqO562J5LqOMOeahOaVsOWtlycsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBldmVyeWRheVZpc2l0TnVtczogWwogICAgICAgICAge21pbjogMCwgbWF4OiAxMiwgbWVzc2FnZTogJ+aXpeWdh+iuv+mXruaVsOmHj+S4jeiDvei2hei/hyAxMiDkuKrlrZfnrKYnfSwKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIHBhdHRlcm46IC9eWzAtOV0qPyQvLCBtZXNzYWdlOiAn6K+36L6T5YWl5aSn5LqO562J5LqOMOeahOaVsOWtlycsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBldmVyeWRheUFjdGl2ZU51bXM6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMTIsIG1lc3NhZ2U6ICfmnIjlnYfmtLvot4PkurrmlbDkuI3og73otoXov4cgMTIg5Liq5a2X56ymJ30sCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBwYXR0ZXJuOiAvXlswLTldKj8kLywgbWVzc2FnZTogJ+ivt+i+k+WFpeWkp+S6juetieS6jjDnmoTmlbDlrZcnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIG1vZHVsZVJ1bGU6IHsKICAgICAgICBtb2R1bGVOYW1lOiBbCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDY0LCBtZXNzYWdlOiAn5Yqf6IO95qih5Z2X5ZCN56ew5LiN6IO96LaF6L+HIDY0IOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBtb2R1bGVEZXNjOiBbCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDIwMDAsIG1lc3NhZ2U6ICflip/og73mqKHlnZfor7TmmI7kuI3og73otoXov4cgMjAwMCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIGd2OiBnZXRWYWxGcm9tT2JqZWN0LAogICAgICBkZXBsb3lMb2NhdGlvbjogbG9jYWxTdG9yYWdlLmdldEl0ZW0oImRsIiksCiAgICAgIG1hbmFnZXJMYWJlbDogJ+i0o+S7u+S6ui/nlLXor50nLAogICAgICBtYW5hZ2VQbGFjZWhvbGRlcjogJ+ivt+mAieaLqei0o+S7u+S6uicsCiAgICAgIHJlZnM6IHsKICAgICAgICAnbmV0d29ya0VWJzogIuaJgOWuieijheacjeWKoeWZqOeOr+WigyIsCiAgICAgICAgJ3NhZmVFVic6ICfmiYDlronoo4XmlbDmja7njq/looMnLAogICAgICAgICdzZXJ2ZXJFVic6ICflhbPogZTnvZHnu5zorr7lpIcnLAogICAgICAgICdkYXRlRVYnOiAi5YWz6IGU5a6J5YWo6K6+5aSHIgogICAgICB9LAogICAgICBjb2xsYXBzZTogWycxJywgJzInLCAnMycsICc0J10sCiAgICAgIHNob3dBZGRTZXJ2ZXI6IGZhbHNlLAogICAgICBzZXJ2ZXJPcHRpb25zOiBbXSwKICAgICAgY3VycmVudEFzc29jaWF0aW9uU2VydmVyOiBbXSwKICAgICAgYWZ0ZXJJbml0OiBmYWxzZSwKICAgICAgc2VsZWN0VHlwZTogWydzeXN0ZW1UeXBlJywnY29uc3RydWN0JywnbG9naW5UeXBlJywndGVjaG5pY2FsJywnZGVwbG95Jywnc3RhdGUnLCdwcm90ZWN0R3JhZGUnLCAnZXZhbHVhdGlvblJlc3VsdHMnLCAnZXZhbHVhdGlvblN0YXR1cycsICdod0lzVHJ1ZVNodXREb3duJ10sCiAgICAgIGRhdGVUeXBlOiBbJ3VvZFRpbWUnLCAnd2FpdGluZ0luc3VyYW5jZUZpbGluZ1RpbWUnLCAnZXZhbHVhdGlvblllYXInXSwKICAgICAgdGV4dGFyZWFUeXBlOiBbJ25ldE1lbW8nLCAncmVtYXJrJ10sCiAgICAgIHJhZGlvR3JvdXBUeXBlOiBbJ2lza2V5JywgJ2lzYmFzZScsICdpc2xvZycsICdpc2FkYXB0JywgJ2lzY2lwaGVyJywgJ2lzcGxhbicsICdpc2xpbmsnLCAnaXNPcGVuTmV0d29yayddLAogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0QWxsU2VydmVyTGlzdCgpOwogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICBpZiAodGhpcy5kZXBsb3lMb2NhdGlvbiA9PT0gJ2ZhaXInKSB7CiAgICAgICAgdGhpcy5tYW5hZ2VyTGFiZWwgPSAn6LSj5Lu75rCR6K2mL+eUteivnScKICAgICAgICB0aGlzLm1hbmFnZVBsYWNlaG9sZGVyID0gJ+ivt+mAieaLqei0o+S7u+awkeitpicKICAgICAgfQogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuaW5pdCgpCiAgICB9KTsKICB9LAogIGFjdGl2YXRlZCgpIHsKICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmluaXQoKQogICAgfSk7CiAgfSwKICB3YXRjaDogewogICAgZnVuY3Rpb25TdGF0ZUxpc3Q6IHsKICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLmxlbmd0aCA+IDApIHsKICAgICAgICAgIG5ld1ZhbC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgICBpZihPYmplY3Qua2V5cyhpdGVtKS5sZW5ndGggPiAwKXsKICAgICAgICAgICAgICBpdGVtLnRlbXBJZCA9IGdlbmVyYXRlU2VjdXJlVVVJRCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSwKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDkuJrliqHkv6Hmga/liqjmgIHlrZfmrrUKICAgIGJ1c2luZXNzQXNzZXRGaWVsZHMoKSB7CiAgICAgIHJldHVybiAodGhpcy5hc3NldExpc3Quc2xpY2UoNywgMTApIHx8IFtdKS5tYXAoZ3JvdXAgPT4gKHsKICAgICAgICAuLi5ncm91cCwKICAgICAgICBmaWVsZHNJdGVtczogZ3JvdXAuZmllbGRzSXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc1Nob3cpCiAgICAgIH0pKTsKICAgIH0sCiAgICAvLyDln7rmnKzkv6Hmga/liqjmgIHlrZfmrrUKICAgIGJhc2ljSW5mb0Fzc2V0RmllbGRzKCkgewogICAgICBsZXQgYXNzZXRGaWVsZHMgPSB0aGlzLmFzc2V0TGlzdC5zbGljZSgwLCA3KTsKICAgICAgcmV0dXJuIGFzc2V0RmllbGRzLm1hcChncm91cCA9PiB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIC4uLmdyb3VwLAogICAgICAgICAgZmllbGRzSXRlbXM6IGdyb3VwLmZpZWxkc0l0ZW1zLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNTaG93KQogICAgICAgIH07CiAgICAgIH0pCiAgICB9LAogICAgLy8g6L2v56Gs5Lu2546v5aKD5Yqo5oCB5a2X5q61CiAgICBydW5UaW1lQXNzZXRGaWVsZHMoKSB7CiAgICAgIHJldHVybiAodGhpcy5hc3NldExpc3Quc2xpY2UoMTAsIDE0KSB8fCBbXSkubWFwKGdyb3VwID0+ICh7CiAgICAgICAgLi4uZ3JvdXAsCiAgICAgICAgZmllbGRzSXRlbXM6IGdyb3VwLmZpZWxkc0l0ZW1zLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNTaG93KQogICAgICB9KSk7CiAgICB9LAogICAgZHluYW1pY1J1bGVzKCkgewogICAgICBjb25zdCBydWxlcyA9IHt9OwogICAgICBjb25zdCBydWxlU2V0cyA9IHsuLi50aGlzLnJ1bGVzLCAuLi50aGlzLmJ1c2luZXNzUnVsZXN9OwogICAgICBsZXQgdmlzaWJsZUFzc2V0RmllbGRzID0gWy4uLnRoaXMuYmFzaWNJbmZvQXNzZXRGaWVsZHMsIC4uLnRoaXMuYnVzaW5lc3NBc3NldEZpZWxkc10KICAgICAgdmlzaWJsZUFzc2V0RmllbGRzLmZvckVhY2goZ3JvdXAgPT4gewogICAgICAgIGdyb3VwLmZpZWxkc0l0ZW1zLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBjb25zdCBmaWVsZEtleSA9IGl0ZW0uZmllbGRLZXk7CiAgICAgICAgICBpZiAoIXJ1bGVzW2ZpZWxkS2V5XSkgewogICAgICAgICAgICBydWxlc1tmaWVsZEtleV0gPSBbXTsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAocnVsZVNldHNbZmllbGRLZXldKSB7CiAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkUnVsZXMgPSBydWxlU2V0c1tmaWVsZEtleV0uZmlsdGVyKHJ1bGUgPT4gIXJ1bGUucmVxdWlyZWQpOwogICAgICAgICAgICBydWxlc1tmaWVsZEtleV0ucHVzaCguLi5maWx0ZXJlZFJ1bGVzKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChpdGVtLnJlcXVpcmVkKSB7CiAgICAgICAgICAgIGNvbnN0IGhhc1JlcXVpcmVkUnVsZSA9IHJ1bGVzW2ZpZWxkS2V5XS5zb21lKHJ1bGUgPT4gcnVsZS5yZXF1aXJlZCkKICAgICAgICAgICAgaWYgKCFoYXNSZXF1aXJlZFJ1bGUpIHsKICAgICAgICAgICAgICBydWxlc1tmaWVsZEtleV0ucHVzaCh7CiAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGAke2l0ZW0uZmllbGROYW1lfeS4jeiDveS4uuepumAsCiAgICAgICAgICAgICAgICB0cmlnZ2VyOiBbJ2JsdXInLCAnY2hhbmdlJ10KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pCiAgICAgIHJldHVybiBydWxlczsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWtl+auteagoemqjOinhOWImQogICAgZ2V0RmllbGRSdWxlcyhmaWVsZCkgewogICAgICByZXR1cm4gdGhpcy5keW5hbWljUnVsZXNbZmllbGQuZmllbGRLZXldIHx8IFtdOwogICAgfSwKCiAgICAvLyDojrflj5blrZfmrrXmiYDljaDliJfmlbAKICAgIGdldEZpZWxkU3BhbihmaWVsZCkgewogICAgICAvLyDnibnmrorlrZfmrrXljaAyNOWIlwogICAgICBjb25zdCBmdWxsU3BhbkZpZWxkcyA9IFsnYXNzb2NpYXRpb25TZXJ2ZXInLCAnbmV0VG9wbycsICduZXRNZW1vJywgJ2V2YWx1YXRpb25SZXBvcnQnLCAnd2FpdGluZ0luc3VyYW5jZUZpbGluZ1NjYW4nLCAncmVtYXJrJ107CiAgICAgIGlmIChmdWxsU3BhbkZpZWxkcy5pbmNsdWRlcyhmaWVsZC5maWVsZEtleSkpIHJldHVybiAyNDsKICAgICAgLy8g5YW25LuW5a2X5q616buY6K6k5Y2gOOWIlwogICAgICByZXR1cm4gODsKICAgIH0sCgogICAgc2hvdWxkU2hvd0ZpZWxkKGZpZWxkKSB7CiAgICAgIC8vIOWFtuS7luezu+e7n+Wkh+azqCAtIOWPquWcqCBzeXN0ZW1UeXBlIOS4uiAxMiDml7bmmL7npLoKICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnb3RoZXJTeXN0ZW1Ob3RlcycpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLnN5c3RlbVR5cGUgPT0gMTI7CiAgICAgIH0KCiAgICAgIC8vIOS/oeWIm+mAgumFjeaXtumXtCAtIOWPquWcqCBpc2FkYXB0IOS4uiAnWScg5pe25pi+56S6CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2FkYXB0RGF0ZScpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmlzYWRhcHQgPT09ICdZJzsKICAgICAgfQoKICAgICAgLy8g5a+G56CB5bqU55So5bu66K6+5pe26Ze0IC0g5Y+q5ZyoIGlzY2lwaGVyIOS4uiAnWScg5pe25pi+56S6CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2NpcGhlckRhdGUnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5pc2NpcGhlciA9PT0gJ1knOwogICAgICB9CgogICAgICAvLyDorabnu7zlr7nmjqUgLSDlj6rlnKjnibnlrprpg6jnvbLkvY3nva7mmL7npLoKICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnaXNsaW5rJykgewogICAgICAgIHJldHVybiB0aGlzLmRlcGxveUxvY2F0aW9uID09PSAnZmFpcic7CiAgICAgIH0KCiAgICAgIC8vIOWFtuS7luWtl+autem7mOiupOaYvuekugogICAgICByZXR1cm4gdHJ1ZTsKICAgIH0sCgogICAgZ2V0RGljdE9wdGlvbnMoZmllbGRLZXkpIHsKICAgICAgY29uc3QgZGljdE1hcCA9IHsKICAgICAgICBzeXN0ZW1UeXBlOiAnc3lzdGVtX3R5cGUnLAogICAgICAgIGNvbnN0cnVjdDogJ2NvbnN0cnVjdF90eXBlJywKICAgICAgICBsb2dpblR5cGU6ICdhcHBfbG9naW5fdHlwZScsCiAgICAgICAgdGVjaG5pY2FsOiAnYXBwX3RlY2huaWNhbCcsCiAgICAgICAgZGVwbG95OiAnYXBwX2RlcGxveScsCiAgICAgICAgc3RhdGU6ICdhc3NldF9zdGF0ZScsCiAgICAgICAgcHJvdGVjdEdyYWRlOiAncHJvdGVjdGlvbl9ncmFkZScsCiAgICAgICAgZXZhbHVhdGlvblJlc3VsdHM6ICdldmFsdWF0aW9uX3Jlc3VsdHMnLAogICAgICAgIGV2YWx1YXRpb25TdGF0dXM6ICdldmFsdWF0aW9uX3N0YXR1cycsCiAgICAgICAgaHdJc1RydWVTaHV0RG93bjogJ2h3X2lzX3RydWVfc2h1dF9kb3duJwogICAgICB9OwoKICAgICAgcmV0dXJuIHRoaXMuZGljdC50eXBlW2RpY3RNYXBbZmllbGRLZXldXSB8fCBbXTsKICAgIH0sCgogICAgLy8g6I635Y+W5a2X5q6155qE5pel5pyf57G75Z6LCiAgICBnZXREYXRlVHlwZShmaWVsZCkgewogICAgICBzd2l0Y2ggKGZpZWxkLmZpZWxkS2V5KSB7CiAgICAgICAgY2FzZSAndW9kVGltZSc6CiAgICAgICAgICByZXR1cm4gJ2RhdGUnOwogICAgICAgICAgY2FzZSAnZXZhbHVhdGlvblllYXInOgogICAgICAgICAgICByZXR1cm4gJ3llYXInOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gJ2RhdGUnOwogICAgICB9CiAgICB9LAoKICAgIC8v5L+h5Yib6YCC6YWN5pe26Ze05pi+56S6CiAgICBzaG93RGFwdCgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5pc2FkYXB0ID09PSAnTicpIHsKICAgICAgICB0aGlzLmZvcm0uYWRhcHREYXRlID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8v5a+G5bGP5bqU55So5bu66K6+5pe26Ze05pi+56S6CiAgICBzaG93Q2lwaGVyKCkgewogICAgICBpZiAodGhpcy5mb3JtLmlzY2lwaGVyID09PSAnTicpIHsKICAgICAgICB0aGlzLmZvcm0uY2lwaGVyRGF0ZSA9IG51bGw7CiAgICAgIH0KICAgIH0sCgogICAgc2VsZWN0VmVuZG9yKHBhcmFtcykgewogICAgICBpZiAodGhpcy5mb3JtLnZlbmRvcnMgPT0gbnVsbCB8fCB0aGlzLmZvcm0udmVuZG9ycyA9PSAnJykgewogICAgICAgIHRoaXMudmVuZG9yc2RhdGEgPSBudWxsOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudmVuZG9yc2RhdGEgPSAnMSc7CiAgICAgIH0KICAgICAgcmV0dXJuIGxpc3RWZW5kb3JCeUFwcGxpY2F0aW9uKHsKICAgICAgICBhcHBsaWNhdGlvbklkOiB0aGlzLmFzc2V0SWQsCiAgICAgICAgYXBwbGljYXRpb25Db2RlOiB0aGlzLmZvcm0udmVuZG9ycywKICAgICAgICAuLi5wYXJhbXMKICAgICAgfSk7CiAgICB9LAogICAgZ2V0QWxsU2VydmVyTGlzdCgpewogICAgICBsaXN0QWxsT3ZlcnZpZXcoeyJhc3NldENsYXNzIjo0fSkudGhlbihyZXMgPT57CiAgICAgICAgdGhpcy5zZXJ2ZXJPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWIneWni+WMliAqLwogICAgYXN5bmMgaW5pdCgpIHsKICAgICAgLy8gbGV0IHBhcmFtcyA9IHRoaXMuJHJvdXRlLnF1ZXJ5OwogICAgICBpZiAodGhpcy5hc3NldElkKSB7CiAgICAgICAgYXdhaXQgZ2V0QXBwbGljYXRpb24odGhpcy5hc3NldElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIC8vIOiOt+WPluW6lOeUqOS/oeaBr+ivpuaDhQogICAgICAgICAgdGhpcy5mb3JtLmFzc2V0SWQgPSB0aGlzLmFzc2V0SWQ7CiAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhLmFwcGxpY2F0aW9uVk87CiAgICAgICAgICBpZihyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYXBwbGljYXRpb25WTyAmJiByZXNwb25zZS5kYXRhLmFwcGxpY2F0aW9uVk8uc3lzdGVtVHlwZSl7CiAgICAgICAgICAgIHRoaXMuZm9ybS5zeXN0ZW1UeXBlID0gcmVzcG9uc2UuZGF0YS5hcHBsaWNhdGlvblZPLnN5c3RlbVR5cGUudG9TdHJpbmcoKTsKICAgICAgICAgIH0KICAgICAgICAgIHdhaXRGb3JWYWx1ZSgoKSA9PiBnZXRWYWxGcm9tT2JqZWN0KCdzaXRlJywgdGhpcy4kcmVmcywgbnVsbCkpLnRoZW4oc2l0ZSA9PiB7CiAgICAgICAgICAgIGlmKCFzaXRlKXsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYoc2l0ZSBpbnN0YW5jZW9mIEFycmF5KXsKICAgICAgICAgICAgICBzaXRlLmZvckVhY2goaXRlbSA9PiBpdGVtLmdldExpc3QoKSk7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICBzaXRlLmdldExpc3QoKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgICAgLy8g6I635Y+W5Lia5Yqh5L+h5oGv6K+m5oOFCiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS5hc3NldElkID0gdGhpcy5hc3NldElkOwogICAgICAgICAgdGhpcy5idXNpbmVzc0Zvcm0gPSByZXNwb25zZS5kYXRhLnRibEJ1c2luZXNzQXBwbGljYXRpb247CiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS51c2VyTnVtcyA9IHRoaXMuYnVzaW5lc3NGb3JtLnVzZXJOdW1zICE9PSBudWxsID8gdGhpcy5idXNpbmVzc0Zvcm0udXNlck51bXMgKyAnJyA6ICcnOwogICAgICAgICAgdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlWaXNpdE51bXMgPSB0aGlzLmJ1c2luZXNzRm9ybS5ldmVyeWRheVZpc2l0TnVtcyAhPT0gbnVsbCA/IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5VmlzaXROdW1zICsgJycgOiAnJzsKICAgICAgICAgIHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyA9IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyAhPT0gbnVsbCA/IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyArICcnIDogJyc7CiAgICAgICAgICB0aGlzLmZ1bmN0aW9uU3RhdGVMaXN0ID0gcmVzcG9uc2UuZGF0YS50YmxCdXNpbmVzc0FwcGxpY2F0aW9uLnRibE1hcHBlckxpc3QgfHwgW3t9LCB7fSwge31dOwogICAgICAgICAgaWYgKHRoaXMuZnVuY3Rpb25TdGF0ZUxpc3QubGVuZ3RoIDwgMykgewogICAgICAgICAgICBsZXQgaSA9IDA7CiAgICAgICAgICAgIHdoaWxlIChpIDwgMyAtIHRoaXMuZnVuY3Rpb25TdGF0ZUxpc3QubGVuZ3RoKSB7CiAgICAgICAgICAgICAgdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5wdXNoKHt9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgIHRoaXMuYWZ0ZXJJbml0ID0gdHJ1ZTsKICAgICAgICB9KQogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5hZnRlckluaXQgPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIC8vIOagoemqjOaVsOaNrgogICAgdmFsaWRhdGVGb3JtKCkgewogICAgICBsZXQgZmxhZyA9IHRydWU7CiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKCF2YWxpZCkgewogICAgICAgICAgZmxhZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuJHJlZnNbJ2J1c2luZXNzRm9ybSddLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAoIXZhbGlkKSB7CiAgICAgICAgICBmbGFnID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIGZsYWc7CiAgICB9LAoKICAgIC8qKiDkv53lrZjmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVNhdmUoKSB7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgbGV0IHBhc3MxLCBwYXNzMiA9IHRydWU7CiAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHBhc3MxID0gdmFsaWQpOyAvLyDns7vnu5/ln7rmnKzkv6Hmga/moKHpqowKICAgICAgICB0aGlzLiRyZWZzWyJidXNpbmVzc0Zvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiBwYXNzMiA9IHZhbGlkKTsgLy8g5Lia5Yqh5L+h5oGv5qCh6aqMCgogICAgICAgIC8vIOWkhOeQhuezu+e7n+WfuuacrOS/oeaBrwogICAgICAgIGxldCBsaW5rID0gdGhpcy5maWx0ZXJOdWxsKHRoaXMuZm9ybS5saW5rcykKICAgICAgICBsaW5rLmZvckVhY2gobCA9PiB7CiAgICAgICAgICBpZiAoIShsLmxpbmtJcCAmJiBsLmxpbmtJcC5sZW5ndGggPiAwKSkgewogICAgICAgICAgICBsLmxpbmtJcCA9IG51bGw7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoIShsLmxpbmtQb3J0ICYmIGwubGlua1BvcnQubGVuZ3RoID4gMCkpIHsKICAgICAgICAgICAgbC5saW5rUG9ydCA9IG51bGw7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICB0aGlzLmZvcm0ubGlua3MgPSBsaW5rOwoKICAgICAgICAvLyDlpITnkIbkuJrliqHkv6Hmga8KICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS50YmxNYXBwZXJMaXN0ID0gdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5maWx0ZXIoZnVuID0+IGZ1bi5tb2R1bGVOYW1lKTsKICAgICAgICBmb3IgKGxldCBtb2R1bGVGb3JtS2V5IGluIHRoaXMuJHJlZnMubW9kdWxlRm9ybSkgewogICAgICAgICAgaWYgKCFwYXNzMikgcmV0dXJuIHJlamVjdCgpOwogICAgICAgICAgdGhpcy4kcmVmcy5tb2R1bGVGb3JtW21vZHVsZUZvcm1LZXldLnZhbGlkYXRlKHZhbGlkID0+IHBhc3MyID0gdmFsaWQpCiAgICAgICAgfQoKICAgICAgICBjb25zb2xlLmxvZyh0aGlzLiRyZWZzLm5ldHdvcmtFViwgdGhpcy4kcmVmcy5zYWZlRVYsIHRoaXMuJHJlZnMuc2VydmVyRVYsIHRoaXMuJHJlZnMuZGF0ZUVWLCAnc3Nzc3NzcycpCgogICAgICAgIC8vIOWkhOeQhui9r+ehrOS7tuS/oeaBrwogICAgICAgIGxldCBmb3JtID0ge307CiAgICAgICAgY29uc3QgaGFyZHdhcmVDb21wb25lbnRzID0gWwogICAgICAgICAgJ3NlcnZlckVWJywKICAgICAgICAgICdkYXRlRVYnLAogICAgICAgICAgJ25ldHdvcmtFVicsCiAgICAgICAgICAnc2FmZUVWJwogICAgICAgIF07CgogICAgICAgIGZvciAobGV0IHJlZiBvZiBoYXJkd2FyZUNvbXBvbmVudHMpIHsKICAgICAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHRoaXMuJHJlZnNbcmVmXTsKICAgICAgICAgIGlmICghY29tcG9uZW50KSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYCR7cmVmfSDnu4Tku7bmnKrmib7liLBgKTsKICAgICAgICAgICAgY29udGludWU7CiAgICAgICAgICB9CgogICAgICAgICAgY29uc3QgY29tcEluc3RhbmNlID0gQXJyYXkuaXNBcnJheShjb21wb25lbnQpID8gY29tcG9uZW50WzBdIDogY29tcG9uZW50OwoKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBjb21wSW5zdGFuY2Uuc3VibWl0KCk7CiAgICAgICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3N0cmluZycpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgICBmb3JtW3JlZl0gPSB7IGxpc3Q6IGRhdGEubGlzdCwgZGVsTGlzdDogZGF0YS5kZWxMaXN0IH07CiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICBjb25zb2xlLmVycm9yKGDosIPnlKggJHtyZWZ9LnN1Ym1pdCgpIOWksei0pTpgLCBlcnJvcik7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7cmVmfSDmj5DkuqTlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKTsKICAgICAgICAgIH0KICAgICAgICB9CgoKICAgICAgICAvLyDmlbTlkIjlj4LmlbAKICAgICAgICBsZXQgcGFyYW1zID0gewogICAgICAgICAgc2VyaWFsVmVyc2lvblVJRDogMCwKICAgICAgICAgIGFwcGxpY2F0aW9uVk86IHRoaXMuZm9ybSwKICAgICAgICAgIHRibEJ1c2luZXNzQXBwbGljYXRpb246IHRoaXMuYnVzaW5lc3NGb3JtLAogICAgICAgICAgaGFyZFdhcmVFVjogZm9ybSwKICAgICAgICB9CgogICAgICAgIGlmICh0aGlzLmFzc2V0SWQgIT0gbnVsbCkgewogICAgICAgICAgdXBkYXRlQXBwbGljYXRpb25JbmZvKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmluaXQoKQogICAgICAgICAgICByZXR1cm4gcmVzb2x2ZSgpOwogICAgICAgICAgfSkuY2F0Y2goKGVycikgPT4gewogICAgICAgICAgICByZXR1cm4gcmVqZWN0KGVycik7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgYWRkQXBwbGljYXRpb25JbmZvKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5hc3NldElkID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgdGhpcy5jaGFuZ2VJZChyZXNwb25zZS5kYXRhKTsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgIHRoaXMuaW5pdCgpCiAgICAgICAgICAgIHJldHVybiByZXNvbHZlKCk7CiAgICAgICAgICB9KS5jYXRjaChlcnIgPT4gewogICAgICAgICAgICByZXR1cm4gcmVqZWN0KGVycik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIGlucHV0VG9TdHJpbmcodmFsLCBuYW1lKSB7CiAgICAgIGlmICh2YWwpCiAgICAgICAgdGhpcy5mb3JtW25hbWVdID0gIiIgKyB2YWw7CiAgICB9LAoKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5wdXNoKHt9KTsKICAgICAgdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgIGl0ZW0udGVtcElkID0gZ2VuZXJhdGVTZWN1cmVVVUlEKCk7CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsKG1vZHVsZUlkLCBpbmRleCkgewogICAgICBjb25zb2xlLmxvZygnZGVsJyx0aGlzLmZ1bmN0aW9uU3RhdGVMaXN0KQogICAgICB0aGlzLmZ1bmN0aW9uU3RhdGVMaXN0LnNwbGljZShpbmRleCwgMSk7CiAgICAgIGlmICghdGhpcy5idXNpbmVzc0Zvcm0uZGVsTGlzdCkgewogICAgICAgIHRoaXMuYnVzaW5lc3NGb3JtLmRlbExpc3QgPSBbXTsKICAgICAgfQogICAgICB0aGlzLmJ1c2luZXNzRm9ybS5kZWxMaXN0LnB1c2gobW9kdWxlSWQpOwogICAgfSwKCiAgICAvKiog6KGo5Y2V6YeN572uICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGFzc2V0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENvZGU6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldE5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzb2Z0d2FyZVZlcnNpb246IHVuZGVmaW5lZCwKICAgICAgICBkZWdyZWVJbXBvcnRhbmNlOiB1bmRlZmluZWQsCiAgICAgICAgbWFuYWdlcjogdW5kZWZpbmVkLAogICAgICAgIGRvbWFpblVybDogdW5kZWZpbmVkLAogICAgICAgIHN5c3RlbVR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZURlc2M6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENsYXNzOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRDbGFzc0Rlc2M6IHVuZGVmaW5lZCwKICAgICAgICBjb25zdHJ1Y3Q6IHVuZGVmaW5lZCwKICAgICAgICBuZXRUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgYXBwVHlwZTogdW5kZWZpbmVkLAogICAgICAgIHNlcnZpY2VHcm91cDogdW5kZWZpbmVkLAogICAgICAgIGZyZXF1ZW5jeTogdW5kZWZpbmVkLAogICAgICAgIHVzYWdlQ291bnQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyU2NhbGU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyT2JqZWN0OiB1bmRlZmluZWQsCiAgICAgICAgdXJsOiB1bmRlZmluZWQsCiAgICAgICAgaXBkOiB1bmRlZmluZWQsCiAgICAgICAgdGVjaG5pY2FsOiB1bmRlZmluZWQsCiAgICAgICAgZGVwbG95OiB1bmRlZmluZWQsCiAgICAgICAgc3RvcmFnZTogdW5kZWZpbmVkLAogICAgICAgIG5ldGVudjogdW5kZWZpbmVkLAogICAgICAgIGlza2V5OiB1bmRlZmluZWQsCiAgICAgICAgZGF0YW51bTogdW5kZWZpbmVkLAogICAgICAgIGlzYmFzZTogIjAiLAogICAgICAgIGlzbGluazogdW5kZWZpbmVkLAogICAgICAgIGlzaGFyZTogdW5kZWZpbmVkLAogICAgICAgIGlzbG9nOiB1bmRlZmluZWQsCiAgICAgICAgaXNwbGFuOiB1bmRlZmluZWQsCiAgICAgICAgaXNhZGFwdDogdW5kZWZpbmVkLAogICAgICAgIGlzY2lwaGVyOiB1bmRlZmluZWQsCiAgICAgICAgYWRhcHREYXRlOiB1bmRlZmluZWQsCiAgICAgICAgY2lwaGVyRGF0ZTogdW5kZWZpbmVkLAogICAgICAgIGZ1bmN0aW9uOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgb3JnbklkOiB1bmRlZmluZWQsCiAgICAgICAgdmVuZG9yczogdW5kZWZpbmVkLAogICAgICAgIHVwVGltZTogdW5kZWZpbmVkLAogICAgICAgIGR3aWQ6IHVuZGVmaW5lZCwKICAgICAgICBjb250YWN0b3I6IHVuZGVmaW5lZCwKICAgICAgICBkb21haW5JZDogdW5kZWZpbmVkLAogICAgICAgIG5ldFNjYWxlOiB1bmRlZmluZWQsCiAgICAgICAgbmV0VG9wbzogdW5kZWZpbmVkLAogICAgICAgIG5ldE1lbW86IHVuZGVmaW5lZCwKICAgICAgICB0YWdzOiAiIiwKICAgICAgICBsaW5rczogW10sCiAgICAgICAgZWlkczogW10sCiAgICAgIH07CiAgICAgIHRoaXMuYnVzaW5lc3NGb3JtID0gewogICAgICAgIHN5c0J1c2luZXNzU3RhdGU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTnVtczogdW5kZWZpbmVkLAogICAgICAgIGV2ZXJ5ZGF5VmlzaXROdW1zOiB1bmRlZmluZWQsCiAgICAgICAgZXZlcnlkYXlBY3RpdmVOdW1zOiB1bmRlZmluZWQsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJidXNpbmVzc0Zvcm0iKTsKICAgIH0sCiAgICAvKiog55So5oi36YCJ5oupICovCiAgICBoYW5kbGVVc2VyU2VsZWN0KHZhbCkgewogICAgICBpZiAodGhpcy5mb3JtLm1hbmFnZXIgPT0gbnVsbCB8fCB0aGlzLmZvcm0ubWFuYWdlciA9PSAnJykgewogICAgICAgIHRoaXMudXNlcmRhdGEgPSBudWxsOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudXNlcmRhdGEgPSAnMSc7CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnBob25lID0gdmFsOwogICAgfSwKICAgIC8qKiDov4fmu6TnqbrlgLwgKi8KICAgIGZpbHRlck51bGwodmFsdWUpIHsKICAgICAgLy8gcmV0dXJuIHZhbHVlLmZpbHRlcigoaXRlbSkgPT4gSlNPTi5zdHJpbmdpZnkoaXRlbSkgIT09ICd7fScpOwogICAgICByZXR1cm4gdmFsdWUuZmlsdGVyKChpdGVtKSA9PiBPYmplY3Qua2V5cyhpdGVtKS5sZW5ndGggIT09IDApOwogICAgfSwKICAgIGFkZFNlcnZlclN1Y2Nlc3Mocm93KXsKICAgICAgdGhpcy5nZXRBbGxTZXJ2ZXJMaXN0KCk7CiAgICAgIHRoaXMuc2hvd0FkZFNlcnZlciA9IGZhbHNlOwogICAgfSwKICAgIGFkZFNlcnZlckNhbmNlbCgpewogICAgICB0aGlzLnNob3dBZGRTZXJ2ZXIgPSBmYWxzZTsKICAgIH0sCiAgICBhZGRBc3NldEhhbmRsZSgpewogICAgICB0aGlzLnNob3dBZGRTZXJ2ZXIgPSB0cnVlOwogICAgfSwKICAgIHNlcnZlclNlbGVjdChkYXRhKXsKICAgICAgaWYoZGF0YSl7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2Fzc29jaWF0aW9uU2VydmVyJywgZGF0YS5tYXAoaXRlbSA9PiBpdGVtLnNlcnZlcklkKSkKICAgICAgfQogICAgfSwKICAgIHNlcnZlckNoYW5nZSh2YWwpewogICAgICBjb25zb2xlLmxvZygic2VydmVyOiIsdmFsKQogICAgICBpZighdmFsIHx8IHZhbC5sZW5ndGg8MSl7CiAgICAgICAgdGhpcy5jdXJyZW50QXNzb2NpYXRpb25TZXJ2ZXIgPSBbXTsKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMuY3VycmVudEFzc29jaWF0aW9uU2VydmVyID0gdmFsLm1hcChpdGVtID0+IHRoaXMuc2VydmVyT3B0aW9ucy5maW5kKHNlcnZlciA9PiBzZXJ2ZXIuYXNzZXRJZCA9PT0gaXRlbSkpOwogICAgICB9CiAgICAgIHJldHVybiB2YWw7CiAgICB9CiAgfSwKfQo="}, {"version": 3, "sources": ["systemDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "systemDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <div class=\"box-container\" style=\"overflow-x: hidden;\" v-loading=\"loading\">\n    <!--系统基本信息-->\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" >\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染字段 -->\n          <template v-for=\"group in basicInfoAssetFields\">\n            <el-row\n              :gutter=\"20\"\n              type=\"flex\"\n              :key=\"group.formName\"\n              v-if=\"group.isShow\"\n              style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\" v-if=\"group.isShow\">\n                  <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <template v-if=\"group.formName === '外部连接信息'\">\n                <el-col :span=\"24\">\n                  <ApplicationLink\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    v-model=\"form.links\"/>\n                </el-col>\n              </template>\n\n              <template v-else-if=\"group.formName === '运营维护情况'\">\n                <el-col :span=\"24\">\n                  <ApplicationSite\n                    ref=\"site\"\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    :value.sync=\"form.eids\"\n                    :asset-id=\"form.assetId\"\n                    multiple/>\n                </el-col>\n              </template>\n\n              <template v-else>\n                <el-col\n                  v-for=\"field in group.fieldsItems\"\n                  :key=\"field.fieldKey\"\n                  :style=\"radioGroupType.includes(field.fieldKey) ? { display: 'flex' } : ''\"\n                  :span=\"getFieldSpan(field)\"\n                  v-if=\"shouldShowField(field)\"\n                >\n                  <!-- 其他系统备注 -->\n                  <template v-if=\"field.fieldKey === 'otherSystemNotes'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.systemType == 12\"\n                    >\n                      <el-input\n                        v-model=\"form.otherSystemNotes\"\n                        placeholder=\"请输入其它系统备注\"\n                      />\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：信创适配时间 -->\n                  <template v-else-if=\"field.fieldKey === 'adaptDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.isadapt === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.adaptDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：密码应用建设时间 -->\n                  <template v-else-if=\"field.fieldKey === 'cipherDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.iscipher === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.cipherDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：警综对接 -->\n                  <template v-else-if=\"field.fieldKey === 'islink'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"deployLocation === 'fair'\"\n                    >\n                      <el-radio-group v-model=\"form.islink\">\n                        <el-radio\n                          v-for=\"dict in dict.type.sys_yes_no\"\n                          :key=\"dict.value\"\n                          :label=\"dict.value\"\n                        >{{ dict.label }}\n                        </el-radio>\n                      </el-radio-group>\n                    </el-form-item>\n                  </template>\n                  <!-- 关联服务器字段处理 -->\n                  <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      class=\"associationServer\"\n                    >\n                      <el-select\n                        v-model=\"form.associationServer\"\n                        placeholder=\"请选择服务器\"\n                        multiple\n                        filterable\n                        clearable\n                        @change=\"serverChange\"\n                      >\n                        <el-option\n                          v-for=\"server in serverOptions\"\n                          :key=\"server.assetId\"\n                          :label=\"server.assetName\"\n                          :value=\"server.assetId\"\n                        >\n                          <span style=\"float: left\">{{ server.assetName }}</span>\n                          <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ server.ip }}</span>\n                        </el-option>\n                      </el-select>\n                      <el-button\n                        size=\"mini\"\n                        plain\n                        type=\"primary\"\n                        style=\"margin-left: 10px;\"\n                        @click=\"addAssetHandle\"\n                        :disabled=\"!$editable.value\"\n                      >\n                        新增服务器\n                      </el-button>\n                    </el-form-item>\n                  </template>\n\n                  <el-form-item\n                    v-else\n                    :label=\"field.fieldName\"\n                    :prop=\"field.fieldKey\"\n                    :rules=\"getFieldRules(field)\"\n                  >\n                    <template v-if=\"field.fieldKey === 'deptId'\">\n                      <dept-select\n                        v-model=\"form.deptId\"\n                        is-current\n                        :isAllData=\"!$editable.value\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'manager'\">\n                      <user-select\n                        v-model=\"form.manager\"\n                        :placeholder=\"managePlaceholder\"\n                        :userdata=\"userdata\"\n                        multiple\n                        @setPhone=\"handleUserSelect\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'domainId'\">\n                      <NetworkSelect v-model=\"form.domainId\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'tags'\">\n                      <Dynamic-Tag v-model=\"form.tags\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'vendor'\">\n                      <VendorSelect2\n                        v-model=\"form.vendors\"\n                        multiple\n                        placeholder=\"请选择开发合作企业\"\n                        :vendorsdata=\"vendorsdata\"\n                        :selectVendor=\"selectVendor\"\n                        :isDisabled=\"!$editable.value\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'waitingInsuranceFilingScan' || field.fieldKey === 'evaluationReport' || field.fieldKey === 'netTopo'\">\n                      <file-upload\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </template>\n\n                    <!-- 默认字段渲染 -->\n                    <template v-else>\n                      <!-- 下拉选择框 -->\n                      <el-select\n                        v-if=\"selectType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        :popper-append-to-body=\"false\"\n                        clearable\n                        filterable\n                        v-bind=\"field.props\"\n                      >\n                        <el-option\n                          v-for=\"item in getDictOptions(field.fieldKey)\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\"\n                        />\n                      </el-select>\n\n                      <!-- 日期选择器 -->\n                      <el-date-picker\n                        v-else-if=\"dateType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :type=\"getDateType(field)\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 单选按钮组 -->\n                      <el-radio-group\n                        v-else-if=\"radioGroupType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        v-bind=\"field.props\"\n                      >\n                        <el-radio\n                          v-for=\"item in dict.type.sys_yes_no\"\n                          :key=\"item.value\"\n                          :label=\"item.value\"\n                          @change=\"field.fieldKey === 'isadapt' ? showDapt : field.fieldKey === 'iscipher' ? showCipher : null \"\n                        >{{ item.label }}</el-radio>\n                      </el-radio-group>\n\n                      <!-- 多行文本输入 -->\n                      <el-input\n                        v-else-if=\"textareaType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        type=\"textarea\"\n                        :rows=\"3\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 文件上传 -->\n                      <file-upload\n                        v-else-if=\"field.type === 'file'\"\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 默认文本输入 -->\n                      <el-input\n                        v-else\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n                    </template>\n                  </el-form-item>\n                </el-col>\n              </template>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--业务基本信息-->\n    <el-form ref=\"businessForm\" :model=\"businessForm\" :rules=\"businessRules\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染业务信息字段 -->\n          <template v-for=\"group in businessAssetFields\">\n            <el-row :key=\"group.formName\" :gutter=\"20\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\" v-if=\"group.isShow\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '用户规模' && group.isShow\" src=\"@/assets/images/application/yhgm.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '业务描述' && group.isShow\" src=\"@/assets/images/application/ywms.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '功能模块' && group.isShow\" src=\"@/assets/images/application/gnmk.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <div class=\"my-form\" v-if=\"group.isShow\">\n                <!-- 用户规模特殊处理 -->\n                <template v-if=\"group.formName === '用户规模'\">\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"覆盖地域\" prop=\"coverArea\">\n                      <el-select v-model=\"businessForm.coverArea\" placeholder=\"请选择覆盖地域\">\n                        <el-option\n                          v-for=\"dict in dict.type.cover_area\"\n                          :key=\"dict.value\"\n                          :label=\"dict.label\"\n                          :value=\"dict.value\"\n                        ></el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"使用对象\" prop=\"serviceGroup\">\n                      <dict-select v-model=\"businessForm.serviceGroup\" dict-name=\"serve_group\" placeholder=\"请选择使用对象\"\n                                   multiple\n                                   clearable/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"授权用户数\" prop=\"userNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.userNums\" @input=\"val=>inputToString(val,'userNums')\"\n                        placeholder=\"输入用户数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统日均访客量\" prop=\"everydayVisitNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayVisitNums\"\n                        @input=\"val=>inputToString(val,'everydayVisitNums')\"\n                        placeholder=\"输入系统日均访客量\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统月均活跃人数\" prop=\"everydayActiveNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayActiveNums\"\n                        @input=\"val=>inputToString(val,'everydayActiveNums')\"\n                        placeholder=\"输入系统月均活跃人数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 业务描述特殊处理 -->\n                <template v-else-if=\"group.formName === '业务描述'\">\n                  <el-col :span=\"24\">\n                    <el-form-item :label=\"'总体系统业务说明'\" prop=\"sysBusinessState\">\n                      <el-input :rows=\"6\" :maxlength=\"800\" v-model=\"businessForm.sysBusinessState\" type=\"textarea\"\n                                placeholder=\"请输入系统业务说明..\"/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"上传操作手册\" prop=\"operateHandbook\">\n                      <file-upload :disUpload=\"!$editable.value\"\n                                   v-model=\"businessForm.operateHandbook\"\n                                   :limit=\"5\"\n                                   :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 功能模块特殊处理 -->\n                <template v-else-if=\"group.formName === '功能模块'\">\n                  <el-col :span=\"24\" class=\"my-form\">\n                    <div v-for=\"(item, index) in functionStateList\" :key=\"index\">\n                      <el-form ref=\"moduleForm\" :model=\"item\" :rules=\"moduleRule\" :disabled=\"!$editable.value\">\n                        <el-row :gutter=\"20\" type=\"flex\" align=\"middle\" style=\"flex-wrap: wrap;\">\n                          <el-col :span=\"4\">\n                            <el-form-item prop=\"moduleName\">\n                              <el-input v-model=\"item.moduleName\" placeholder=\"功能模块名称\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"18\">\n                            <el-form-item prop=\"moduleDesc\">\n                              <el-input :rows=\"3\" v-model=\"item.moduleDesc\" type=\"textarea\"\n                                        placeholder=\"请将该功能模块实现的功能、用途做全面的说明，并保证内容应正确、完整、一致和可验证。\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"2\">\n                            <el-button\n                              size=\"mini\"\n                              type=\"text\"\n                              icon=\"el-icon-remove\"\n                              @click=\"handleDel(item.moduleId,index)\"\n                            >删除\n                            </el-button>\n                          </el-col>\n                        </el-row>\n                      </el-form>\n                    </div>\n                    <el-row type=\"flex\" justify=\"end\" style=\"flex-wrap: wrap;\">\n                      <el-col :span=\"2\">\n                        <el-button\n                          type=\"primary\"\n                          plain\n                          icon=\"el-icon-plus\"\n                          size=\"mini\"\n                          @click=\"handleAdd\"\n                        >新增\n                        </el-button>\n                      </el-col>\n                    </el-row>\n                  </el-col>\n                </template>\n              </div>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--系统软硬件环境-->\n    <el-form ref=\"hardWareForm\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染软硬件环境字段 -->\n          <template v-for=\"group in runTimeAssetFields\">\n            <el-row :key=\"group.formName\" v-if=\"group.isShow\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <el-col :span=\"24\">\n                <!-- 根据分组名称渲染不同子组件 -->\n                <template v-if=\"group.formName === '所安装服务器环境'\">\n                  <serverEV\n                    ref=\"serverEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :data-list=\"currentAssociationServer\"\n                    @selected=\"serverSelect\"\n                    v-if=\"afterInit\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '所安装数据库环境'\">\n                  <dateEV\n                    ref=\"dateEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联网络设备'\">\n                  <networkEV\n                    ref=\"networkEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联安全设备'\">\n                  <safeEV\n                    ref=\"safeEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"/>\n                </template>\n              </el-col>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <edit-server\n      ref=\"adds\"\n      title=\"添加服务器\"\n      :edit-flag-visible.sync=\"showAddServer\"\n      @cancel=\"addServerCancel\"\n      @confirm=\"addServerSuccess\"/>\n\n  </div>\n</template>\n\n<script>\nimport {addApplicationInfo, updateApplicationInfo, getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\n\nexport default {\n  name: \"systemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"应用名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetClass: [\n          {required: true, message: \"资产分类不能为空\", trigger: \"blur\"},\n        ],\n        domainId : [\n          {required: true, message: \"主部署网络不能为空\", trigger: \"blur\"},\n        ],\n        domainUrl: [\n          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},\n          {\n            pattern: /^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*$/,\n            message: \"请输入正确的域名\",\n            trigger: ['blur', 'change']\n          },\n        ],\n        manager: [\n          {required: true, message: \"负责人不能为空\", trigger: \"blur\"},\n        ],\n        userId: [\n          // { required: true, message: \"用户ID不能为空\", trigger: \"blur\" }\n        ],\n        deptId: [\n          {required: true, message: \"单位不能为空\", trigger: \"blur\"},\n        ],\n        phone: [\n          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},\n          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的联系电话\", trigger: ['blur', 'change']},\n        ],\n        url: [\n          {required: true, message: \"登录地址不能为空\", trigger: \"blur\"},\n          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},\n          {\n            // 正则表达式用于验证 URL 格式\n            // 支持 http/https 协议，允许 IP 地址或域名，支持端口号和路径\n            //pattern: /^(https?:\\/\\/)?(([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[\\w.-]*)*$/,\n            //pattern: /^(?:#\\/?[^\\s#]+|(https?|ftp):\\/\\/([\\w.-]+|\\[[\\da-fA-F:]+\\])(:\\d+)?(\\/[^?\\s#]*)?(\\?[^\\s#]*)?(#.*)?)$/,\n            pattern: /^(https?:\\/\\/)?((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[^\\s?#]*)?(\\?[^\\s#]*)?(#.*)?$/,\n            message: \"请输入正确的登录地址\",\n            trigger: ['blur', 'change']\n          }\n        ],\n        ipd: [\n          {required: true, message: \"Ip地址段不能为空\", trigger: \"blur\"},\n          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},\n          {\n            pattern: /^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(,((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})*$/,\n            message: \"请输入正确的IP地址段，多个IP地址用逗号隔开\",\n            trigger: ['blur', 'change']\n          },\n        ],\n\n        netMemo: [\n          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},\n        ],\n      },\n      businessRules: {\n        sysBusinessState: [\n          {required: false, min: 0, max: 800, message: '拓扑图说明不能超过 800 个字符', trigger: 'blur'},\n        ],\n        userNums: [\n          {required: false, max: 12, message: '用户数量不能超过 12 个字符', trigger: 'blur'},\n          {required: false, pattern: /^[0-9]*$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayVisitNums: [\n          {min: 0, max: 12, message: '日均访问数量不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayActiveNums: [\n          {min: 0, max: 12, message: '月均活跃人数不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n      },\n      moduleRule: {\n        moduleName: [\n          {min: 0, max: 64, message: '功能模块名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        moduleDesc: [\n          {min: 0, max: 2000, message: '功能模块说明不能超过 2000 个字符', trigger: 'blur'},\n        ],\n      },\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      selectType: ['systemType','construct','loginType','technical','deploy','state','protectGrade', 'evaluationResults', 'evaluationStatus', 'hwIsTrueShutDown'],\n      dateType: ['uodTime', 'waitingInsuranceFilingTime', 'evaluationYear'],\n      textareaType: ['netMemo', 'remark'],\n      radioGroupType: ['iskey', 'isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'isOpenNetwork'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if(Object.keys(item).length > 0){\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    }\n  },\n  computed: {\n    // 业务信息动态字段\n    businessAssetFields() {\n      return (this.assetList.slice(7, 10) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    // 基本信息动态字段\n    basicInfoAssetFields() {\n      let assetFields = this.assetList.slice(0, 7);\n      return assetFields.map(group => {\n        return {\n          ...group,\n          fieldsItems: group.fieldsItems.filter(item => item.isShow)\n        };\n      })\n    },\n    // 软硬件环境动态字段\n    runTimeAssetFields() {\n      return (this.assetList.slice(10, 14) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    dynamicRules() {\n      const rules = {};\n      const ruleSets = {...this.rules, ...this.businessRules};\n      let visibleAssetFields = [...this.basicInfoAssetFields, ...this.businessAssetFields]\n      visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (ruleSets[fieldKey]) {\n            const filteredRules = ruleSets[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required)\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: ['blur', 'change']\n              });\n            }\n          }\n        })\n      })\n      return rules;\n    }\n  },\n  methods: {\n    // 字段校验规则\n    getFieldRules(field) {\n      return this.dynamicRules[field.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getFieldSpan(field) {\n      // 特殊字段占24列\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan', 'remark'];\n      if (fullSpanFields.includes(field.fieldKey)) return 24;\n      // 其他字段默认占8列\n      return 8;\n    },\n\n    shouldShowField(field) {\n      // 其他系统备注 - 只在 systemType 为 12 时显示\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType == 12;\n      }\n\n      // 信创适配时间 - 只在 isadapt 为 'Y' 时显示\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n\n      // 密码应用建设时间 - 只在 iscipher 为 'Y' 时显示\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n\n      // 警综对接 - 只在特定部署位置显示\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n\n      // 其他字段默认显示\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down'\n      };\n\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    // 获取字段的日期类型\n    getDateType(field) {\n      switch (field.fieldKey) {\n        case 'uodTime':\n          return 'date';\n          case 'evaluationYear':\n            return 'year';\n        default:\n          return 'date';\n      }\n    },\n\n    //信创适配时间显示\n    showDapt() {\n      if (this.form.isadapt === 'N') {\n        this.form.adaptDate = null;\n      }\n    },\n    //密屏应用建设时间显示\n    showCipher() {\n      if (this.form.iscipher === 'N') {\n        this.form.cipherDate = null;\n      }\n    },\n\n    selectVendor(params) {\n      if (this.form.vendors == null || this.form.vendors == '') {\n        this.vendorsdata = null;\n      } else {\n        this.vendorsdata = '1';\n      }\n      return listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        ...params\n      });\n    },\n    getAllServerList(){\n      listAllOverview({\"assetClass\":4}).then(res =>{\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          if(response.data && response.data.applicationVO && response.data.applicationVO.systemType){\n            this.form.systemType = response.data.applicationVO.systemType.toString();\n          }\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(()=>{\n          this.afterInit = true;\n        })\n      }else {\n        this.afterInit = true;\n      }\n    },\n\n    // 校验数据\n    validateForm() {\n      let flag = true;\n      this.$refs['form'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      this.$refs['businessForm'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      return flag;\n    },\n\n    /** 保存按钮操作 */\n    handleSave() {\n      return new Promise((resolve, reject) => {\n        let pass1, pass2 = true;\n        this.$refs[\"form\"].validate(valid => pass1 = valid); // 系统基本信息校验\n        this.$refs[\"businessForm\"].validate(valid => pass2 = valid); // 业务信息校验\n\n        // 处理系统基本信息\n        let link = this.filterNull(this.form.links)\n        link.forEach(l => {\n          if (!(l.linkIp && l.linkIp.length > 0)) {\n            l.linkIp = null;\n          }\n          if (!(l.linkPort && l.linkPort.length > 0)) {\n            l.linkPort = null;\n          }\n        })\n        this.form.links = link;\n\n        // 处理业务信息\n        this.businessForm.tblMapperList = this.functionStateList.filter(fun => fun.moduleName);\n        for (let moduleFormKey in this.$refs.moduleForm) {\n          if (!pass2) return reject();\n          this.$refs.moduleForm[moduleFormKey].validate(valid => pass2 = valid)\n        }\n\n        console.log(this.$refs.networkEV, this.$refs.safeEV, this.$refs.serverEV, this.$refs.dateEV, 'sssssss')\n\n        // 处理软硬件信息\n        let form = {};\n        const hardwareComponents = [\n          'serverEV',\n          'dateEV',\n          'networkEV',\n          'safeEV'\n        ];\n\n        for (let ref of hardwareComponents) {\n          const component = this.$refs[ref];\n          if (!component) {\n            console.error(`${ref} 组件未找到`);\n            continue;\n          }\n\n          const compInstance = Array.isArray(component) ? component[0] : component;\n\n          try {\n            const data = compInstance.submit();\n            if (typeof data === 'string') {\n              this.$message.error(data);\n              return;\n            }\n            form[ref] = { list: data.list, delList: data.delList };\n          } catch (error) {\n            console.error(`调用 ${ref}.submit() 失败:`, error);\n            this.$message.error(`${ref} 提交失败: ${error.message}`);\n          }\n        }\n\n\n        // 整合参数\n        let params = {\n          serialVersionUID: 0,\n          applicationVO: this.form,\n          tblBusinessApplication: this.businessForm,\n          hardWareEV: form,\n        }\n\n        if (this.assetId != null) {\n          updateApplicationInfo(params).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.init()\n            return resolve();\n          }).catch((err) => {\n            return reject(err);\n          });\n        } else {\n          addApplicationInfo(params).then(response => {\n            this.form.assetId = response.data;\n            this.changeId(response.data);\n            this.$modal.msgSuccess(\"新增成功\");\n            this.init()\n            return resolve();\n          }).catch(err => {\n            return reject(err);\n          });\n        }\n      })\n    },\n\n    inputToString(val, name) {\n      if (val)\n        this.form[name] = \"\" + val;\n    },\n\n    handleAdd() {\n      this.functionStateList.push({});\n      this.functionStateList.forEach((item, index) => {\n        item.tempId = generateSecureUUID();\n      })\n    },\n    handleDel(moduleId, index) {\n      console.log('del',this.functionStateList)\n      this.functionStateList.splice(index, 1);\n      if (!this.businessForm.delList) {\n        this.businessForm.delList = [];\n      }\n      this.businessForm.delList.push(moduleId);\n    },\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n    /** 用户选择 */\n    handleUserSelect(val) {\n      if (this.form.manager == null || this.form.manager == '') {\n        this.userdata = null;\n      } else {\n        this.userdata = '1';\n      }\n      this.form.phone = val;\n    },\n    /** 过滤空值 */\n    filterNull(value) {\n      // return value.filter((item) => JSON.stringify(item) !== '{}');\n      return value.filter((item) => Object.keys(item).length !== 0);\n    },\n    addServerSuccess(row){\n      this.getAllServerList();\n      this.showAddServer = false;\n    },\n    addServerCancel(){\n      this.showAddServer = false;\n    },\n    addAssetHandle(){\n      this.showAddServer = true;\n    },\n    serverSelect(data){\n      if(data){\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n    serverChange(val){\n      console.log(\"server:\",val)\n      if(!val || val.length<1){\n        this.currentAssociationServer = [];\n      }else {\n        this.currentAssociationServer = val.map(item => this.serverOptions.find(server => server.assetId === item));\n      }\n      return val;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.box-container {\n  padding-right: 20px;\n  .my-form {\n    width: 100%;\n    margin: 0 10px;\n  }\n}\n::v-deep .el-select-dropdown {\n  position: absolute !important;\n  left: 0 !important;\n  top: 30px!important;\n}\n::v-deep .el-date-editor {\n  width: 100%;\n}\n\n::v-deep .associationServer{\n  .el-form-item__content{\n    display: flex;\n    align-items: center;\n  }\n  .el-form-item__label{\n    display: contents;\n  }\n}\n</style>\n"]}]}