package com.ruoyi.safe.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 主机事件对象 ffsafe_host_events
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class FfsafeHostEvents extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键ID */
    private Long id;

    private Long eventId;

    /** 主机IP */
    @Excel(name = "主机IP")
    private String hostIp;

    /** 检测事项 */
    @Excel(name = "检测事项")
    private String monitorItem;

    /** 综合风险 */
    @Excel(name = "综合风险")
    private String overallRisk;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 处置状态 */
    @Excel(name = "处置状态")
    private String handlingStatus;

    /** 告警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date alarmTime;

    /** 主机名称 */
    @Excel(name = "主机名称")
    private String hostName;

    /** 操作系统 */
    @Excel(name = "操作系统")
    private String operatingSystem;

    /** MD5哈希值 */
    @Excel(name = "MD5哈希值")
    private String md5Hash;

    /** 可疑文件 */
    @Excel(name = "可疑文件")
    private String suspiciousFile;

    /** SHA256哈希值 */
    @Excel(name = "SHA256哈希值")
    private String sha256Hash;

    /** 文件创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "文件创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fileCreateTime;

    /** 文件修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "文件修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fileModifyTime;

    /** 检查引擎 */
    @Excel(name = "检查引擎")
    private String inspectionEngine;

    /** 异常类型 */
    @Excel(name = "异常类型")
    private String anomalyType;

    /** 检出信息 */
    @Excel(name = "检出信息")
    private String detectionInfo;

    /** AI模型名称 */
    @Excel(name = "AI模型名称")
    private String aiModel;

    /** 所属探针 */
    @Excel(name = "所属探针")
    private Long deviceConfigId;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 记录最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录最后更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

}
