<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.FfsafeHostEventsMapper">

    <resultMap type="FfsafeHostEvents" id="FfsafeHostEventsResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="monitorItem"    column="monitor_item"    />
        <result property="overallRisk"    column="overall_risk"    />
        <result property="category"    column="category"    />
        <result property="handlingStatus"    column="handling_status"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="hostName"    column="host_name"    />
        <result property="operatingSystem"    column="operating_system"    />
        <result property="md5Hash"    column="md5_hash"    />
        <result property="suspiciousFile"    column="suspicious_file"    />
        <result property="sha256Hash"    column="sha256_hash"    />
        <result property="fileCreateTime"    column="file_create_time"    />
        <result property="fileModifyTime"    column="file_modify_time"    />
        <result property="inspectionEngine"    column="inspection_engine"    />
        <result property="anomalyType"    column="anomaly_type"    />
        <result property="detectionInfo"    column="detection_info"    />
        <result property="aiModel"    column="ai_model"    />
        <result property="deviceConfigId"    column="device_config_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectFfsafeHostEventsVo">
        select id,event_id, host_ip, monitor_item, overall_risk, category, handling_status, alarm_time, host_name, operating_system, md5_hash, suspicious_file, sha256_hash, file_create_time, file_modify_time, inspection_engine, anomaly_type, detection_info, ai_model, device_config_id, created_at, updated_at from ffsafe_host_events
    </sql>

    <select id="selectFfsafeHostEventsList" parameterType="FfsafeHostEvents" resultMap="FfsafeHostEventsResult">
        <include refid="selectFfsafeHostEventsVo"/>
        <where>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="monitorItem != null  and monitorItem != ''"> and monitor_item = #{monitorItem}</if>
            <if test="overallRisk != null  and overallRisk != ''"> and overall_risk = #{overallRisk}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="handlingStatus != null  and handlingStatus != ''"> and handling_status = #{handlingStatus}</if>
            <if test="alarmTime != null "> and alarm_time = #{alarmTime}</if>
            <if test="hostName != null  and hostName != ''"> and host_name like concat('%', #{hostName}, '%')</if>
            <if test="operatingSystem != null  and operatingSystem != ''"> and operating_system = #{operatingSystem}</if>
            <if test="md5Hash != null  and md5Hash != ''"> and md5_hash = #{md5Hash}</if>
            <if test="suspiciousFile != null  and suspiciousFile != ''"> and suspicious_file = #{suspiciousFile}</if>
            <if test="sha256Hash != null  and sha256Hash != ''"> and sha256_hash = #{sha256Hash}</if>
            <if test="fileCreateTime != null "> and file_create_time = #{fileCreateTime}</if>
            <if test="fileModifyTime != null "> and file_modify_time = #{fileModifyTime}</if>
            <if test="inspectionEngine != null  and inspectionEngine != ''"> and inspection_engine = #{inspectionEngine}</if>
            <if test="anomalyType != null  and anomalyType != ''"> and anomaly_type = #{anomalyType}</if>
            <if test="detectionInfo != null  and detectionInfo != ''"> and detection_info = #{detectionInfo}</if>
            <if test="aiModel != null  and aiModel != ''"> and ai_model = #{aiModel}</if>
            <if test="deviceConfigId != null "> and device_config_id = #{deviceConfigId}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>

    <select id="selectFfsafeHostEventsById" parameterType="Long" resultMap="FfsafeHostEventsResult">
        <include refid="selectFfsafeHostEventsVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeHostEventsByIds" parameterType="Long" resultMap="FfsafeHostEventsResult">
        <include refid="selectFfsafeHostEventsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeHostEvents" parameterType="FfsafeHostEvents" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_host_events
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="monitorItem != null">monitor_item,</if>
            <if test="overallRisk != null">overall_risk,</if>
            <if test="category != null">category,</if>
            <if test="handlingStatus != null">handling_status,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="hostName != null">host_name,</if>
            <if test="operatingSystem != null">operating_system,</if>
            <if test="md5Hash != null">md5_hash,</if>
            <if test="suspiciousFile != null">suspicious_file,</if>
            <if test="sha256Hash != null">sha256_hash,</if>
            <if test="fileCreateTime != null">file_create_time,</if>
            <if test="fileModifyTime != null">file_modify_time,</if>
            <if test="inspectionEngine != null">inspection_engine,</if>
            <if test="anomalyType != null">anomaly_type,</if>
            <if test="detectionInfo != null">detection_info,</if>
            <if test="aiModel != null">ai_model,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="monitorItem != null">#{monitorItem},</if>
            <if test="overallRisk != null">#{overallRisk},</if>
            <if test="category != null">#{category},</if>
            <if test="handlingStatus != null">#{handlingStatus},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="hostName != null">#{hostName},</if>
            <if test="operatingSystem != null">#{operatingSystem},</if>
            <if test="md5Hash != null">#{md5Hash},</if>
            <if test="suspiciousFile != null">#{suspiciousFile},</if>
            <if test="sha256Hash != null">#{sha256Hash},</if>
            <if test="fileCreateTime != null">#{fileCreateTime},</if>
            <if test="fileModifyTime != null">#{fileModifyTime},</if>
            <if test="inspectionEngine != null">#{inspectionEngine},</if>
            <if test="anomalyType != null">#{anomalyType},</if>
            <if test="detectionInfo != null">#{detectionInfo},</if>
            <if test="aiModel != null">#{aiModel},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <insert id="saveBatch">
        insert into ffsafe_host_events (event_id,host_ip,monitor_item,overall_risk,category,handling_status,alarm_time,host_name,
                                        operating_system,md5_hash,suspicious_file,sha256_hash,file_create_time,file_modify_time,
                                        inspection_engine,anomaly_type,detection_info,ai_model,device_config_id,created_at,updated_at) values
        <foreach collection="saveList" item="item" index="index" separator=",">
             (#{item.eventId},#{item.hostIp},#{item.monitorItem},#{item.overallRisk},#{item.category},#{item.handlingStatus},#{item.alarmTime},#{item.hostName},
            #{item.operatingSystem},#{item.md5Hash},#{item.suspiciousFile},#{item.sha256Hash},#{item.fileCreateTime},#{item.fileModifyTime},
            #{item.inspectionEngine},#{item.anomalyType},#{item.detectionInfo},#{item.aiModel},#{item.deviceConfigId},#{item.createdAt},#{item.updatedAt}
             )
        </foreach>
    </insert>

    <update id="updateFfsafeHostEvents" parameterType="FfsafeHostEvents">
        update ffsafe_host_events
        <trim prefix="SET" suffixOverrides=",">
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="monitorItem != null">monitor_item = #{monitorItem},</if>
            <if test="overallRisk != null">overall_risk = #{overallRisk},</if>
            <if test="category != null">category = #{category},</if>
            <if test="handlingStatus != null">handling_status = #{handlingStatus},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="hostName != null">host_name = #{hostName},</if>
            <if test="operatingSystem != null">operating_system = #{operatingSystem},</if>
            <if test="md5Hash != null">md5_hash = #{md5Hash},</if>
            <if test="suspiciousFile != null">suspicious_file = #{suspiciousFile},</if>
            <if test="sha256Hash != null">sha256_hash = #{sha256Hash},</if>
            <if test="fileCreateTime != null">file_create_time = #{fileCreateTime},</if>
            <if test="fileModifyTime != null">file_modify_time = #{fileModifyTime},</if>
            <if test="inspectionEngine != null">inspection_engine = #{inspectionEngine},</if>
            <if test="anomalyType != null">anomaly_type = #{anomalyType},</if>
            <if test="detectionInfo != null">detection_info = #{detectionInfo},</if>
            <if test="aiModel != null">ai_model = #{aiModel},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostEventsById" parameterType="Long">
        delete from ffsafe_host_events where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostEventsByIds" parameterType="String">
        delete from ffsafe_host_events where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="truncateTable">
        TRUNCATE TABLE ffsafe_host_events;
    </delete>

    <delete id="deleteFfsafeHostEventsByDeviceConfigId">
        delete from ffsafe_host_events where device_config_id = #{id}
    </delete>
</mapper>
