# FfsafeClientService 数据重复问题修复任务

## 任务概述
修复 `FfsafeClientService.processHostIntrusionAttackDetails` 方法中的并发竞态条件导致的数据重复问题，解决第1295行 `IllegalStateException: Duplicate key` 错误。

## 问题诊断
### 根本原因
1. **并发执行灾难**：多线程异步执行相同的数据同步逻辑
2. **时序竞态条件**：多个线程同时查询详情表，都认为记录不存在
3. **事务隔离问题**：独立事务无法看到其他线程未提交的插入
4. **数据库约束缺失**：详情表缺少 `(attack_id, detail_type)` 唯一约束

### 错误现象
```
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, ...)
        at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
        at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
```

## 修复计划

### 执行顺序
1. **步骤1**：立即修复 Stream 操作崩溃
2. **步骤3**：清理现有重复数据
3. **步骤2**：添加数据库唯一约束
4. **步骤4**：优化详情处理逻辑
5. **步骤5**：增强错误处理
6. **步骤6**：验证修复效果

### 详细步骤

#### 步骤1：修复 Collectors.toMap() 崩溃 [P0]
**文件**：`aqsoc-monitor/src/main/java/com/ruoyi/monitor2/changting/client/FfsafeClientService.java`
**位置**：第1294-1295行

**修改前**：
```java
Map<Long, FfsafeHostIntrusionAttackDetail> existingDetailMap = existingDetails.stream()
    .collect(Collectors.toMap(FfsafeHostIntrusionAttackDetail::getAttackId, detail -> detail));
```

**修改后**：
```java
Map<Long, List<FfsafeHostIntrusionAttackDetail>> existingDetailMap = existingDetails.stream()
    .collect(Collectors.groupingBy(FfsafeHostIntrusionAttackDetail::getAttackId));
```

#### 步骤2：添加数据库唯一约束 [P1]
```sql
ALTER TABLE ffsafe_host_intrusion_attack_detail 
ADD UNIQUE KEY uk_attack_detail_type (attack_id, detail_type);
```

#### 步骤3：清理重复数据 [P1]
```sql
-- 删除重复记录，保留ID最小的
DELETE d1 FROM ffsafe_host_intrusion_attack_detail d1
INNER JOIN ffsafe_host_intrusion_attack_detail d2 
WHERE d1.attack_id = d2.attack_id 
  AND d1.detail_type = d2.detail_type 
  AND d1.id > d2.id;
```

## 执行记录
- [x] 步骤1：修复Stream操作 - 已完成
  - 将 `Collectors.toMap()` 改为 `Collectors.groupingBy()`
  - 修改业务逻辑支持一对多关系
  - 根据 `detail_type` 精确匹配
- [x] 步骤3：清理重复数据 - 已完成
  - 备份重复数据到 `ffsafe_host_intrusion_attack_detail_backup`
  - 删除2条重复记录，保留ID最小的
- [x] 步骤2：添加唯一约束 - 已完成
  - 添加 `uk_attack_detail_type (attack_id, detail_type)` 约束
  - 测试约束有效性
- [x] 步骤4：优化处理逻辑 - 已完成（与步骤1合并）
- [x] 步骤5：增强错误处理 - 已完成
  - 添加 `DuplicateKeyException` 特殊处理
  - 实现逐条插入降级策略
- [x] 步骤6：验证效果 - 已完成
  - 数据库无重复记录
  - 唯一约束正常工作
  - 代码编译无错误

## 修复总结
### 核心修改
1. **第1294-1296行**：修复 `Collectors.toMap()` 崩溃
2. **第1314-1335行**：支持根据 `detail_type` 精确匹配
3. **第1357-1378行**：增强异常处理，支持并发场景
4. **数据库层面**：添加唯一约束防止重复插入

### 技术改进
- **消除特殊情况**：不再假设一对一关系
- **简化逻辑**：使用 `groupingBy` 自然支持一对多
- **向后兼容**：不破坏现有功能
- **并发安全**：数据库约束 + 应用层降级
