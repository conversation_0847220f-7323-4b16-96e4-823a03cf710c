package cn.anmte.aqsoc.work.service.impl;

import cn.anmte.aqsoc.work.domain.WorkHwResultLink;
import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.mapper.WorkHwResultLinkMapper;
import cn.anmte.aqsoc.work.mapper.WorkHwTaskMapper;
import cn.anmte.aqsoc.work.service.IWorkHwResultLinkService;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-09-01 16:45
 */
@Service
public class WorkHwResultLinkServiceImpl extends ServiceImpl<WorkHwResultLinkMapper, WorkHwResultLink> implements IWorkHwResultLinkService {
}
