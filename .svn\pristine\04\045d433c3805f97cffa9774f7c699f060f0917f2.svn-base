package cn.anmte.aqsoc.work.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fumixuan.gencode.common.util.Pagination;
import lombok.Data;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkHwPagination extends Pagination {

    @Schema(description = "自增id")
    private Integer id;

    @Schema(description = "事务模版id")
    private Integer templateId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "年度")
    private String year;

    @Schema(description = "责任人")
    private String userId;

    @Schema(description = "HW开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwStart;

    @Schema(description = "HW结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwEnd;

    @Schema(description = "技术支撑单位")
    private String supportOrgs;

    @Schema(description = "参与人员")
    private String supportUsers;

    @Schema(description = "护网数据json")
    private String dataJson;

    /**
     * 菜单id
     */
    private String menuId;
    private String moduleId;

    private Long deptId;
}
