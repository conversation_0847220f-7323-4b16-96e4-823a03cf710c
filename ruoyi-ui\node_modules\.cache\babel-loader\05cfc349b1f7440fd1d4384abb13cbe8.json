{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\formDesign\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\formDesign\\index.vue", "mtime": 1756794280339}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_Form", "_interopRequireDefault", "require", "_Preview", "_PreviewDialog", "_FormDesign", "name", "components", "Form", "preview", "previewDialog", "data", "query", "keyword", "downloadFormVisible", "sort", "dialogVisible", "previewVisible", "previewDialogVisible", "list", "list<PERSON>uery", "currentPage", "pageSize", "sidx", "previewType", "total", "listLoading", "formVisible", "addVisible", "currRow", "categoryList", "created", "initData", "methods", "search", "reset", "_this", "_objectSpread2", "default", "getFormList", "then", "res", "pagination", "handleDel", "id", "_this2", "$confirm", "type", "del", "$message", "message", "msg", "duration", "onClose", "catch", "copy", "_this3", "copyForm", "exportModel", "_this4", "exportData", "$download", "export", "url", "split", "handleAdd", "webType", "addOrUpdateHandle", "closeForm", "isRefresh", "addForm", "flowType", "formType", "_this5", "$nextTick", "$refs", "init", "row", "dataSource", "_this6", "previewPc", "_this7", "enCode", "fullName", "formId", "enabledMark", "_this8", "str", "EnabledMark", "releaseForm", "_this9", "release", "rollBackForm", "_this10"], "sources": ["src/views/zeroCode/formDesign/index.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n          @submit.native.prevent>\n          <el-row :gutter=\"16\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"关键词\">\n                <el-input v-model=\"query.keyword\" placeholder=\"请输入关键词查询\" clearable\n                          @keyup.enter.native=\"search()\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"search()\"\n                  v-hasPermi=\"['zeroCode:flowFormDesign:query']\"\n                >\n                  查询\n                </el-button>\n                <el-button\n                  size=\"small\"\n                  class=\"btn2\"\n                  @click=\"reset()\"\n                  v-hasPermi=\"['zeroCode:flowFormDesign:query']\"\n                >重置\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">流程表单列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"dialogVisible = true\"\n                >新增</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <upload-btn url=\"/proxy/api/flowForm/Form/Actions/ImportData\" accept=\".fff\"\n                            @on-success=\"reset()\" />\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <JNPF-table v-loading=\"listLoading\" :data=\"list\">\n          <el-table-column prop=\"fullName\" label=\"表单名称\" min-width=\"150\" />\n          <el-table-column prop=\"enCode\" label=\"表单编码\" width=\"200\" />\n          <el-table-column prop=\"formType\" label=\"表单类型\" width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.formType == 2 ? \"自定义表单\" : \"系统表单\"}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"creatorUser\" label=\"创建人\" width=\"120\" />\n          <el-table-column prop=\"creatorTime\" label=\"创建时间\" :formatter=\"jnpf.tableDateFormat\"\n            width=\"120\" />\n          <el-table-column prop=\"lastModifyTime\" label=\"最后修改时间\" :formatter=\"jnpf.tableDateFormat\"\n            width=\"120\" />\n          <el-table-column prop=\"sortCode\" label=\"排序\" width=\"70\"  />\n          <el-table-column label=\"状态\" width=\"80\" >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.enabledMark == 1 ? 'success' : 'info'\" disable-transitions>\n                {{scope.row.enabledMark==1?'已发布':'未发布'}}</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" fixed=\"right\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <tableOpts @edit=\"addOrUpdateHandle(scope.row.id,scope.row.formType,0)\"\n                @del=\"handleDel(scope.row.id)\">\n                <el-dropdown>\n                  <span class=\"el-dropdown-link\">\n                    <el-button type=\"text\" size=\"mini\"\n                    >更多\n                      <i class=\"el-icon-arrow-down el-icon--right\"></i>\n                    </el-button>\n                  </span>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item @click.native=\"releaseForm(scope.row.id)\" :disabled=\"scope.row.flowType === 99\">\n                      发布表单</el-dropdown-item>\n                    <!-- <el-dropdown-item @click.native=\"rollBackForm(scope.row.id)\"\n                      v-if=\"scope.row.enabledMark\">\n                      回滚表单</el-dropdown-item> -->\n                    <el-dropdown-item @click.native=\"preview(scope.row,'draftJson',0)\">\n                      设计预览</el-dropdown-item>\n                    <el-dropdown-item @click.native=\"preview(scope.row,'propertyJson',1)\"\n                      v-if=\"scope.row.enabledMark == 1\">\n                      发布预览</el-dropdown-item>\n                    <el-dropdown-item @click.native=\"copy(scope.row.id)\">\n                      复制表单</el-dropdown-item>\n                    <el-dropdown-item @click.native=\"exportModel(scope.row.id)\">\n                      导出表单</el-dropdown-item>\n                  </el-dropdown-menu>\n                </el-dropdown>\n              </tableOpts>\n            </template>\n          </el-table-column>\n        </JNPF-table>\n        <pagination :total=\"total\" :page.sync=\"listQuery.currentPage\"\n          :limit.sync=\"listQuery.pageSize\" @pagination=\"initData\" />\n      </div>\n    </div>\n    <Form v-if=\"formVisible\" ref=\"Form\" @close=\"closeForm\" />\n    <preview v-if=\"previewVisible\" ref=\"preview\" @close=\"previewVisible=false\" />\n    <previewDialog :visible.sync=\"previewDialogVisible\" :id=\"currRow.id\" type=\"flow\"\n      @previewPc=\"previewPc\" :dataSource=\"currRow.dataSource\" :previewType=\"previewType\" />\n    <el-dialog title=\"新建流程表单\" :visible.sync=\"dialogVisible\"\n      class=\"JNPF-dialog JNPF-dialog_center JNPF-dialog-add\" lock-scroll width=\"600px\"\n      :show-close=\"false\">\n      <div class=\"add-main\">\n        <div class=\"add-item add-item-left\" @click=\"addForm(0,2)\">\n          <i class=\"add-icon icon-ym icon-ym-launchFlow\"></i>\n          <div class=\"add-txt\">\n            <p class=\"add-title\">自定义表单</p>\n            <p class=\"add-desc\">在线可视化流程表单</p>\n          </div>\n        </div>\n        <div class=\"add-item\" @click=\"addForm(0,1)\">\n          <i class=\"add-icon icon-ym icon-ym-funcFlow\"></i>\n          <div class=\"add-txt\">\n            <p class=\"add-title\">系统表单</p>\n            <p class=\"add-desc\">代码生成的流程表单</p>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport Form from './Form'\nimport preview from './Preview'\nimport previewDialog from '@/components/PreviewDialog'\nimport { getFormList, release, del, copyForm, exportData } from '@/api/lowCode/FormDesign'\nexport default {\n  name: 'formDesign',\n  components: { Form, preview, previewDialog },\n  data() {\n    return {\n      query: { keyword: '' },\n      downloadFormVisible: false,\n      sort: 'flowForm',\n      dialogVisible: false,\n      previewVisible: false,\n      previewDialogVisible: false,\n      list: [],\n      listQuery: {\n        currentPage: 1,\n        pageSize: 20,\n        sort: 'desc',\n        sidx: ''\n      },\n      previewType: 0,\n      total: 0,\n      listLoading: false,\n      formVisible: false,\n      addVisible: false,\n      currRow: {},\n      categoryList: []\n    }\n  },\n  created() {\n    this.initData()\n  },\n  methods: {\n    search() {\n      this.listQuery = {\n        currentPage: 1,\n        pageSize: 20,\n        sort: 'desc',\n        sidx: ''\n      }\n      this.initData()\n    },\n    reset() {\n      this.query.keyword = ''\n      this.search()\n    },\n    initData() {\n      this.listLoading = true\n      let query = {\n        ...this.listQuery,\n        ...this.query\n      }\n      getFormList(query).then(res => {\n        this.list = res.data.list\n        this.total = res.data.pagination.total\n        this.listLoading = false\n      })\n    },\n    handleDel(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续？\", \"提示\", {\n        type: 'warning'\n      }).then(() => {\n        del(id).then(res => {\n          this.$message({\n            type: 'success',\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.initData()\n            }\n          });\n        })\n      }).catch(() => { });\n    },\n    copy(id) {\n      this.$confirm('您确定要复制该功能表单, 是否继续?', '提示', {\n        type: 'warning'\n      }).then(() => {\n        copyForm(id).then(res => {\n          this.$message({\n            type: 'success',\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.initData()\n            }\n          });\n        })\n      }).catch(() => { });\n    },\n    exportModel(id) {\n      this.$confirm('您确定要导出该功能表单, 是否继续?', '提示', {\n        type: 'warning'\n      }).then(() => {\n        exportData(id).then(res => {\n          //this.jnpf.downloadFile(res.data.url)\n          this.$download.export(\n            \"/proxy\" + res.data.url,\n            res.data.url.split(\"&name=\")[1],\n            true\n          );\n        })\n      }).catch(() => { });\n    },\n    handleAdd(webType) {\n      this.addOrUpdateHandle('', webType)\n    },\n    closeForm(isRefresh) {\n      this.formVisible = false\n      if (isRefresh) {\n        this.initData()\n      }\n    },\n    addForm(flowType, formType) {\n      this.dialogVisible = false\n      this.addOrUpdateHandle('', flowType, formType)\n    },\n    // 新增 / 修改\n    addOrUpdateHandle(id, flowType, formType) {\n      this.formVisible = true\n      this.$nextTick(() => {\n        this.$refs.Form.init(id, flowType, formType)\n      })\n    },\n    preview(row, dataSource, type) {\n      this.previewType = type\n      this.currRow = row\n      this.currRow.dataSource = dataSource\n      this.$nextTick(() => {\n        this.previewDialogVisible = true\n      })\n    },\n    previewPc() {\n      let data = {\n        enCode: this.currRow.enCode,\n        fullName: this.currRow.fullName,\n        formType: this.currRow.formType,\n        formId: this.currRow.id,\n        dataSource: this.currRow.dataSource,\n      }\n      this.previewVisible = true\n      this.$nextTick(() => {\n        this.$refs.preview.init(data)\n      })\n    },\n    enabledMark(id, enabledMark) {\n      let str = enabledMark === 1 ? \"表单停用\" : \"表单启用\"\n      this.$confirm(str + '?', '提示', {\n        type: 'warning'\n      }).then(() => {\n        EnabledMark(id).then(res => {\n          this.$message({\n            type: 'success',\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.initData()\n            }\n          });\n        })\n      }).catch(() => { });\n    },\n    releaseForm(id) {\n      this.$confirm('发布表单会覆盖当前线上版本，是否继续？', '发布确认', {\n        type: 'warning'\n      }).then(() => {\n        release(id, 1).then(res => {\n          this.$message({\n            type: 'success',\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.initData()\n            }\n          });\n        })\n      }).catch(() => { });\n    },\n    rollBackForm(id) {\n      this.$confirm('此操作将当前编辑的表单内容回滚为已经发布的表单内容，是否继续？', '提示', {\n        type: 'warning'\n      }).then(() => {\n        release(id, 0).then(res => {\n          this.$message({\n            type: 'success',\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.initData()\n            }\n          });\n        })\n      }).catch(() => { });\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;AAqJA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAI,IAAA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QAAAC,OAAA;MAAA;MACAC,mBAAA;MACAC,IAAA;MACAC,aAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,IAAA;MACAC,SAAA;QACAC,WAAA;QACAC,QAAA;QACAP,IAAA;QACAQ,IAAA;MACA;MACAC,WAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAC,OAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAd,SAAA;QACAC,WAAA;QACAC,QAAA;QACAP,IAAA;QACAQ,IAAA;MACA;MACA,KAAAS,QAAA;IACA;IACAG,KAAA,WAAAA,MAAA;MACA,KAAAvB,KAAA,CAAAC,OAAA;MACA,KAAAqB,MAAA;IACA;IACAF,QAAA,WAAAA,SAAA;MAAA,IAAAI,KAAA;MACA,KAAAV,WAAA;MACA,IAAAd,KAAA,OAAAyB,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAAlB,SAAA,GACA,KAAAR,KAAA,CACA;MACA,IAAA2B,uBAAA,EAAA3B,KAAA,EAAA4B,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAAjB,IAAA,GAAAsB,GAAA,CAAA9B,IAAA,CAAAQ,IAAA;QACAiB,KAAA,CAAAX,KAAA,GAAAgB,GAAA,CAAA9B,IAAA,CAAA+B,UAAA,CAAAjB,KAAA;QACAW,KAAA,CAAAV,WAAA;MACA;IACA;IACAiB,SAAA,WAAAA,UAAAC,EAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,IAAAQ,eAAA,EAAAJ,EAAA,EAAAJ,IAAA,WAAAC,GAAA;UACAI,MAAA,CAAAI,QAAA;YACAF,IAAA;YACAG,OAAA,EAAAT,GAAA,CAAAU,GAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAR,MAAA,CAAAb,QAAA;YACA;UACA;QACA;MACA,GAAAsB,KAAA;IACA;IACAC,IAAA,WAAAA,KAAAX,EAAA;MAAA,IAAAY,MAAA;MACA,KAAAV,QAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,IAAAiB,oBAAA,EAAAb,EAAA,EAAAJ,IAAA,WAAAC,GAAA;UACAe,MAAA,CAAAP,QAAA;YACAF,IAAA;YACAG,OAAA,EAAAT,GAAA,CAAAU,GAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAG,MAAA,CAAAxB,QAAA;YACA;UACA;QACA;MACA,GAAAsB,KAAA;IACA;IACAI,WAAA,WAAAA,YAAAd,EAAA;MAAA,IAAAe,MAAA;MACA,KAAAb,QAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,IAAAoB,sBAAA,EAAAhB,EAAA,EAAAJ,IAAA,WAAAC,GAAA;UACA;UACAkB,MAAA,CAAAE,SAAA,CAAAC,MAAA,CACA,WAAArB,GAAA,CAAA9B,IAAA,CAAAoD,GAAA,EACAtB,GAAA,CAAA9B,IAAA,CAAAoD,GAAA,CAAAC,KAAA,eACA,IACA;QACA;MACA,GAAAV,KAAA;IACA;IACAW,SAAA,WAAAA,UAAAC,OAAA;MACA,KAAAC,iBAAA,KAAAD,OAAA;IACA;IACAE,SAAA,WAAAA,UAAAC,SAAA;MACA,KAAA1C,WAAA;MACA,IAAA0C,SAAA;QACA,KAAArC,QAAA;MACA;IACA;IACAsC,OAAA,WAAAA,QAAAC,QAAA,EAAAC,QAAA;MACA,KAAAxD,aAAA;MACA,KAAAmD,iBAAA,KAAAI,QAAA,EAAAC,QAAA;IACA;IACA;IACAL,iBAAA,WAAAA,kBAAAvB,EAAA,EAAA2B,QAAA,EAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA9C,WAAA;MACA,KAAA+C,SAAA;QACAD,MAAA,CAAAE,KAAA,CAAAnE,IAAA,CAAAoE,IAAA,CAAAhC,EAAA,EAAA2B,QAAA,EAAAC,QAAA;MACA;IACA;IACA/D,OAAA,WAAAA,QAAAoE,GAAA,EAAAC,UAAA,EAAA/B,IAAA;MAAA,IAAAgC,MAAA;MACA,KAAAvD,WAAA,GAAAuB,IAAA;MACA,KAAAlB,OAAA,GAAAgD,GAAA;MACA,KAAAhD,OAAA,CAAAiD,UAAA,GAAAA,UAAA;MACA,KAAAJ,SAAA;QACAK,MAAA,CAAA7D,oBAAA;MACA;IACA;IACA8D,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAtE,IAAA;QACAuE,MAAA,OAAArD,OAAA,CAAAqD,MAAA;QACAC,QAAA,OAAAtD,OAAA,CAAAsD,QAAA;QACAX,QAAA,OAAA3C,OAAA,CAAA2C,QAAA;QACAY,MAAA,OAAAvD,OAAA,CAAAe,EAAA;QACAkC,UAAA,OAAAjD,OAAA,CAAAiD;MACA;MACA,KAAA7D,cAAA;MACA,KAAAyD,SAAA;QACAO,MAAA,CAAAN,KAAA,CAAAlE,OAAA,CAAAmE,IAAA,CAAAjE,IAAA;MACA;IACA;IACA0E,WAAA,WAAAA,YAAAzC,EAAA,EAAAyC,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,GAAAF,YAAA;MACA,KAAAvC,QAAA,CAAAyC,GAAA;QACAxC,IAAA;MACA,GAAAP,IAAA;QACAgD,WAAA,CAAA5C,EAAA,EAAAJ,IAAA,WAAAC,GAAA;UACA6C,MAAA,CAAArC,QAAA;YACAF,IAAA;YACAG,OAAA,EAAAT,GAAA,CAAAU,GAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAiC,MAAA,CAAAtD,QAAA;YACA;UACA;QACA;MACA,GAAAsB,KAAA;IACA;IACAmC,WAAA,WAAAA,YAAA7C,EAAA;MAAA,IAAA8C,MAAA;MACA,KAAA5C,QAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,IAAAmD,mBAAA,EAAA/C,EAAA,KAAAJ,IAAA,WAAAC,GAAA;UACAiD,MAAA,CAAAzC,QAAA;YACAF,IAAA;YACAG,OAAA,EAAAT,GAAA,CAAAU,GAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAqC,MAAA,CAAA1D,QAAA;YACA;UACA;QACA;MACA,GAAAsB,KAAA;IACA;IACAsC,YAAA,WAAAA,aAAAhD,EAAA;MAAA,IAAAiD,OAAA;MACA,KAAA/C,QAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,IAAAmD,mBAAA,EAAA/C,EAAA,KAAAJ,IAAA,WAAAC,GAAA;UACAoD,OAAA,CAAA5C,QAAA;YACAF,IAAA;YACAG,OAAA,EAAAT,GAAA,CAAAU,GAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAwC,OAAA,CAAA7D,QAAA;YACA;UACA;QACA;MACA,GAAAsB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}