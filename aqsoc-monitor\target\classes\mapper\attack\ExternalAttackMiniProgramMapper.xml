<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.external.mapper.ExternalAttackMiniProgramMapper">

    <resultMap type="ExternalAttackMiniProgram" id="ExternalAttackMiniProgramResult">
        <result property="id"    column="id"    />
        <result property="miniProgramName"    column="mini_program_name"    />
        <result property="originalAccountId"    column="original_account_id"    />
        <result property="miniProgramAppId"    column="mini_program_app_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="appUpdateTime"    column="app_update_time"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="discoveryTime"    column="discovery_time"    />
        <result property="entryType"    column="entry_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="wechatId"    column="wechat_id"    />
        <result property="programType"    column="program_type"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="remark"    column="remark"    />
        <result property="deviceConfigId"    column="device_config_id"    />
        <result property="deptName" column="dept_name"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
    </resultMap>

    <sql id="selectExternalAttackMiniProgramVo">
        select a.id, a.mini_program_name, a.original_account_id, a.mini_program_app_id, a.dept_id, a.app_update_time, a.responsible_person, a.discovery_time,a.entry_type, a.create_by, a.create_time, a.update_by, a.update_time, a.wechat_id, a.program_type, a.link_url, a.remark, a.device_config_id,
        GROUP_CONCAT(CONCAT(m.nick_name, '-', m.phonenumber) ORDER BY FIND_IN_SET(m.user_id, a.responsible_person) SEPARATOR ',') AS responsible_person_name, d.dept_name
        from external_attack_mini_program a
        left join sys_user m ON FIND_IN_SET(m.user_id, a.responsible_person) > 0
        left join sys_dept d on a.dept_id=d.dept_id
    </sql>

    <select id="selectExternalAttackMiniProgramList" parameterType="ExternalAttackMiniProgram" resultMap="ExternalAttackMiniProgramResult">
        <include refid="selectExternalAttackMiniProgramVo"/>
        <where>
            <if test="miniProgramName != null  and miniProgramName != ''"> and a.mini_program_name like concat('%', #{miniProgramName}, '%')</if>
            <if test="originalAccountId != null  and originalAccountId != ''"> and a.original_account_id like concat('%', #{originalAccountId}, '%')</if>
            <if test="miniProgramAppId != null  and miniProgramAppId != ''"> and a.mini_program_app_id like concat('%', #{miniProgramAppId}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="deviceConfigId != null"> and a.device_config_id = #{deviceConfigId}</if>
        </where>
        GROUP BY a.id
        <if test="params != null">
            <if test="params.orderByColumn != null and params.isAsc != null">
                ORDER BY ${params.orderByColumn} ${params.isAsc}
            </if>
        </if>
    </select>

    <select id="selectExternalAttackMiniProgramById" parameterType="Long" resultMap="ExternalAttackMiniProgramResult">
        <include refid="selectExternalAttackMiniProgramVo"/>
        where id = #{id}
    </select>

    <select id="selectExternalAttackMiniProgramByIds" parameterType="Long" resultMap="ExternalAttackMiniProgramResult">
        <include refid="selectExternalAttackMiniProgramVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertExternalAttackMiniProgram" parameterType="ExternalAttackMiniProgram" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_mini_program
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="miniProgramName != null">mini_program_name,</if>
            <if test="originalAccountId != null">original_account_id,</if>
            <if test="miniProgramAppId != null">mini_program_app_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="appUpdateTime != null">app_update_time,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="discoveryTime != null">discovery_time,</if>
            <if test="entryType != null">entry_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="wechatId != null">wechat_id,</if>
            <if test="programType != null">program_type,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="remark != null">remark,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="miniProgramName != null">#{miniProgramName},</if>
            <if test="originalAccountId != null">#{originalAccountId},</if>
            <if test="miniProgramAppId != null">#{miniProgramAppId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="appUpdateTime != null">#{appUpdateTime},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="discoveryTime != null">#{discoveryTime},</if>
            <if test="entryType != null">#{entryType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="wechatId != null">#{wechatId},</if>
            <if test="programType != null">#{programType},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
         </trim>
    </insert>

    <update id="updateExternalAttackMiniProgram" parameterType="ExternalAttackMiniProgram">
        update external_attack_mini_program
        <trim prefix="SET" suffixOverrides=",">
            <if test="miniProgramName != null">mini_program_name = #{miniProgramName},</if>
            <if test="originalAccountId != null">original_account_id = #{originalAccountId},</if>
            <if test="miniProgramAppId != null">mini_program_app_id = #{miniProgramAppId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="appUpdateTime != null">app_update_time = #{appUpdateTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="wechatId != null">wechat_id = #{wechatId},</if>
            <if test="programType != null">program_type = #{programType},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExternalAttackMiniProgramById" parameterType="Long">
        delete from external_attack_mini_program where id = #{id}
    </delete>

    <delete id="deleteExternalAttackMiniProgramByIds" parameterType="String">
        delete from external_attack_mini_program where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkMiniProgramExistence" resultType="int">
        SELECT COUNT(*) FROM external_attack_mini_program
        <where>
            <if test="miniProgramAppId != null and miniProgramAppId != ''">
                AND mini_program_app_id = #{miniProgramAppId}
            </if>
            and mini_program_name = #{miniProgramName}
            <!-- 修改时排除指定 id -->
            <if test="id != null "> AND id != #{id} </if>
        </where>
    </select>

    <select id="selectByAppIds" parameterType="java.util.List" resultMap="ExternalAttackMiniProgramResult">
        SELECT * FROM external_attack_mini_program WHERE mini_program_app_id IN
        <foreach item="id" index="index" collection="appIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        SELECT count(1) FROM external_attack_mini_program
    </select>

    <delete id="deleteByEntryTypeAndAppId" parameterType="java.util.Set">
        DELETE FROM external_attack_mini_program
        WHERE CONCAT(IFNULL(mini_program_name,'null'), '-', IFNULL(mini_program_app_id,'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="deviceConfigId != null">
            AND device_config_id = #{deviceConfigId}
        </if>
    </delete>

    <!-- 选择性更新：只更新第三方接口返回的原有字段，保护用户维护的新增字段 -->
    <update id="selectiveUpdateByCondition" parameterType="ExternalAttackMiniProgram">
        UPDATE external_attack_mini_program
        <trim prefix="SET" suffixOverrides=",">
            <!-- 只更新第三方接口返回的原有字段 -->
            <if test="miniProgramName != null">mini_program_name = #{miniProgramName},</if>
            <if test="originalAccountId != null">original_account_id = #{originalAccountId},</if>
            <if test="miniProgramAppId != null">mini_program_app_id = #{miniProgramAppId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="appUpdateTime != null">app_update_time = #{appUpdateTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <!-- 注意：不更新新增字段 wechat_id, program_type, link_url, remark -->
        </trim>
        WHERE CONCAT(IFNULL(mini_program_name,'null'), '-', IFNULL(mini_program_app_id,'null')) = CONCAT(IFNULL(#{miniProgramName},'null'), '-', IFNULL(#{miniProgramAppId},'null'))
          AND entry_type = 1
    </update>

    <!-- 根据唯一键查询现有记录 -->
    <select id="selectByUniqueKey" parameterType="String" resultMap="ExternalAttackMiniProgramResult">
        <include refid="selectExternalAttackMiniProgramVo"/>
        WHERE CONCAT(IFNULL(a.mini_program_name,'null'), '-', IFNULL(a.mini_program_app_id,'null')) = #{uniqueKey}
          AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量查询现有记录 -->
    <select id="selectByUniqueKeys" parameterType="java.util.Set" resultMap="ExternalAttackMiniProgramResult">
        <include refid="selectExternalAttackMiniProgramVo"/>
        WHERE CONCAT(IFNULL(a.mini_program_name,'null'), '-', IFNULL(a.mini_program_app_id,'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量插入微信小程序 -->
    <insert id="batchInsertExternalAttackMiniProgram" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_mini_program (
            mini_program_name, original_account_id, mini_program_app_id, dept_id,
            app_update_time, responsible_person, discovery_time, entry_type,
            create_by, create_time, update_by, update_time, wechat_id, program_type, link_url, remark
        ) values
        <foreach collection="entityList" item="item" separator=",">
            (
                #{item.miniProgramName}, #{item.originalAccountId}, #{item.miniProgramAppId}, #{item.deptId},
                #{item.appUpdateTime}, #{item.responsiblePerson}, #{item.discoveryTime}, #{item.entryType},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime},
                #{item.wechatId}, #{item.programType}, #{item.linkUrl}, #{item.remark}
            )
        </foreach>
    </insert>

</mapper>
