package cn.anmte.aqsoc.work.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-25 16:27
 */
@Data
@TableName("work_hw_result_link")
@Schema(name = "work_hw_result_link", description = "HW任务成果链接")
public class WorkHwResultLink implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** HW任务ID */
    private Long taskId;

    /** 事务记录ID */
    private Long workRecordId;

    /** flowId */
    private String fFlowtaskid;

    /** 名称 */
    private String label;

    /** 链接 */
    private String linkUrl;
}
