<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.quartz.mapper.SysJobMapper">

	<resultMap type="com.ruoyi.quartz.domain.SysJob" id="SysJobResult">
		<id     property="jobId"          column="job_id"          />
		<result property="planId"         column="plan_id"         />
		<result property="jobName"        column="job_name"        />
		<result property="jobGroup"       column="job_group"       />
		<result property="invokeTarget"   column="invoke_target"   />
		<result property="cronExpression" column="cron_expression" />
		<result property="misfirePolicy"  column="misfire_policy"  />
		<result property="concurrent"     column="concurrent"      />
		<result property="status"         column="status"          />
		<result property="createBy"       column="create_by"       />
		<result property="createTime"     column="create_time"     />
		<result property="updateBy"       column="update_by"       />
		<result property="updateTime"     column="update_time"     />
		<result property="remark"         column="remark"          />
		<result property="period"         column="period"          />
		<result property="currentStatus"  column="current_status"  />
		<result property="jobType"  column="job_type"  />
		<result property="cronTransfer"  column="cron_transfer"  />
		<result property="isDel"  column="is_del"  />
		<result property="lastRunTime" column="last_run_time" />
		<result property="deviceConfigId" column="device_config_id" />
	</resultMap>

	<sql id="selectJobVo">
		SELECT
			t1.job_id,
			t1.plan_id,
			t1.job_name,
			t1.job_group,
			t1.invoke_target,
			t1.cron_expression,
			t1.misfire_policy,
			t1.`concurrent`,
			t1.`status`,
			t1.create_by,
			t1.create_time,
			t1.remark,
			t1.period,
			t1.current_status,
			t1.job_type,
			t1.cron_transfer,
			t1.device_config_id
		FROM
			sys_job t1
			LEFT JOIN sys_user su ON su.user_name=t1.create_by
    </sql>

	<select id="selectJobList" parameterType="com.ruoyi.quartz.domain.SysJob" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		<where>
			t1.is_del = 0
			<if test="jobName != null and jobName != ''">
				AND t1.job_name like concat('%', #{jobName}, '%')
			</if>
			<if test="jobGroup != null and jobGroup != ''">
				AND t1.job_group = #{jobGroup}
			</if>
			<if test="status != null and status != ''">
				AND t1.`status` = #{status}
			</if>
			<if test="currentStatus != null">
				AND t1.current_status = #{currentStatus}
			</if>
			<if test="jobType != null">
				AND t1.job_type = #{jobType}
			</if>
			<if test="invokeTarget != null and invokeTarget != ''">
				AND SUBSTRING(t1.invoke_target,LOCATE('|', t1.invoke_target) + 1) LIKE concat('%', #{invokeTarget}, '%')
			</if>
			<if test="remark != null and remark != ''">
				AND t1.remark like concat(#{remark}, '%')
			</if>
			<if test="deviceConfigId != null">
				AND t1.device_config_id = #{deviceConfigId}
			</if>
			<if test="params.dataScope != null and params.dataScope != ''">
				${params.dataScope}
			</if>
		</where>
		order by t1.create_time desc
	</select>
	<select id="selectJobListByType" parameterType="com.ruoyi.quartz.domain.SysJob" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		<where>
			t1.is_del = 0 AND t1.job_type != 1 AND t1.job_type != 2
			<if test="jobName != null and jobName != ''">
				AND t1.job_name like concat('%', #{jobName}, '%')
			</if>
			<if test="jobGroup != null and jobGroup != ''">
				AND t1.job_group = #{jobGroup}
			</if>
			<if test="status != null and status != ''">
				AND t1.`status` = #{status}
			</if>
			<if test="currentStatus != null">
				AND t1.current_status = #{currentStatus}
			</if>
			<if test="jobType != null">
				AND t1.job_type = #{jobType}
			</if>
			<if test="invokeTarget != null and invokeTarget != ''">
				AND t1.invoke_target like concat('%', #{invokeTarget}, '%')
			</if>
			<if test="remark != null and remark != ''">
				AND t1.remark like concat(#{remark}, '%')
			</if>
			<if test="deviceConfigId != null">
				AND t1.device_config_id = #{deviceConfigId}
			</if>
			<if test="params.dataScope != null and params.dataScope != ''">
				${params.dataScope}
			</if>
		</where>
		order by t1.create_time desc
	</select>
	<select id="selectNameCount" parameterType="com.ruoyi.quartz.domain.SysJob" resultType="java.lang.Integer">
		select count(1) from sys_job where job_name = #{jobName} and job_group = #{jobGroup} and is_del = 0 AND job_type = #{jobType}
		<if test="jobId != null">and job_id <![CDATA[ <> ]]> #{jobId}</if>
	</select>

	<select id="selectJobAll" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
	</select>

	<update id="updateAllJobExplore" parameterType="java.lang.Long">
		update monitor_explore_job set status = 2 where job_id = #{jobId} and status = 1
	</update>

	<select id="selectJobById" parameterType="java.lang.Long" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		where t1.job_id = #{jobId}
	</select>
	<select id="list" resultType="com.ruoyi.quartz.domain.SysJob" resultMap="SysJobResult">
		SELECT
			t1.job_id,
			t1.plan_id,
			t1.job_name,
			t1.job_group,
			t1.invoke_target,
			t1.cron_expression,
			t1.misfire_policy,
			t1.`concurrent`,
			t1.`status`,
			t1.create_by,
			t1.create_time,
			t1.remark,
			t1.period,
			t1.current_status,
			t1.job_type,
			t1.cron_transfer,
			t1.device_config_id,
			t2.create_time AS last_run_time
		FROM
			sys_job t1
			LEFT JOIN sys_job_log t2 ON t2.job_id = t1.job_id
			LEFT JOIN sys_user su ON su.user_name=t1.create_by
		<where>
			t1.is_del = 0
			<if test="jobName != null and jobName != ''">
				AND t1.job_name like concat('%', #{jobName}, '%')
			</if>
			<if test="jobGroup != null and jobGroup != ''">
				AND t1.job_group = #{jobGroup}
			</if>
			<if test="status != null and status != ''">
				AND t1.`status` = #{status}
			</if>
			<if test="currentStatus != null">
				AND t1.current_status = #{currentStatus}
			</if>
			<if test="jobType != null">
				AND t1.job_type = #{jobType}
			</if>
			<if test="invokeTarget != null and invokeTarget != ''">
				AND t1.invoke_target like concat('%', #{invokeTarget}, '%')
			</if>
			<if test="remark != null and remark != ''">
				AND t1.remark like concat(#{remark}, '%')
			</if>
			<if test="startTime != null">
				AND t2.create_time >= #{startTime}
			</if>
			<if test="endTime != null">
				AND t2.create_time &lt;= #{endTime}
			</if>
			<if test="deviceConfigId != null">
				AND t1.device_config_id = #{deviceConfigId}
			</if>
			<if test="params.dataScope != null and params.dataScope != ''">
				${params.dataScope}
			</if>
		</where>
		GROUP BY
		t1.job_id
		order by t2.create_time desc
	</select>

	<delete id="deleteJobById" parameterType="java.lang.Long">
 		delete from sys_job where job_id = #{jobId}
 	</delete>

	<delete id="deleteJobWithLogic" parameterType="java.lang.Long">
 		update sys_job set is_del = 1 where job_id = #{jobId}
 	</delete>

 	<delete id="deleteJobByIds" parameterType="java.lang.Long">
 		delete from sys_job where job_id in
 		<foreach collection="array" item="jobId" open="(" separator="," close=")">
 			#{jobId}
        </foreach>
 	</delete>

 	<update id="updateJob" parameterType="com.ruoyi.quartz.domain.SysJob">
 		update sys_job
 		<set>
			<if test="planId != null and planId != ''">plan_id = #{planId},</if>
 			<if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
 			<if test="status !=null">status = #{status},</if>
 			<if test="remark != null and remark != ''">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="period != null">period = #{period},</if>
 			<if test="currentStatus != null">current_status = #{currentStatus},</if>
 			<if test="cronTransfer != null and cronTransfer != ''">cron_transfer = #{cronTransfer},</if>
 		    <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
 			update_time = sysdate()
 		</set>
 		where job_id = #{jobId}
	</update>

	<update id="updateJobByPlanId" parameterType="com.ruoyi.quartz.domain.SysJob">
		update sys_job
		<set>
			<if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
			<if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
			<if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
			<if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
			<if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
			<if test="status !=null">status = #{status},</if>
			<if test="remark != null and remark != ''">remark = #{remark},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="period != null">period = #{period},</if>
			<if test="currentStatus != null">current_status = #{currentStatus},</if>
			<if test="cronTransfer != null and cronTransfer != ''">cron_transfer = #{cronTransfer},</if>
		    <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
			update_time = sysdate()
		</set>
		where plan_id = #{planId}
	</update>

 	<insert id="insertJob" parameterType="com.ruoyi.quartz.domain.SysJob" useGeneratedKeys="true" keyProperty="jobId">
 		insert into sys_job(
 			<if test="jobId != null and jobId != 0">job_id,</if>
 			<if test="jobName != null and jobName != ''">job_name,</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group,</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
 			<if test="concurrent != null and concurrent != ''">concurrent,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="period != null">period,</if>
			<if test="currentStatus != null">current_status,</if>
			<if test="jobType != null">job_type,</if>
			<if test="cronTransfer != null and cronTransfer != ''">cron_transfer,</if>
 		    <if test="deviceConfigId != null">device_config_id,</if>
 			create_time
 		)values(
 			<if test="jobId != null and jobId != 0">#{jobId},</if>
 			<if test="jobName != null and jobName != ''">#{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">#{concurrent},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="period != null">#{period},</if>
			<if test="currentStatus != null">#{currentStatus},</if>
			<if test="jobType != null">#{jobType},</if>
			<if test="cronTransfer != null and cronTransfer != ''">#{cronTransfer},</if>
			<if test="deviceConfigId != null">#{deviceConfigId},</if>
 			sysdate()
 		)
	</insert>

</mapper>
