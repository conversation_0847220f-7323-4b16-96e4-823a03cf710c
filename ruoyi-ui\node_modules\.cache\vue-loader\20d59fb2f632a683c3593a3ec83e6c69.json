{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue?vue&type=template&id=dd0cb8d8&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue", "mtime": 1756710899653}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}