{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\big-form.vue?vue&type=style&index=0&id=a65d177a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\big-form.vue", "mtime": 1756794280169}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBmb250LWZhY2UgewogIGZvbnQtZmFtaWx5OiBlbGVjdHJvbmljRm9udDsKICBzcmM6IHVybCguLi8uLi8uLi9hc3NldHMvZm9udHMvRFMtRElHSS50dGYpOwp9Ci5mbGV4LWNlbnRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKfQoKOjp2LWRlZXAgLmVsLWNhcmQgewogIGJveC1zaGFkb3c6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDA7Cn0KCjo6di1kZWVwIC5lbC1jYXJkX19ib2R5IHsKICBwYWRkaW5nOiAxNXB4IDIwcHg7Cn0KCi5uYXYtZGl2IHsKICBtYXJnaW46IDAgMjBweCAxMHB4IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4ICNFOUU5RTkgc29saWQ7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICMyOWI1ZWU7CiAgbGluZS1oZWlnaHQ6IDQwcHg7Cn0KCi5uYXYtdGl0bGUgewogIG1hcmdpbi1yaWdodDogNXB4Owp9CgoudGV4dF9pdGVtMSB7CiAgbWFyZ2luLXRvcDogOHB4OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnRleHRfaXRlbV9sYWJlbCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjN2Y3ZjdmOwp9CgoubGluZSB7CiAgLy9ib3JkZXItbGVmdDogMXB4IGRhc2hlZCAjY2MzMjMyOwogIGJvcmRlci1sZWZ0OiAxcHggZG90dGVkICNjYzMyMzI7CiAgYm9yZGVyLWltYWdlLXNvdXJjZTogdXJsKCcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL2JpYW5rdWFuZ3hpYW4uc3ZnJyk7CiAgYm9yZGVyLWltYWdlLXNsaWNlOiAxOwogIGJvcmRlci1pbWFnZS1yZXBlYXQ6IHJlcGVhdDsKfQoKLmxpbmU6bGFzdC1jaGlsZCB7CiAgYm9yZGVyLXJpZ2h0OiBub25lOwp9CgoubGluZTpmaXJzdC1jaGlsZCB7CiAgYm9yZGVyLWxlZnQ6IG5vbmU7Cn0KCi5saW5rUG9pbnQgewogIGNvbG9yOiAjMjliNWVlOwogIGN1cnNvcjogcG9pbnRlcjsKfQoKLmdyZWVuQ2FyZCB7CiAgaGVpZ2h0OiAxNTNweDsKICBtYXJnaW4tdG9wOiA2cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMTIsIDE4MiwgMywgMC4wOTgwMzkyMTU2ODYyNzQ1MSk7CiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCByZ2JhKDExMiwgMTgyLCAzLCAxKTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3RhcnQ7Cn0KCi53YXJuQ2FyZCB7CiAgaGVpZ2h0OiAxNTNweDsKICBtYXJnaW4tdG9wOiA2cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMzYsIDEyOCwgMTQxLCAwLjA5ODAzOTIxNTY4NjI3NDUxKTsKICBib3JkZXItbGVmdDogNHB4IHNvbGlkIHJnYmEoMjM2LCAxMjgsIDE0MSwgMSk7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHN0YXJ0Owp9CgouYmx1ZUNhcmQgewogIGhlaWdodDogMTUzcHg7CiAgbWFyZ2luLXRvcDogNnB4OwogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNjcsIDEzMCwgMjUzLCAwLjA5ODAzOTIxNTY4NjI3NDUxKTsKICBib3JkZXItbGVmdDogNHB4IHNvbGlkIHJnYmEoNjcsIDEzMCwgMjUzLCAxKTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3RhcnQ7Cn0KCi5odWlDYXJkIHsKICBoZWlnaHQ6IDE1M3B4OwogIG1hcmdpbi10b3A6IDZweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIxNSwgMjE1LCAyMTUsIDAuMDk4MDM5MjE1Njg2Mjc0NTEpOwogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgcmdiYSgyMTUsIDIxNSwgMjE1LCAxKTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3RhcnQ7Cn0KCi5qaW54aW5nIHsKICBiYWNrZ3JvdW5kOiBpbmhlcml0OwogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTI5LCAyMTEsIDI0OCwgMC4wOTgwMzkyMTU2ODYyNzQ1MSkKfQoKLnJpZ2h0LWl0ZW0gewogIG1hcmdpbjogMTBweCAxMHB4IDAgMTBweDsKICB3aWR0aDogNzAlOwogIC8vY3Vyc29yOiBwb2ludGVyOwp9CgoubGVmdC1pdGVtIHsKICBib3JkZXItbGVmdDogMXB4IGRvdHRlZCAgI2EwYTBhMDsKICB3aWR0aDogMzAlOwogIG92ZXJmbG93LXg6IGhpZGRlbjsKICBvdmVyZmxvdy15OiBhdXRvOwp9Cgoucm93LWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Owp9Ci5iaWdzY3JlZW4gewogIGhlaWdodDogMTAwJTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjJGNEY4Owp9CgouZWxsaXBzaXMgewogIHdoaXRlLXNwYWNlOiBub3dyYXA7IC8qIOehruS/neaWh+acrOWcqOS4gOihjOWGheaYvuekuiAqLwogIG92ZXJmbG93OiBoaWRkZW47IC8qIOmakOiXj+a6ouWHuueahOWGheWuuSAqLwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyAvKiDkvb/nlKjnnIHnlaXlj7fooajnpLrmuqLlh7rnmoTmlofmnKwgKi8KfQoKI215RElWIHsKICAvKmJhY2tncm91bmQ6IHVybCgiLi4vLi4vLi4vYXNzZXRzL2ljb25zL2dmZ3Rlc3QuZ2lmIikgcm91bmQ7Ki8KICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgei1pbmRleDogOTk7CiAgaGVpZ2h0OiAzMXB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgY3Vyc29yOiBwb2ludGVyOwp9CiNteURJVjo6LXdlYmtpdC1zY3JvbGxiYXIgewogIGRpc3BsYXk6bm9uZQp9Ci5pdGVtVGV4dCB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMDsKICBsZWZ0OiAwOwogIHotaW5kZXg6IDk7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDMxcHg7CiAgbGluZS1oZWlnaHQ6IDMxcHg7Cn0KLml0ZW1CZ20gewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OiAwOwogIHotaW5kZXg6IDg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsKICBoZWlnaHQ6IDMxcHg7CiAgd2lkdGg6IDEwMCU7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgcmdiYSgyMzYsIDEyOCwgMTQxLCAwLjI5OCksIHJnYmEoMjM2LCAxMjgsIDE0MSwgMC4yOTgpKTsKICBhbmltYXRpb246IG1vdmVUb3AgMS41cyBpbmZpbml0ZTsgLyogMS4156eS5YaF5peg6ZmQ5b6q546v77yM5p2l5Zue5Lqk5pu/ICovCn0KLnJlZGljb24gewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OiAtNXB4OwogIGhlaWdodDogMzFweCAhaW1wb3J0YW50Owp9Ci5yZWRpY29uMSB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHJpZ2h0OiAtNXB4OwogIGhlaWdodDogMzFweCAhaW1wb3J0YW50Owp9CkBrZXlmcmFtZXMgbW92ZVRvcCB7CiAgMCUgewogICAgdG9wOiAtMzFweDsKICB9CiAgMTAwJSB7CiAgICB0b3A6IDMxcHg7CiAgfQp9CgouYm94LWNhcmQxIHsKICAuYm94LWNhcmQxLW9uZXsKICAgIC5ib3gtY2FyZDEtb25lLWxlZnR7CiAgICAgIHdpZHRoOiA0MHB4OwogICAgICBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAvL2JvcmRlci1yaWdodDogMXB4IGRvdHRlZCAjY2MzMjMyOwogICAgICAvL2JvcmRlci1pbWFnZS1zb3VyY2U6IHVybCgnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy9iaWFua3Vhbmd4aWFuLnN2ZycpOwogICAgICAvL2JvcmRlci1pbWFnZS1zbGljZTogMjsKICAgICAgLy9ib3JkZXItaW1hZ2Utd2lkdGg6IDNweDsKICAgICAgLy9ib3JkZXItaW1hZ2Utb3V0c2V0OiAxOwogICAgICAvL2JvcmRlci1pbWFnZS1yZXBlYXQ6IHJlcGVhdDsKICAgICAgLy9ib3JkZXItbGVmdC13aWR0aDogMXB4OwogICAgfQogICAgLmJveC1jYXJkMS1vbmUtcmlnaHR7CiAgICAgIC8qYm9yZGVyLWltYWdlLXNvdXJjZTogdXJsKCcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL2JpYW5rdWFuZ3hpYW4yLnN2ZycpOwogICAgICBib3JkZXItaW1hZ2Utc2xpY2U6IDE7CiAgICAgIGJvcmRlci1pbWFnZS13aWR0aDogMnB4OwogICAgICBib3JkZXItaW1hZ2UtcmVwZWF0OiByZXBlYXQ7Ki8KICAgICAgYm9yZGVyLWxlZnQ6IDFweCBkb3R0ZWQgI2EwYTBhMDsKICAgIH0KICB9CiAgOjp2LWRlZXAgLmVsLWNhcmRfX2JvZHkgewogICAgcGFkZGluZzogMCAyMHB4OwogICAgaGVpZ2h0OiAxMDAlOwogIH0KfQouYm94LWNhcmQyIHsKICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvdTc2ODkuc3ZnJyk7CiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsKICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiB0b3A7CiAgYmFja2dyb3VuZC1yZXBlYXQ6IHJvdW5kOwogIDo6di1kZWVwIC5lbC1jYXJkX19ib2R5IHsKICAgIHBhZGRpbmc6IDA7CiAgICBoZWlnaHQ6IDEwMCU7CiAgfQp9CgouaXRlbVN0YXRlIHsKICBjb2xvcjogIzY2NjY2NjsKICBmbG9hdDogcmlnaHQ7CiAgZm9udC1zaXplOiAxMnB4OwogIGxpbmUtaGVpZ2h0OiAzMXB4OwogIHNwYW4gewogICAgbWFyZ2luOiAwIDVweDsKICAgIHN2ZyB7CiAgICAgIHdpZHRoOiAxMHB4OwogICAgICBoZWlnaHQ6IDEwcHg7CiAgICB9CiAgfQp9CgouY3VzdG9tLXByb2dyZXNzIHsKICA6OnYtZGVlcCAuZWwtcHJvZ3Jlc3MtYmFyX19pbm5lciB7CiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMTY5LCAyNTUsIDIyNCwgMSkgMyUsIHJnYmEoMjYsIDI0MSwgMTQ4LCAxKSA5OCUpOwogIH0KfQoKLyogQ2hyb21lLCBTYWZhcmksIE9wZXJhICovCkAtd2Via2l0LWtleWZyYW1lcyBteW1vdmUgewogIDMwJSB7YmFja2dyb3VuZDogI2ZmYmFjMSBib3R0b20gcmlnaHQvNTBweCA1MHB4O30KfQoKLyogU3RhbmRhcmQgc3ludGF4ICovCkBrZXlmcmFtZXMgbXltb3ZlIHsKICAzMCUge2JhY2tncm91bmQ6ICNmZmNjYzIgYm90dG9tIHJpZ2h0LzUwcHggNTBweDt9Cn0KCi8qLml0ZW0tbGlzdHsKICBvdmVyZmxvdy14OiBoaWRkZW47CiAgb3ZlcmZsb3cteTogYXV0bzsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0MDBweCk7Cn0qLwo="}, {"version": 3, "sources": ["big-form.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAglBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "big-form.vue", "sourceRoot": "src/views/aqsoc/hw-work", "sourcesContent": ["<template>\n  <div v-if=\"localVisible\" class=\"JNPF-common-layout bigscreen\" ref=\"bigscreen\">\n    <div class=\"JNPF-common-layout-center\">\n      <el-row :gutter=\"10\" style=\"height: 160px\" ref=\"headRow\">\n        <el-col :span=\"11\">\n          <el-row>\n            <el-card class=\"box-card\" style=\"height: 160px\">\n              <div slot=\"header\" class=\"clearfix\" shadow=\"always\">\n                <el-row>\n                  <el-col :span=\"21\">\n                    <div style=\"display: flex;justify-content: start;\"><svg-icon icon-class=\"xdzl\" style=\"width: 31px;height: 31px\" /><span\n                      style=\"font-size: 24px; font-weight: bold; margin-left: 5px\">{{ hwWork.year }}年度HW行动</span></div>\n                  </el-col>\n                  <el-col :span=\"3\">\n                    <div style=\"margin-right: auto\">\n                      <img title=\"全屏\" style=\"cursor: pointer; width: 24px;height: 24px;\" src=\"@/assets/icons/qp.png\" @click=\"handleFullScreen()\">&nbsp;&nbsp;&nbsp;&nbsp;\n                      <img title=\"编辑\" style=\"cursor: pointer; width: 24px;height: 24px;\" src=\"@/assets/icons/bj1.png\" @click=\"updateFrom(hwWork.id)\">&nbsp;&nbsp;&nbsp;&nbsp;\n                      <img title=\"返回\" style=\"cursor: pointer; width: 24px;height: 24px;\" src=\"@/assets/icons/fh.png\"\n                           @click=\"goBack()\">\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n              <div class=\"text_item\" style=\"height: 83px; overflow: auto\">\n                <el-row>\n                  <el-col :span=\"10\">\n                    <div style=\"font-size: 14px; height: 30px\"><span class=\"text_item_label\">HW开始时间：</span>{{ hwWork.hwStart }}</div>\n                    <div style=\"font-size: 14px; height: 30px\"><span class=\"text_item_label\">HW结束时间：</span>{{ hwWork.hwEnd }}</div>\n                    <div style=\"font-size: 14px; height: 30px\"><span class=\"text_item_label\">联络人：</span>{{ hwWork.userNames }}</div>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <div style=\"font-size: 14px; height: 30px\"><span class=\"text_item_label\">创建时间：</span>{{ hwWork.createTime }}</div>\n                    <el-row>\n                      <el-col :span=\"6\" style=\"width: 120px\">\n                        <span class=\"text_item_label\" style=\"font-weight: bold\">技术支撑单位：</span>\n                      </el-col>\n                      <el-col :span=\"16\" style=\"margin-bottom: 20px;\">\n                        <el-row v-for=\"(dept,index) in usersGroupByDept\" :key=\"index\" >\n                          <el-col :span=\"12\" >\n                            <div style=\"border: 1px dashed #a0a0a0;font-size: 14px; font-weight: bold;margin-right: 5px;padding: 0 8px;\">{{ dept.deptName }}</div>\n                          </el-col>\n                          <el-col :span=\"12\">\n                            <span  v-for=\"(user,userIndex) in dept.users\" :key=\"userIndex\" >\n                              <el-button v-if=\"userIndex + user.nickName !== buttonType\" size=\"mini\" style=\"margin: 0 3px 3px 0;height: 23px;\" @click=\"checkStepUser(user, userIndex)\">{{ user.nickName }}</el-button>\n                              <el-button v-if=\"userIndex + user.nickName  === buttonType\" size=\"mini\" type=\"success\" style=\"margin: 0 3px 3px 0;height: 23px;\" @click=\"checkStepUser1(user, userIndex)\">{{ user.nickName }}</el-button>\n                            </span>\n                          </el-col>\n                        </el-row>\n                      </el-col>\n                    </el-row>\n                  </el-col>\n                </el-row>\n              </div>\n            </el-card>\n          </el-row>\n        </el-col>\n        <el-col :span=\"3\">\n          <el-card class=\"box-card box-card2\" shadow=\"always\" style=\"height: 160px; display: flex; justify-content: center;\">\n            <div style=\"display: flex; flex-direction: column;\">\n              <div style=\"font-size: 18px;margin: 10px; padding-top: 15%; color: #7f7f7f;\">HW{{ remindTitle }}倒计时</div>\n              <div style=\"display: flex; align-items: flex-end; margin-top: 15px\">\n                <span style=\"font-weight: bold;font-size: 36px;margin-left: 20px;color:dodgerblue;font-family: electronicFont;\">{{day}}</span><span style=\"margin: 0 10px; font-size: 16px; color: #4382FD\">天</span><span style=\"font-size: 20px;color: #D9001B;font-weight: bold; font-family: electronicFont;\">{{hour}}:{{min}}:{{second}}</span>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"10\">\n          <el-card class=\"box-card box-card1\" shadow=\"always\" style=\"height: 160px\">\n            <div class=\"box-card1-one\" style=\"display: flex;justify-content: start; height: 100%\">\n              <div class=\"box-card1-one-left\">\n                <div style=\"font-size: 18px;font-weight: bold; padding: 0 20px 0 0; color: #333333\">报平安</div>\n              </div>\n              <div class=\"box-card1-one-right\" style=\"height: 100%; padding: 16px 0;width: 99%;\">\n                <div style=\"margin-left: 20px;height: 100%; overflow-y: auto; overflow-x: hidden\">\n                <el-row style=\"margin-right: 0.5px\">\n                  <el-col :span=\"8\" v-for=\"(item, index) in dayList\" :key=\"index\">\n                    <div style=\"border: 0.5px solid #a0a0a0; line-height: 40px; text-align: center;font-size:14px;margin-right: -0.5px; margin-bottom: -1px;cursor: pointer;\" @click=\"daySafe(item)\">\n                        <span style=\"color: #7f7f7f;font-size: 14px;\">{{ item.date }}</span><span style=\"color: #333;font-size: 14px;\">【{{ item.dayWeek }}】</span>\n                        <el-popover\n                      placement=\"bottom\"\n                      width=\"400\"\n                      @show=\"daySafePopoverShow(item)\"\n                      :append-to-body=\"false\"\n                      :disabled=\"!fullscreen\"\n                      trigger=\"hover\">\n                      <div>\n                        <el-form ref=\"hoverForm\" size=\"small\" label-width=\"100px\" label-position=\"right\">\n                          <template>\n                            <el-col :span=\"24\">\n                              <jnpf-form-tip-item label=\"情况说明\" prop=\"remark\">\n                                <el-input autosize v-model=\"currentDayInfo.remark\" placeholder=\"\" :disable=\"true\" readonly type=\"textarea\" clearable :style='{\"width\":\"100%\"}'>\n                                </el-input>\n                              </jnpf-form-tip-item>\n                            </el-col>\n                            <el-col :span=\"24\">\n                              <jnpf-form-tip-item label=\"附件\" prop=\"hwEnd\">\n                                <file-upload ref=\"fileUpload\" v-model=\"currentDayInfo.files\"\n                                             :disUpload=\"true\" @href-click=\"fullscreen=false\"\n                                />\n                              </jnpf-form-tip-item>\n                            </el-col>\n                          </template>\n                        </el-form>\n                      </div>\n                      <span slot=\"reference\">\n                        <el-button v-if=\"item.isSafe==='1'\" type=\"primary\" plain>平安</el-button>\n                        <el-button v-if=\"item.isSafe==='2'\" type=\"danger\" plain>异常</el-button>\n                        <el-button v-if=\"item.isSafe==='0'\" disabled>待报</el-button>\n                      </span>\n                    </el-popover>\n                      </div>\n                  </el-col>\n                </el-row>\n              </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"10\" style=\"margin: 10px 0 0 0; flex: 1\">\n        <el-card class=\"box-card\" style=\"height: 100%;\">\n          <div slot=\"header\" class=\"clearfix\" shadow=\"always\" style=\"padding: 0px 15px 0 0px\" ref=\"boxHead\">\n            <el-row>\n              <el-col :span=\"4\">\n                <div style=\"display: flex;justify-content: start; align-items: center\">\n                  <svg-icon icon-class=\"sw\" style=\"width: 23px;height: 23px; margin-right: 5px\" /><span style=\"font-size: 16px; font-weight: bold;\">HW事务进展</span>\n                </div>\n              </el-col>\n              <el-col :span=\"10\" id=\"myDIV\" style=\"overflow: hidden;\">\n                <div class=\"itemText\">\n                  <svg-icon icon-class=\"redzb\" class=\"redicon\"></svg-icon>\n                  <div style=\"font-size: 14px;font-weight: unset\" @click=\"openNewTab({url:'/service-ledger/theratManage'})\" >当前威胁告警 <span\n                    style=\"font-size: 24px;font-weight: 700;font-family: electronicFont;\">{{ alarmNum }}</span></div>\n                  <div style=\"font-size: 14px;font-weight: unset\" @click=\"openNewTab({url:'/service-ledger/theratManage?tabs=five'})\" >已成功阻断 <span\n                    style=\"font-size: 24px;font-weight: 700;font-family: electronicFont;\">{{ stopedNum }}</span></div>\n                  <div style=\"font-size: 14px;font-weight: unset\" @click=\"openNewTab({url:'/service-ledger/theratManage?handle=1'})\" >已处置威胁 <span\n                    style=\"font-size: 24px;font-weight: 700;font-family: electronicFont;\">{{ handledAlarmNum }}</span></div>\n                  <div style=\"font-size: 14px;font-weight: unset\" @click=\"openNewTab({url:'/service-ledger/theratManage?datasource=7'})\">蜜罐告警 <span\n                    style=\"font-size: 24px;font-weight: 700;font-family: electronicFont;\">{{ hpAlarmNum }}</span></div>\n                </div>\n                <svg-icon icon-class=\"redyb\" class=\"redicon1\"></svg-icon>\n                <div class=\"itemBgm\" style=\"top: 0\"></div>\n              </el-col>\n              <el-col :span=\"10\">\n                <div class=\"itemState\">\n                  <span><svg-icon icon-class=\"huise\"/> 未开始    </span>\n                  <span><svg-icon icon-class=\"blueDian\"/> 进行中    </span>\n                  <span><svg-icon icon-class=\"redDian\"/> 延期    </span>\n                  <span><svg-icon icon-class=\"greenDian\"/> 已完成</span>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n          <div ref=\"box\" class=\"item-list\" :style=\"{overflow: 'hidden auto',height: cardHeight}\">\n            <el-row :gutter=\"20\">\n              <el-col v-for=\"(item, index) in stagesTemp\" :key=\"index\" :span=\"6\" :class=\"item.state!=='1'?'line':'line jinxing'\">\n                <el-row>\n                  <el-col :span=\"22\" style=\"font-size: 14px; height: 25px; line-height: 25px; color: #333333; font-weight: bold; text-indent: 5px\">{{ item.title }}</el-col>\n                  <el-col :span=\"2\" style=\"font-size: 12px; height: 25px; line-height: 25px; color: #7f7f7f; text-indent: 10px\">{{ item.percent }}%</el-col>\n                  <el-col :span=\"24\">\n                    <el-progress class=\"custom-progress\" :show-text=\"false\" :percentage=\"item.percent\"></el-progress>\n                  </el-col>\n                  <el-col v-for=\"(step, stepIndex) in item.steps\" :key=\"stepIndex\" :span=\"24\">\n                    <div :class=\"stepClass(step.state)\">\n                      <div class=\"right-item\" @click=\"stepUpdate(step)\">\n                        <div style=\"font-size: 16px; color: #7f7f7f\">{{ step.title }}\n                          <el-tooltip placement=\"top\" effect=\"light\">\n                            <div slot=\"content\" v-html=\"step.tips\"></div>\n                            <i class=\"el-icon-info\"></i>\n                          </el-tooltip>\n                        </div>\n                        <div class=\"text_item1\">\n                          <div><span class=\"text_item_label\">计划开始时间：</span>{{ step.startTime }}</div>\n                        </div>\n                        <div class=\"text_item1\">\n                          <div><span class=\"text_item_label\">计划完成时间：</span>{{ step.endTime }}</div>\n                        </div>\n                        <div class=\"text_item1\">\n                          <div><span class=\"text_item_label\">负责人：</span>{{ step.userNames }}</div>\n                        </div>\n                        <div class=\"text_item1\">\n                          <div><span class=\"text_item_label\">实际完成时间：</span>{{ step.finishTime }}</div>\n                        </div>\n                      </div>\n                      <div class=\"left-item\">\n                        <div style=\"margin: 10px 10px 0 10px\">\n                          <div class=\"row-item\">\n                            <svg-icon icon-class=\"cg\" style=\"width: 20px;height: 20px;\" />\n                            <div style=\"margin: 0 10px 0 5px; font-size: 16px; color: #666666\">成果</div>\n<!--                            <svg-icon icon-class=\"xg\" style=\"width: 22px;height: 22px;cursor: pointer;\" @click=\"checkStep(step, item)\" />-->\n                          </div>\n                          <div v-if=\"step.needFile\" class=\"text_item1 ellipsis\"  v-for=\"file in step.files\" :title=\"file.name\">\n                            <span class=\"linkPoint\">\n                            <el-link :href=\"encodeURI(file.url)\" :underline=\"false\" target=\"_blank\" :download=\"file.name\">\n                              <span class=\"el-icon-document\" style=\"color: #4382FD;\">{{ file.name}}</span>\n                            </el-link>\n                            </span>\n                        </div>\n                          <div v-if=\"step.links\" class=\"text_item1 ellipsis\"  v-for=\"link in step.links\" :title=\"link.label\">\n                            <span class=\"linkPoint\" @click=\"openNewTab(link)\" style=\"color: #4382FD;\">{{ link.label }}\n                            </span>\n                          </div>\n                      </div>\n                    </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-row>\n    </div>\n    <Form :visible.sync=\"formVisible\" ref=\"addForm\" @refresh=\"refresh\"></Form>\n    <day-form :visible.sync=\"dayFormVisible\" ref=\"dayForm\" @refresh=\"refresh\"></day-form>\n    <step-form :visible.sync=\"stepFormVisible\" ref=\"stepForm\" @close=\"submitStages\"></step-form>\n    <step-check-form :visible.sync=\"stepCheckFormVisible\" ref=\"checkForm\" @save=\"submitStages\"></step-check-form>\n  </div>\n</template>\n<script>\nimport {getNum,updateStages,getInfo,getCountNum} from '@/api/aqsoc/work-hw/crud'\nimport {getInfo as getDayInfo,getList as getDayList} from '@/api/aqsoc/work-hw-day/crud'\nimport {mapGetters} from 'vuex'\nimport Form from './form'\nimport DayForm from './day-form'\nimport StepForm from './step-form'\nimport StepCheckForm from \"./step-check-form\";\n\nexport default {\n  components: {StepCheckForm, StepForm, DayForm, Form},\n  props: {\n    visible: {\n      type: Boolean,\n      required: true\n    }\n  },\n  data() {\n    return {\n      remindTitle:'开始',\n      formVisible: false,\n      dayFormVisible: false,\n      stepFormVisible: false,\n      stepCheckFormVisible: false,\n      fullscreen: false,\n      curStartTime: '2024-12-31 23:59:59',\n      day: '0',\n      hour: '00',\n      min: '00',\n      second: '00',\n      timer: null,\n      hwWork: {},\n      usersGroupByDept:{},\n      stages: [],\n      stagesTemp: [],\n      dayList: [],\n      alarmNum: 0,\n      stopedNum: 0,\n      handledAlarmNum: 0,\n      hpAlarmNum: 0,\n      dataForm: {},\n      buttonType: null,\n      buttonFlag: null,\n      cardHeight: '600px',\n      currentDayInfo: {},\n    }\n  },\n  filters: {\n    toNumber(value) {\n      return Number(value);\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo']),\n    localVisible: {\n      get() {\n        return this.visible\n      },\n      set(value) {\n        this.$emit('update:visible', value)\n      }\n    },\n    remainDays() {\n      if (this.hwWork.hwEnd) {\n        return this.daysBetween(new Date(), new Date(this.hwWork.hwEnd))\n      }\n      return 0\n    },\n  },\n  watch: {},\n  created() {\n    this.cardHeight = window.innerHeight - 400 + 'px'\n\n  },\n  beforeDestroy() {\n    // 清除定时器\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  mounted() {\n    window.addEventListener('resize', this.handleResize)\n  },\n  methods: {\n    getCountNum(link){\n      if(link.countId){\n        getCountNum(link.countId).then(res=>{\n          link.num = res.data\n        })\n      }\n    },\n    openNewTab(link){\n      const baseUrl = window.location.origin;\n      /*if (link.label.includes('漏洞')) return window.open(baseUrl +'/service-ledger/frailty', '_blank')\n      if (link.label.includes('蜜罐')) return window.open(baseUrl +'/service-ledger/theratManage?type=2', '_blank')\n      if (link.label.includes('阻断')) return window.open(baseUrl +'/service-ledger/theratManage?type=4', '_blank')*/\n      const newUrl = `${baseUrl}${link.url}`;\n      window.open(newUrl, '_blank');\n    },\n    countTime () {\n      // 获取当前时间\n      let date = new Date()\n      let now = date.getTime()\n\n      // 设置截止时间\n      let endDate = new Date(this.curStartTime) // this.curStartTime需要倒计时的日期\n      let end = endDate.getTime()\n\n      // 时间差\n      let leftTime = end - now\n\n      // 定义变量 day,h,m,s保存倒计时的时间\n      if (leftTime >= 0) {\n        // 天\n        this.day = Math.floor(leftTime / 1000 / 60 / 60 / 24)\n        // 时\n        let h = Math.floor(leftTime / 1000 / 60 / 60 % 24)\n        this.hour = h < 10 ? '0' + h : h\n        // 分\n        let m = Math.floor(leftTime / 1000 / 60 % 60)\n        this.min = m < 10 ? '0' + m : m\n        // 秒\n        let s = Math.floor(leftTime / 1000 % 60)\n        this.second = s < 10 ? '0' + s : s\n      } else {\n        this.day = 0\n        this.hour = '00'\n        this.min = '00'\n        this.second = '00'\n      }\n      // 等于0的时候不调用\n      if (Number(this.hour) === 0 && Number(this.day) === 0 && Number(this.min) === 0 && Number(this.second) === 0) {\n        return\n      } else {\n        // 递归每秒调用countTime方法，显示动态时间效果,\n        this.timer = setTimeout(this.countTime, 1000)\n      }\n    },\n    checkStepUser(user, key) {\n      this.stagesTemp = []\n      this.buttonType = key + user.nickName\n      this.buttonFlag = key + user.nickName\n      // 筛选 下面事务\n      const temp =  JSON.parse(JSON.stringify(this.stages));\n      temp.forEach((e, key) => {\n        let stagesTempSteps = []\n        e.steps.forEach(f => {\n          if (f.userNames) {\n            if (f.userNames.indexOf(user.nickName) !== -1 && f.userIds.indexOf(user.userId) !== -1) {\n              stagesTempSteps.push(f)\n            }\n          }\n        })\n        this.stagesTemp.push(e)\n        this.stagesTemp[key].steps = stagesTempSteps\n      })\n    },\n    checkStepUser1(user, key) {\n      this.stagesTemp = []\n      this.buttonType = key + user.nickName\n      if (this.buttonFlag === this.buttonType) {\n        this.buttonType = null\n        // 还原 下面事务\n        this.stagesTemp = this.stages\n      } else {\n        // 筛选 下面事务\n        const temp =  JSON.parse(JSON.stringify(this.stages));\n        temp.forEach((e, key) => {\n          let stagesTempSteps = []\n          e.steps.forEach(f => {\n            if (f.userNames) {\n              if (f.userNames.indexOf(user.nickName) !== -1 && f.userIds.indexOf(user.userId) !== -1) {\n                stagesTempSteps.push(f)\n              }\n            }\n          })\n          this.stagesTemp.push(e)\n          this.stagesTemp[key].steps = stagesTempSteps\n        })\n      }\n    },\n    handleResize() {\n      let h = this.$refs.bigscreen.offsetHeight;\n      let headHeight = this.$refs.headRow.$el.offsetHeight || 160;\n      let boxHeadHeight = this.$refs.boxHead.offsetHeight || 45;\n      //window.innerHeight\n      // this.cardHeight = window.innerHeight - headHeight - boxHeadHeight + 'px'\n      this.cardHeight = h-160-boxHeadHeight-10-15-15-7 + 'px'\n      console.log(h-160-boxHeadHeight)\n      console.log(this.cardHeight,555)\n    },\n    stepClass(stepState) {\n      switch (stepState) {\n        case '1':\n          return 'blueCard'\n        case '2':\n          return 'greenCard'\n        case '3':\n          return 'warnCard'\n        default:\n          return 'huiCard'\n      }\n    },\n    daysBetween(date1, date2) {\n      if (date1 >= date2) {\n        return 0\n      }\n      const oneDay = 24 * 60 * 60 * 1000;\n      const firstDate = date1.getTime();\n      const secondDate = date2.getTime();\n      const difference = Math.abs(firstDate - secondDate);\n      return Math.ceil(difference / oneDay);\n    },\n    init(row, isDetail, isAudit) {\n      console.log('护网工作台init', row)\n      this.hwWork = row\n      // 倒计时\n      const now = new Date();\n      if(now< new Date(this.hwWork.hwStart)){\n        this.curStartTime = this.hwWork.hwStart\n      }else if(now< new Date(this.hwWork.hwEnd)){\n        this.remindTitle = '结束'\n        this.curStartTime = this.hwWork.hwEnd\n      }\n      this.countTime()\n      // 统计\n      getNum(\"getAlarmNum\", this.hwWork.id).then(res => {\n        this.alarmNum = res.data\n      })\n      getNum(\"getStopedNum\", this.hwWork.id).then(res => {\n        this.stopedNum = res.data\n      })\n      getNum(\"getHandledAlarmNum\", this.hwWork.id).then(res => {\n        this.handledAlarmNum = res.data\n      })\n      getNum(\"getHpAlarmNum\", this.hwWork.id).then(res => {\n        this.hpAlarmNum = res.data\n      })\n      getDayList({hwId: this.hwWork.id, pageSize: -1}).then(res => {\n        this.dayList = res.data.list\n      })\n      getInfo(this.hwWork.id).then(res=>{\n        this.hwWork=res.data\n        this.usersGroupByDept = res.data.usersGroupByDept\n        // 阶段json转对象\n        this.stages = JSON.parse(this.hwWork.dataJson).stages\n        // 遍历links\n        for(const stage of this.stages){\n          if(stage.steps && stage.steps.length>0){\n            for(const step of stage.steps){\n              if(step.links){\n                for(const link of step.links){\n                  this.getCountNum(link)\n                }\n              }\n            }\n          }\n        }\n        this.stagesTemp = this.stages\n      })\n\n    },\n    updateFrom(id, isDetail, isAudit) {\n      this.formVisible = true\n      this.$nextTick(() => {\n        this.$refs.addForm.init(id, isDetail, isAudit)\n      })\n    },\n    daySafe(item, isDetail, isAudit){\n      this.dayFormVisible = true\n      this.$nextTick(() => {\n        this.$refs.dayForm.init(item.id, true, isAudit)\n      })\n    },\n    stepUpdate(step) {\n      return;\n      if(this.buttonType!=null){\n        this.$message.warning(\"筛选视图禁止编辑\")\n        return\n      }\n      if(step.state=='2'){\n        this.$message.warning(\"已完成，不可修改计划\")\n        return\n      }\n      this.stepFormVisible = true\n      this.$nextTick(() => {\n        this.$refs.stepForm.init(step)\n      })\n    },\n    checkStep(step, stage) {\n      if(this.buttonType!=null){\n        this.$message.warning(\"筛选视图禁止编辑\")\n        return\n      }\n      if(step.startTime==''||step.endTime==''||step.userIds==''){\n        this.$message.warning(\"请先填写计划开始时间、计划结束时间、责任人\")\n        return\n      }\n      if(stage.state!=='1'){\n        this.$message.warning(\"本阶段不在进行中，不能提交成果\")\n        return\n      }\n      this.stepCheckFormVisible = true\n      this.$nextTick(() => {\n        this.$refs.checkForm.init(step)\n      })\n    },\n    refresh(isrRefresh) {\n      this.formVisible = false\n      this.dayFormVisible = false\n      this.stepFormVisible = false\n      this.stepCheckFormVisible = false\n      if (isrRefresh) this.init(this.hwWork)\n    },\n    submitStages(){\n      // 阶段对象转json\n      this.hwWork.dataJson = JSON.stringify({stages:this.stages})\n      updateStages(this.hwWork).then(res=>{\n        //后台可能会更改阶段的状态，所以需要重新init\n        //this.hwWork=res.data\n        this.refresh(true)\n      });\n    },\n    // 全屏事件\n    handleFullScreen() {\n      let element = document.getElementsByClassName(\"bigscreen\")[0];\n      // let element = document.getElementsByTagName(\"body\")[0];\n      // 判断是否已经是全屏\n      // 如果是全屏，退出\n      this.fullscreen = document.fullscreen;\n      if (this.fullscreen) {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitCancelFullScreen) {\n          document.webkitCancelFullScreen();\n        } else if (document.mozCancelFullScreen) {\n          document.mozCancelFullScreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      } else {\n        // 否则，进入全屏\n        if (element.requestFullscreen) {\n          element.requestFullscreen();\n        } else if (element.webkitRequestFullScreen) {\n          element.webkitRequestFullScreen();\n        } else if (element.mozRequestFullScreen) {\n          element.mozRequestFullScreen();\n        } else if (element.msRequestFullscreen) {\n          // IE11\n          element.msRequestFullscreen();\n        }\n\n      }\n      // 改变当前全屏状态\n      this.fullscreen = !this.fullscreen;\n    },\n    goBack() {\n      this.localVisible = false\n      this.$emit('refresh', true);\n    },\n    daySafePopoverShow(item){\n      this.currentDayInfo = {};\n      getDayInfo(item.id).then(res => {\n        this.currentDayInfo = res.data;\n      })\n    },\n  },\n}\n\n</script>\n<style lang=\"scss\" scoped>\n@font-face {\n  font-family: electronicFont;\n  src: url(../../../assets/fonts/DS-DIGI.ttf);\n}\n.flex-center {\n  display: flex;\n  justify-content: center;\n}\n\n::v-deep .el-card {\n  box-shadow: none;\n  border: none;\n  border-radius: 0;\n}\n\n::v-deep .el-card__body {\n  padding: 15px 20px;\n}\n\n.nav-div {\n  margin: 0 20px 10px 20px;\n  border-bottom: 1px #E9E9E9 solid;\n  font-weight: bold;\n  color: #29b5ee;\n  line-height: 40px;\n}\n\n.nav-title {\n  margin-right: 5px;\n}\n\n.text_item1 {\n  margin-top: 8px;\n  font-size: 14px;\n}\n\n.text_item_label {\n  font-size: 14px;\n  color: #7f7f7f;\n}\n\n.line {\n  //border-left: 1px dashed #cc3232;\n  border-left: 1px dotted #cc3232;\n  border-image-source: url('../../../assets/images/biankuangxian.svg');\n  border-image-slice: 1;\n  border-image-repeat: repeat;\n}\n\n.line:last-child {\n  border-right: none;\n}\n\n.line:first-child {\n  border-left: none;\n}\n\n.linkPoint {\n  color: #29b5ee;\n  cursor: pointer;\n}\n\n.greenCard {\n  height: 153px;\n  margin-top: 6px;\n  background-color: rgba(112, 182, 3, 0.09803921568627451);\n  border-left: 4px solid rgba(112, 182, 3, 1);\n  display: flex;\n  justify-content: start;\n}\n\n.warnCard {\n  height: 153px;\n  margin-top: 6px;\n  background-color: rgba(236, 128, 141, 0.09803921568627451);\n  border-left: 4px solid rgba(236, 128, 141, 1);\n  display: flex;\n  justify-content: start;\n}\n\n.blueCard {\n  height: 153px;\n  margin-top: 6px;\n  background-color: rgba(67, 130, 253, 0.09803921568627451);\n  border-left: 4px solid rgba(67, 130, 253, 1);\n  display: flex;\n  justify-content: start;\n}\n\n.huiCard {\n  height: 153px;\n  margin-top: 6px;\n  background-color: rgba(215, 215, 215, 0.09803921568627451);\n  border-left: 4px solid rgba(215, 215, 215, 1);\n  display: flex;\n  justify-content: start;\n}\n\n.jinxing {\n  background: inherit;\n  background-color: rgba(129, 211, 248, 0.09803921568627451)\n}\n\n.right-item {\n  margin: 10px 10px 0 10px;\n  width: 70%;\n  //cursor: pointer;\n}\n\n.left-item {\n  border-left: 1px dotted  #a0a0a0;\n  width: 30%;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.row-item {\n  display: flex;\n  justify-content: flex-start;\n}\n.bigscreen {\n  height: 100%;\n  background-color: #F2F4F8;\n}\n\n.ellipsis {\n  white-space: nowrap; /* 确保文本在一行内显示 */\n  overflow: hidden; /* 隐藏溢出的内容 */\n  text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */\n}\n\n#myDIV {\n  /*background: url(\"../../../assets/icons/gfgtest.gif\") round;*/\n  position: relative;\n  z-index: 99;\n  height: 31px;\n  overflow-y: auto;\n  cursor: pointer;\n}\n#myDIV::-webkit-scrollbar {\n  display:none\n}\n.itemText {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 9;\n  display: flex;\n  justify-content: space-around;\n  width: 100%;\n  height: 31px;\n  line-height: 31px;\n}\n.itemBgm {\n  position: absolute;\n  left: 0;\n  z-index: 8;\n  display: flex;\n  justify-content: space-around;\n  height: 31px;\n  width: 100%;\n  background: linear-gradient(to bottom, rgba(236, 128, 141, 0.298), rgba(236, 128, 141, 0.298));\n  animation: moveTop 1.5s infinite; /* 1.5秒内无限循环，来回交替 */\n}\n.redicon {\n  position: absolute;\n  left: -5px;\n  height: 31px !important;\n}\n.redicon1 {\n  position: absolute;\n  right: -5px;\n  height: 31px !important;\n}\n@keyframes moveTop {\n  0% {\n    top: -31px;\n  }\n  100% {\n    top: 31px;\n  }\n}\n\n.box-card1 {\n  .box-card1-one{\n    .box-card1-one-left{\n      width: 40px;\n      display: flex; align-items: center;\n      //border-right: 1px dotted #cc3232;\n      //border-image-source: url('../../../assets/images/biankuangxian.svg');\n      //border-image-slice: 2;\n      //border-image-width: 3px;\n      //border-image-outset: 1;\n      //border-image-repeat: repeat;\n      //border-left-width: 1px;\n    }\n    .box-card1-one-right{\n      /*border-image-source: url('../../../assets/images/biankuangxian2.svg');\n      border-image-slice: 1;\n      border-image-width: 2px;\n      border-image-repeat: repeat;*/\n      border-left: 1px dotted #a0a0a0;\n    }\n  }\n  ::v-deep .el-card__body {\n    padding: 0 20px;\n    height: 100%;\n  }\n}\n.box-card2 {\n  background-image: url('../../../assets/images/u7689.svg');\n  background-size: cover;\n  background-position: top;\n  background-repeat: round;\n  ::v-deep .el-card__body {\n    padding: 0;\n    height: 100%;\n  }\n}\n\n.itemState {\n  color: #666666;\n  float: right;\n  font-size: 12px;\n  line-height: 31px;\n  span {\n    margin: 0 5px;\n    svg {\n      width: 10px;\n      height: 10px;\n    }\n  }\n}\n\n.custom-progress {\n  ::v-deep .el-progress-bar__inner {\n    background: linear-gradient(90deg, rgba(169, 255, 224, 1) 3%, rgba(26, 241, 148, 1) 98%);\n  }\n}\n\n/* Chrome, Safari, Opera */\n@-webkit-keyframes mymove {\n  30% {background: #ffbac1 bottom right/50px 50px;}\n}\n\n/* Standard syntax */\n@keyframes mymove {\n  30% {background: #ffccc2 bottom right/50px 50px;}\n}\n\n/*.item-list{\n  overflow-x: hidden;\n  overflow-y: auto;\n  height: calc(100vh - 400px);\n}*/\n</style>\n"]}]}