package com.ruoyi.external.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.validation.Validator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.external.domain.ExternalAttackOfficialAccount;
import com.ruoyi.external.mapper.ExternalAttackOfficialAccountMapper;
import com.ruoyi.external.model.ExternalAttackOfficialAccountExcelForm;
import com.ruoyi.external.service.IExternalAttackOfficialAccountService;
import com.ruoyi.external.util.ExternalAttackValidationUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信公众号Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
@Slf4j
@Service
public class ExternalAttackOfficialAccountServiceImpl implements IExternalAttackOfficialAccountService
{
    // 每批次查询的最大元素数量
    private static final int BATCH_SIZE = 500;

    @Autowired
    private ExternalAttackOfficialAccountMapper externalAttackOfficialAccountMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper userMapper;


    private Long defaultDeptId = 100L;

    @PostConstruct
    public void initDefaultDeptId() {
        SysDept defaultDept = deptMapper.selectDeptByParentId(0L);
        if (Objects.nonNull(defaultDept)) {
            this.defaultDeptId = Objects.nonNull(defaultDept.getDeptId()) ? defaultDept.getDeptId() : 100L;
        }
    }

    /**
     * 查询微信公众号
     *
     * @param id 微信公众号主键
     * @return 微信公众号
     */
    @Override
    public ExternalAttackOfficialAccount selectExternalAttackOfficialAccountById(Long id)
    {
        return externalAttackOfficialAccountMapper.selectExternalAttackOfficialAccountById(id);
    }

    /**
     * 批量查询微信公众号
     *
     * @param ids 微信公众号主键集合
     * @return 微信公众号集合
     */
    @Override
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountByIds(Long[] ids)
    {
        return externalAttackOfficialAccountMapper.selectExternalAttackOfficialAccountByIds(ids);
    }

    /**
     * 查询微信公众号列表
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 微信公众号
     */
    @Override
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountList(ExternalAttackOfficialAccount externalAttackOfficialAccount)
    {
        return externalAttackOfficialAccountMapper.selectExternalAttackOfficialAccountList(externalAttackOfficialAccount);
    }

    /**
     * 新增微信公众号
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    @Override
    public int insertExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount)
    {
        // 录入类型为手动时，才校验，因为数据同步时，已有去重逻辑
        if (Objects.equals(externalAttackOfficialAccount.getEntryType(),2)) {
            validateOfficialAccountUniqueness(externalAttackOfficialAccount, null);
        }


        externalAttackOfficialAccount.setCreateTime(DateUtils.getNowDate());
        // 如果没有deptId，则设置为默认部门id(定级部门)
        if (Objects.isNull(externalAttackOfficialAccount.getDeptId())) {
            externalAttackOfficialAccount.setDeptId(defaultDeptId);
        }
        return externalAttackOfficialAccountMapper.insertExternalAttackOfficialAccount(externalAttackOfficialAccount);
    }

    /**
     * 修改微信公众号
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    @Override
    public int updateExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount)
    {
        validateOfficialAccountUniqueness(externalAttackOfficialAccount, externalAttackOfficialAccount.getId());


        externalAttackOfficialAccount.setUpdateTime(DateUtils.getNowDate());
        return externalAttackOfficialAccountMapper.updateExternalAttackOfficialAccount(externalAttackOfficialAccount);
    }

    /**
     * 检查 微信公众号 是否已存在（排除指定 id）
     * xxAppId非空的校验唯一性,空的就不校验唯一
     *
     * @param record
     * @param excludeId
     */
    private void validateOfficialAccountUniqueness(ExternalAttackOfficialAccount record, Long excludeId) {
        String officialAccountAppId = record.getOfficialAccountAppId();
        String officialAccountName = record.getOfficialAccountName();
        if (externalAttackOfficialAccountMapper.checkOfficialAccountExistence(officialAccountAppId, officialAccountName, excludeId) > 0) {
            throw new ServiceException("微信公众号已存在");
        }
    }

    /**
     * 校验微信公众号是否存在
     *
     * @param officialAccountAppId
     * @param officialAccountName
     * @return
     */
    private boolean validateOfficialAccount(String officialAccountAppId, String officialAccountName) {
        return externalAttackOfficialAccountMapper.checkOfficialAccountExistence(officialAccountAppId, officialAccountName,null) > 0;
    }

    /**
     * 删除微信公众号信息
     *
     * @param id 微信公众号主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackOfficialAccountById(Long id)
    {
        return externalAttackOfficialAccountMapper.deleteExternalAttackOfficialAccountById(id);
    }

    /**
     * 批量删除微信公众号
     *
     * @param ids 需要删除的微信公众号主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackOfficialAccountByIds(Long[] ids)
    {
        return externalAttackOfficialAccountMapper.deleteExternalAttackOfficialAccountByIds(ids);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExternalAttackOfficialAccount(List<ExternalAttackOfficialAccountExcelForm> officialAccountExcelFormList, boolean updateSupport, String operName) {
        if (CollUtil.isEmpty(officialAccountExcelFormList)){
            throw new ServiceException("导入微信小程序数据不能为空！");
        }
        // 检查导入数据条数是否超过500条
        if (officialAccountExcelFormList.size() > 500) {
            throw new ServiceException("单次导入数据条数不能超过500条！");
        }
        // 查询部门列表
        List<SysDept> queryDeptList = deptMapper.selectDeptList(new SysDept());
        Map<String, SysDept> deptMap = queryDeptList.stream().collect(Collectors.toMap(
                SysDept::getDeptName,
                Function.identity(),
                (dept1, dept2) -> { // mergeFunction, 解决键冲突的方法
                    // 按ID大小比较
                    return dept1.getDeptId() < dept2.getDeptId() ? dept1 : dept2;
                }));
        // 查询用户列表
        List<SysUser> sysUsers = userMapper.selectUserList(new SysUser());
        Map<String, Long> userMap = sysUsers.stream().collect(Collectors.toMap(
                user -> user.getNickName() + "-" + user.getPhonenumber(), // 新的键生成方式
                SysUser::getUserId, // 值为 SysUser 的 userId
                (id1, id2) -> { // 解决键冲突的方法
                    // 如果两个用户有相同的键，则选择一个值，这里简单地返回第一个值
                    return id1;
                }
        ));

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        //记录新增和更新域名
        List<ExternalAttackOfficialAccount> insertOfficialAccountList = new ArrayList<>();
        List<ExternalAttackOfficialAccount> updateOfficialAccountList = new ArrayList<>();
        //检查新增微信小程序的App ID否有重复的
        HashSet<String> officialAccountAppIds = new HashSet<>();
        //先检查小程序APP ID是否存在和校验数据是否正确
        for (ExternalAttackOfficialAccountExcelForm officialAccountExcelForm : officialAccountExcelFormList)
        {
            try
            {
                if (!officialAccountAppIds.contains(StrUtil.format("{}-{}", officialAccountExcelForm.getOfficialAccountName(), officialAccountExcelForm.getOfficialAccountAppId())))
                {
                    BeanValidators.validateWithException(validator, officialAccountExcelForm);
                    // 域名重复
                    if (this.validateOfficialAccount(officialAccountExcelForm.getOfficialAccountAppId(), officialAccountExcelForm.getOfficialAccountName())){
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、公众号名称 " + officialAccountExcelForm.getOfficialAccountName() + " 公众号APP ID " + officialAccountExcelForm.getOfficialAccountAppId() + " 的数据已存在");
                    }else if(!deptMap.containsKey(officialAccountExcelForm.getDeptName())){
                        // 校验部门
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、公众号APP ID " + officialAccountExcelForm.getOfficialAccountAppId() + " 输入的所属部门不正确");
                    }else {
                        String responsiblePerson = officialAccountExcelForm.getResponsiblePersonName();
                        Pair<Boolean, String> booleanStringPair = Pair.of(true, "默认校验通过");
                        if(StrUtil.isNotBlank(responsiblePerson)){
                            booleanStringPair = ExternalAttackValidationUtils.checkResponsibilityExists(sysUsers, responsiblePerson);
                        }
                        if (!booleanStringPair.getKey()) {
                            // 校验负责人输入格式是否正确/负责人是否存在
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、公众号APP ID " + officialAccountExcelForm.getOfficialAccountAppId() + booleanStringPair.getValue());
                        } else {
                            ExternalAttackOfficialAccount externalAttackOfficialAccount = new ExternalAttackOfficialAccount();
                            BeanUtils.copyBeanProp(externalAttackOfficialAccount, officialAccountExcelForm);
                            externalAttackOfficialAccount.setDeptId(deptMap.get(officialAccountExcelForm.getDeptName()).getDeptId());
                            String userIds = ExternalAttackValidationUtils.getUserIds(responsiblePerson, userMap);
                            externalAttackOfficialAccount.setResponsiblePerson(userIds);
                            externalAttackOfficialAccount.setCreateBy(operName);
                            // 设置录入类型为2手动
                            externalAttackOfficialAccount.setEntryType(2);
                            insertOfficialAccountList.add(externalAttackOfficialAccount);
                            successMsg.append("<br/>" + successNum + "、公众号APP ID " + externalAttackOfficialAccount.getOfficialAccountAppId() + " 导入成功");
                            officialAccountAppIds.add(StrUtil.format("{}-{}", officialAccountExcelForm.getOfficialAccountName(), officialAccountExcelForm.getOfficialAccountAppId()));
                        }
                    }
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、公众号名称 " + officialAccountExcelForm.getOfficialAccountName() + " 公众号APP ID " + officialAccountExcelForm.getOfficialAccountAppId() + " 已存在或重复添加");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公众号APP ID " + officialAccountExcelForm.getOfficialAccountAppId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            //插入域名数据
            for (ExternalAttackOfficialAccount externalAttackOfficialAccount : insertOfficialAccountList) {
                this.insertExternalAttackOfficialAccount(externalAttackOfficialAccount);
                successNum++;
            }
            if (updateSupport){
                for (ExternalAttackOfficialAccount externalAttackOfficialAccount : updateOfficialAccountList) {
                    this.updateExternalAttackOfficialAccount(externalAttackOfficialAccount);
                    successNum++;
                }
            }
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    @Override
    public List<ExternalAttackOfficialAccount> selectByOfficialAccountAppIds(Set<String> appIds) {
        List<ExternalAttackOfficialAccount> result = new ArrayList<>();
        int totalSize = appIds.size();
        int batchSize = BATCH_SIZE;
        // 将 Set 转换为 List，以便支持 subList 操作
        List<String> appIdsList = new ArrayList<>(appIds);
        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<String> batchAppIds = appIdsList.subList(i, end);
            result.addAll(externalAttackOfficialAccountMapper.selectByAppIds(batchAppIds));
        }
        return result;
    }

    @Override
    public int countNum() {
        return externalAttackOfficialAccountMapper.countNum();
    }

    @Override
    public void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId) {
           if (CollUtil.isEmpty(uniqueKeys)) {
            return;
        }
        externalAttackOfficialAccountMapper.deleteByEntryTypeAndAppId(uniqueKeys, deviceConfigId);
    }

}
