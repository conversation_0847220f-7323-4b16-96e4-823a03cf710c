{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue?vue&type=template&id=4f88bc19&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue", "mtime": 1756794280235}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}