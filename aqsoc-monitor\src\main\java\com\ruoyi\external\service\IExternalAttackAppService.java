package com.ruoyi.external.service;

import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackApp;
import com.ruoyi.external.model.ExternalAttackAppExcelForm;

/**
 * APP应用程序Service接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface IExternalAttackAppService
{
    /**
     * 查询APP应用程序
     *
     * @param id APP应用程序主键
     * @return APP应用程序
     */
    public ExternalAttackApp selectExternalAttackAppById(Long id);

    /**
     * 批量查询APP应用程序
     *
     * @param ids APP应用程序主键集合
     * @return APP应用程序集合
     */
    public List<ExternalAttackApp> selectExternalAttackAppByIds(Long[] ids);

    /**
     * 查询APP应用程序列表
     *
     * @param externalAttackApp APP应用程序
     * @return APP应用程序集合
     */
    public List<ExternalAttackApp> selectExternalAttackAppList(ExternalAttackApp externalAttackApp);

    /**
     * 新增APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    public int insertExternalAttackApp(ExternalAttackApp externalAttackApp);

    /**
     * 修改APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    public int updateExternalAttackApp(ExternalAttackApp externalAttackApp);

    /**
     * 删除APP应用程序信息
     *
     * @param id APP应用程序主键
     * @return 结果
     */
    public int deleteExternalAttackAppById(Long id);

    /**
     * 批量删除APP应用程序
     *
     * @param ids 需要删除的APP应用程序主键集合
     * @return 结果
     */
    public int deleteExternalAttackAppByIds(Long[] ids);

    String importExternalAttackApp(List<ExternalAttackAppExcelForm> attackAppExcelFormList, boolean updateSupport, String operName);

    /**
     * 根据appId批量查询App应用程序,每500条查一次并最终汇总
     * @param appIds
     * @return
     */
    List<ExternalAttackApp> selectByAppIds(Set<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId);

}
