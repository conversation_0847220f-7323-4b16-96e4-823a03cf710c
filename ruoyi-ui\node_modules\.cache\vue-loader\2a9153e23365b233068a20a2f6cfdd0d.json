{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue", "mtime": 1756710899571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEFsbFVzZXJMaXN0QnlEZXB0LGdldEFsbFVzZXJMaXN0IH0gZnJvbSAiLi4vLi4vLi4vYXBpL3N5c3RlbS91c2VyIgppbXBvcnQgeyBhZGRPcmRlciB9IGZyb20gIi4uLy4uLy4uL2FwaS90b29sL3dvcmsiOwppbXBvcnQgRGVwdFNlbGVjdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL3NlbGVjdC9kZXB0U2VsZWN0IjsKaW1wb3J0IFJlbGV2YW5jeUdhcEluZm8gZnJvbSAiLi9yZWxldmFuY3lHYXBJbmZvIjsKaW1wb3J0IFJlbGV2YW5jeUFsYXJtSW5mbyBmcm9tICIuL3JlbGV2YW5jeUFsYXJtSW5mbyI7CmltcG9ydCByZWxldmFuY3lXZWJ2dWxuSW5mbyBmcm9tICIuL3JlbGV2YW5jeVdlYnZ1bG5JbmZvIjsKaW1wb3J0IHtsaXN0V2ViVnVsbn0gZnJvbSAiQC9hcGkvbW9uaXRvcjIvd2VidnVsbiI7IC8vd2Vi5ryP5rSeCmltcG9ydCB7Z2V0VnVsbkRlYWxMaXN0fSBmcm9tICJAL2FwaS9tb25pdG9yMi9hc3NldEZyYWlsdHkiOyAvL0lQ5ryP5rSeCmltcG9ydCB7bGlzdEFsYXJtfSBmcm9tICJAL2FwaS90aHJlYXRlbi90aHJlYXRlbldhcm4iOyAvL+WogeiDgeS6i+S7tgppbXBvcnQgY29tTWl4aW4gZnJvbSAnQC92aWV3cy96ZXJvQ29kZS93b3JrRmxvdy93b3JrRmxvd0Zvcm0vbWl4aW4nCmltcG9ydCB7Z2V0TXVsVHlwZURpY3R9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOwppbXBvcnQge2dldEFwcGxpY2F0aW9uTGlzdEJ5Q29uZGl0aW9uLGdldEJ1c2luZXNzSW5mbyxnZXRBcHBsaWNhdGlvbkRldGFpbHN9IGZyb20gIkAvYXBpL3NhZmUvYXBwbGljYXRpb24iOwppbXBvcnQgeyBhc3NldFNlbGVjdExpc3QsZ2V0SW5mb0J5aWRzLGxpc3RCeUFwcGxpY2F0aW9uSWQgfSBmcm9tICJAL2FwaS9hc3NldFNlbGVjdC9hc3NldFNlbGVjdC5qcyI7CmltcG9ydCBBcHBsaWNhdGlvblNlbGVjdCBmcm9tICIuL2FwcGxpY2F0aW9uU2VsZWN0LnZ1ZSI7CmltcG9ydCBTZWxlY3RlZEV2ZW50TGlzdCBmcm9tICIuL3NlbGVjdGVkX2V2ZW50X2xpc3QudnVlIjsKaW1wb3J0IFNlbGVjdEV2ZW50IGZyb20gIi4vc2VsZWN0X2V2ZW50LnZ1ZSI7CmltcG9ydCBBbGFybURldGFpbCBmcm9tICJAL3ZpZXdzL2Jhc2lzL3NlY3VyaXR5V2Fybi9hbGFybURldGFpbC52dWUiOwppbXBvcnQgbmV3QWRkbG9vcGhvbGUgZnJvbSAiQC92aWV3cy9mcmFpbHR5L2xvb3Bob2xlL25ld0FkZGxvb3Bob2xlLnZ1ZSI7CmltcG9ydCBuZXdBZGR3ZWJ2dWxuIGZyb20gIkAvdmlld3MvZnJhaWx0eS93ZWJ2dWxuL25ld0FkZHdlYnZ1bG4iOwppbXBvcnQgcmVwb3J0VGFyZ2V0IGZyb20gIkAvdmlld3MvdG9kb0l0ZW0vdG9kby9yZXBvcnRfdGFyZ2V0LnZ1ZSI7CmltcG9ydCB7bGlzdERlcHR9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiY3JlYXRlV29yayIsCiAgbWl4aW5zOiBbY29tTWl4aW5dLAogIGRpY3RzOiBbCiAgICAnbG9vcGhvbGVfY2F0ZWdvcnknLAogICAgJ3RocmVhdGVuX2FsYXJtX3R5cGUnLAogICAgJ3dvcmtfb3JkZXJfcmVwb3J0X3R5cGUnLAogICAgJ3dvcmtfb3JkZXJfdXJnZW5jeScsCiAgICAnd29ya19vcmRlcl9wdWJsaWMnLAogICAgJ3dvcmtfb3JkZXJfc2V2ZXJpdHlfbGV2ZWwnLAogIF0sCiAgY29tcG9uZW50czoge0FsYXJtRGV0YWlsLCBBcHBsaWNhdGlvblNlbGVjdCwgUmVsZXZhbmN5QWxhcm1JbmZvLCBSZWxldmFuY3lHYXBJbmZvLCBEZXB0U2VsZWN0LHJlbGV2YW5jeVdlYnZ1bG5JbmZvLAogICAgbmV3QWRkbG9vcGhvbGUsbmV3QWRkd2VidnVsbixTZWxlY3RlZEV2ZW50TGlzdCxTZWxlY3RFdmVudCxyZXBvcnRUYXJnZXR9LAogIHByb3BzOiB7CiAgICB3b3JrVHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmU6IHRydWUKICAgIH0sCiAgICBtSWQ6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICByZXF1aXJlOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY29tcG9uZW50TmFtZTogJ2NyZWF0ZVdvcmsnLAogICAgICBzY3JvbGxDYWNoZTogbnVsbCwKICAgICAgb3BlbkV2ZW50U2VsZWN0RGlhbG9nOiBmYWxzZSwKICAgICAgYXNzZXREYXRhOiB7fSwKICAgICAgb3BlbkV2ZW50RGV0YWlsRGlhbG9nOiBmYWxzZSwKICAgICAgY3VycmVudEV2ZW50VHlwZTogMCwKICAgICAgcXVlcnlFdmVudFBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIGV2ZW50TGlzdExvYWRpbmc6IGZhbHNlLAogICAgICBldmVudExpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgcnVsZUZvcm06IHsKICAgICAgICB3b3JrTmFtZTogbnVsbCwKICAgICAgICBjb21wbGF0ZVRpbWU6IG51bGwsCiAgICAgICAgaGFuZGxlRGVwdDogbnVsbCwKICAgICAgICBoYW5kbGVVc2VyOiBudWxsLAogICAgICAgIHdvcmtUeXBlOiBudWxsLAogICAgICAgIGV2ZW50VHlwZTogbnVsbCwKICAgICAgICBhc3NvY2lhdGVkSXBzOiBbXSwKICAgICAgfSwKICAgICAgZGF0YUZvcm06IHsKICAgICAgfSwKICAgICAgZGF0YVJ1bGU6e30sCiAgICAgIHNlbGVjdGVkRXZlbnQ6IFtdLAogICAgICB0ZW1wU2VsZWN0ZWRFdmVudDogW10sCiAgICAgIHNldmVyaXR5T3B0aW9uczogewogICAgICAgICcxJzogWwogICAgICAgICAge3R5cGU6ICdpbmZvJywgbGFiZWw6ICfmnKrnn6UnLCB2YWx1ZTogMH0se3R5cGU6ICdzdWNjZXNzJywgbGFiZWw6ICfkvY7ljbEnLCB2YWx1ZTogMX0se3R5cGU6ICdwcmltYXJ5JywgbGFiZWw6ICfkuK3ljbEnLCB2YWx1ZTogMn0se3R5cGU6ICd3YXJuaW5nJywgbGFiZWw6ICfpq5jljbEnLCB2YWx1ZTogM30se3R5cGU6ICdkYW5nZXInLCBsYWJlbDogJ+S4pemHjScsIHZhbHVlOiA0fSwKICAgICAgICBdLAogICAgICAgICcyJzogWwogICAgICAgICAge3R5cGU6ICdpbmZvJywgbGFiZWw6ICfmnKrnn6UnLCB2YWx1ZTogMH0se3R5cGU6ICdzdWNjZXNzJywgbGFiZWw6ICfkvY7ljbEnLCB2YWx1ZTogMX0se3R5cGU6ICdwcmltYXJ5JywgbGFiZWw6ICfkuK3ljbEnLCB2YWx1ZTogMn0se3R5cGU6ICd3YXJuaW5nJywgbGFiZWw6ICfpq5jljbEnLCB2YWx1ZTogM30se3R5cGU6ICdkYW5nZXInLCBsYWJlbDogJ+S4pemHjScsIHZhbHVlOiA0fSwKICAgICAgICBdLAogICAgICAgICczJzogWwogICAgICAgICAge3R5cGU6ICdpbmZvJywgbGFiZWw6ICfmnKrnn6UnLCB2YWx1ZTogMH0se3R5cGU6ICdzdWNjZXNzJywgbGFiZWw6ICfml6DlqIHog4EnLCB2YWx1ZTogMX0se3R5cGU6ICdwcmltYXJ5JywgbGFiZWw6ICfkvY7ljbEnLCB2YWx1ZTogMn0se3R5cGU6ICd3YXJuaW5nJywgbGFiZWw6ICfkuK3ljbEnLCB2YWx1ZTogM30se3R5cGU6ICd3YXJuaW5nJywgbGFiZWw6ICfpq5jljbEnLCB2YWx1ZTogNH0se3R5cGU6ICdkYW5nZXInLCBsYWJlbDogJ+S4pemHjScsIHZhbHVlOiA1fSwKICAgICAgICBdLAogICAgICB9LAogICAgICB0aHJlYXRlbkRpY3Q6IFtdLAogICAgICBydWxlczogewogICAgICAgIHdvcmtOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6YCa5oql5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IG1pbjogMywgbWF4OiA4MCwgbWVzc2FnZTogJ+mVv+W6puWcqCAzIOWIsCA4MCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgYXBwbGljYXRpb25JZDogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fpgInmi6nkuJrliqHns7vnu58nLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBldmVudENyZWF0ZVRpbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nkuovku7blj5HnlJ/ml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBleHBlY3RDb21wbGV0ZVRpbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforqHliJLlrozmiJDml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBoYW5kbGVEZXB0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5aSE572u5Y2V5L2NJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgaGFuZGxlVXNlcjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWkhOe9ruS6uicsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIGhhbmRsZVVzZXJQaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXlpITnva7kurrnlLXor50nLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgcmVtYXJrNjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemAmuaKpeexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICB9LAogICAgICBmbG93U3RhdGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXlrqHmoLgnLAogICAgICAgICAgdmFsdWU6IDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5b6F5aSE572uJywKICAgICAgICAgIHZhbHVlOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+W+heWuoeaguCcsCiAgICAgICAgICB2YWx1ZTogMgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXpqozor4EnLAogICAgICAgICAgdmFsdWU6IDMKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5bey5a6M5oiQJywKICAgICAgICAgIHZhbHVlOiA0CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+acquWIhumFjScsCiAgICAgICAgICB2YWx1ZTogOTkKICAgICAgICB9CiAgICAgIF0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIGFsZXJ0VGFibGVEYXRhOiBbXSwKICAgICAgaGFuZGxlT3B0aW9uOiBbXSwKICAgICAgbWFuYWdlT3B0aW9uOiBbXSwKICAgICAgaGFuZGxlT3B0aW9uQ29weTogW10sCiAgICAgIG1hbmFnZU9wdGlvbkNvcHk6IFtdLAogICAgICBpbmZvcm1Gb3JtOiB7fSwKICAgICAgaW5mb3JtUnVsZXM6IHsKICAgICAgICBldmVudFNvdXJjZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5LqL5Lu25p2l5rqQJywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0sCiAgICAgICAgaGFuZGxlT3BpbmlvbjogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWkhOeQhuW7uuiuricsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICBdLAogICAgICAgIGV2ZW50Tm90aWZpY2F0aW9uOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5LqL5Lu26YCa5oql5YaF5a65JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0sCiAgICAgICAgZGVzY3JpYmVGaWxlVXJsOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5o+P6L+w6ZmE5Lu2JywgdHJpZ2dlcjogJ2NoYW5nZSd9CiAgICAgICAgXSwKICAgICAgICAvKnJlbWFyazg6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpScsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICBdLCovCiAgICAgIH0sCiAgICAgIGZlZWRiYWNrRm9ybToge30sCiAgICAgIGZlZWRiYWNrUnVsZXM6IHsKICAgICAgICBldmVudERlc2NyaXB0aW9uOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5aSE55CG57uT5p6cJywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0sCiAgICAgICAgaGFuZGxlU2l0dWF0aW9uOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5aSE55CG5oOF5Ya1JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0KICAgICAgfSwKICAgICAgYXBwbGljYXRpb25MaXN0OiBbXSwKICAgICAgYXBwbGljYXRpb25EaWFsb2c6IGZhbHNlLAogICAgICBtYWNBaXBMaXN0OiBbXSwKICAgICAgYXNzZXRJZHM6IFtdLAogICAgICBjdXJyZW50QXBwbGljYXRpb25TZWxlY3Q6IG51bGwsCiAgICAgIGV2ZW50VHlwZU9wdGlvbjogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5ryP5rSeJywKICAgICAgICAgIHZhbHVlOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+WQjumXqCcsCiAgICAgICAgICB2YWx1ZTogMgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflpJbpk74nLAogICAgICAgICAgdmFsdWU6IDMKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGV2ZW50TGV2ZWxPcHRpb246IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+KFoOe6pycsCiAgICAgICAgICB2YWx1ZTogJzEnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+KFoee6pycsCiAgICAgICAgICB2YWx1ZTogJzInCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+KFoue6pycsCiAgICAgICAgICB2YWx1ZTogJzMnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+KFo+e6pycsCiAgICAgICAgICB2YWx1ZTogJzQnCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgYXBwbGljYXRpb25JbmZvOiB7fSwKICAgICAgaXNDaGFuZ2VFdmVudFR5cGU6IGZhbHNlLAogICAgICBldmVudFR5cGVCdG5LZXk6IDAsCiAgICAgIGlzRnJvbUV2ZW50OiBmYWxzZSwKICAgICAgaXNDaGFuZ2VGb3JtOiBmYWxzZSwKICAgICAgZm9ybU9wZXJhdGVzUmVjb3JkOiBbXSwKICAgICAgcmVwb3J0VGFyZ2V0Rm9ybTogW10sCiAgICAgIHJlcG9ydFRhcmdldEFjdGl2ZTogJzAnLAogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGluZm9ybUZvcm06IHsKICAgICAgaGFuZGxlcihuZXdWYWwsb2xkVmFsKXsKICAgICAgICB0aGlzLmlzQ2hhbmdlRm9ybSA9IHRydWU7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0sCiAgICBydWxlRm9ybTogewogICAgICBoYW5kbGVyKG5ld1ZhbCxvbGRWYWwpewogICAgICAgIHRoaXMuaXNDaGFuZ2VGb3JtID0gdHJ1ZTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgIGZlZWRiYWNrRm9ybTogewogICAgICBoYW5kbGVyKG5ld1ZhbCxvbGRWYWwpewogICAgICAgIHRoaXMuaXNDaGFuZ2VGb3JtID0gdHJ1ZTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgICdydWxlRm9ybS5oYW5kbGVEZXB0JzogewogICAgICBkZWVwOiBmYWxzZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIC8qaWYoIXRoaXMuc2V0dGluZy5mb3JtRGF0YSB8fCBPYmplY3Qua2V5cyh0aGlzLnNldHRpbmcuZm9ybURhdGEpLmxlbmd0aCA9PT0gMCl7CiAgICAgICAgICB0aGlzLmdldFVzZXJMaXN0KCkKICAgICAgICB9Ki8KICAgICAgICBpZih2YWwpewogICAgICAgICAgdGhpcy5nZXRVc2VyTGlzdCgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgICdydWxlRm9ybS5tYW5hZ2VEZXB0JzogewogICAgICBkZWVwOiBmYWxzZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIC8qaWYoIXRoaXMuc2V0dGluZy5mb3JtRGF0YSB8fCBPYmplY3Qua2V5cyh0aGlzLnNldHRpbmcuZm9ybURhdGEpLmxlbmd0aCA9PT0gMCl7CiAgICAgICAgICB0aGlzLmdldFVzZXJMaXN0KCkKICAgICAgICB9Ki8KICAgICAgICBpZih2YWwpewogICAgICAgICAgdGhpcy5nZXRNYW5hZ2VVc2VyTGlzdCgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgICdydWxlRm9ybS5hcHBsaWNhdGlvbklkJzogewogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXIobmV3VmFsLG9sZFZhbCl7CiAgICAgICAgaWYobmV3VmFsICYmIG5ld1ZhbCAhPSBvbGRWYWwpewogICAgICAgICAgaWYoIXRoaXMuc2V0dGluZy5mb3JtRGF0YSB8fCBPYmplY3Qua2V5cyh0aGlzLnNldHRpbmcuZm9ybURhdGEpLmxlbmd0aCA9PT0gMCl7CiAgICAgICAgICAgIHRoaXMuZ2V0QXBwbGljYXRpb25EZXRhaWxzKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgJ3NldHRpbmcucm93JzogewogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXIodmFsKXsKICAgICAgICBpZih2YWwgJiYgT2JqZWN0LmtleXModmFsKS5sZW5ndGg+MCl7CiAgICAgICAgICBpZih2YWwuZXZlbnRUeXBlICYmIHZhbC5ldmVudFR5cGUgIT0gMCl7CiAgICAgICAgICAgIHRoaXMuY3VycmVudEV2ZW50VHlwZSA9IHZhbC5ldmVudFR5cGU7CiAgICAgICAgICB9CiAgICAgICAgICBpZih2YWwuY3JlYXRlVGltZSl7CiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCAnZXZlbnRDcmVhdGVUaW1lJywgdmFsLmNyZWF0ZVRpbWUpOwogICAgICAgICAgfQogICAgICAgICAgLyppZih2YWwuaWQpewogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwgJ2V2ZW50SWRzJywgW3ZhbC5pZF0pOwogICAgICAgICAgfSovCiAgICAgICAgICBsZXQgZGVwdElkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnZGVwdElkJyk7CiAgICAgICAgICBsZXQgZGVwdE5hbWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdkZXB0TmFtZScpOwogICAgICAgICAgaWYodmFsLmRlcHRJZCB8fCAodmFsLmV2ZW50VHlwZSA9PT0gMyAmJiB2YWwuZGVwdElkU3RyKSl7CiAgICAgICAgICAgIGRlcHRJZCA9IHZhbC5kZXB0SWQ/dmFsLmRlcHRJZDp2YWwuZGVwdElkU3RyLnNwbGl0KCcsJylbMF07CiAgICAgICAgICAgIGRlcHROYW1lID0gdmFsLmRlcHROYW1lLnNwbGl0KCcsJylbMF07CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnJlcG9ydFRhcmdldEZvcm0gPSBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBkZXB0TmFtZTogZGVwdE5hbWUsCiAgICAgICAgICAgICAgZGVwdElkOiBkZXB0SWQsCiAgICAgICAgICAgICAgaGFuZGxlVGl0bGU6IG51bGwsCiAgICAgICAgICAgICAgZXZlbnREZXNjcmlwdGlvbjogbnVsbCwKICAgICAgICAgICAgICBoYW5kbGVTaXR1YXRpb246IG51bGwsCiAgICAgICAgICAgICAgb3RoZXJTaXR1YXRpb246IG51bGwsCiAgICAgICAgICAgICAgZmVlZGJhY2tGaWxlVXJsOiBudWxsLAogICAgICAgICAgICAgIGZvcm1EYXRhOiBbewogICAgICAgICAgICAgICAgaGFuZGxlRGVwdDogZGVwdElkLAogICAgICAgICAgICAgICAgaGFuZGxlVXNlcjogJycsCiAgICAgICAgICAgICAgICBhcHBsaWNhdGlvbklkOiB2YWwuYnVzaW5lc3NBcHBsaWNhdGlvbnM/dmFsLmJ1c2luZXNzQXBwbGljYXRpb25zWzBdLmFzc2V0SWQ6bnVsbCwKICAgICAgICAgICAgICAgIGFzc2V0TmFtZTogdmFsLmJ1c2luZXNzQXBwbGljYXRpb25zP3ZhbC5idXNpbmVzc0FwcGxpY2F0aW9uc1swXS5hc3NldE5hbWU6bnVsbCwKICAgICAgICAgICAgICAgIGxvZ2luVXJsOiB2YWwuYnVzaW5lc3NBcHBsaWNhdGlvbnM/dmFsLmJ1c2luZXNzQXBwbGljYXRpb25zWzBdLnVybDpudWxsLAogICAgICAgICAgICAgICAgbWFuYWdlcjogdmFsLmJ1c2luZXNzQXBwbGljYXRpb25zP3BhcnNlSW50KHZhbC5idXNpbmVzc0FwcGxpY2F0aW9uc1swXS5tYW5hZ2VyKTpudWxsLAogICAgICAgICAgICAgICAgcGhvbmU6IHZhbC5idXNpbmVzc0FwcGxpY2F0aW9ucz92YWwuYnVzaW5lc3NBcHBsaWNhdGlvbnNbMF0ubWFuYWdlclBob25lOm51bGwsCiAgICAgICAgICAgICAgICBldmVudERhdGE6IHsKICAgICAgICAgICAgICAgICAgJ3R5cGUxJzogdmFsLmV2ZW50VHlwZSA9PT0gMT9bCiAgICAgICAgICAgICAgICAgICAge3R5cGU6IHZhbC5ldmVudFR5cGUsIGV2ZW50SWQ6IHZhbC5pZH0KICAgICAgICAgICAgICAgICAgXTpbXSwKICAgICAgICAgICAgICAgICAgJ3R5cGUyJzogdmFsLmV2ZW50VHlwZSA9PT0gMj9bCiAgICAgICAgICAgICAgICAgICAge3R5cGU6IHZhbC5ldmVudFR5cGUsIGV2ZW50SWQ6IHZhbC5pZH0KICAgICAgICAgICAgICAgICAgXTpbXSwKICAgICAgICAgICAgICAgICAgJ3R5cGUzJzogdmFsLmV2ZW50VHlwZSA9PT0gMz9bCiAgICAgICAgICAgICAgICAgICAge3R5cGU6IHZhbC5ldmVudFR5cGUsIGV2ZW50SWQ6IHZhbC5pZH0KICAgICAgICAgICAgICAgICAgXTpbXSwKICAgICAgICAgICAgICAgICAgJ3R5cGU0JzogdmFsLmV2ZW50VHlwZSA9PT0gND9bCiAgICAgICAgICAgICAgICAgICAge3R5cGU6IHZhbC5ldmVudFR5cGUsIGV2ZW50SWQ6IHZhbC5pZH0KICAgICAgICAgICAgICAgICAgXTpbXQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH1dCiAgICAgICAgICAgIH0KICAgICAgICAgIF07CgogICAgICAgICAgLy/pu5jorqTmn6XkuJrliqHns7vnu58KICAgICAgICAgIC8qdGhpcy4kbmV4dFRpY2soKCk9PnsKICAgICAgICAgICAgbGV0IGlwdjQgPSB0aGlzLmdldElwdjQoKTsKICAgICAgICAgICAgaWYoaXB2NCl7CiAgICAgICAgICAgICAgdGhpcy5pc0Zyb21FdmVudCA9IHRydWU7CiAgICAgICAgICAgICAgZ2V0QXBwbGljYXRpb25MaXN0QnlDb25kaXRpb24oewogICAgICAgICAgICAgICAgaXB2NDogaXB2NCwKICAgICAgICAgICAgICAgIGV2ZW50VHlwZTogdGhpcy5jdXJyZW50RXZlbnRUeXBlLAogICAgICAgICAgICAgICAgYXBwbGljYXRpb25JZDogdGhpcy5zZXR0aW5nICYmIHRoaXMuc2V0dGluZy5yb3cgJiYgdGhpcy5zZXR0aW5nLnJvdy5idXNpbmVzc0FwcGxpY2F0aW9ucyAmJiB0aGlzLnNldHRpbmcucm93LmJ1c2luZXNzQXBwbGljYXRpb25zLmxlbmd0aD4wP3RoaXMuc2V0dGluZy5yb3cuYnVzaW5lc3NBcHBsaWNhdGlvbnNbMF0uYXNzZXRJZDpudWxsCiAgICAgICAgICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYocmVzLmRhdGEgJiYgcmVzLmRhdGEubGVuZ3RoPjApewogICAgICAgICAgICAgICAgICBsZXQgZmlyc3REYXRhID0gcmVzLmRhdGFbMF07CiAgICAgICAgICAgICAgICAgIGxpc3RCeUFwcGxpY2F0aW9uSWQoewogICAgICAgICAgICAgICAgICAgIGFwcGxpY2F0aW9uSWQ6IGZpcnN0RGF0YS5hc3NldElkLAogICAgICAgICAgICAgICAgICAgIGlwOiBpcHY0LAogICAgICAgICAgICAgICAgICAgIGV2ZW50VHlwZTogdGhpcy5jdXJyZW50RXZlbnRUeXBlLAogICAgICAgICAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU6IDEKICAgICAgICAgICAgICAgICAgfSkudGhlbihhc3NldFJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgbGV0IGFzc2V0RGF0YSA9IFtdOwogICAgICAgICAgICAgICAgICAgIGlmKGFzc2V0UmVzLnJvd3MgJiYgYXNzZXRSZXMucm93cy5sZW5ndGg+MCl7CiAgICAgICAgICAgICAgICAgICAgICBhc3NldERhdGEucHVzaChhc3NldFJlcy5yb3dzWzBdKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB7CiAgICAgICAgICAgICAgICAgICAgICBhcHBsaWNhdGlvbjogZmlyc3REYXRhLAogICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWQ6IGFzc2V0RGF0YQogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5hc3NvY2lhdGVkSXBzID0gZGF0YS5zZWxlY3RlZC5tYXAoaXRlbSA9PiBpdGVtLmlwKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmFzc29jaWF0ZWRJcHMgPSB0aGlzLnJ1bGVGb3JtLmFzc29jaWF0ZWRJcHMuZmlsdGVyKChpdGVtLCBpbmRleCwgc2VsZikgPT4gc2VsZi5pbmRleE9mKGl0ZW0pID09PSBpbmRleCk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5yZW1hcmsxID0gSlNPTi5zdHJpbmdpZnkoZGF0YS5zZWxlY3RlZC5tYXAoaXRlbSA9PiBpdGVtLmFzc2V0SWQpKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwnYXBwbGljYXRpb25JZCcsZGF0YS5hcHBsaWNhdGlvbi5hc3NldElkKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmFwcGxpY2F0aW9uTmFtZSA9IGRhdGEuYXBwbGljYXRpb24uYXNzZXROYW1lOwogICAgICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudEFwcGxpY2F0aW9uU2VsZWN0ID0gZGF0YTsKICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgICB9KSovCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgJ3NldHRpbmcucm93cyc6IHsKICAgICAgZGVlcDogdHJ1ZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKHZhbCl7CiAgICAgICAgaWYodmFsICYmIHZhbC5sZW5ndGg+MCl7CiAgICAgICAgICBpZih2YWxbMF0uZXZlbnRUeXBlICYmIHZhbFswXS5ldmVudFR5cGUgIT0gMCl7CiAgICAgICAgICAgIHRoaXMuY3VycmVudEV2ZW50VHlwZSA9IHZhbFswXS5ldmVudFR5cGU7CiAgICAgICAgICB9CiAgICAgICAgICBpZih2YWxbMF0uY3JlYXRlVGltZSl7CiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCAnZXZlbnRDcmVhdGVUaW1lJywgdmFsWzBdLmNyZWF0ZVRpbWUpOwogICAgICAgICAgfQogICAgICAgICAgbGV0IGV2ZW50SWRzID0gdmFsLm1hcChpdGVtID0+IGl0ZW0uaWQpOwogICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sICdldmVudElkcycsIGV2ZW50SWRzKTsKICAgICAgICAgIGxldCByb3cgPSB2YWxbMF07CiAgICAgICAgICB0aGlzLnJlcG9ydFRhcmdldEZvcm0gPSBbXTsKICAgICAgICAgIGxldCBkZXB0U2V0ID0gbmV3IFNldCgpOwogICAgICAgICAgbGV0IGRlcHRJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ2RlcHRJZCcpOwogICAgICAgICAgbGV0IGRlcHROYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnZGVwdE5hbWUnKTsKICAgICAgICAgIHZhbC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZighaXRlbS5kZXB0SWQpewogICAgICAgICAgICAgIGl0ZW0uZGVwdElkID0gZGVwdElkOwogICAgICAgICAgICAgIGl0ZW0uZGVwdE5hbWUgPSBkZXB0TmFtZTsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZihpdGVtLmRlcHRJZFN0cil7CiAgICAgICAgICAgICAgaXRlbS5kZXB0SWQgPSBpdGVtLmRlcHRJZFN0ci5zcGxpdCgnLCcpWzBdOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGRlcHRTZXQuYWRkKHsKICAgICAgICAgICAgICBkZXB0TmFtZTogaXRlbS5kZXB0TmFtZS5zcGxpdCgnLCcpWzBdLAogICAgICAgICAgICAgIGRlcHRJZDogaXRlbS5kZXB0SWQsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSkKICAgICAgICAgIGRlcHRTZXQuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgbGV0IG1hdGNoQXJyID0gdmFsLmZpbHRlcih2YWxJdGVtID0+IHZhbEl0ZW0uZGVwdElkID09PSBpdGVtLmRlcHRJZCk7CiAgICAgICAgICAgIGxldCBmaXJzdCA9IG1hdGNoQXJyWzBdOwogICAgICAgICAgICB0aGlzLnJlcG9ydFRhcmdldEZvcm0ucHVzaCgKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBkZXB0TmFtZTogaXRlbS5kZXB0TmFtZSwKICAgICAgICAgICAgICAgIGRlcHRJZDogaXRlbS5kZXB0SWQsCiAgICAgICAgICAgICAgICBoYW5kbGVUaXRsZTogbnVsbCwKICAgICAgICAgICAgICAgIGV2ZW50RGVzY3JpcHRpb246IG51bGwsCiAgICAgICAgICAgICAgICBoYW5kbGVTaXR1YXRpb246IG51bGwsCiAgICAgICAgICAgICAgICBvdGhlclNpdHVhdGlvbjogbnVsbCwKICAgICAgICAgICAgICAgIGZlZWRiYWNrRmlsZVVybDogbnVsbCwKICAgICAgICAgICAgICAgIGZvcm1EYXRhOiBbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBoYW5kbGVEZXB0OiBpdGVtLmRlcHRJZCwKICAgICAgICAgICAgICAgICAgICBhcHBsaWNhdGlvbklkOiBmaXJzdC5idXNpbmVzc0FwcGxpY2F0aW9ucz9maXJzdC5idXNpbmVzc0FwcGxpY2F0aW9uc1swXS5hc3NldElkOm51bGwsCiAgICAgICAgICAgICAgICAgICAgYXNzZXROYW1lOiBmaXJzdC5idXNpbmVzc0FwcGxpY2F0aW9ucz9maXJzdC5idXNpbmVzc0FwcGxpY2F0aW9uc1swXS5hc3NldE5hbWU6bnVsbCwKICAgICAgICAgICAgICAgICAgICBsb2dpblVybDogZmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnM/Zmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnNbMF0udXJsOm51bGwsCiAgICAgICAgICAgICAgICAgICAgbWFuYWdlcjogZmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnM/cGFyc2VJbnQoZmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnNbMF0ubWFuYWdlcik6bnVsbCwKICAgICAgICAgICAgICAgICAgICBwaG9uZTogZmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnM/Zmlyc3QuYnVzaW5lc3NBcHBsaWNhdGlvbnNbMF0ubWFuYWdlclBob25lOm51bGwsCiAgICAgICAgICAgICAgICAgICAgZXZlbnREYXRhOiB7CiAgICAgICAgICAgICAgICAgICAgICAndHlwZTEnOiB0aGlzLmN1cnJlbnRFdmVudFR5cGU9PT0xP21hdGNoQXJyLm1hcChpdGVtID0+ICh7dHlwZTogMSwgZXZlbnRJZDogaXRlbS5pZH0pKTpbXSwKICAgICAgICAgICAgICAgICAgICAgICd0eXBlMic6IHRoaXMuY3VycmVudEV2ZW50VHlwZT09PTI/bWF0Y2hBcnIubWFwKGl0ZW0gPT4gKHt0eXBlOiAyLCBldmVudElkOiBpdGVtLmlkfSkpOltdLAogICAgICAgICAgICAgICAgICAgICAgJ3R5cGUzJzogdGhpcy5jdXJyZW50RXZlbnRUeXBlPT09Mz9tYXRjaEFyci5tYXAoaXRlbSA9PiAoe3R5cGU6IDMsIGV2ZW50SWQ6IGl0ZW0uaWR9KSk6W10sCiAgICAgICAgICAgICAgICAgICAgICAndHlwZTQnOiB0aGlzLmN1cnJlbnRFdmVudFR5cGU9PT00P21hdGNoQXJyLm1hcChpdGVtID0+ICh7dHlwZTogNCwgZXZlbnRJZDogaXRlbS5pZH0pKTpbXQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgKQogICAgICAgICAgfSkKICAgICAgICAgIC8qLy/pu5jorqTmn6XkuJrliqHns7vnu58KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpPT57CiAgICAgICAgICAgIGxldCBpcHY0ID0gdGhpcy5nZXRJcHY0KCk7CiAgICAgICAgICAgIGlmKGlwdjQpewogICAgICAgICAgICAgIHRoaXMuaXNGcm9tRXZlbnQgPSB0cnVlOwogICAgICAgICAgICAgIGdldEFwcGxpY2F0aW9uTGlzdEJ5Q29uZGl0aW9uKHsKICAgICAgICAgICAgICAgIGlwdjQ6IGlwdjQsCiAgICAgICAgICAgICAgICBldmVudFR5cGU6IHRoaXMuY3VycmVudEV2ZW50VHlwZSwKICAgICAgICAgICAgICAgIGFwcGxpY2F0aW9uSWQ6IHJvdy5idXNpbmVzc0FwcGxpY2F0aW9ucyAmJiByb3cuYnVzaW5lc3NBcHBsaWNhdGlvbnMubGVuZ3RoPjA/cm93LmJ1c2luZXNzQXBwbGljYXRpb25zWzBdLmFzc2V0SWQ6bnVsbAogICAgICAgICAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhICYmIHJlcy5kYXRhLmxlbmd0aD4wKXsKICAgICAgICAgICAgICAgICAgbGV0IGZpcnN0RGF0YSA9IHJlcy5kYXRhWzBdOwogICAgICAgICAgICAgICAgICBsaXN0QnlBcHBsaWNhdGlvbklkKHsKICAgICAgICAgICAgICAgICAgICBhcHBsaWNhdGlvbklkOiBmaXJzdERhdGEuYXNzZXRJZCwKICAgICAgICAgICAgICAgICAgICBpcDogaXB2NCwKICAgICAgICAgICAgICAgICAgICBldmVudFR5cGU6IHRoaXMuY3VycmVudEV2ZW50VHlwZSwKICAgICAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxCiAgICAgICAgICAgICAgICAgIH0pLnRoZW4oYXNzZXRSZXMgPT4gewogICAgICAgICAgICAgICAgICAgIGxldCBhc3NldERhdGEgPSBbXTsKICAgICAgICAgICAgICAgICAgICBpZihhc3NldFJlcy5yb3dzICYmIGFzc2V0UmVzLnJvd3MubGVuZ3RoPjApewogICAgICAgICAgICAgICAgICAgICAgYXNzZXREYXRhLnB1c2goYXNzZXRSZXMucm93c1swXSk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGxldCBkYXRhID0gewogICAgICAgICAgICAgICAgICAgICAgYXBwbGljYXRpb246IGZpcnN0RGF0YSwKICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkOiBhc3NldERhdGEKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uYXNzb2NpYXRlZElwcyA9IGRhdGEuc2VsZWN0ZWQubWFwKGl0ZW0gPT4gaXRlbS5pcCk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5hc3NvY2lhdGVkSXBzID0gdGhpcy5ydWxlRm9ybS5hc3NvY2lhdGVkSXBzLmZpbHRlcigoaXRlbSwgaW5kZXgsIHNlbGYpID0+IHNlbGYuaW5kZXhPZihpdGVtKSA9PT0gaW5kZXgpOwogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0ucmVtYXJrMSA9IEpTT04uc3RyaW5naWZ5KGRhdGEuc2VsZWN0ZWQubWFwKGl0ZW0gPT4gaXRlbS5hc3NldElkKSk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sJ2FwcGxpY2F0aW9uSWQnLGRhdGEuYXBwbGljYXRpb24uYXNzZXRJZCk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5hcHBsaWNhdGlvbk5hbWUgPSBkYXRhLmFwcGxpY2F0aW9uLmFzc2V0TmFtZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRBcHBsaWNhdGlvblNlbGVjdCA9IGRhdGE7CiAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfSkqLwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgICdzZXR0aW5nLmZvcm1EYXRhJzogewogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXIodmFsKXsKICAgICAgICBpZih2YWwgJiYgT2JqZWN0LmtleXModmFsKS5sZW5ndGggIT09IDApewogICAgICAgICAgdGhpcy5pbml0Rm9ybSgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgICdzZXR0aW5nLmZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0JzogewogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXIodmFsKXsKICAgICAgICBpZighdGhpcy5zZXR0aW5nLmZsb3dUZW1wbGF0ZUpzb24pewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBsZXQgYWxsTWF0Y2hOb2RlcyA9IFtdOwogICAgICAgIHRoaXMubG9vcEdldEZsb3dOb2RlKHRoaXMuc2V0dGluZy5mbG93VGVtcGxhdGVKc29uLCdlbmQnLGFsbE1hdGNoTm9kZXMpOwogICAgICAgIGxldCBhbGxGb3JtT3BlcmF0ZXMgPSB7fTsKICAgICAgICBhbGxNYXRjaE5vZGVzLm1hcChpdGVtID0+IGl0ZW0ucHJvcGVydGllcy5mb3JtT3BlcmF0ZXMpLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpdGVtLmZvckVhY2goYXJySXRlbSA9PiB7CiAgICAgICAgICAgIGlmKCFhbGxGb3JtT3BlcmF0ZXNbYXJySXRlbS5pZF0pewogICAgICAgICAgICAgIGFsbEZvcm1PcGVyYXRlc1thcnJJdGVtLmlkXSA9IHsKICAgICAgICAgICAgICAgIHJlYWQ6IGFyckl0ZW0ucmVhZCwKICAgICAgICAgICAgICAgIHdyaXRlOiBhcnJJdGVtLndyaXRlCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgICAgaWYoIWFsbEZvcm1PcGVyYXRlc1thcnJJdGVtLmlkXS5yZWFkKXsKICAgICAgICAgICAgICAgIGFsbEZvcm1PcGVyYXRlc1thcnJJdGVtLmlkXS5yZWFkID0gYXJySXRlbS5yZWFkOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZighYWxsRm9ybU9wZXJhdGVzW2Fyckl0ZW0uaWRdLndyaXRlKXsKICAgICAgICAgICAgICAgIGFsbEZvcm1PcGVyYXRlc1thcnJJdGVtLmlkXS53cml0ZSA9IGFyckl0ZW0ud3JpdGU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0pOwogICAgICAgIHRoaXMuZm9ybU9wZXJhdGVzUmVjb3JkID0gT2JqZWN0LmVudHJpZXMoYWxsRm9ybU9wZXJhdGVzKS5tYXAoKFtpZCwgdmFsdWVdKSA9PiAoewogICAgICAgICAgaWQ6IGlkLAogICAgICAgICAgcmVhZDogdmFsdWUucmVhZCwKICAgICAgICAgIHdyaXRlOiB2YWx1ZS53cml0ZQogICAgICAgIH0pKTsKICAgICAgfQogICAgfSwKICAgIGN1cnJlbnRFdmVudFR5cGU6IHsKICAgICAgaGFuZGxlcihuZXdWYWwsb2xkVmFsKXsKICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwnd29ya1R5cGUnLG5ld1ZhbCk7CiAgICAgICAgLyogaWYobmV3VmFsKXsKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5yZXNldFF1ZXJ5KCk7CiAgICAgICAgICB9KQogICAgICAgIH0gKi8KICAgICAgfQogICAgfSwKICAgICdydWxlRm9ybS5oYW5kbGVVc2VyJzogewogICAgICBoYW5kbGVyKG5ld1ZhbCxvbGRWYWwpewoKICAgICAgfQogICAgfSwKICAgIHJlcG9ydFRhcmdldEZvcm06IHsKICAgICAgZGVlcDogdHJ1ZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKG5ld1ZhbCxvbGRWYWwpewogICAgICAgIHRoaXMuJGVtaXQoJ3JlcG9ydERhdGFDaGFuZ2UnLG5ld1ZhbCk7CiAgICAgIH0KICAgIH0sCiAgfSwKICBjcmVhdGVkKCkgewogICAgZ2V0TXVsVHlwZURpY3QoewogICAgICBkaWN0VHlwZTondGhyZWF0ZW5fYWxhcm1fdHlwZScKICAgIH0pLnRoZW4ocmVzPT57CiAgICAgIHRoaXMudGhyZWF0ZW5EaWN0PXJlcy5kYXRhOwogICAgICAvLyBUT0RPICDmmoLlrprkuLrojrflj5bkuKTlsYIgIOW9k+WJjeaWueazleaciemXrumimCDlhbblroPniYjmnKzkuZ/mnInkv67mlLnpnIDopoHnrYnlvoXlkI7nu63kv67mlLkKICAgICAgLyogaWYgKHRoaXMudGhyZWF0ZW5EaWN0Lmxlbmd0aCA+IDAgJiYgdGhpcy50aHJlYXRlbkRpY3RbMF0uY2hpbGRyZW4ubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMucnVsZUZvcm0uZXZlbnRUeXBlID0gdGhpcy50aHJlYXRlbkRpY3RbMF0uZGljdFZhbHVlICsgJy8nICsgdGhpcy50aHJlYXRlbkRpY3RbMF0uY2hpbGRyZW5bMF0uZGljdFZhbHVlCiAgICAgIH0gKi8KICAgICAgdGhpcy50aHJlYXRlbkRpY3QuZm9yRWFjaChpdGVtPT57CiAgICAgICAgaXRlbS52YWx1ZT1pdGVtLmRpY3RWYWx1ZTsKICAgICAgICBpdGVtLmxhYmVsPWl0ZW0uZGljdExhYmVsOwogICAgICAgIGl0ZW0uY2hpbGRyZW4uZm9yRWFjaChjSXRlbT0+ewogICAgICAgICAgY0l0ZW0udmFsdWU9Y0l0ZW0uZGljdFZhbHVlOwogICAgICAgICAgY0l0ZW0ubGFiZWw9Y0l0ZW0uZGljdExhYmVsOwogICAgICAgIH0pCiAgICAgIH0pCiAgICB9KTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIC8v6buY6K6k5YC8CiAgICAgIHRoaXMuc2V0Rm9ybURlZmF1bHQoKTsKICAgIH0pCiAgfSwKICBjb21wdXRlZDogewogICAgY2F0ZWdvcnlEaWN0KCl7CiAgICAgIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAxIHx8IHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAyKXsKICAgICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUubG9vcGhvbGVfY2F0ZWdvcnk7CiAgICAgIH1lbHNlIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAzKXsKICAgICAgICByZXR1cm4gdGhpcy50aHJlYXRlbkRpY3Q7CiAgICAgIH0KICAgIH0sCiAgICByZXBvcnRPcHRpb25zKCl7CiAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS53b3JrX29yZGVyX3JlcG9ydF90eXBlOwogICAgfSwKICAgIHVyZ2VuY3lPcHRpb25zKCl7CiAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS53b3JrX29yZGVyX3VyZ2VuY3k7CiAgICB9LAogICAgaXNQdWJsaWNPcHRpb25zKCl7CiAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS53b3JrX29yZGVyX3B1YmxpYzsKICAgIH0sCiAgICBzZXZlcml0eUxldmVsT3B0aW9ucygpewogICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUud29ya19vcmRlcl9zZXZlcml0eV9sZXZlbDsKICAgIH0sCiAgICAvLyDojrflj5blvZPliY3pg6jpl6jmlbDmja7vvIjlronlhajorr/pl67vvIkKICAgIGN1cnJlbnREZXB0RGF0YSgpIHsKICAgICAgcmV0dXJuIHRoaXMucmVwb3J0VGFyZ2V0Rm9ybSAmJiB0aGlzLnJlcG9ydFRhcmdldEZvcm0ubGVuZ3RoPjAgPyB0aGlzLnJlcG9ydFRhcmdldEZvcm1bcGFyc2VJbnQodGhpcy5yZXBvcnRUYXJnZXRBY3RpdmUpXSA6IG51bGw7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Rm9ybSgpewogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdhcHBsaWNhdGlvbklkJyk7CiAgICAgIHRoaXMuY3VycmVudEFwcGxpY2F0aW9uU2VsZWN0ID0gewogICAgICAgIGFwcGxpY2F0aW9uOiB7CiAgICAgICAgICBhc3NldElkOiB0aGlzLnNldHRpbmcuZm9ybURhdGEuYXBwbGljYXRpb25JZCwKICAgICAgICB9LAogICAgICAgIHNlbGVjdGVkOiB0aGlzLnNldHRpbmcuZm9ybURhdGEucmVtYXJrMT9KU09OLnBhcnNlKHRoaXMuc2V0dGluZy5mb3JtRGF0YS5yZW1hcmsxKS5tYXAoc2UgPT4ge3JldHVybiB7YXNzZXRJZDogc2V9fSk6W10sCiAgICAgIH0KICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnYXNzb2NpYXRlZElwcycpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdldmVudElkcycpOwogICAgICB0aGlzLiRzZXQodGhpcy5xdWVyeUV2ZW50UGFyYW1zLCJpZHMiLHRoaXMuc2V0dGluZy5mb3JtRGF0YS5ldmVudElkcyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ3dvcmtUeXBlJyk7CiAgICAgIHRoaXMuY3VycmVudEV2ZW50VHlwZSA9IHRoaXMuc2V0dGluZy5mb3JtRGF0YS53b3JrVHlwZTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnd29ya05hbWUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnbG9naW5VcmwnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnaGFuZGxlRGVwdCcpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdtYW5hZ2VEZXB0Jyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ2hhbmRsZURlcHROYW1lJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ21hbmFnZURlcHROYW1lJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ2hhbmRsZVVzZXInKTsKICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sIm1hbmFnZVVzZXIiLHRoaXMuc2V0dGluZy5mb3JtRGF0YS5tYW5hZ2VVc2VyP3BhcnNlSW50KHRoaXMuc2V0dGluZy5mb3JtRGF0YS5tYW5hZ2VVc2VyKSA6IG51bGwpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdoYW5kbGVVc2VyUGhvbmUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnaGFuZGxlVXNlck5hbWUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnbWFuYWdlVXNlck5hbWUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAncmVtYXJrNicpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdpc3N1ZScpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdleHBlY3RDb21wbGV0ZVRpbWUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnZXZlbnRDcmVhdGVUaW1lJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ2FwcGxpY2F0aW9uTmFtZScpOwogICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwidXJnZW5jeSIsdGhpcy5zZXR0aW5nLmZvcm1EYXRhLnVyZ2VuY3k/dGhpcy5zZXR0aW5nLmZvcm1EYXRhLnVyZ2VuY3kudG9TdHJpbmcoKSA6ICcxJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ2lzUHVibGljJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ3NldmVyaXR5TGV2ZWwnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAncmVwb3J0RGF0ZScpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdwZXJpb2QnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLnJ1bGVGb3JtLCAnc2lnbmVkJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5ydWxlRm9ybSwgJ3Byb29mcmVhZCcpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMucnVsZUZvcm0sICdlZGl0b3InKTsKCiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5pbmZvcm1Gb3JtLCAnZGVzY3JpYmVGaWxlVXJsJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5pbmZvcm1Gb3JtLCAnZXZlbnRMZXZlbCcpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMuaW5mb3JtRm9ybSwgJ2V2ZW50Tm90aWZpY2F0aW9uJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5pbmZvcm1Gb3JtLCAnZXZlbnRTb3VyY2UnKTsKICAgICAgLy8gdGhpcy5zZXRGb3JtRGF0YSh0aGlzLmluZm9ybUZvcm0sICd3b3JrVHlwZScpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMuaW5mb3JtRm9ybSwgJ2hhbmRsZU9waW5pb24nKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLmluZm9ybUZvcm0sICdldmVudFR5cGUnKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLmluZm9ybUZvcm0sICdyZW1hcms4Jyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5mZWVkYmFja0Zvcm0sICdldmVudERlc2NyaXB0aW9uJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5mZWVkYmFja0Zvcm0sICdoYW5kbGVTaXR1YXRpb24nKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLmZlZWRiYWNrRm9ybSwgJ290aGVyU2l0dWF0aW9uJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5mZWVkYmFja0Zvcm0sICd3b3JrTm8nKTsKICAgICAgdGhpcy5zZXRGb3JtRGF0YSh0aGlzLmZlZWRiYWNrRm9ybSwgJ2ZlZWRiYWNrRGF0ZScpOwogICAgICB0aGlzLnNldEZvcm1EYXRhKHRoaXMuZmVlZGJhY2tGb3JtLCAnZmVlZGJhY2tGaWxlVXJsJyk7CiAgICAgIHRoaXMuc2V0Rm9ybURhdGEodGhpcy5mZWVkYmFja0Zvcm0sICdyZW1hcms3Jyk7CiAgICAgIC8v6YCa5oql5a+56LGh6LWL5YC8CiAgICAgIHRoaXMucmVwb3J0VGFyZ2V0Rm9ybSA9IHRoaXMuc2V0dGluZy5mb3JtRGF0YS5yZXBvcnRUYXJnZXRGb3JtOwoKICAgICAgdGhpcy5yZWZyZXNoV29yZCgpOwogICAgfSwKICAgIHNldEZvcm1EYXRhKGZvcm0sIGNvbHVtbil7CiAgICAgIGxldCBkYXRhID0gdGhpcy5zZXR0aW5nLmZvcm1EYXRhW2NvbHVtbl07CiAgICAgIGlmKCdldmVudFR5cGUnID09PSBjb2x1bW4pewogICAgICAgIGlmKGRhdGEgJiYgZGF0YS5pbmRleE9mKCIvIikhPT0tMSl7CiAgICAgICAgICBkYXRhID0gZGF0YS5zcGxpdCgiLyIpOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLiRzZXQoZm9ybSwgY29sdW1uLCBkYXRhKTsKICAgIH0sCiAgICBzZXRGb3JtRGVmYXVsdCgpewogICAgICBpZighdGhpcy5ydWxlRm9ybS51cmdlbmN5KXsKICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwndXJnZW5jeScsJzEnKTsKICAgICAgfQogICAgICBpZighdGhpcy5ydWxlRm9ybS5yZXBvcnREYXRlKXsKICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwncmVwb3J0RGF0ZScsdGhpcy5qbnBmLnRvRGF0ZShuZXcgRGF0ZSgpLCAneXl5eS1NTS1kZCBISDptbTpzcycpKTsKICAgICAgfQogICAgICBpZighdGhpcy5ydWxlRm9ybS5zaWduZWQpewogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdzaWduZWQnLCflj7bnkJsnKTsKICAgICAgfQogICAgICBpZighdGhpcy5ydWxlRm9ybS5wcm9vZnJlYWQpewogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdwcm9vZnJlYWQnLCfnjovkuq4nKTsKICAgICAgfQogICAgICBpZighdGhpcy5ydWxlRm9ybS5lZGl0b3IpewogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdlZGl0b3InLCfku5jmiawnKTsKICAgICAgfQogICAgfSwKICAgIHN1Ym1pdEZvcm0oZm9ybU5hbWUpIHsKICAgICAgY29uc3QgdGhhdCA9IHRoaXMKICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGF0LnJ1bGVGb3JtLmJ1c2luZXNzSWQgPSB0aGF0Lm1JZAogICAgICAgICAgdGhhdC5ydWxlRm9ybS53b3JrVHlwZSA9IHRoYXQud29ya1R5cGUKICAgICAgICAgIGFkZE9yZGVyKHRoYXQucnVsZUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfpgJrmiqXliJvlu7rmiJDlip8nLAogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB0aGF0LnJlc2V0Rm9ybSgpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6YCa5oql5Yib5bu65aSx6LSlJykKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5sb2coJ2Vycm9yIHN1Ym1pdCEhJyk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2xvc2VXb3JrJykKICAgIH0sCiAgICBvcGVuRm9jdXMoKSB7CiAgICAgIGlmICghdGhpcy5ydWxlRm9ybS5oYW5kbGVEZXB0KSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nlpITnkIbpg6jpl6jvvIEnKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCl7CiAgICAgIHRoaXMucXVlcnlFdmVudFBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy50b3RhbCA9IDA7CiAgICAgIHRoaXMuZ2V0RXZlbnREYXRhTGlzdCgpOwogICAgfSwKICAgIHJlc2V0UXVlcnkoKXsKICAgICAgdGhpcy5xdWVyeUV2ZW50UGFyYW1zPXsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiB0aGlzLnF1ZXJ5RXZlbnRQYXJhbXMucGFnZVNpemUKICAgICAgfTsKICAgICAgdGhpcy5nZXRFdmVudERhdGFMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlRXZlbnREZXRhaWwocm93KXsKICAgICAgdGhpcy5hc3NldERhdGE9IHsuLi5yb3d9OwogICAgICB0aGlzLm9wZW5EZXRhaWwodHJ1ZSk7CiAgICB9LAogICAgb3BlbkRldGFpbCh2YWwpewogICAgICB0aGlzLm9wZW5FdmVudERldGFpbERpYWxvZyA9IHZhbDsKICAgIH0sCiAgICBldmVudEJ0bkNsaWNrKHR5cGUsZXZ0KXsKICAgICAgaWYodGhpcy5jdXJyZW50RXZlbnRUeXBlICE9IHR5cGUgJiYgdGhpcy5jdXJyZW50RXZlbnRUeXBlICE9IDApewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIh+aNouS6i+S7tuexu+Wei+Wwhua4heepuuS5i+WJjemAieaLqeeahOS6i+S7tiwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5zZWxlY3RlZEV2ZW50ID0gW107CiAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmV2ZW50SWRzID0gW107CiAgICAgICAgICB0aGlzLmN1cnJlbnRFdmVudFR5cGUgPSB0eXBlOwogICAgICAgICAgdGhpcy4kcmVmcy5ldmVudExpc3QgJiYgdGhpcy4kcmVmcy5ldmVudExpc3QuY2xlYXJTZWxlY3Rpb24oKTsKICAgICAgICAgIHRoaXMuaXNDaGFuZ2VFdmVudFR5cGUgPSB0cnVlOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuZXZlbnRUeXBlQnRuS2V5Kys7CiAgICAgICAgfSk7CiAgICAgIH1lbHNlIHsKICAgICAgICBpZih0aGlzLmN1cnJlbnRFdmVudFR5cGUgIT0gMCAmJiB0aGlzLmN1cnJlbnRFdmVudFR5cGUgPT0gdHlwZSl7CiAgICAgICAgICBpZih0aGlzLmlzRnJvbUV2ZW50KXsKICAgICAgICAgICAgLy/ku47kuovku7blj5HotbfvvIzkuI3lhYHorrjlj5bmtogKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy4kY29uZmlybSgn5Y+W5raI6YCJ5Lit5LqL5Lu257G75Z6L5bCG5riF56m65LmL5YmN6YCJ5oup55qE5LqL5Lu2LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5ldmVudExpc3QgPSBbXTsKICAgICAgICAgICAgdGhpcy50b3RhbCA9IDA7CiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRFdmVudCA9IFtdOwogICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmV2ZW50SWRzID0gW107CiAgICAgICAgICAgIHRoaXMuY3VycmVudEV2ZW50VHlwZSA9IDA7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuZXZlbnRMaXN0ICYmIHRoaXMuJHJlZnMuZXZlbnRMaXN0LmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgICAgIHRoaXMuaXNDaGFuZ2VFdmVudFR5cGUgPSB0cnVlOwogICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBldnQudGFyZ2V0OwogICAgICAgICAgICBpZih0YXJnZXQpewogICAgICAgICAgICAgIGlmKHRhcmdldC5ub2RlTmFtZSA9PT0gJ1NQQU4nKXsKICAgICAgICAgICAgICAgIGlmKHRhcmdldC5wYXJlbnROb2RlKXsKICAgICAgICAgICAgICAgICAgdGFyZ2V0LnBhcmVudE5vZGUuYmx1cigpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB0YXJnZXQuYmx1cigpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CgogICAgICAgICAgfSk7CiAgICAgICAgfWVsc2UgewogICAgICAgICAgdGhpcy5jdXJyZW50RXZlbnRUeXBlID0gdHlwZTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBmbG93U3RhdGVGb3JtYXR0ZXIocm93LCBjb2x1bW4sIGNlbGxWYWx1ZSwgaW5kZXgpewogICAgICBsZXQgbmFtZSA9ICfmnKrliIbphY0nOwogICAgICBsZXQgbWF0Y2ggPSB0aGlzLmZsb3dTdGF0ZU9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0udmFsdWU9PWNlbGxWYWx1ZSk7CiAgICAgIGlmKG1hdGNoKXsKICAgICAgICBuYW1lID0gbWF0Y2gubGFiZWw7CiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWU7CiAgICB9LAogICAgZ2V0RXZlbnREYXRhTGlzdCgpewogICAgICBpZighdGhpcy5jdXJyZW50RXZlbnRUeXBlICYmICghdGhpcy5kYXRhRm9ybS5ldmVudElkcyB8fCB0aGlzLmRhdGFGb3JtLmV2ZW50SWRzLmxlbmd0aDwxKSl7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAwKXsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgdGhpcy5ldmVudExpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5yZXF1ZXN0TGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGxldCByZXNEYXRhID0gdGhpcy5jb252ZXJ0RXZlbnREYXRhKHJlc3BvbnNlLnJvd3MpOwogICAgICAgIHRoaXMuZXZlbnRMaXN0ID0gcmVzRGF0YTsKICAgICAgICB0aGlzLnRvdGFsPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmV2ZW50TGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkKICAgIH0sCiAgICAvL+i9rOaNogogICAgY29udmVydEV2ZW50RGF0YShzcmNEYXRhTGlzdCl7CiAgICAgIGlmKCFzcmNEYXRhTGlzdCl7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICAgIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAzKXsKICAgICAgICByZXR1cm4gc3JjRGF0YUxpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsCiAgICAgICAgICAgIHRpdGxlOiBpdGVtLnRocmVhdGVuTmFtZSwKICAgICAgICAgICAgY2F0ZWdvcnk6IGl0ZW0udGhyZWF0ZW5UeXBlLAogICAgICAgICAgICBzZXZlcml0eTogaXRlbS5hbGFybUxldmVsLAogICAgICAgICAgICB3ZWJVcmw6IGl0ZW0uZGVzdElwLAogICAgICAgICAgICBoYW5kbGVTdGF0dXM6IGl0ZW0uaGFuZGxlU3RhdGUsCiAgICAgICAgICAgIHdvcmtTdGF0ZTogaXRlbS5vcmRlclN0YXRlLAogICAgICAgICAgICBkYXRhU291cmNlOiBpdGVtLmRhdGFTb3VyY2UsCiAgICAgICAgICAgIHNjYW5OdW06IGl0ZW0uYWxhcm1OdW0sCiAgICAgICAgICAgIHVwZGF0ZVRpbWU6IGl0ZW0udXBkYXRlVGltZSwKICAgICAgICAgICAgY3JlYXRlVGltZTogaXRlbS5jcmVhdGVUaW1lLAogICAgICAgICAgICBob3N0UG9ydDogaXRlbS5kZXN0UG9ydCwKICAgICAgICAgICAgZmxvd1N0YXRlOiBpdGVtLmZsb3dTdGF0ZQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH1lbHNlIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAxKXsKICAgICAgICByZXR1cm4gc3JjRGF0YUxpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS53ZWJVcmwgPSBpdGVtLmhvc3RJcDsKICAgICAgICAgIHJldHVybiBpdGVtOwogICAgICAgIH0pOwogICAgICB9ZWxzZSB7CiAgICAgICAgcmV0dXJuIHNyY0RhdGFMaXN0OwogICAgICB9CgogICAgfSwKICAgIHJlcXVlc3RMaXN0KCl7CiAgICAgIGxldCBxdWVyeVBhcmFtcyA9IHRoaXMuY29udmVydFF1ZXJ5UGFyYW1zKHRoaXMucXVlcnlFdmVudFBhcmFtcyk7CiAgICAgIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAxKXsKICAgICAgICAvL0lQ5ryP5rSe5LqL5Lu2CiAgICAgICAgcmV0dXJuIGdldFZ1bG5EZWFsTGlzdChxdWVyeVBhcmFtcyk7CiAgICAgIH1lbHNlIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAyKXsKICAgICAgICAvL+W6lOeUqOa8j+a0nuS6i+S7tgogICAgICAgIHJldHVybiBsaXN0V2ViVnVsbihxdWVyeVBhcmFtcyk7CiAgICAgIH1lbHNlIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSAzKXsKICAgICAgICAvL+WogeiDgeS6i+S7tgogICAgICAgIHJldHVybiBsaXN0QWxhcm0ocXVlcnlQYXJhbXMpOwogICAgICB9CiAgICB9LAogICAgY29udmVydFF1ZXJ5UGFyYW1zKHNyY1BhcmFtcyl7CiAgICAgIGlmKCF0aGlzLmlzUmVhZCgnbGlzdF9zZWxlY3QnKSl7CiAgICAgICAgc3JjUGFyYW1zLmlkcyA9IHRoaXMuZGF0YUZvcm0uZXZlbnRJZHM7CiAgICAgIH0KICAgICAgaWYodGhpcy5jdXJyZW50RXZlbnRUeXBlID09IDMpewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBwYWdlTnVtOiBzcmNQYXJhbXMucGFnZU51bSwKICAgICAgICAgIHBhZ2VTaXplOiBzcmNQYXJhbXMucGFnZVNpemUsCiAgICAgICAgICB0aHJlYXRlbk5hbWU6IHNyY1BhcmFtcy50aXRsZSwKICAgICAgICAgIHRocmVhdGVuVHlwZTogc3JjUGFyYW1zLmNhdGVnb3J5P3NyY1BhcmFtcy5jYXRlZ29yeS5qb2luKCcvJyk6bnVsbCwKICAgICAgICAgIGFsYXJtTGV2ZWw6IHNyY1BhcmFtcy5zZXZlcml0eSwKICAgICAgICAgIGRlc3RJcDogc3JjUGFyYW1zLndlYlVybCwKICAgICAgICAgIGRlc3RQb3J0OiBzcmNQYXJhbXMuaG9zdFBvcnQsCiAgICAgICAgICBoYW5kbGVTdGF0ZTogc3JjUGFyYW1zLmhhbmRsZVN0YXR1cywKICAgICAgICAgIG9yZGVyU3RhdGU6IHNyY1BhcmFtcy53b3JrU3RhdGUsCiAgICAgICAgICBkYXRhU291cmNlOiBzcmNQYXJhbXMuZGF0YVNvdXJjZSwKICAgICAgICAgIGNyZWF0ZVRpbWU6IHNyY1BhcmFtcy5jcmVhdGVUaW1lLAogICAgICAgICAgaWRzOiBzcmNQYXJhbXMuaWRzCiAgICAgICAgfQogICAgICB9ZWxzZSBpZih0aGlzLmN1cnJlbnRFdmVudFR5cGUgPT0gMSl7CiAgICAgICAgc3JjUGFyYW1zLmhvc3RJcCA9IHNyY1BhcmFtcy53ZWJVcmw7CiAgICAgICAgcmV0dXJuIHNyY1BhcmFtczsKICAgICAgfWVsc2UgewogICAgICAgIHJldHVybiBzcmNQYXJhbXM7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVFdmVudFNlbGVjdGVkKHZhbCwgZXZlbnQpewogICAgICBpZighdGhpcy5ydWxlRm9ybS5ldmVudElkcyl7CiAgICAgICAgdGhpcy5ydWxlRm9ybS5ldmVudElkcyA9IFtdOwogICAgICB9CiAgICAgIGxldCB0ZW1wQXJyID0gWy4uLnRoaXMucnVsZUZvcm0uZXZlbnRJZHNdOwogICAgICB0ZW1wQXJyLnB1c2goLi4udmFsKTsKICAgICAgaWYgKGV2ZW50KSB7CiAgICAgICAgdGhpcy5ydWxlRm9ybS5ldmVudFR5cGUgPSBldmVudAogICAgICB9CiAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdldmVudElkcycsdGVtcEFycik7CiAgICAgIHRoaXMuc2VsZWN0ZWRFdmVudCA9IHRoaXMucnVsZUZvcm0uZXZlbnRJZHM7CiAgICB9LAogICAgaGFuZGxlU2V2ZXJpdHlUYWcoc2V2ZXJpdHksa2V5KXsKICAgICAgaWYoIXNldmVyaXR5KXsKICAgICAgICByZXR1cm4gJ+acquefpSc7CiAgICAgIH0KICAgICAgaWYodGhpcy5zZXZlcml0eU9wdGlvbnNbdGhpcy5jdXJyZW50RXZlbnRUeXBlLnRvU3RyaW5nKCldKXsKICAgICAgICBsZXQgbWF0Y2hJdGVtID0gdGhpcy5zZXZlcml0eU9wdGlvbnNbdGhpcy5jdXJyZW50RXZlbnRUeXBlLnRvU3RyaW5nKCldLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09IHNldmVyaXR5KTsKICAgICAgICBpZighbWF0Y2hJdGVtKXsKICAgICAgICAgIHJldHVybiAn5pyq55+lJzsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIG1hdGNoSXRlbVtrZXldOwogICAgICB9CiAgICAgIHJldHVybiAnJzsKICAgIH0sCiAgICBvcGVuQXBwbGljYXRpb25TZWxlY3QoKXsKICAgICAgLypnZXRBcHBsaWNhdGlvbkxpc3RCeUNvbmRpdGlvbih7CiAgICAgICAgLy8gaXB2NDogdGhpcy5nZXRJcHY0KCkKICAgICAgICBpcHY0OiBudWxsCiAgICAgIH0pLnRoZW4ocmVzPT57CiAgICAgICAgdGhpcy5hcHBsaWNhdGlvbkxpc3Q9cmVzLmRhdGE7CiAgICAgICAgdGhpcy5hcHBsaWNhdGlvbkRpYWxvZyA9IHRydWU7CiAgICAgIH0pOyovCiAgICAgIHRoaXMuYXBwbGljYXRpb25EaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIGdldElwdjQoKXsKICAgICAgaWYoIXRoaXMuc2V0dGluZy5yb3cgJiYgKCF0aGlzLnNldHRpbmcucm93cyB8fCB0aGlzLnNldHRpbmcucm93cy5sZW5ndGg8MSkpewogICAgICAgIHJldHVybiBudWxsOwogICAgICB9CiAgICAgIGxldCByb3cgPSB0aGlzLnNldHRpbmcucm93IHx8IHRoaXMuc2V0dGluZy5yb3dzWzBdOwogICAgICBpZih0aGlzLmN1cnJlbnRFdmVudFR5cGUgPT0gMSl7CiAgICAgICAgLy9JUOa8j+a0nuS6i+S7tgogICAgICAgIHJldHVybiByb3cuaG9zdElwOwogICAgICB9ZWxzZSBpZih0aGlzLmN1cnJlbnRFdmVudFR5cGUgPT0gMil7CiAgICAgICAgLy/lupTnlKjmvI/mtJ7kuovku7YKICAgICAgICByZXR1cm4gcm93LndlYlVybDsKICAgICAgfWVsc2UgaWYodGhpcy5jdXJyZW50RXZlbnRUeXBlID09IDMpewogICAgICAgIC8v5aiB6IOB5LqL5Lu2CiAgICAgICAgcmV0dXJuIHJvdy5kZXN0SXA7CiAgICAgIH1lbHNlIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PSA0KXsKICAgICAgICAvL+W8seWPo+S7pOa8j+a0ngogICAgICAgIHJldHVybiByb3cuaG9zdElwOwogICAgICB9CiAgICB9LAogICAgYXBwbGljYXRpb25TZWxlY3RlZChkYXRhKXsKICAgICAgdGhpcy5jdXJyZW50QXBwbGljYXRpb25TZWxlY3QgPSBkYXRhOwogICAgICB0aGlzLnJ1bGVGb3JtLmFzc29jaWF0ZWRJcHMgPSBkYXRhLnNlbGVjdGVkLm1hcChpdGVtID0+IGl0ZW0uaXApOwogICAgICB0aGlzLnJ1bGVGb3JtLmFzc29jaWF0ZWRJcHMgPSB0aGlzLnJ1bGVGb3JtLmFzc29jaWF0ZWRJcHMuZmlsdGVyKChpdGVtLCBpbmRleCwgc2VsZikgPT4gc2VsZi5pbmRleE9mKGl0ZW0pID09PSBpbmRleCk7CiAgICAgIHRoaXMucnVsZUZvcm0ucmVtYXJrMSA9IEpTT04uc3RyaW5naWZ5KGRhdGEuc2VsZWN0ZWQubWFwKGl0ZW0gPT4gaXRlbS5hc3NldElkKSk7CiAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdhcHBsaWNhdGlvbklkJyxkYXRhLmFwcGxpY2F0aW9uLmFzc2V0SWQpOwogICAgICB0aGlzLnJ1bGVGb3JtLmFwcGxpY2F0aW9uTmFtZSA9IGRhdGEuYXBwbGljYXRpb24uYXNzZXROYW1lOwogICAgICB0aGlzLmFwcGxpY2F0aW9uRGlhbG9nID0gZmFsc2U7CiAgICAgIHRoaXMucmVmcmVzaFdvcmQoKTsKICAgIH0sCiAgICBzdWJtaXRBcHBsaWNhdGlvblNlbGVjdCgpewogICAgICB0aGlzLiRyZWZzLmFwcGxpY2F0aW9uU2VsZWN0LnN1Ym1pdCgpOwogICAgfSwKICAgIGdldEFwcGxpY2F0aW9uRGV0YWlscygpewogICAgICBsZXQgX3RoYXQgPSB0aGlzOwogICAgICBnZXRBcHBsaWNhdGlvbkRldGFpbHModGhpcy5ydWxlRm9ybS5hcHBsaWNhdGlvbklkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYocmVzLmRhdGEudXJsKXsKICAgICAgICAgIHRoaXMuJHNldChfdGhhdC5ydWxlRm9ybSwnbG9naW5VcmwnLHJlcy5kYXRhLnVybCk7CiAgICAgICAgfQogICAgICAgIGlmKHJlcy5kYXRhLmRlcHRfaWQpewogICAgICAgICAgdGhpcy4kc2V0KF90aGF0LnJ1bGVGb3JtLCdoYW5kbGVEZXB0JyxyZXMuZGF0YS5kZXB0X2lkKTsKICAgICAgICAgIHRoaXMuJHNldChfdGhhdC5ydWxlRm9ybSwnbWFuYWdlRGVwdCcscmVzLmRhdGEuZGVwdF9pZCk7CgogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICB0aGlzLmdldE1hbmFnZVVzZXJMaXN0KCk7CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgICB0aGlzLmFwcGxpY2F0aW9uSW5mbyA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfSwKICAgIGV2ZW50VHlwZUJ0bkRpc2FibGUoKXsKICAgICAgaWYoIi0xIiA9PSB0aGlzLnNldHRpbmcub3BUeXBlKXsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYodGhpcy5zZXR0aW5nLnJvdyAmJiB0aGlzLnNldHRpbmcucm93LmV2ZW50VHlwZSl7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgICAgcmV0dXJuIGZhbHNlOwogICAgfSwKICAgIGdldE1hbmFnZVVzZXJMaXN0KCkgewogICAgICAvLyB0aGlzLnJ1bGVGb3JtLmhhbmRsZVVzZXIgPSAnJwogICAgICB0aGlzLm1hbmFnZU9wdGlvbiA9IFtdCiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLm1hbmFnZURlcHQpIHsKICAgICAgICBnZXRBbGxVc2VyTGlzdCh7CiAgICAgICAgICAvLyBkZXB0SWQ6IHRoaXMucnVsZUZvcm0ubWFuYWdlRGVwdAogICAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIHRoaXMubWFuYWdlT3B0aW9uID0gcmVzLnJvd3M7CiAgICAgICAgICAgIHRoaXMubWFuYWdlT3B0aW9uQ29weSA9IFsuLi50aGlzLm1hbmFnZU9wdGlvbl07CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAvKmlmKHRoaXMubWFuYWdlT3B0aW9uICYmICF0aGlzLm1hbmFnZU9wdGlvbi5maW5kKG1hbmFnZVVzZXJJdGVtID0+IG1hbmFnZVVzZXJJdGVtLnVzZXJJZCA9PSB0aGlzLnJ1bGVGb3JtLm1hbmFnZVVzZXIpKXsKICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0ubWFuYWdlVXNlciA9ICcnOwogICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5oYW5kbGVVc2VyUGhvbmUgPSAnJzsKICAgICAgICAgICAgICB9Ki8KCiAgICAgICAgICAgICAgaWYodGhpcy5hcHBsaWNhdGlvbkluZm8pewogICAgICAgICAgICAgICAgaWYodGhpcy5hcHBsaWNhdGlvbkluZm8ubWFuYWdlcil7CiAgICAgICAgICAgICAgICAgIGxldCBtYW5hZ2VyID0gdGhpcy5hcHBsaWNhdGlvbkluZm8ubWFuYWdlci5zcGxpdCgnLCcpWzBdOwogICAgICAgICAgICAgICAgICAvKmlmKHRoaXMubWFuYWdlT3B0aW9uLmZpbmQobWFuYWdlVXNlckl0ZW0gPT4gbWFuYWdlVXNlckl0ZW0udXNlcklkID09IG1hbmFnZXIpKXsKICAgICAgICAgICAgICAgICAgICAvLyB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwnaGFuZGxlVXNlcicscGFyc2VJbnQobWFuYWdlcikpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdtYW5hZ2VVc2VyJyxwYXJzZUludChtYW5hZ2VyKSk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgICAgICAgICAgICAgICAgfSovCiAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdtYW5hZ2VVc2VyJyxwYXJzZUludChtYW5hZ2VyKSk7CiAgICAgICAgICAgICAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZih0aGlzLmFwcGxpY2F0aW9uSW5mby5waG9uZW51bWJlcil7CiAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdoYW5kbGVVc2VyUGhvbmUnLHRoaXMuYXBwbGljYXRpb25JbmZvLnBob25lbnVtYmVyKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgfSwKICAgIGdldFVzZXJMaXN0KCkgewogICAgICAvLyB0aGlzLnJ1bGVGb3JtLmhhbmRsZVVzZXIgPSAnJwogICAgICB0aGlzLmhhbmRsZU9wdGlvbiA9IFtdCiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLmhhbmRsZURlcHQpIHsKICAgICAgICBnZXRBbGxVc2VyTGlzdEJ5RGVwdCh7CiAgICAgICAgICBkZXB0SWQ6IHRoaXMucnVsZUZvcm0uaGFuZGxlRGVwdAogICAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIHRoaXMuaGFuZGxlT3B0aW9uID0gcmVzLnJvd3M7CiAgICAgICAgICAgIHRoaXMuaGFuZGxlT3B0aW9uQ29weSA9IFsuLi50aGlzLmhhbmRsZU9wdGlvbl07CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICBpZih0aGlzLmhhbmRsZU9wdGlvbiAmJiAhdGhpcy5oYW5kbGVPcHRpb24uZmluZChoYW5kbGVVc2VySXRlbSA9PiBoYW5kbGVVc2VySXRlbS51c2VySWQgPT0gdGhpcy5ydWxlRm9ybS5oYW5kbGVVc2VyKSl7CiAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmhhbmRsZVVzZXIgPSAnJzsKICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdoYW5kbGVVc2VyJywnJyk7CiAgICAgICAgICAgICAgICAvLyB0aGlzLnJ1bGVGb3JtLmhhbmRsZVVzZXJQaG9uZSA9ICcnOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgaWYodGhpcy5hcHBsaWNhdGlvbkluZm8pewogICAgICAgICAgICAgICAgaWYodGhpcy5hcHBsaWNhdGlvbkluZm8ubWFuYWdlcil7CiAgICAgICAgICAgICAgICAgIGxldCBtYW5hZ2VyID0gdGhpcy5hcHBsaWNhdGlvbkluZm8ubWFuYWdlci5zcGxpdCgnLCcpWzBdOwogICAgICAgICAgICAgICAgICBpZih0aGlzLmhhbmRsZU9wdGlvbi5maW5kKGhhbmRsZVVzZXJJdGVtID0+IGhhbmRsZVVzZXJJdGVtLnVzZXJJZCA9PSBtYW5hZ2VyKSl7CiAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5oYW5kbGVPcHRpb24gJiYgdGhpcy5oYW5kbGVPcHRpb24uZmluZChoYW5kbGVVc2VySXRlbSA9PiBoYW5kbGVVc2VySXRlbS51c2VySWQgPT0gbWFuYWdlcikpewogICAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sJ2hhbmRsZVVzZXInLHBhcnNlSW50KG1hbmFnZXIpKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmKHRoaXMuYXBwbGljYXRpb25JbmZvLnBob25lbnVtYmVyKXsKICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sJ2hhbmRsZVVzZXJQaG9uZScsdGhpcy5hcHBsaWNhdGlvbkluZm8ucGhvbmVudW1iZXIpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgbWFuYWdlVXNlckZpbHRlcih2YWwpewogICAgICBpZih2YWwpewogICAgICAgIHRoaXMubWFuYWdlT3B0aW9uID0gdGhpcy5tYW5hZ2VPcHRpb25Db3B5LmZpbHRlcihvcHRpb24gPT4gewogICAgICAgICAgcmV0dXJuIG9wdGlvbi51c2VyTmFtZS5pbmRleE9mKHZhbCkgIT0gLTEgfHwgb3B0aW9uLm5pY2tOYW1lLmluZGV4T2YodmFsKSAhPSAtMTsKICAgICAgICB9KTsKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMubWFuYWdlT3B0aW9uID0gWy4uLnRoaXMubWFuYWdlT3B0aW9uQ29weV07CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVVc2VyRmlsdGVyKHZhbCl7CiAgICAgIGlmKHZhbCl7CiAgICAgICAgdGhpcy5oYW5kbGVPcHRpb24gPSB0aGlzLmhhbmRsZU9wdGlvbkNvcHkuZmlsdGVyKG9wdGlvbiA9PiB7CiAgICAgICAgICByZXR1cm4gb3B0aW9uLnVzZXJOYW1lLmluZGV4T2YodmFsKSAhPSAtMSB8fCBvcHRpb24ubmlja05hbWUuaW5kZXhPZih2YWwpICE9IC0xOwogICAgICAgIH0pOwogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5oYW5kbGVPcHRpb24gPSBbLi4udGhpcy5oYW5kbGVPcHRpb25Db3B5XTsKICAgICAgfQogICAgfSwKICAgIG1hbmFnZVVzZXJWaXNpYmxlQ2hhbmdlKCl7CiAgICAgIHRoaXMubWFuYWdlT3B0aW9uID0gWy4uLnRoaXMubWFuYWdlT3B0aW9uQ29weV07CiAgICB9LAogICAgaGFuZGxlVXNlclZpc2libGVDaGFuZ2UoKXsKICAgICAgdGhpcy5oYW5kbGVPcHRpb24gPSBbLi4udGhpcy5oYW5kbGVPcHRpb25Db3B5XTsKICAgIH0sCiAgICBtYW5hZ2VVc2VyQ2hhbmdlKHJvdyl7CiAgICAgIGlmKHJvdyl7CiAgICAgICAgbGV0IG1hdGNoVXNlciA9IHRoaXMubWFuYWdlT3B0aW9uLmZpbmQoaXRlbSA9PiBpdGVtLnVzZXJJZD09cm93KTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwnaGFuZGxlVXNlclBob25lJyxtYXRjaFVzZXI/bWF0Y2hVc2VyLnBob25lbnVtYmVyOicnKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVVzZXJDaGFuZ2Uocm93KXsKICAgICAgaWYocm93KXsKICAgICAgICBsZXQgbWF0Y2hVc2VyID0gdGhpcy5oYW5kbGVPcHRpb24uZmluZChpdGVtID0+IGl0ZW0udXNlcklkPT1yb3cpOwogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCdoYW5kbGVVc2VyUGhvbmUnLG1hdGNoVXNlcj9tYXRjaFVzZXIucGhvbmVudW1iZXI6JycpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlVXNlclBob25lSW5wdXQoKXsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgIH0sCiAgICB2YWxpZGF0ZUFsbEZvcm0oKSB7CiAgICAgIC8qaWYodGhpcy5pc1JlYWQoJ2xpc3Rfc2VsZWN0JykgJiYgKHRoaXMuY3VycmVudEV2ZW50VHlwZSAmJiB0aGlzLmN1cnJlbnRFdmVudFR5cGUgIT09IDApKXsKICAgICAgICBpZighdGhpcy5ydWxlRm9ybS5ldmVudElkcyB8fCB0aGlzLnJ1bGVGb3JtLmV2ZW50SWRzLmxlbmd0aCA8IDEpewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeWFs+iBlOS6i+S7ticpOwogICAgICAgICAgdGhpcy4kcmVmcy5ldmVudF9saXN0ICYmIHRoaXMuJHJlZnMuZXZlbnRfbGlzdC5zY3JvbGxJbnRvVmlldygpOwogICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsKICAgICAgICAgICAgcmVqZWN0KCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0qLwoKICAgICAgbGV0IHZhbGlkYXRlRm9ybXMgPSB0aGlzLmdldFZhbGlkYXRlRm9ybSgpOwogICAgICBpZighdmFsaWRhdGVGb3JtcyB8fCB2YWxpZGF0ZUZvcm1zLmxlbmd0aCA8IDEpewogICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgICByZXNvbHZlKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgcmV0dXJuIFByb21pc2UuYWxsKHZhbGlkYXRlRm9ybXMpOwogICAgfSwKICAgIHZhbGlkYXRlRm9ybShmb3JtTmFtZSl7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgaWYoIXRoaXMuJHJlZnNbZm9ybU5hbWVdKXsKICAgICAgICAgIHJlamVjdCgpOwogICAgICAgIH0KICAgICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICByZXNvbHZlKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHJlamVjdCgpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICBnZXRWYWxpZGF0ZUZvcm0oKXsKICAgICAgbGV0IHJlcyA9IFtdOwogICAgICAvLyBsZXQgYWN0aXZlRm9ybXMgPSB0aGlzLmdldEFjdGl2ZUZvcm0oKTsKICAgICAgbGV0IGFjdGl2ZUZvcm1zID0gWydydWxlRm9ybScsJ3BlcnNvbkZvcm0nLCd0aW1lRm9ybScsJ2luZm9ybUZvcm0nLCdmZWVkYmFja0Zvcm0nXTsKICAgICAgaWYoYWN0aXZlRm9ybXMgJiYgYWN0aXZlRm9ybXMubGVuZ3RoPjApewogICAgICAgIGFjdGl2ZUZvcm1zLmZvckVhY2goZm9ybU5hbWUgPT4gewogICAgICAgICAgaWYodGhpcy4kcmVmc1tmb3JtTmFtZV0pewogICAgICAgICAgICByZXMucHVzaCh0aGlzLnZhbGlkYXRlRm9ybShmb3JtTmFtZSkpOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0KICAgICAgbGV0IHJlZlJlcG9ydFRhcmdldCA9IHRoaXMuJHJlZnMucmVwb3J0VGFyZ2V0OwogICAgICBpZihyZWZSZXBvcnRUYXJnZXQpewogICAgICAgIHJlcy5wdXNoKC4uLnJlZlJlcG9ydFRhcmdldC52YWxpZGF0ZSgpKTsKICAgICAgfQogICAgICByZXR1cm4gcmVzOwogICAgfSwKICAgIGdldEFjdGl2ZUZvcm0oKXsKICAgICAgbGV0IHJlcyA9IFtdOwogICAgICBsZXQgZmxvd1ZhcmlhYmxlID0gdGhpcy5zZXR0aW5nLmZsb3dWYXJpYWJsZTsKICAgICAgaWYoZmxvd1ZhcmlhYmxlICYmIGZsb3dWYXJpYWJsZS5sZW5ndGggPiAwKXsKICAgICAgICBsZXQgbWF0Y2hJdGVtID0gZmxvd1ZhcmlhYmxlLmZpbmQoaXRlbSA9PiBpdGVtLmtleSA9PSAnZm9ybU5hbWVzJyk7CiAgICAgICAgaWYobWF0Y2hJdGVtICYmIG1hdGNoSXRlbS52YWx1ZSl7CiAgICAgICAgICBsZXQgbmFtZXMgPSBtYXRjaEl0ZW0udmFsdWUuc3BsaXQoJywnKTsKICAgICAgICAgIG5hbWVzLmZvckVhY2gobmFtZSA9PiB7CiAgICAgICAgICAgIHJlcy5wdXNoKG5hbWUpOwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIHJlczsKICAgIH0sCiAgICBoYW5kbGVDb2x1bW4oY29sdW1uKXsKICAgICAgbGV0IGZvcm1PcGVyYXRlcyA9IHRoaXMuc2V0dGluZy5mb3JtT3BlcmF0ZXM7CiAgICAgIGlmKCFmb3JtT3BlcmF0ZXMgfHwgZm9ybU9wZXJhdGVzLmxlbmd0aCA8IDEpewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIGxldCBtYXRjaE9wZXJhdGUgPSBmb3JtT3BlcmF0ZXMuZmluZChpdGVtID0+IGl0ZW0uaWQ9PWNvbHVtbik7CiAgICAgIGlmKG1hdGNoT3BlcmF0ZSl7CiAgICAgICAgcmV0dXJuIG1hdGNoT3BlcmF0ZS5yZWFkOwogICAgICB9ZWxzZSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgIH0sCiAgICBpc1JlYWQobmFtZSl7CiAgICAgIGxldCBmb3JtT3BlcmF0ZXMgPSBbXTsKICAgICAgaWYoIXRoaXMuc2V0dGluZy5yZWFkb25seSl7CiAgICAgICAgZm9ybU9wZXJhdGVzID0gdGhpcy5zZXR0aW5nLmZvcm1PcGVyYXRlczsKICAgICAgfWVsc2UgewogICAgICAgIGZvcm1PcGVyYXRlcyA9IHRoaXMuZm9ybU9wZXJhdGVzUmVjb3JkOwogICAgICB9CiAgICAgIGlmKCFmb3JtT3BlcmF0ZXMgfHwgZm9ybU9wZXJhdGVzLmxlbmd0aCA8IDEpewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIGxldCBtYXRjaE9wZXJhdGUgPSBmb3JtT3BlcmF0ZXMuZmluZChpdGVtID0+IGl0ZW0uaWQ9PW5hbWUpOwogICAgICBpZihtYXRjaE9wZXJhdGUpewogICAgICAgIHJldHVybiBtYXRjaE9wZXJhdGUucmVhZDsKICAgICAgfWVsc2UgewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICB9LAogICAgaXNSZWFkT3JOdWxsKG5hbWUpewogICAgICBsZXQgZm9ybU9wZXJhdGVzID0gdGhpcy5zZXR0aW5nLmZvcm1PcGVyYXRlczsKICAgICAgaWYoIWZvcm1PcGVyYXRlcyB8fCBmb3JtT3BlcmF0ZXMubGVuZ3RoIDwgMSl7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGxldCBtYXRjaE9wZXJhdGUgPSBmb3JtT3BlcmF0ZXMuZmluZChpdGVtID0+IGl0ZW0uaWQ9PW5hbWUpOwogICAgICBpZihtYXRjaE9wZXJhdGUpewogICAgICAgIHJldHVybiBtYXRjaE9wZXJhdGUucmVhZDsKICAgICAgfWVsc2UgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGlzV3JpdGVPck51bGwobmFtZSl7CiAgICAgIGlmKHRoaXMuc2V0dGluZy5yZWFkb25seSl7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGxldCBmb3JtT3BlcmF0ZXMgPSB0aGlzLnNldHRpbmcuZm9ybU9wZXJhdGVzOwogICAgICBpZighZm9ybU9wZXJhdGVzIHx8IGZvcm1PcGVyYXRlcy5sZW5ndGggPCAxKXsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgbGV0IG1hdGNoT3BlcmF0ZSA9IGZvcm1PcGVyYXRlcy5maW5kKGl0ZW0gPT4gaXRlbS5pZD09bmFtZSk7CiAgICAgIGlmKG1hdGNoT3BlcmF0ZSl7CiAgICAgICAgcmV0dXJuIG1hdGNoT3BlcmF0ZS53cml0ZTsKICAgICAgfWVsc2UgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGlzV3JpdGUobmFtZSl7CiAgICAgIGlmKHRoaXMuc2V0dGluZy5yZWFkb25seSl7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGxldCBmb3JtT3BlcmF0ZXMgPSB0aGlzLnNldHRpbmcuZm9ybU9wZXJhdGVzOwogICAgICBpZighZm9ybU9wZXJhdGVzIHx8IGZvcm1PcGVyYXRlcy5sZW5ndGggPCAxKXsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICBsZXQgbWF0Y2hPcGVyYXRlID0gZm9ybU9wZXJhdGVzLmZpbmQoaXRlbSA9PiBpdGVtLmlkPT1uYW1lKTsKICAgICAgaWYobWF0Y2hPcGVyYXRlKXsKICAgICAgICByZXR1cm4gbWF0Y2hPcGVyYXRlLndyaXRlOwogICAgICB9ZWxzZSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgIH0sCiAgICBpc0hpZGVPck51bGwobmFtZSl7CiAgICAgIGxldCBmb3JtT3BlcmF0ZXMgPSB0aGlzLnNldHRpbmcuZm9ybU9wZXJhdGVzOwogICAgICBpZighZm9ybU9wZXJhdGVzIHx8IGZvcm1PcGVyYXRlcy5sZW5ndGggPCAxKXsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICBsZXQgbWF0Y2hPcGVyYXRlID0gZm9ybU9wZXJhdGVzLmZpbmQoaXRlbSA9PiBpdGVtLmlkPT1uYW1lKTsKICAgICAgaWYobWF0Y2hPcGVyYXRlKXsKICAgICAgICByZXR1cm4gbWF0Y2hPcGVyYXRlLmhpZGU9PT10cnVlOwogICAgICB9ZWxzZSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgIH0sCiAgICBpc0hpZGUobmFtZSl7CiAgICAgIGxldCBmb3JtT3BlcmF0ZXMgPSB0aGlzLnNldHRpbmcuZm9ybU9wZXJhdGVzOwogICAgICBpZighZm9ybU9wZXJhdGVzIHx8IGZvcm1PcGVyYXRlcy5sZW5ndGggPCAxKXsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICBsZXQgbWF0Y2hPcGVyYXRlID0gZm9ybU9wZXJhdGVzLmZpbmQoaXRlbSA9PiBpdGVtLmlkPT1uYW1lKTsKICAgICAgaWYobWF0Y2hPcGVyYXRlKXsKICAgICAgICByZXR1cm4gbWF0Y2hPcGVyYXRlLmhpZGU9PT10cnVlOwogICAgICB9ZWxzZSB7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICB9LAogICAgYmVmb3JlU3VibWl0KCl7CiAgICAgIC8v5Yqo5oCB6KGo5Y2VCiAgICAgIHRoaXMuZGF0YUZvcm0gPSB7fTsKICAgICAgLy8gbGV0IGFjdGl2ZUZvcm1zID0gdGhpcy5nZXRBY3RpdmVGb3JtKCk7CiAgICAgIGxldCBhY3RpdmVGb3JtcyA9IFsncnVsZUZvcm0nLCdwZXJzb25Gb3JtJywndGltZUZvcm0nLCdpbmZvcm1Gb3JtJywnZmVlZGJhY2tGb3JtJ107CiAgICAgIGlmKGFjdGl2ZUZvcm1zICYmIGFjdGl2ZUZvcm1zLmxlbmd0aD4wKXsKICAgICAgICBhY3RpdmVGb3Jtcy5mb3JFYWNoKGZvcm1OYW1lID0+IHsKICAgICAgICAgIGlmKHRoaXMuJHJlZnNbZm9ybU5hbWVdKXsKICAgICAgICAgICAgdGhpcy5kYXRhRm9ybSA9IHsuLi50aGlzLmRhdGFGb3JtLCAuLi50aGlzW2Zvcm1OYW1lXX07CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgICBpZih0aGlzLnNldHRpbmcuZmxvd1ZhcmlhYmxlKXsKICAgICAgICBsZXQgbWF0Y2ggPSB0aGlzLnNldHRpbmcuZmxvd1ZhcmlhYmxlLmZpbmQoaXRlbSA9PiBpdGVtLmtleSA9PSAnd29ya05vUHJlZml4Jyk7CiAgICAgICAgaWYobWF0Y2gpewogICAgICAgICAgdGhpcy5kYXRhRm9ybS53b3JrTm9QcmVmaXggPSBtYXRjaC52YWx1ZTsKICAgICAgICB9CiAgICAgIH0KICAgICAgLyogICAgICBpZiAodGhpcy5kYXRhRm9ybS5ldmVudFR5cGUpIHsKICAgICAgICAgICAgICB0aGlzLmRhdGFGb3JtLmV2ZW50VHlwZSA9IHRoaXMuZGF0YUZvcm0uZXZlbnRUeXBlLmpvaW4oJy8nKTsKICAgICAgICAgICAgfSovCiAgICAgIGlmKHRoaXMuZGF0YUZvcm0gJiYgdGhpcy5kYXRhRm9ybS5ldmVudFR5cGUpewogICAgICAgIGlmKEFycmF5LmlzQXJyYXkodGhpcy5kYXRhRm9ybS5ldmVudFR5cGUpKXsKICAgICAgICAgIHRoaXMuZGF0YUZvcm0uZXZlbnRUeXBlID0gdGhpcy5kYXRhRm9ybS5ldmVudFR5cGUuam9pbignLycpOwogICAgICAgIH0KICAgICAgfWVsc2UgewogICAgICAgIGlmKHRoaXMuY3VycmVudEV2ZW50VHlwZSA9PT0gNCl7CiAgICAgICAgICAvL+W8seWPo+S7pAogICAgICAgICAgdGhpcy5kYXRhRm9ybS5ldmVudFR5cGUgPSAn5byx5Y+j5LukJzsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8v6YCa5oql5a+56LGhCiAgICAgIGxldCByZXBvcnRUYXJnZXRSZWYgPSB0aGlzLiRyZWZzLnJlcG9ydFRhcmdldDsKICAgICAgaWYocmVwb3J0VGFyZ2V0UmVmKXsKICAgICAgICB0aGlzLmRhdGFGb3JtLnJlcG9ydFRhcmdldEZvcm0gPSByZXBvcnRUYXJnZXRSZWYuc3VibWl0Rm9ybSgpOwogICAgICB9CiAgICAgIHJldHVybiB0aGlzLmRhdGFGb3JtOwogICAgfSwKICAgIGlzU2VsZWN0QWJsZShyb3csaW5kZXgpewogICAgICBpZih0aGlzLnNldHRpbmcgJiYgKCF0aGlzLnNldHRpbmcub3JpZ2luVHlwZSB8fCAodGhpcy5zZXR0aW5nLm9yaWdpblR5cGUgJiYgdGhpcy5zZXR0aW5nLm9yaWdpblR5cGUgIT0gJ2V2ZW50JykpICYmICh0aGlzLnNldHRpbmcucm93ICYmIHRoaXMuc2V0dGluZy5yb3cuZXZlbnRJZHMgJiYgdGhpcy5zZXR0aW5nLnJvdy5ldmVudElkcy5maW5kKGl0ZW0gPT4gaXRlbSA9PSByb3cuaWQpKSl7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgICAgcmV0dXJuICghcm93LmZsb3dTdGF0ZSB8fCByb3cuZmxvd1N0YXRlID09ICc5OScpOwogICAgfSwKICAgIHNlbGVjdEV2ZW50Q2xpY2soKXsKICAgICAgaWYoIXRoaXMuY3VycmVudEV2ZW50VHlwZSl7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+35YWI6YCJ5oup5LqL5Lu257G75Z6LJyk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHRoaXMub3BlbkV2ZW50U2VsZWN0RGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICBpc1JlcXVpcmVkKHByb3ApewogICAgICBpZighdGhpcy5mb3JtT3BlcmF0ZXMpewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBsZXQgbWF0Y2ggPSB0aGlzLmZvcm1PcGVyYXRlcy5maW5kKGl0ZW0gPT4gaXRlbS5pZCA9PT0gcHJvcCk7CiAgICAgIGlmKG1hdGNoICYmIG1hdGNoLnJlcXVpcmVkKXsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAogICAgcmVmcmVzaFdvcmQoKXsKICAgICAgLyogaWYodGhpcy5pc0NoYW5nZUZvcm0pewogICAgICAgIHRoaXMuaXNDaGFuZ2VGb3JtID0gZmFsc2U7CiAgICAgICAgLy/mmoLlrZgKICAgICAgICAvLyB0aGlzLiRldmVudEJ1cy4kZW1pdCgnc2VuZFdvcmtGb3JtJywgdGhpcy5iZWZvcmVTdWJtaXQoKSk7CiAgICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQuJHBhcmVudCAmJiB0aGlzLiRwYXJlbnQuJHBhcmVudC4kcGFyZW50LmhhbmRsZVNob3dXb3JkICYmIHRoaXMuJHBhcmVudC4kcGFyZW50LiRwYXJlbnQuaGFuZGxlU2hvd1dvcmQoKTsKICAgICAgfSAqLwogICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC4kcGFyZW50ICYmIHRoaXMuJHBhcmVudC4kcGFyZW50LiRwYXJlbnQuaGFuZGxlU2hvd1dvcmQgJiYgdGhpcy4kcGFyZW50LiRwYXJlbnQuJHBhcmVudC5oYW5kbGVTaG93V29yZCgpOwogICAgfSwKICAgIHNlbmREYXRhRm9ybSgpewogICAgICAvL+aaguWtmAogICAgICByZXR1cm4gdGhpcy5iZWZvcmVTdWJtaXQoKTsKICAgIH0sCiAgICBsb29wR2V0Rmxvd05vZGUodHJlZURhdGEsbm9kZUlkLGFycil7CiAgICAgIGlmKCF0cmVlRGF0YSl7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGFyci5wdXNoKHsKICAgICAgICBub2RlSWQ6IHRyZWVEYXRhLm5vZGVJZCwKICAgICAgICBwcm9wZXJ0aWVzOiB0cmVlRGF0YS5wcm9wZXJ0aWVzLAogICAgICAgIHN0YXRlOiB0cmVlRGF0YS5zdGF0ZSwKICAgICAgICB0eXBlOiB0cmVlRGF0YS50eXBlCiAgICAgIH0pCiAgICAgIGlmKHRyZWVEYXRhLm5vZGVJZCA9PT0gbm9kZUlkKXsKICAgICAgICByZXR1cm47CiAgICAgIH1lbHNlIHsKICAgICAgICByZXR1cm4gdGhpcy5sb29wR2V0Rmxvd05vZGUodHJlZURhdGEuY2hpbGROb2RlLG5vZGVJZCxhcnIpOwogICAgICB9CiAgICB9LAogICAgYWRkRGVwdCgpewogICAgICB0aGlzLiRyZWZzLnJlcG9ydFRhcmdldCAmJiB0aGlzLiRyZWZzLnJlcG9ydFRhcmdldC5hZGREZXB0KCk7CiAgICB9LAogICAgcmVwb3J0VGFyZ2V0QWN0aXZlQ2hhbmdlKHZhbCl7CiAgICAgIGlmKCF2YWwpewogICAgICAgIHRoaXMucmVwb3J0VGFyZ2V0QWN0aXZlID0gJzAnOwogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5yZXBvcnRUYXJnZXRBY3RpdmUgPSB2YWw7CiAgICAgIH0KICAgIH0sCiAgfQp9Cg=="}, {"version": 3, "sources": ["work_flow.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+RA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "work_flow.vue", "sourceRoot": "src/views/todoItem/todo", "sourcesContent": ["<template>\n  <div class=\"main\" ref=\"main\">\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"base_content\">\n      <div class=\"title\"><i class=\"el-icon-info\" /> 基础信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"!isHide('workName')\">\n            <el-form-item label=\"通报名称\" prop=\"workName\" ref=\"workName\">\n              <el-input size=\"small\" :disabled=\"!isWrite('ruleForm') || !isWrite('workName')\" v-model.trim=\"ruleForm.workName\" placeholder=\"请填写通报名称\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('period')\" style=\"height: 59px\">\n            <el-form-item label=\"期号\" prop=\"period\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" :required=\"isRequired('period')\">\n              <el-input-number style=\"width: 100%\" size=\"small\" v-model=\"ruleForm.period\" controls-position=\"right\" :min=\"1\" :max=\"999999\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" @change=\"refreshWord\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\" v-if=\"isReadOrNull('issue')\">\n            <el-form-item label=\"期刊\" prop=\"issue\" :disabled=\"!isWrite('ruleForm') || !isWrite('issue')\">\n              <el-input :disabled=\"true\" size=\"small\" v-model.trim=\"ruleForm.issue\" placeholder=\"\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('remark6') || isWriteOrNull('remark6')\">\n            <el-form-item label=\"通报类型\" prop=\"remark6\" ref=\"remark6\">\n              <el-select size=\"small\" v-model=\"ruleForm.remark6\" placeholder=\"请选择通报类型\" :disabled=\"!isWrite('ruleForm') || !isWrite('remark6')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in reportOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('urgency')\">\n            <el-form-item label=\"紧急程度\" prop=\"urgency\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\">\n              <el-select size=\"small\" v-model=\"ruleForm.urgency\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in urgencyOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('severityLevel')\">\n            <el-form-item label=\"严重程度\" prop=\"severityLevel\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\">\n              <el-select size=\"small\" v-model=\"ruleForm.severityLevel\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in severityLevelOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('isPublic')\">\n            <el-form-item label=\"是否公开\" prop=\"isPublic\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\">\n              <el-select size=\"small\" v-model=\"ruleForm.isPublic\" :default-first-option=\"true\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in isPublicOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"time_content\">\n      <div class=\"title\"><i class=\"el-icon-time\" /> 时间信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"timeForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n<!--          <el-col :span=\"8\" v-if=\"!isHide('eventCreateTime')\">\n            <el-form-item label=\"发现时间\" prop=\"eventCreateTime\" ref=\"eventCreateTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('eventCreateTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.eventCreateTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('reportDate')\">\n            <el-form-item label=\"通报日期\" prop=\"reportDate\" :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\">\n              <el-date-picker\n                v-model=\"ruleForm.reportDate\"\n                @change=\"refreshWord\"\n                type=\"datetime\"\n                style=\"width: 100%\"\n                :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"请选择通报日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"!isHide('expectCompleteTime')\">\n            <el-form-item label=\"计划完成\" prop=\"expectCompleteTime\" ref=\"expectCompleteTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('expectCompleteTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.expectCompleteTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"person_content\">\n      <div class=\"title\"><i class=\"el-icon-user-solid\" /> 人员信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"personForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"isReadOrNull('signed')\">\n            <el-form-item label=\"签发\" prop=\"signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" :required=\"isRequired('signed')\">\n              <el-input size=\"small\" v-model=\"ruleForm.signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('proofread')\">\n            <el-form-item label=\"核稿\" prop=\"proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" :required=\"isRequired('proofread')\">\n              <el-input size=\"small\" v-model=\"ruleForm.proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('editor')\">\n            <el-form-item label=\"编辑\" prop=\"editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" :required=\"isRequired('editor')\">\n              <el-input size=\"small\" v-model=\"ruleForm.editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <!-- 告知单 开始 -->\n    <div class=\"ext_content\" v-if=\"isRead('informForm')\">\n      <div class=\"title\">\n        <i class=\"el-icon-s-order\" /> 通报内容\n      </div>\n      <el-form :model=\"informForm\" :rules=\"informRules\" ref=\"informForm\" label-width=\"120px\" size=\"medium\" class=\"demo-informForm\" style=\"margin-top: 10px\">\n<!--        <el-form-item label=\"事件来源\" prop=\"eventSource\" v-if=\"!isHide('eventSource')\" ref=\"eventSource\">\n          <el-input size=\"small\" v-model=\"informForm.eventSource\" placeholder=\"请填写事件来源\" maxlength=\"50\" :disabled=\"!isWriteOrNull('eventSource')\" />\n        </el-form-item>\n        <el-form-item label=\"事件类型\" prop=\"eventType\" v-if=\"!isHide('eventType')\" ref=\"eventType\">\n          <el-cascader size=\"small\" v-model=\"informForm.eventType\" :options=\"threatenDict\" clearable placeholder=\"请选择事件类型\" style=\"width: 100%\" :props=\"{ label: 'dictLabel', value: 'dictValue' }\" :disabled=\"!isWriteOrNull('eventType')\">\n            <template slot-scope=\"{ node, data }\">\n              <span>{{ data.dictLabel }}</span>\n              <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n            </template>\n          </el-cascader>\n          &lt;!&ndash;          <el-select v-model=\"informForm.eventType\" placeholder=\"请选择事件类型\">\n                      <el-option\n                        v-for=\"item in eventTypeOption\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>&ndash;&gt;\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"eventLevel\" v-if=\"!isHide('eventLevel')\" ref=\"eventLevel\">\n          <el-select size=\"small\" v-model=\"informForm.eventLevel\" placeholder=\"请选择事件级别\" :disabled=\"!isWriteOrNull('eventLevel')\">\n            <el-option\n              v-for=\"item in eventLevelOption\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-form-item>-->\n        <el-form-item label=\"事件通报\" prop=\"eventNotification\" ref=\"eventNotification\" v-if=\"!isHide('eventNotification')\" :required=\"isRequired('eventNotification')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.eventNotification\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写事件通报内容\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('eventNotification')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"处理建议\" prop=\"handleOpinion\" ref=\"handleOpinion\" v-if=\"!isHide('handleOpinion')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.handleOpinion\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写处理建议\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('handleOpinion')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述附件\" prop=\"describeFileUrl\" ref=\"describeFileUrl\" v-if=\"!isHide('describeFileUrl')\">\n          <file-upload v-model=\"informForm.describeFileUrl\"\n                       :disUpload=\"!isWrite('informForm') || !isWriteOrNull('describeFileUrl')\"\n                       :limit=\"5\"\n                       :file-type=\"['doc', 'docx', 'xlsx','xls', 'ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                       @change=\"refreshWord\"\n          />\n        </el-form-item>\n        <el-form-item label=\"发\" prop=\"remark8\" ref=\"remark8\" v-if=\"!isHideOrNull('remark8')\" :show-message=\"false\">\n          <el-input size=\"small\" type=\"text\" v-model=\"informForm.remark8\" placeholder=\"\" show-word-limit maxlength=\"50\" :disabled=\"!isWriteOrNull('remark8')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    <!-- 告知单 结束 -->\n\n    <!-- 告知单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('informFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 告知单审核签名签章 结束 -->\n\n    <!-- 通报对象 开始 -->\n    <div class=\"ext_content\" v-if=\"true\">\n      <div class=\"title\">\n        <i class=\"el-icon-aim\" /> 通报对象\n        <div class=\"title-right\"><div style=\"cursor: pointer;\" @click=\"addDept\" v-if=\"setting && setting.opType === '-1'\">+新增单位</div></div>\n      </div>\n      <report-target ref=\"reportTarget\" :dept-data-list=\"reportTargetForm\" :setting=\"setting\" style=\"margin-top: 10px;\" @refreshWord=\"refreshWord\" @activeNameChange=\"reportTargetActiveChange\"/>\n    </div>\n    <!-- 通报对象 结束 -->\n\n    <!-- 反馈单 开始 -->\n        <div class=\"ext_content\" v-if=\"isRead('feedbackForm') && reportTargetForm && reportTargetForm.length > 0\">\n          <div class=\"title\">\n            <i class=\"el-icon-s-order\" /> 反馈单信息\n          </div>\n          <el-form :model=\"currentDeptData\" :rules=\"feedbackRules\" ref=\"feedbackForm\" label-width=\"120px\" size=\"medium\" class=\"demo-feedbackForm\" style=\"margin-top: 10px\">\n            <el-form-item label=\"处置标题\" prop=\"remark7\" v-if=\"!isHideOrNull('remark7')\" :required=\"isRequired('remark7')\" :show-message=\"false\">\n              <el-input :disabled=\"!isWriteOrNull('remark7')\" size=\"small\" v-model=\"currentDeptData.handleTitle\" placeholder=\"请输入处置标题\" maxlength=\"50\" @blur=\"refreshWord\" />\n            </el-form-item>\n            <div style=\"display: flex;\" v-if=\"!isWrite('feedbackForm')\">\n    <!--          <el-form-item label=\"事件处理单文号\" prop=\"workNo\" v-if=\"!isHide('workNo')\" style=\"width: 50%\" ref=\"workNo\">\n                <el-input size=\"small\" v-model=\"feedbackForm.workNo\" placeholder=\"\" :disabled=\"true\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"反馈日期\" prop=\"feedbackDate\" v-if=\"!isHide('feedbackDate')\" style=\"width: 50%\" ref=\"feedbackDate\">\n                <el-input size=\"small\" v-model=\"feedbackForm.feedbackDate\" placeholder=\"\" :disabled=\"!isWriteOrNull('feedbackDate')\" @blur=\"refreshWord\"></el-input>\n              </el-form-item>-->\n            </div>\n            <el-form-item label=\"处理结果\" prop=\"eventDescription\" ref=\"eventDescription\" v-if=\"!isHide('eventDescription')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.eventDescription\" placeholder=\"请填写处理结果\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('eventDescription')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"处理情况\" prop=\"handleSituation\" ref=\"handleSituation\" v-if=\"!isHide('handleSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.handleSituation\" placeholder=\"请填写处理情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('handleSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"其他情况\" prop=\"otherSituation\" ref=\"otherSituation\" v-if=\"!isHide('otherSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.otherSituation\" placeholder=\"请填写其他情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('otherSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"附件\" prop=\"feedbackFileUrl\" ref=\"feedbackFileUrl\" v-if=\"!isHide('feedbackFileUrl')\">\n              <file-upload v-model=\"currentDeptData.feedbackFileUrl\"\n                           :disUpload=\"!isWrite('feedbackForm') || !isWriteOrNull('feedbackFileUrl')\"\n                           :limit=\"5\"\n                           :file-type=\"['doc','docx', 'xls', 'xlsx','ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                           @change=\"refreshWord\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n    <!-- 反馈单 结束 -->\n\n    <!-- 反馈单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('feedbackFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 反馈单审核签名签章 结束 -->\n\n    <!--  业务系统-资产选择  -->\n    <el-dialog v-if=\"applicationDialog\" title=选择资产 :visible.sync=\"applicationDialog\" class=\"application_dialog\" width=\"80%\" append-to-body>\n      <application-select v-if=\"applicationDialog\" ref=\"applicationSelect\" :value=\"currentApplicationSelect\" :isMultipleSelect=\"true\" :application-list=\"applicationList\" @cancel=\"applicationDialog = false\" @applicationSelected=\"applicationSelected\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitApplicationSelect\">确 定</el-button>\n        <el-button @click=\"applicationDialog = false\">取 消</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog v-if=\"openEventDetailDialog\" title=\"查看事件详情\" :visible.sync=\"openEventDetailDialog\" width=\"80%\" append-to-body>\n      <alarm-detail v-if=\"openEventDetailDialog && currentEventType == 3\" @openDetail=\"openDetail\" :asset-data=\"assetData\"/>\n      <new-addloophole v-if=\"openEventDetailDialog && currentEventType == 1\" :loophole-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n      <new-addwebvuln v-if=\"openEventDetailDialog && currentEventType == 2\" :webvuln-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n    </el-dialog>\n\n    <!--  事件选择弹窗-开始  -->\n    <select-event v-if=\"openEventSelectDialog\" :selected-event-ids=\"ruleForm.eventIds\" :dest-ips=\"ruleForm.associatedIps\" :open.sync=\"openEventSelectDialog\" :setting=\"setting\" :threaten-dict=\"threatenDict\" :current-event-type=\"parseInt(currentEventType)\" :severity-options=\"severityOptions\" @cancel=\"openEventSelectDialog=false\" @selected=\"handleEventSelected\"/>\n    <!--  事件选择弹窗-结束  -->\n  </div>\n</template>\n\n<script>\nimport { getAllUserListByDept,getAllUserList } from \"../../../api/system/user\"\nimport { addOrder } from \"../../../api/tool/work\";\nimport DeptSelect from \"../../components/select/deptSelect\";\nimport RelevancyGapInfo from \"./relevancyGapInfo\";\nimport RelevancyAlarmInfo from \"./relevancyAlarmInfo\";\nimport relevancyWebvulnInfo from \"./relevancyWebvulnInfo\";\nimport {listWebVuln} from \"@/api/monitor2/webvuln\"; //web漏洞\nimport {getVulnDealList} from \"@/api/monitor2/assetFrailty\"; //IP漏洞\nimport {listAlarm} from \"@/api/threaten/threatenWarn\"; //威胁事件\nimport comMixin from '@/views/zeroCode/workFlow/workFlowForm/mixin'\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getApplicationListByCondition,getBusinessInfo,getApplicationDetails} from \"@/api/safe/application\";\nimport { assetSelectList,getInfoByids,listByApplicationId } from \"@/api/assetSelect/assetSelect.js\";\nimport ApplicationSelect from \"./applicationSelect.vue\";\nimport SelectedEventList from \"./selected_event_list.vue\";\nimport SelectEvent from \"./select_event.vue\";\nimport AlarmDetail from \"@/views/basis/securityWarn/alarmDetail.vue\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport newAddwebvuln from \"@/views/frailty/webvuln/newAddwebvuln\";\nimport reportTarget from \"@/views/todoItem/todo/report_target.vue\";\nimport {listDept} from \"@/api/system/dept\";\n\nexport default {\n  name: \"createWork\",\n  mixins: [comMixin],\n  dicts: [\n    'loophole_category',\n    'threaten_alarm_type',\n    'work_order_report_type',\n    'work_order_urgency',\n    'work_order_public',\n    'work_order_severity_level',\n  ],\n  components: {AlarmDetail, ApplicationSelect, RelevancyAlarmInfo, RelevancyGapInfo, DeptSelect,relevancyWebvulnInfo,\n    newAddloophole,newAddwebvuln,SelectedEventList,SelectEvent,reportTarget},\n  props: {\n    workType: {\n      type: String,\n      require: true\n    },\n    mId: {\n      type: Number,\n      require: true\n    }\n  },\n  data() {\n    return {\n      componentName: 'createWork',\n      scrollCache: null,\n      openEventSelectDialog: false,\n      assetData: {},\n      openEventDetailDialog: false,\n      currentEventType: 0,\n      queryEventParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      eventListLoading: false,\n      eventList: [],\n      total: 0,\n      ruleForm: {\n        workName: null,\n        complateTime: null,\n        handleDept: null,\n        handleUser: null,\n        workType: null,\n        eventType: null,\n        associatedIps: [],\n      },\n      dataForm: {\n      },\n      dataRule:{},\n      selectedEvent: [],\n      tempSelectedEvent: [],\n      severityOptions: {\n        '1': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '2': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '3': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},\n        ],\n      },\n      threatenDict: [],\n      rules: {\n        workName: [\n          { required: true, message: '请输入通报名称', trigger: 'blur' },\n          { min: 3, max: 80, message: '长度在 3 到 80 个字符', trigger: 'blur' }\n        ],\n        applicationId: [\n          { required: false, message: '请选择业务系统', trigger: 'change' }\n        ],\n        eventCreateTime: [\n          { required: true, message: '请选择事件发生时间', trigger: 'change' }\n        ],\n        expectCompleteTime: [\n          { required: true, message: '计划完成时间', trigger: 'change' }\n        ],\n        handleDept: [\n          { required: true, message: '请选择处置单位', trigger: 'change' }\n        ],\n        handleUser: [\n          { required: true, message: '请选择处置人', trigger: 'change' }\n        ],\n        handleUserPhone: [\n          { required: false, message: '请输入处置人电话', trigger: 'blur' }\n        ],\n        remark6: [\n          { required: true, message: '请选择通报类型', trigger: 'change' }\n        ],\n      },\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      tableData: [],\n      alertTableData: [],\n      handleOption: [],\n      manageOption: [],\n      handleOptionCopy: [],\n      manageOptionCopy: [],\n      informForm: {},\n      informRules: {\n        eventSource: [\n          {required: true, message: '请输入事件来源', trigger: 'blur'}\n        ],\n        handleOpinion: [\n          {required: false, message: '请输入处理建议', trigger: 'blur'}\n        ],\n        eventNotification: [\n          {required: false, message: '请输入事件通报内容', trigger: 'blur'}\n        ],\n        describeFileUrl: [\n          {required: false, message: '请上传描述附件', trigger: 'change'}\n        ],\n        /*remark8: [\n          {required: true, message: '请输入', trigger: 'blur'}\n        ],*/\n      },\n      feedbackForm: {},\n      feedbackRules: {\n        eventDescription: [\n          {required: false, message: '请输入处理结果', trigger: 'blur'}\n        ],\n        handleSituation: [\n          {required: false, message: '请输入处理情况', trigger: 'blur'}\n        ]\n      },\n      applicationList: [],\n      applicationDialog: false,\n      macAipList: [],\n      assetIds: [],\n      currentApplicationSelect: null,\n      eventTypeOption: [\n        {\n          label: '漏洞',\n          value: 1\n        },\n        {\n          label: '后门',\n          value: 2\n        },\n        {\n          label: '外链',\n          value: 3\n        }\n      ],\n      eventLevelOption: [\n        {\n          label: 'Ⅰ级',\n          value: '1'\n        },\n        {\n          label: 'Ⅱ级',\n          value: '2'\n        },\n        {\n          label: 'Ⅲ级',\n          value: '3'\n        },\n        {\n          label: 'Ⅳ级',\n          value: '4'\n        },\n      ],\n      applicationInfo: {},\n      isChangeEventType: false,\n      eventTypeBtnKey: 0,\n      isFromEvent: false,\n      isChangeForm: false,\n      formOperatesRecord: [],\n      reportTargetForm: [],\n      reportTargetActive: '0',\n    }\n  },\n  watch: {\n    informForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    ruleForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    feedbackForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    'ruleForm.handleDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getUserList();\n        }\n      }\n    },\n    'ruleForm.manageDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getManageUserList();\n        }\n      }\n    },\n    'ruleForm.applicationId': {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        if(newVal && newVal != oldVal){\n          if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n            this.getApplicationDetails();\n          }\n        }\n      }\n    },\n    'setting.row': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length>0){\n          if(val.eventType && val.eventType != 0){\n            this.currentEventType = val.eventType;\n          }\n          if(val.createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val.createTime);\n          }\n          /*if(val.id){\n            this.$set(this.ruleForm, 'eventIds', [val.id]);\n          }*/\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          if(val.deptId || (val.eventType === 3 && val.deptIdStr)){\n            deptId = val.deptId?val.deptId:val.deptIdStr.split(',')[0];\n            deptName = val.deptName.split(',')[0];\n          }\n          this.reportTargetForm = [\n            {\n              deptName: deptName,\n              deptId: deptId,\n              handleTitle: null,\n              eventDescription: null,\n              handleSituation: null,\n              otherSituation: null,\n              feedbackFileUrl: null,\n              formData: [{\n                handleDept: deptId,\n                handleUser: '',\n                applicationId: val.businessApplications?val.businessApplications[0].assetId:null,\n                assetName: val.businessApplications?val.businessApplications[0].assetName:null,\n                loginUrl: val.businessApplications?val.businessApplications[0].url:null,\n                manager: val.businessApplications?parseInt(val.businessApplications[0].manager):null,\n                phone: val.businessApplications?val.businessApplications[0].managerPhone:null,\n                eventData: {\n                  'type1': val.eventType === 1?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type2': val.eventType === 2?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type3': val.eventType === 3?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type4': val.eventType === 4?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[]\n                }\n              }]\n            }\n          ];\n\n          //默认查业务系统\n          /*this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: this.setting && this.setting.row && this.setting.row.businessApplications && this.setting.row.businessApplications.length>0?this.setting.row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.rows': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && val.length>0){\n          if(val[0].eventType && val[0].eventType != 0){\n            this.currentEventType = val[0].eventType;\n          }\n          if(val[0].createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val[0].createTime);\n          }\n          let eventIds = val.map(item => item.id);\n          this.$set(this.ruleForm, 'eventIds', eventIds);\n          let row = val[0];\n          this.reportTargetForm = [];\n          let deptSet = new Set();\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          val.forEach(item => {\n            if(!item.deptId){\n              item.deptId = deptId;\n              item.deptName = deptName;\n            }\n            if(item.deptIdStr){\n              item.deptId = item.deptIdStr.split(',')[0];\n            }\n            deptSet.add({\n              deptName: item.deptName.split(',')[0],\n              deptId: item.deptId,\n            });\n          })\n          deptSet.forEach(item => {\n            let matchArr = val.filter(valItem => valItem.deptId === item.deptId);\n            let first = matchArr[0];\n            this.reportTargetForm.push(\n              {\n                deptName: item.deptName,\n                deptId: item.deptId,\n                handleTitle: null,\n                eventDescription: null,\n                handleSituation: null,\n                otherSituation: null,\n                feedbackFileUrl: null,\n                formData: [\n                  {\n                    handleDept: item.deptId,\n                    applicationId: first.businessApplications?first.businessApplications[0].assetId:null,\n                    assetName: first.businessApplications?first.businessApplications[0].assetName:null,\n                    loginUrl: first.businessApplications?first.businessApplications[0].url:null,\n                    manager: first.businessApplications?parseInt(first.businessApplications[0].manager):null,\n                    phone: first.businessApplications?first.businessApplications[0].managerPhone:null,\n                    eventData: {\n                      'type1': this.currentEventType===1?matchArr.map(item => ({type: 1, eventId: item.id})):[],\n                      'type2': this.currentEventType===2?matchArr.map(item => ({type: 2, eventId: item.id})):[],\n                      'type3': this.currentEventType===3?matchArr.map(item => ({type: 3, eventId: item.id})):[],\n                      'type4': this.currentEventType===4?matchArr.map(item => ({type: 4, eventId: item.id})):[]\n                    }\n                  }\n                ]\n              }\n            )\n          })\n          /*//默认查业务系统\n          this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: row.businessApplications && row.businessApplications.length>0?row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.formData': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length !== 0){\n          this.initForm();\n        }\n      }\n    },\n    'setting.flowTaskOperatorRecordList': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(!this.setting.flowTemplateJson){\n          return;\n        }\n        let allMatchNodes = [];\n        this.loopGetFlowNode(this.setting.flowTemplateJson,'end',allMatchNodes);\n        let allFormOperates = {};\n        allMatchNodes.map(item => item.properties.formOperates).forEach(item => {\n          item.forEach(arrItem => {\n            if(!allFormOperates[arrItem.id]){\n              allFormOperates[arrItem.id] = {\n                read: arrItem.read,\n                write: arrItem.write\n              }\n            }else {\n              if(!allFormOperates[arrItem.id].read){\n                allFormOperates[arrItem.id].read = arrItem.read;\n              }\n              if(!allFormOperates[arrItem.id].write){\n                allFormOperates[arrItem.id].write = arrItem.write;\n              }\n            }\n          })\n        });\n        this.formOperatesRecord = Object.entries(allFormOperates).map(([id, value]) => ({\n          id: id,\n          read: value.read,\n          write: value.write\n        }));\n      }\n    },\n    currentEventType: {\n      handler(newVal,oldVal){\n        this.$set(this.ruleForm,'workType',newVal);\n        /* if(newVal){\n          this.$nextTick(() => {\n            this.resetQuery();\n          })\n        } */\n      }\n    },\n    'ruleForm.handleUser': {\n      handler(newVal,oldVal){\n\n      }\n    },\n    reportTargetForm: {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        this.$emit('reportDataChange',newVal);\n      }\n    },\n  },\n  created() {\n    getMulTypeDict({\n      dictType:'threaten_alarm_type'\n    }).then(res=>{\n      this.threatenDict=res.data;\n      // TODO  暂定为获取两层  当前方法有问题 其它版本也有修改需要等待后续修改\n      /* if (this.threatenDict.length > 0 && this.threatenDict[0].children.length > 0) {\n        this.ruleForm.eventType = this.threatenDict[0].dictValue + '/' + this.threatenDict[0].children[0].dictValue\n      } */\n      this.threatenDict.forEach(item=>{\n        item.value=item.dictValue;\n        item.label=item.dictLabel;\n        item.children.forEach(cItem=>{\n          cItem.value=cItem.dictValue;\n          cItem.label=cItem.dictLabel;\n        })\n      })\n    });\n  },\n  mounted() {\n    this.$nextTick(() => {\n      //默认值\n      this.setFormDefault();\n    })\n  },\n  computed: {\n    categoryDict(){\n      if(this.currentEventType == 1 || this.currentEventType == 2){\n        return this.dict.type.loophole_category;\n      }else if(this.currentEventType == 3){\n        return this.threatenDict;\n      }\n    },\n    reportOptions(){\n      return this.dict.type.work_order_report_type;\n    },\n    urgencyOptions(){\n      return this.dict.type.work_order_urgency;\n    },\n    isPublicOptions(){\n      return this.dict.type.work_order_public;\n    },\n    severityLevelOptions(){\n      return this.dict.type.work_order_severity_level;\n    },\n    // 获取当前部门数据（安全访问）\n    currentDeptData() {\n      return this.reportTargetForm && this.reportTargetForm.length>0 ? this.reportTargetForm[parseInt(this.reportTargetActive)] : null;\n    }\n  },\n  methods: {\n    initForm(){\n      this.setFormData(this.ruleForm, 'applicationId');\n      this.currentApplicationSelect = {\n        application: {\n          assetId: this.setting.formData.applicationId,\n        },\n        selected: this.setting.formData.remark1?JSON.parse(this.setting.formData.remark1).map(se => {return {assetId: se}}):[],\n      }\n      this.setFormData(this.ruleForm, 'associatedIps');\n      this.setFormData(this.ruleForm, 'eventIds');\n      this.$set(this.queryEventParams,\"ids\",this.setting.formData.eventIds);\n      this.setFormData(this.ruleForm, 'workType');\n      this.currentEventType = this.setting.formData.workType;\n      this.setFormData(this.ruleForm, 'workName');\n      this.setFormData(this.ruleForm, 'loginUrl');\n      this.setFormData(this.ruleForm, 'handleDept');\n      this.setFormData(this.ruleForm, 'manageDept');\n      this.setFormData(this.ruleForm, 'handleDeptName');\n      this.setFormData(this.ruleForm, 'manageDeptName');\n      this.setFormData(this.ruleForm, 'handleUser');\n      this.$set(this.ruleForm,\"manageUser\",this.setting.formData.manageUser?parseInt(this.setting.formData.manageUser) : null);\n      this.setFormData(this.ruleForm, 'handleUserPhone');\n      this.setFormData(this.ruleForm, 'handleUserName');\n      this.setFormData(this.ruleForm, 'manageUserName');\n      this.setFormData(this.ruleForm, 'remark6');\n      this.setFormData(this.ruleForm, 'issue');\n      this.setFormData(this.ruleForm, 'expectCompleteTime');\n      this.setFormData(this.ruleForm, 'eventCreateTime');\n      this.setFormData(this.ruleForm, 'applicationName');\n      this.$set(this.ruleForm,\"urgency\",this.setting.formData.urgency?this.setting.formData.urgency.toString() : '1');\n      this.setFormData(this.ruleForm, 'isPublic');\n      this.setFormData(this.ruleForm, 'severityLevel');\n      this.setFormData(this.ruleForm, 'reportDate');\n      this.setFormData(this.ruleForm, 'period');\n      this.setFormData(this.ruleForm, 'signed');\n      this.setFormData(this.ruleForm, 'proofread');\n      this.setFormData(this.ruleForm, 'editor');\n\n      this.setFormData(this.informForm, 'describeFileUrl');\n      this.setFormData(this.informForm, 'eventLevel');\n      this.setFormData(this.informForm, 'eventNotification');\n      this.setFormData(this.informForm, 'eventSource');\n      // this.setFormData(this.informForm, 'workType');\n      this.setFormData(this.informForm, 'handleOpinion');\n      this.setFormData(this.informForm, 'eventType');\n      this.setFormData(this.informForm, 'remark8');\n      this.setFormData(this.feedbackForm, 'eventDescription');\n      this.setFormData(this.feedbackForm, 'handleSituation');\n      this.setFormData(this.feedbackForm, 'otherSituation');\n      this.setFormData(this.feedbackForm, 'workNo');\n      this.setFormData(this.feedbackForm, 'feedbackDate');\n      this.setFormData(this.feedbackForm, 'feedbackFileUrl');\n      this.setFormData(this.feedbackForm, 'remark7');\n      //通报对象赋值\n      this.reportTargetForm = this.setting.formData.reportTargetForm;\n\n      this.refreshWord();\n    },\n    setFormData(form, column){\n      let data = this.setting.formData[column];\n      if('eventType' === column){\n        if(data && data.indexOf(\"/\")!==-1){\n          data = data.split(\"/\");\n        }\n      }\n      this.$set(form, column, data);\n    },\n    setFormDefault(){\n      if(!this.ruleForm.urgency){\n        this.$set(this.ruleForm,'urgency','1');\n      }\n      if(!this.ruleForm.reportDate){\n        this.$set(this.ruleForm,'reportDate',this.jnpf.toDate(new Date(), 'yyyy-MM-dd HH:mm:ss'));\n      }\n      if(!this.ruleForm.signed){\n        this.$set(this.ruleForm,'signed','叶琛');\n      }\n      if(!this.ruleForm.proofread){\n        this.$set(this.ruleForm,'proofread','王亮');\n      }\n      if(!this.ruleForm.editor){\n        this.$set(this.ruleForm,'editor','付扬');\n      }\n    },\n    submitForm(formName) {\n      const that = this\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          that.ruleForm.businessId = that.mId\n          that.ruleForm.workType = that.workType\n          addOrder(that.ruleForm).then(res => {\n            if (res.code === 200) {\n              this.$message({\n                message: '通报创建成功',\n                type: 'success'\n              })\n              that.resetForm()\n            } else {\n              this.$message.error('通报创建失败')\n            }\n          })\n        } else {\n          console.log('error submit!!');\n          return false;\n        }\n      })\n    },\n    resetForm() {\n      this.$emit('closeWork')\n    },\n    openFocus() {\n      if (!this.ruleForm.handleDept) {\n        this.$message.warning('请先选择处理部门！');\n      }\n    },\n    handleQuery(){\n      this.queryEventParams.pageNum = 1;\n      this.total = 0;\n      this.getEventDataList();\n    },\n    resetQuery(){\n      this.queryEventParams={\n        pageNum: 1,\n        pageSize: this.queryEventParams.pageSize\n      };\n      this.getEventDataList();\n    },\n    handleEventDetail(row){\n      this.assetData= {...row};\n      this.openDetail(true);\n    },\n    openDetail(val){\n      this.openEventDetailDialog = val;\n    },\n    eventBtnClick(type,evt){\n      if(this.currentEventType != type && this.currentEventType != 0){\n        this.$confirm('切换事件类型将清空之前选择的事件, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.selectedEvent = [];\n          this.ruleForm.eventIds = [];\n          this.currentEventType = type;\n          this.$refs.eventList && this.$refs.eventList.clearSelection();\n          this.isChangeEventType = true;\n        }).catch(() => {\n          this.eventTypeBtnKey++;\n        });\n      }else {\n        if(this.currentEventType != 0 && this.currentEventType == type){\n          if(this.isFromEvent){\n            //从事件发起，不允许取消\n            return false;\n          }\n          this.$confirm('取消选中事件类型将清空之前选择的事件, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.eventList = [];\n            this.total = 0;\n            this.selectedEvent = [];\n            this.ruleForm.eventIds = [];\n            this.currentEventType = 0;\n            this.$refs.eventList && this.$refs.eventList.clearSelection();\n            this.isChangeEventType = true;\n            const target = evt.target;\n            if(target){\n              if(target.nodeName === 'SPAN'){\n                if(target.parentNode){\n                  target.parentNode.blur();\n                }\n              }\n              target.blur();\n            }\n          }).catch(() => {\n\n          });\n        }else {\n          this.currentEventType = type;\n        }\n      }\n    },\n    flowStateFormatter(row, column, cellValue, index){\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value==cellValue);\n      if(match){\n        name = match.label;\n      }\n      return name;\n    },\n    getEventDataList(){\n      if(!this.currentEventType && (!this.dataForm.eventIds || this.dataForm.eventIds.length<1)){\n        return false;\n      }\n      if(this.currentEventType == 0){\n        return false;\n      }\n      this.eventListLoading = true;\n      this.requestList().then(response => {\n        let resData = this.convertEventData(response.rows);\n        this.eventList = resData;\n        this.total= response.total;\n        this.eventListLoading = false;\n      })\n    },\n    //转换\n    convertEventData(srcDataList){\n      if(!srcDataList){\n        return [];\n      }\n      if(this.currentEventType == 3){\n        return srcDataList.map(item => {\n          return {\n            id: item.id,\n            title: item.threatenName,\n            category: item.threatenType,\n            severity: item.alarmLevel,\n            webUrl: item.destIp,\n            handleStatus: item.handleState,\n            workState: item.orderState,\n            dataSource: item.dataSource,\n            scanNum: item.alarmNum,\n            updateTime: item.updateTime,\n            createTime: item.createTime,\n            hostPort: item.destPort,\n            flowState: item.flowState\n          }\n        })\n      }else if(this.currentEventType == 1){\n        return srcDataList.map(item => {\n          item.webUrl = item.hostIp;\n          return item;\n        });\n      }else {\n        return srcDataList;\n      }\n\n    },\n    requestList(){\n      let queryParams = this.convertQueryParams(this.queryEventParams);\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return getVulnDealList(queryParams);\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return listWebVuln(queryParams);\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return listAlarm(queryParams);\n      }\n    },\n    convertQueryParams(srcParams){\n      if(!this.isRead('list_select')){\n        srcParams.ids = this.dataForm.eventIds;\n      }\n      if(this.currentEventType == 3){\n        return {\n          pageNum: srcParams.pageNum,\n          pageSize: srcParams.pageSize,\n          threatenName: srcParams.title,\n          threatenType: srcParams.category?srcParams.category.join('/'):null,\n          alarmLevel: srcParams.severity,\n          destIp: srcParams.webUrl,\n          destPort: srcParams.hostPort,\n          handleState: srcParams.handleStatus,\n          orderState: srcParams.workState,\n          dataSource: srcParams.dataSource,\n          createTime: srcParams.createTime,\n          ids: srcParams.ids\n        }\n      }else if(this.currentEventType == 1){\n        srcParams.hostIp = srcParams.webUrl;\n        return srcParams;\n      }else {\n        return srcParams;\n      }\n    },\n    handleEventSelected(val, event){\n      if(!this.ruleForm.eventIds){\n        this.ruleForm.eventIds = [];\n      }\n      let tempArr = [...this.ruleForm.eventIds];\n      tempArr.push(...val);\n      if (event) {\n        this.ruleForm.eventType = event\n      }\n      this.$set(this.ruleForm,'eventIds',tempArr);\n      this.selectedEvent = this.ruleForm.eventIds;\n    },\n    handleSeverityTag(severity,key){\n      if(!severity){\n        return '未知';\n      }\n      if(this.severityOptions[this.currentEventType.toString()]){\n        let matchItem = this.severityOptions[this.currentEventType.toString()].find(item => item.value == severity);\n        if(!matchItem){\n          return '未知';\n        }\n        return matchItem[key];\n      }\n      return '';\n    },\n    openApplicationSelect(){\n      /*getApplicationListByCondition({\n        // ipv4: this.getIpv4()\n        ipv4: null\n      }).then(res=>{\n        this.applicationList=res.data;\n        this.applicationDialog = true;\n      });*/\n      this.applicationDialog = true;\n    },\n    getIpv4(){\n      if(!this.setting.row && (!this.setting.rows || this.setting.rows.length<1)){\n        return null;\n      }\n      let row = this.setting.row || this.setting.rows[0];\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return row.hostIp;\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return row.webUrl;\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return row.destIp;\n      }else if(this.currentEventType == 4){\n        //弱口令漏洞\n        return row.hostIp;\n      }\n    },\n    applicationSelected(data){\n      this.currentApplicationSelect = data;\n      this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n      this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n      this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n      this.$set(this.ruleForm,'applicationId',data.application.assetId);\n      this.ruleForm.applicationName = data.application.assetName;\n      this.applicationDialog = false;\n      this.refreshWord();\n    },\n    submitApplicationSelect(){\n      this.$refs.applicationSelect.submit();\n    },\n    getApplicationDetails(){\n      let _that = this;\n      getApplicationDetails(this.ruleForm.applicationId).then(res => {\n        if(res.data.url){\n          this.$set(_that.ruleForm,'loginUrl',res.data.url);\n        }\n        if(res.data.dept_id){\n          this.$set(_that.ruleForm,'handleDept',res.data.dept_id);\n          this.$set(_that.ruleForm,'manageDept',res.data.dept_id);\n\n          this.$nextTick(() => {\n            this.getManageUserList();\n          })\n        }\n        this.applicationInfo = res.data;\n      })\n    },\n    eventTypeBtnDisable(){\n      if(\"-1\" == this.setting.opType){\n        return false;\n      }\n      if(this.setting.row && this.setting.row.eventType){\n        return true;\n      }\n      return false;\n    },\n    getManageUserList() {\n      // this.ruleForm.handleUser = ''\n      this.manageOption = []\n      if (this.ruleForm.manageDept) {\n        getAllUserList({\n          // deptId: this.ruleForm.manageDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.manageOption = res.rows;\n            this.manageOptionCopy = [...this.manageOption];\n            this.$nextTick(() => {\n              /*if(this.manageOption && !this.manageOption.find(manageUserItem => manageUserItem.userId == this.ruleForm.manageUser)){\n                this.ruleForm.manageUser = '';\n                this.ruleForm.handleUserPhone = '';\n              }*/\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  /*if(this.manageOption.find(manageUserItem => manageUserItem.userId == manager)){\n                    // this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                    this.$forceUpdate();\n                  }*/\n                  this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                  this.$forceUpdate();\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    getUserList() {\n      // this.ruleForm.handleUser = ''\n      this.handleOption = []\n      if (this.ruleForm.handleDept) {\n        getAllUserListByDept({\n          deptId: this.ruleForm.handleDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.handleOption = res.rows;\n            this.handleOptionCopy = [...this.handleOption];\n            this.$nextTick(() => {\n              if(this.handleOption && !this.handleOption.find(handleUserItem => handleUserItem.userId == this.ruleForm.handleUser)){\n                this.ruleForm.handleUser = '';\n                this.$set(this.ruleForm,'handleUser','');\n                // this.ruleForm.handleUserPhone = '';\n              }\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  if(this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                    if(this.handleOption && this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                      this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    }\n                  }\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    manageUserFilter(val){\n      if(val){\n        this.manageOption = this.manageOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.manageOption = [...this.manageOptionCopy];\n      }\n    },\n    handleUserFilter(val){\n      if(val){\n        this.handleOption = this.handleOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.handleOption = [...this.handleOptionCopy];\n      }\n    },\n    manageUserVisibleChange(){\n      this.manageOption = [...this.manageOptionCopy];\n    },\n    handleUserVisibleChange(){\n      this.handleOption = [...this.handleOptionCopy];\n    },\n    manageUserChange(row){\n      if(row){\n        let matchUser = this.manageOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserChange(row){\n      if(row){\n        let matchUser = this.handleOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserPhoneInput(){\n      this.$forceUpdate();\n    },\n    validateAllForm() {\n      /*if(this.isRead('list_select') && (this.currentEventType && this.currentEventType !== 0)){\n        if(!this.ruleForm.eventIds || this.ruleForm.eventIds.length < 1){\n          this.$modal.msgError('请选择关联事件');\n          this.$refs.event_list && this.$refs.event_list.scrollIntoView();\n          return new Promise((resolve, reject) => {\n            reject();\n          });\n        }\n      }*/\n\n      let validateForms = this.getValidateForm();\n      if(!validateForms || validateForms.length < 1){\n        return new Promise((resolve, reject) => {\n          resolve();\n        });\n      }\n      return Promise.all(validateForms);\n    },\n    validateForm(formName){\n      return new Promise((resolve, reject) => {\n        if(!this.$refs[formName]){\n          reject();\n        }\n        this.$refs[formName].validate((valid) => {\n          if (valid) {\n            resolve()\n          } else {\n            reject()\n          }\n        })\n      })\n    },\n    getValidateForm(){\n      let res = [];\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            res.push(this.validateForm(formName));\n          }\n        })\n      }\n      let refReportTarget = this.$refs.reportTarget;\n      if(refReportTarget){\n        res.push(...refReportTarget.validate());\n      }\n      return res;\n    },\n    getActiveForm(){\n      let res = [];\n      let flowVariable = this.setting.flowVariable;\n      if(flowVariable && flowVariable.length > 0){\n        let matchItem = flowVariable.find(item => item.key == 'formNames');\n        if(matchItem && matchItem.value){\n          let names = matchItem.value.split(',');\n          names.forEach(name => {\n            res.push(name);\n          })\n        }\n      }\n      return res;\n    },\n    handleColumn(column){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==column);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isRead(name){\n      let formOperates = [];\n      if(!this.setting.readonly){\n        formOperates = this.setting.formOperates;\n      }else {\n        formOperates = this.formOperatesRecord;\n      }\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isReadOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return false;\n      }\n    },\n    isWriteOrNull(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return false;\n      }\n    },\n    isWrite(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return true;\n      }\n    },\n    isHideOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return true;\n      }\n    },\n    isHide(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return false;\n      }\n    },\n    beforeSubmit(){\n      //动态表单\n      this.dataForm = {};\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            this.dataForm = {...this.dataForm, ...this[formName]};\n          }\n        })\n      }\n      if(this.setting.flowVariable){\n        let match = this.setting.flowVariable.find(item => item.key == 'workNoPrefix');\n        if(match){\n          this.dataForm.workNoPrefix = match.value;\n        }\n      }\n      /*      if (this.dataForm.eventType) {\n              this.dataForm.eventType = this.dataForm.eventType.join('/');\n            }*/\n      if(this.dataForm && this.dataForm.eventType){\n        if(Array.isArray(this.dataForm.eventType)){\n          this.dataForm.eventType = this.dataForm.eventType.join('/');\n        }\n      }else {\n        if(this.currentEventType === 4){\n          //弱口令\n          this.dataForm.eventType = '弱口令';\n        }\n      }\n\n      //通报对象\n      let reportTargetRef = this.$refs.reportTarget;\n      if(reportTargetRef){\n        this.dataForm.reportTargetForm = reportTargetRef.submitForm();\n      }\n      return this.dataForm;\n    },\n    isSelectAble(row,index){\n      if(this.setting && (!this.setting.originType || (this.setting.originType && this.setting.originType != 'event')) && (this.setting.row && this.setting.row.eventIds && this.setting.row.eventIds.find(item => item == row.id))){\n        return true;\n      }\n      return (!row.flowState || row.flowState == '99');\n    },\n    selectEventClick(){\n      if(!this.currentEventType){\n        this.$message.error('请先选择事件类型');\n        return false;\n      }\n      this.openEventSelectDialog = true;\n    },\n    isRequired(prop){\n      if(!this.formOperates){\n        return false;\n      }\n      let match = this.formOperates.find(item => item.id === prop);\n      if(match && match.required){\n        return true;\n      }\n      return false;\n    },\n    refreshWord(){\n      /* if(this.isChangeForm){\n        this.isChangeForm = false;\n        //暂存\n        // this.$eventBus.$emit('sendWorkForm', this.beforeSubmit());\n        this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n      } */\n      this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n    },\n    sendDataForm(){\n      //暂存\n      return this.beforeSubmit();\n    },\n    loopGetFlowNode(treeData,nodeId,arr){\n      if(!treeData){\n        return;\n      }\n      arr.push({\n        nodeId: treeData.nodeId,\n        properties: treeData.properties,\n        state: treeData.state,\n        type: treeData.type\n      })\n      if(treeData.nodeId === nodeId){\n        return;\n      }else {\n        return this.loopGetFlowNode(treeData.childNode,nodeId,arr);\n      }\n    },\n    addDept(){\n      this.$refs.reportTarget && this.$refs.reportTarget.addDept();\n    },\n    reportTargetActiveChange(val){\n      if(!val){\n        this.reportTargetActive = '0';\n      }else {\n        this.reportTargetActive = val;\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n@import \"../../../styles/track.css\";\n</style>\n<style lang=\"scss\" scoped>\n.main{\n  background-color: #F2F4F8;\n\n  > div{\n    background-color: #ffffff;\n    padding: 15px;\n  }\n  > div:not(:first-child){\n    margin-top: 0.8vh;\n  }\n\n  .base_content{\n    .ips{\n      display: flex;\n      > div{\n        background-color: #E7F2FF;\n        color: #0778FF;\n        border-width: 1px;\n        border-style: solid;\n        border-color: #0778FF;\n        border-radius: 3px;\n        height: 32px;\n        text-align: center;\n        overflow: hidden;\n        font-size: 0.5vw;\n      }\n      > div:not(:first-child){\n        margin-left: 1%;\n      }\n      .ips_item{\n        width: 31%;\n      }\n      .ips_item_overflow{\n        width: 7%;\n      }\n    }\n  }\n\n  .event_type_body{\n    > div:not(:first-child){\n      margin-top: 1.5vh;\n    }\n\n    .event_type_select{\n      display: flex;\n      .label{\n        align-content: center;\n      }\n      .event_type_btn{\n        display: flex;\n        margin-left: 20px;\n        > .event_type_btn_item:not(:first-child){\n          margin-left: 10px;\n        }\n\n        .btn_active{\n          background: #1890ff;\n          border-color: #1890ff;\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n\n  .title-right{\n    float: right;\n    font-size: 14px;\n    color: #6c6c6c;\n  }\n}\n</style>\n"]}]}