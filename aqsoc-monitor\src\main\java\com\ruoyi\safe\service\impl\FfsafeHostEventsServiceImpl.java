package com.ruoyi.safe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.safe.mapper.FfsafeHostEventsMapper;
import com.ruoyi.safe.domain.FfsafeHostEvents;
import com.ruoyi.safe.service.IFfsafeHostEventsService;

/**
 * 主机事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class FfsafeHostEventsServiceImpl implements IFfsafeHostEventsService
{
    @Autowired
    private FfsafeHostEventsMapper ffsafeHostEventsMapper;

    /**
     * 查询主机事件
     *
     * @param id 主机事件主键
     * @return 主机事件
     */
    @Override
    public FfsafeHostEvents selectFfsafeHostEventsById(Long id)
    {
        return ffsafeHostEventsMapper.selectFfsafeHostEventsById(id);
    }

    /**
     * 批量查询主机事件
     *
     * @param ids 主机事件主键集合
     * @return 主机事件集合
     */
    @Override
    public List<FfsafeHostEvents> selectFfsafeHostEventsByIds(Long[] ids)
    {
        return ffsafeHostEventsMapper.selectFfsafeHostEventsByIds(ids);
    }

    /**
     * 查询主机事件列表
     *
     * @param ffsafeHostEvents 主机事件
     * @return 主机事件
     */
    @Override
    public List<FfsafeHostEvents> selectFfsafeHostEventsList(FfsafeHostEvents ffsafeHostEvents)
    {
        return ffsafeHostEventsMapper.selectFfsafeHostEventsList(ffsafeHostEvents);
    }

    /**
     * 新增主机事件
     *
     * @param ffsafeHostEvents 主机事件
     * @return 结果
     */
    @Override
    public int insertFfsafeHostEvents(FfsafeHostEvents ffsafeHostEvents)
    {
        return ffsafeHostEventsMapper.insertFfsafeHostEvents(ffsafeHostEvents);
    }

    /**
     * 修改主机事件
     *
     * @param ffsafeHostEvents 主机事件
     * @return 结果
     */
    @Override
    public int updateFfsafeHostEvents(FfsafeHostEvents ffsafeHostEvents)
    {
        return ffsafeHostEventsMapper.updateFfsafeHostEvents(ffsafeHostEvents);
    }

    /**
     * 删除主机事件信息
     *
     * @param id 主机事件主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostEventsById(Long id)
    {
        return ffsafeHostEventsMapper.deleteFfsafeHostEventsById(id);
    }

    /**
     * 批量删除主机事件
     *
     * @param ids 需要删除的主机事件主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostEventsByIds(Long[] ids)
    {
        return ffsafeHostEventsMapper.deleteFfsafeHostEventsByIds(ids);
    }

    @Override
    public void saveBatch(List<FfsafeHostEvents> saveList) {
        ffsafeHostEventsMapper.saveBatch(saveList);
    }

    @Override
    public void truncateTable() {
        ffsafeHostEventsMapper.truncateTable();
    }

    @Override
    public void deleteFfsafeHostEventsByDeviceConfigId(Long id) {
        ffsafeHostEventsMapper.deleteFfsafeHostEventsByDeviceConfigId( id);
    }
}
