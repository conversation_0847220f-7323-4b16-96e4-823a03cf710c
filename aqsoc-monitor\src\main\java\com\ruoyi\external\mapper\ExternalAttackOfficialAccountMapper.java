package com.ruoyi.external.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackOfficialAccount;
import org.apache.ibatis.annotations.Param;

/**
 * 微信公众号Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface ExternalAttackOfficialAccountMapper 
{
    /**
     * 查询微信公众号
     * 
     * @param id 微信公众号主键
     * @return 微信公众号
     */
    public ExternalAttackOfficialAccount selectExternalAttackOfficialAccountById(Long id);

    /**
     * 批量查询微信公众号
     *
     * @param ids 微信公众号主键集合
     * @return 微信公众号集合
     */
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountByIds(Long[] ids);

    /**
     * 查询微信公众号列表
     * 
     * @param externalAttackOfficialAccount 微信公众号
     * @return 微信公众号集合
     */
    public List<ExternalAttackOfficialAccount> selectExternalAttackOfficialAccountList(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 新增微信公众号
     *
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    public int insertExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 批量插入微信公众号
     *
     * @param entityList 微信公众号实体列表
     * @return 插入成功的记录数
     */
    int batchInsertExternalAttackOfficialAccount(@Param("entityList") List<ExternalAttackOfficialAccount> entityList);

    /**
     * 修改微信公众号
     * 
     * @param externalAttackOfficialAccount 微信公众号
     * @return 结果
     */
    public int updateExternalAttackOfficialAccount(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 删除微信公众号
     * 
     * @param id 微信公众号主键
     * @return 结果
     */
    public int deleteExternalAttackOfficialAccountById(Long id);

    /**
     * 批量删除微信公众号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExternalAttackOfficialAccountByIds(Long[] ids);
    /**
     * 检查 微信公众号 是否已存在（排除指定 id）
     * @param officialAccountAppId
     * @param officialAccountName
     * @param id
     */
    int checkOfficialAccountExistence(@Param("officialAccountAppId") String officialAccountAppId, @Param("officialAccountName") String officialAccountName, @Param("id") Long id);

    List<ExternalAttackOfficialAccount> selectByAppIds(@Param("appIds") List<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(@Param("uniqueKeys") Set<String> uniqueKeys, @Param("deviceConfigId") Long deviceConfigId);

    /**
     * 选择性更新微信公众号：只更新第三方接口返回的原有字段，保护用户维护的新增字段
     *
     * @param externalAttackOfficialAccount 微信公众号实体，包含要更新的数据
     * @return 更新的记录数
     */
    int selectiveUpdateByCondition(ExternalAttackOfficialAccount externalAttackOfficialAccount);

    /**
     * 根据唯一键查询现有记录，用于智能同步时的数据检测
     *
     * @param uniqueKey 唯一键，格式为 "officialAccountName-officialAccountAppId"
     * @return 微信公众号实体，如果不存在则返回null
     */
    ExternalAttackOfficialAccount selectByUniqueKey(@Param("uniqueKey") String uniqueKey);

    /**
     * 批量查询现有记录，用于智能同步时的批量数据检测
     *
     * @param uniqueKeys 唯一键集合
     * @return 微信公众号实体列表
     */
    List<ExternalAttackOfficialAccount> selectByUniqueKeys(@Param("uniqueKeys") Set<String> uniqueKeys);
}
