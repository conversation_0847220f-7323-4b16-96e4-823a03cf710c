{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue", "mtime": 1756710899653}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBXb3JkUHJldmlldyBmcm9tICJAL2NvbXBvbmVudHMvV29yZFByZXZpZXcvaW5kZXgudnVlIjsKaW1wb3J0IFByaW50RGlhbG9nIGZyb20gIkAvY29tcG9uZW50cy9QcmludERpYWxvZyI7CmltcG9ydCBTaWduSW1nRGlhbG9nIGZyb20gIkAvY29tcG9uZW50cy9TaWduSW1nRGlhbG9nIjsKaW1wb3J0IGluZm9ybVJlY29yZCBmcm9tICIuL0Zsb3dSZWNvcmQvaW5mb3JtUmVjb3JkLnZ1ZSI7CmltcG9ydCBpbmZvcm1TaWduUmVjb3JkIGZyb20gIi4vRmxvd1JlY29yZC9pbmZvcm1TaWduUmVjb3JkLnZ1ZSI7CmltcG9ydCBmZWVkYmFja1JlY29yZCBmcm9tICIuL0Zsb3dSZWNvcmQvZmVlZGJhY2tSZWNvcmQudnVlIjsKaW1wb3J0IGZlZWRiYWNrU2lnblJlY29yZCBmcm9tICIuL0Zsb3dSZWNvcmQvZmVlZGJhY2tTaWduUmVjb3JkLnZ1ZSI7CmltcG9ydCBjaGVja1JlY29yZCBmcm9tICIuL0Zsb3dSZWNvcmQvY2hlY2tSZWNvcmQudnVlIjsKaW1wb3J0IGZvcm1SZWNvcmQgZnJvbSAnLi9GbG93UmVjb3JkL2Zvcm1SZWNvcmQudnVlJzsKaW1wb3J0IHdvcmtGbG93IGZyb20gJ0Avdmlld3MvdG9kb0l0ZW0vdG9kby93b3JrX2Zsb3cudnVlJzsKaW1wb3J0IHsKICBBc3NpZ24sCiAgQXVkaXQsCiAgQ2FuY2VsLAogIENhbmRpZGF0ZXMsCiAgRmxvd0JlZm9yZUluZm8sCiAgUmVjYWxsLAogIFJlamVjdCwKICBSZWplY3RMaXN0LAogIHJlc3RvcmUsCiAgUmVzdXJnZW5jZSwKICBSZXN1cmdlbmNlTGlzdCwKICBTYXZlQXVkaXQsCiAgc3ViRmxvd0luZm8sCiAgc3VzcGVuZCwKICBUcmFuc2ZlciwKfSBmcm9tICJAL2FwaS9sb3dDb2RlL0Zsb3dCZWZvcmUiOwppbXBvcnQge1ByZXNzLCBSZXZva2V9IGZyb20gIkAvYXBpL2xvd0NvZGUvRmxvd0xhdW5jaCI7CmltcG9ydCB7Q3JlYXRlLCBVcGRhdGV9IGZyb20gIkAvYXBpL2xvd0NvZGUvd29ya0Zsb3dGb3JtIjsKaW1wb3J0IHJlY29yZExpc3QgZnJvbSAiLi9SZWNvcmRMaXN0IjsKaW1wb3J0IENvbW1lbnQgZnJvbSAiLi9Db21tZW50IjsKaW1wb3J0IFJlY29yZFN1bW1hcnkgZnJvbSAiLi9SZWNvcmRTdW1tYXJ5IjsKaW1wb3J0IENhbmRpZGF0ZUZvcm0gZnJvbSAiLi9DYW5kaWRhdGVGb3JtIjsKaW1wb3J0IEVycm9yRm9ybSBmcm9tICIuL0Vycm9yRm9ybSI7CmltcG9ydCBDYW5kaWRhdGVVc2VyU2VsZWN0IGZyb20gIi4vQ2FuZGlkYXRlVXNlclNlbGVjdCI7CmltcG9ydCBQcm9jZXNzIGZyb20gIkAvY29tcG9uZW50cy9Qcm9jZXNzL1ByZXZpZXciOwppbXBvcnQgUHJpbnRCcm93c2UgZnJvbSAiQC9jb21wb25lbnRzL1ByaW50QnJvd3NlIjsKaW1wb3J0IEFjdGlvbkRpYWxvZyBmcm9tICJAL3ZpZXdzL3dvcmtGbG93L2NvbXBvbmVudHMvQWN0aW9uRGlhbG9nIjsKaW1wb3J0IEhhc0ZyZWVBcHByb3ZlciBmcm9tICIuL0hhc0ZyZWVBcHByb3ZlciI7CmltcG9ydCBTdXNwZW5kRGlhbG9nIGZyb20gIi4vU3VzcGVuZERpYWxvZyI7CmltcG9ydCBDb21tb25Xb3Jkc0RpYWxvZyBmcm9tICIuL0NvbW1vbldvcmRzRGlhbG9nIjsKaW1wb3J0IHttYXBHZXR0ZXJzfSBmcm9tICJ2dWV4IjsKaW1wb3J0IHtjb21tb25HZXRXb3JkLCBjb21tb25HZXRXb3JkQnlUZW1wLGdldE9yZGVyfSBmcm9tICJAL2FwaS90b29sL3dvcmsiOwppbXBvcnQgQ29tbW9uV29yZHMgZnJvbSAiQC92aWV3cy96ZXJvQ29kZS93b3JrRmxvdy9jb21wb25lbnRzL2NvbW1vbldvcmRzLnZ1ZSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkZsb3dCb3giLAogIGNvbXBvbmVudHM6IHsKICAgIENvbW1vbldvcmRzLAogICAgUHJpbnREaWFsb2csCiAgICBTaWduSW1nRGlhbG9nLAogICAgSGFzRnJlZUFwcHJvdmVyLAogICAgcmVjb3JkTGlzdCwKICAgIFByb2Nlc3MsCiAgICBQcmludEJyb3dzZSwKICAgIENvbW1lbnQsCiAgICBSZWNvcmRTdW1tYXJ5LAogICAgQ2FuZGlkYXRlRm9ybSwKICAgIENhbmRpZGF0ZVVzZXJTZWxlY3QsCiAgICBFcnJvckZvcm0sCiAgICBBY3Rpb25EaWFsb2csCiAgICBTdXNwZW5kRGlhbG9nLAogICAgQ29tbW9uV29yZHNEaWFsb2csCiAgICBpbmZvcm1SZWNvcmQsCiAgICBpbmZvcm1TaWduUmVjb3JkLAogICAgZmVlZGJhY2tSZWNvcmQsCiAgICBmZWVkYmFja1NpZ25SZWNvcmQsCiAgICBjaGVja1JlY29yZCwKICAgIGZvcm1SZWNvcmQsCiAgICBXb3JkUHJldmlldywKICAgIHdvcmtGbG93CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nRmxvd1Byb2Nlc3NWaXNpYmxlOiBmYWxzZSwKICAgICAgcHJpbnRUZW1wbGF0ZUlkOiAiIiwKICAgICAgcHJpbnREaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc3ViRmxvd1RhYjogIiIsCiAgICAgIHJlc3VyZ2VuY2VWaXNpYmxlOiBmYWxzZSwKICAgICAgYWN0aW9uVmlzaWJsZTogZmFsc2UsCiAgICAgIHJlc3VyZ2VuY2VGb3JtOiB7CiAgICAgICAgdGFza05vZGVJZDogIiIsCiAgICAgICAgaGFuZGxlT3BpbmlvbjogIiIsCiAgICAgICAgZmlsZUxpc3Q6IFtdLAogICAgICB9LAogICAgICByZXN1cmdlbmNlUnVsZXM6IHsKICAgICAgICB0YXNrTm9kZUlkOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6IqC54K5IiwKICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwKICAgICAgYXNzaWduTm9kZUxpc3Q6IFtdLAogICAgICByZXN1cmdlbmNlTm9kZUxpc3Q6IFtdLAogICAgICBjdXJyZW50VmlldzogIiIsCiAgICAgIHByZXZpZXdUaXRsZTogIiIsCiAgICAgIGZvcm1EYXRhOiB7fSwKICAgICAgc2V0dGluZzoge30sCiAgICAgIG1vbml0b3JMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgZnVsbE5hbWU6ICIxIiwKICAgICAgICAgIGZsb3dOYW1lOiAiMSIsCiAgICAgICAgICBzdGFydFRpbWU6ICIxIiwKICAgICAgICAgIHVzZXJOYW1lOiAiMSIsCiAgICAgICAgICB0aGlzU3RlcDogIjEiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgZnVsbE5hbWU6ICIxIiwKICAgICAgICAgIGZsb3dOYW1lOiAiMSIsCiAgICAgICAgICBzdGFydFRpbWU6ICIxIiwKICAgICAgICAgIHVzZXJOYW1lOiAiMSIsCiAgICAgICAgICB0aGlzU3RlcDogIjEiLAogICAgICAgIH0sCiAgICAgIF0sCiAgICAgIGZsb3dGb3JtSW5mbzoge30sCiAgICAgIGZsb3dUZW1wbGF0ZUluZm86IHt9LAogICAgICBmbG93VGFza0luZm86IHt9LAogICAgICBmbG93VGFza05vZGVMaXN0OiBbXSwKICAgICAgZmxvd1RlbXBsYXRlSnNvbjoge30sCiAgICAgIGZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0OiBbXSwKICAgICAgcHJvcGVydGllczoge30sCiAgICAgIGVuZFRpbWU6IDAsCiAgICAgIHN1c3BlbmRWaXNpYmxlOiBmYWxzZSwKICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgIGhhbmRsZUlkOiAiIiwKICAgICAgYWN0aXZlVGFiOiAiMCIsCiAgICAgIGlzQ29tbWVudDogZmFsc2UsCiAgICAgIGlzU3VtbWFyeTogZmFsc2UsCiAgICAgIHN1bW1hcnlUeXBlOiAwLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIGFwcHJvdmFsQnRuTG9hZGluZzogZmFsc2UsCiAgICAgIHJlc3VyZ2VuY2VCdG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgY2FuZGlkYXRlTG9hZGluZzogZmFsc2UsCiAgICAgIGNhbmRpZGF0ZVZpc2libGU6IGZhbHNlLAogICAgICBoYXNGcmVlQXBwcm92ZXJWaXNpYmxlOiBmYWxzZSwKICAgICAgc2lnblZpc2libGU6IGZhbHNlLAogICAgICBjYW5kaWRhdGVUeXBlOiAxLAogICAgICBicmFuY2hMaXN0OiBbXSwKICAgICAgY2FuZGlkYXRlTGlzdDogW10sCiAgICAgIGNhbmRpZGF0ZUZvcm06IHsKICAgICAgICBicmFuY2hMaXN0OiBbXSwKICAgICAgICBjYW5kaWRhdGVMaXN0OiBbXSwKICAgICAgICBmaWxlTGlzdDogW10sCiAgICAgICAgaGFuZGxlT3BpbmlvbjogIiIsCiAgICAgICAgcmVqZWN0U3RlcDogIiIsCiAgICAgICAgcmVqZWN0VHlwZTogMSwKICAgICAgfSwKICAgICAgcHJpbnRCcm93c2VWaXNpYmxlOiBmYWxzZSwKICAgICAgcmVqZWN0TGlzdDogW10sCiAgICAgIHNob3dSZWplY3Q6IGZhbHNlLAogICAgICBldmVudFR5cGU6ICIiLAogICAgICBzaWduSW1nOiAiIiwKICAgICAgY29weUlkczogW10sCiAgICAgIGZ1bGxOYW1lOiAiIiwKICAgICAgdGhpc1N0ZXA6ICIiLAogICAgICBhbGxCdG5EaXNhYmxlZDogZmFsc2UsCiAgICAgIGZsb3dVcmdlbnQ6IDEsCiAgICAgIGZsb3dVcmdlbnRMaXN0OiBbCiAgICAgICAgeyBuYW1lOiAi5pmu6YCaIiwgY29sb3I6ICIjNDA5RUZGIiwgc3RhdGU6IDEgfSwKICAgICAgICB7IG5hbWU6ICLph43opoEiLCBjb2xvcjogIiNFNkEyM0MiLCBzdGF0ZTogMiB9LAogICAgICAgIHsgbmFtZTogIue0p+aApSIsIGNvbG9yOiAiI0Y1NkM2QyIsIHN0YXRlOiAzIH0sCiAgICAgIF0sCiAgICAgIGVycm9yVmlzaWJsZTogZmFsc2UsCiAgICAgIGVycm9yTm9kZUxpc3Q6IFtdLAogICAgICBpc1ZhbGlkYXRlOiBmYWxzZSwKICAgICAgbW9yZUJ0bkxpc3Q6IFtdLAogICAgICBzdWJGbG93VmlzaWJsZTogZmFsc2UsCiAgICAgIGZsb3dCb3hWaXNpYmxlOiBmYWxzZSwKICAgICAgc3ViRmxvd0luZm9MaXN0OiBbXSwKICAgICAgY29tbW9uV29yZHNWaXNpYmxlOiBmYWxzZSwKICAgICAgcmVjb3JkVHlwZTogMCwKICAgICAgbGFzdFJlY29yZDoge30sCiAgICAgIHJlY29yZEJ0bkFycjogW10sCiAgICAgIGV4cG9ydEJ0bkFycjogW10sCiAgICAgIGN1cnJlbnRXb3JkQnRuOiBudWxsLAogICAgICB3b3JkUHJldmlld0xvYWRpbmc6IGZhbHNlLAogICAgICB3b3JkU3JjOiBudWxsLAogICAgICBpc1NlbGZEZXB0OiBmYWxzZSwKICAgICAgZm9ybUFjdGl2ZVRhYnM6ICcxJywKICAgICAgY3VycmVudFdvcmRGb3JtOiBudWxsLAogICAgICB3b3JkVGFyZ2V0RGVwdDogbnVsbCwKICAgICAgd29yZFRhcmdldERlcHRPcHRpb25zOiBbXSwKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgdGl0bGUoKSB7CiAgICAgIGlmIChbMiwgMywgNF0uaW5jbHVkZXModGhpcy5zZXR0aW5nLm9wVHlwZSkpIHJldHVybiB0aGlzLmZ1bGxOYW1lOwogICAgICByZXR1cm4gdGhpcy50aGlzU3RlcAogICAgICAgID8gdGhpcy5mdWxsTmFtZSArICIvIiArIHRoaXMudGhpc1N0ZXAKICAgICAgICA6IHRoaXMuZnVsbE5hbWU7CiAgICB9LAogICAgc2VsZWN0U3RhdGUoKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5mbG93VXJnZW50TGlzdC5maW5kSW5kZXgoCiAgICAgICAgKGMpID0+IHRoaXMuZmxvd1VyZ2VudCA9PT0gYy5zdGF0ZQogICAgICApOwogICAgICByZXR1cm4gaW5kZXg7CiAgICB9LAogICAgLi4ubWFwR2V0dGVycyhbInVzZXJJbmZvIl0pLAogICAgYXVkaXRUZXh0KCl7CiAgICAgIGxldCB0aXRsZSA9ICflrqHmibnpgJrov4cnOwogICAgICBpZih0aGlzLnByb3BlcnRpZXMgJiYgdGhpcy5wcm9wZXJ0aWVzLnN1Ym1pdEJ0blRleHQgJiYgdGhpcy5wcm9wZXJ0aWVzLnN1Ym1pdEJ0blRleHQuaW5kZXhPZign5o+Q5LqkJykgIT09IC0xKXsKICAgICAgICB0aXRsZSA9ICfmj5DkuqQnOwogICAgICB9CiAgICAgIHJldHVybiB0aXRsZTsKICAgIH0sCiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy4kZXZlbnRCdXMuJG9uKCdzZW5kV29ya0Zvcm0nLCAodmFsKSA9PiB7CiAgICAgIHRoaXMuY3VycmVudFdvcmRGb3JtID0gdmFsOwogICAgICB0aGlzLndvcmRCdG5TZWxlY3RlZCh0aGlzLmN1cnJlbnRXb3JkQnRuKTsKICAgIH0pCiAgICB0aGlzLiRldmVudEJ1cy4kb24oJ3NldEZvcm1BY3RpdmVUYWJzJywgKHZhbCkgPT4gewogICAgICB0aGlzLmZvcm1BY3RpdmVUYWJzID0gdmFsOwogICAgfSkKICB9LAogIHdhdGNoOiB7CiAgICBhY3RpdmVUYWIodmFsKSB7CiAgICAgIGlmICh2YWwgPT09ICJjb21tZW50IikgewogICAgICAgIHRoaXMuJHJlZnMuY29tbWVudCAmJiB0aGlzLiRyZWZzLmNvbW1lbnQuaW5pdCgpOwogICAgICAgIHRoaXMubW9yZUJ0bkxpc3QucHVzaCh7IGxhYmVsOiAi6K+EIOiuuiIsIGtleTogImNvbW1lbnQiIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMubW9yZUJ0bkxpc3QgPSB0aGlzLm1vcmVCdG5MaXN0LmZpbHRlcigobykgPT4gby5rZXkgIT0gImNvbW1lbnQiKTsKICAgICAgfQogICAgICBpZiAodmFsID09PSAicmVjb3JkU3VtbWFyeSIpIHsKICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFN1bW1hcnkgJiYgdGhpcy4kcmVmcy5yZWNvcmRTdW1tYXJ5LmluaXQoKTsKICAgICAgfQogICAgfSwKICAgIGZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0KHZhbCl7CiAgICAgIHRoaXMucmVjb3JkQnRuQXJyID0gW107CiAgICAgIGlmKHZhbCAmJiB2YWwubGVuZ3RoPjApewogICAgICAgIGZvciAobGV0IGkgPSB2YWwubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHsKICAgICAgICAgIGxldCBvID0gdmFsW2ldOwogICAgICAgICAgbGV0IG1hdGNoSXRlbSA9IHRoaXMucmVjb3JkQnRuQXJyLmZpbmQoKGkpID0+IGkubm9kZUNvZGUgPT0gby5ub2RlQ29kZSk7CiAgICAgICAgICBpZighbWF0Y2hJdGVtKXsKICAgICAgICAgICAgdGhpcy5yZWNvcmRCdG5BcnIucHVzaChvKTsKICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgLy/mm7/mjaIKICAgICAgICAgICAgdGhpcy5yZWNvcmRCdG5BcnIuc3BsaWNlKHRoaXMucmVjb3JkQnRuQXJyLmluZGV4T2YobWF0Y2hJdGVtKSwxLG8pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGZsb3dVcmdlbnQodmFsKXsKICAgICAgbGV0IG1hdGNoID0gdGhpcy5mbG93VXJnZW50TGlzdC5maW5kKChjKSA9PiBjLnN0YXRlID09PSB2YWwpOwogICAgICBpZighbWF0Y2gpewogICAgICAgIHRoaXMuZmxvd1VyZ2VudCA9IDE7CiAgICAgIH0KICAgIH0sCiAgICBjdXJyZW50V29yZEJ0bjogewogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXIodmFsKSB7CiAgICAgICAgaWYodmFsKXsKICAgICAgICAgIHRoaXMuaGFuZGxlU2hvd1dvcmQoKTsKICAgICAgICB9CiAgICAgIH0sCiAgICB9LAogICAgZm9ybUFjdGl2ZVRhYnModmFsKXsKICAgICAgdGhpcy5jdXJyZW50V29yZEJ0biA9IG51bGw7CiAgICAgIGlmKHZhbCA9PT0gIjIiKXsKICAgICAgICB0aGlzLmxvb3BXb3JkQnRucygpOwogICAgICB9CiAgICB9LAogIH0sCiAgbWV0aG9kczogewogICAgY29tbW9uKHZhbCkgewogICAgICB0aGlzLmNvbW1vbldvcmRzVmlzaWJsZSA9IGZhbHNlOwogICAgICBpZiAodmFsKSB7CiAgICAgICAgaWYgKHRoaXMucmVzdXJnZW5jZVZpc2libGUpIHsKICAgICAgICAgIHRoaXMucmVzdXJnZW5jZUZvcm0uaGFuZGxlT3BpbmlvbiArPSB2YWwuY29tbW9uV29yZHNUZXh0OwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uaGFuZGxlT3BpbmlvbiArPSB2YWwuY29tbW9uV29yZHNUZXh0OwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGJlZm9yZUNsb3NlKCkgewogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy5jb21tb25Xb3Jkc0RpYWxvZy5jbG9zZSgpOwogICAgfSwKICAgIGFkZFNpZ24oKSB7CiAgICAgIHRoaXMuc2lnblZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5TaWduSW1nLmluaXQoKTsKICAgICAgfSk7CiAgICB9LAogICAgc2lnbkRpYWxvZyh2YWwpIHsKICAgICAgdGhpcy5zaWduVmlzaWJsZSA9IGZhbHNlOwogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5zaWduSW1nID0gdmFsOwogICAgICB9CiAgICB9LAogICAgYXBwcm92ZXJEaWFsb2cobmVlZENsb3NlKSB7CiAgICAgIGlmIChuZWVkQ2xvc2UpIHRoaXMuJGVtaXQoImNsb3NlIiwgdHJ1ZSk7CiAgICB9LAogICAgYWN0aXZlQ2xpY2soKSB7CiAgICAgIGxldCBkYXRhID0KICAgICAgICB0aGlzLnN1YkZsb3dJbmZvTGlzdC5maWx0ZXIoCiAgICAgICAgICAobykgPT4gby5mbG93VGFza0luZm8uaWQgPT0gdGhpcy5zdWJGbG93VGFiCiAgICAgICAgKSB8fCBbXTsKICAgICAgaWYgKGRhdGEubGVuZ3RoKSB7CiAgICAgICAgdGhpcy5mdWxsTmFtZSA9IGRhdGFbMF0uZmxvd1Rhc2tJbmZvLmZ1bGxOYW1lOwogICAgICAgIHRoaXMuZmxvd1Rhc2tPcGVyYXRvclJlY29yZExpc3QgPQogICAgICAgICAgZGF0YVswXS5mbG93VGFza09wZXJhdG9yUmVjb3JkTGlzdCB8fCBbXTsKICAgICAgICBsZXQgdGVtcGxhdGVKc29uID0gZGF0YVswXS5mbG93VGFza0luZm8uZmxvd1RlbXBsYXRlSnNvbgogICAgICAgICAgPyBKU09OLnBhcnNlKGRhdGFbMF0uZmxvd1Rhc2tJbmZvLmZsb3dUZW1wbGF0ZUpzb24pCiAgICAgICAgICA6IG51bGw7CiAgICAgICAgdGhpcy5pc0NvbW1lbnQgPSB0ZW1wbGF0ZUpzb24ucHJvcGVydGllcy5pc0NvbW1lbnQ7CiAgICAgICAgdGhpcy5pc1N1bW1hcnkgPSB0ZW1wbGF0ZUpzb24ucHJvcGVydGllcy5pc1N1bW1hcnk7CiAgICAgICAgdGhpcy5zdW1tYXJ5VHlwZSA9IHRlbXBsYXRlSnNvbi5wcm9wZXJ0aWVzLnN1bW1hcnlUeXBlOwogICAgICAgIHRoaXMuZmxvd1VyZ2VudCA9IGRhdGFbMF0uZmxvd1Rhc2tJbmZvLmZsb3dVcmdlbnQgfHwgMTsKICAgICAgICB0aGlzLnNldHRpbmcuaWQgPSBkYXRhWzBdLmZsb3dUYXNrSW5mby5pZDsKICAgICAgfQogICAgfSwKICAgIHN1YkZsb3coZW5Db2RlKSB7CiAgICAgIGxldCBmbG93VGFza05vZGVMaXN0ID0gdGhpcy5mbG93VGFza05vZGVMaXN0LmZpbHRlcigKICAgICAgICAocmVzKSA9PiByZXMubm9kZUNvZGUgPT0gZW5Db2RlCiAgICAgICk7CiAgICAgIGlmICghZmxvd1Rhc2tOb2RlTGlzdC5sZW5ndGgpIHJldHVybjsKICAgICAgaWYgKAogICAgICAgICFmbG93VGFza05vZGVMaXN0WzBdLnR5cGUgfHwKICAgICAgICBmbG93VGFza05vZGVMaXN0WzBdLm5vZGVUeXBlICE9ICJzdWJGbG93IgogICAgICApCiAgICAgICAgcmV0dXJuOwogICAgICBsZXQgaXRlbSA9IHsKICAgICAgICBzdWJGbG93VmlzaWJsZTogdHJ1ZSwKICAgICAgICAuLi5mbG93VGFza05vZGVMaXN0LAogICAgICAgIC4uLnRoaXMuc2V0dGluZywKICAgICAgfTsKICAgICAgdGhpcy5mbG93Qm94VmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLkZsb3dCb3guaW5pdChpdGVtKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlUmVzdXJnZW5jZShlcnJvclJ1bGVVc2VyTGlzdCkgewogICAgICB0aGlzLiRyZWZzWyJyZXN1cmdlbmNlRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICghdmFsaWQpIHJldHVybjsKICAgICAgICBsZXQgcXVlcnkgPSB7CiAgICAgICAgICAuLi50aGlzLnJlc3VyZ2VuY2VGb3JtLAogICAgICAgICAgdGFza0lkOiB0aGlzLnNldHRpbmcudGFza0lkLAogICAgICAgICAgcmVzdXJnZW5jZTogdGhpcy5mbG93VGFza0luZm8uY29tcGxldGlvbiA9PSAxMDAsCiAgICAgICAgfTsKICAgICAgICBpZiAoZXJyb3JSdWxlVXNlckxpc3QpIHF1ZXJ5LmVycm9yUnVsZVVzZXJMaXN0ID0gZXJyb3JSdWxlVXNlckxpc3Q7CiAgICAgICAgdGhpcy5yZXN1cmdlbmNlQnRuTG9hZGluZyA9IHRydWU7CiAgICAgICAgUmVzdXJnZW5jZShxdWVyeSkKICAgICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gcmVzLmRhdGE7CiAgICAgICAgICAgIGlmIChlcnJvckRhdGEgJiYgQXJyYXkuaXNBcnJheShlcnJvckRhdGEpICYmIGVycm9yRGF0YS5sZW5ndGgpIHsKICAgICAgICAgICAgICB0aGlzLmVycm9yTm9kZUxpc3QgPSBlcnJvckRhdGE7CiAgICAgICAgICAgICAgdGhpcy5ldmVudFR5cGUgPSAicmVzdXJnZW5jZSI7CiAgICAgICAgICAgICAgdGhpcy5lcnJvclZpc2libGUgPSB0cnVlOwogICAgICAgICAgICAgIHRoaXMucmVzdXJnZW5jZUJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAwMCwKICAgICAgICAgICAgICAgIG9uQ2xvc2U6ICgpID0+IHsKICAgICAgICAgICAgICAgICAgdGhpcy5yZXN1cmdlbmNlQnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgiY2xvc2UiLCB0cnVlKTsKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLnJlc3VyZ2VuY2VCdG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgZmxvd1Jlc3VyZ2VuY2UoKSB7CiAgICAgIHRoaXMucmVzdXJnZW5jZVZpc2libGUgPSB0cnVlOwogICAgICBSZXN1cmdlbmNlTGlzdCh0aGlzLnNldHRpbmcudGFza0lkKS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLnJlc3VyZ2VuY2VOb2RlTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBnb0JhY2soaXNSZWZyZXNoKSB7CiAgICAgIHRoaXMuJGVtaXQoImNsb3NlIiwgaXNSZWZyZXNoKTsKICAgIH0sCiAgICBpbml0KGRhdGEpIHsKICAgICAgdGhpcy5hY3RpdmVUYWIgPSAiMCI7CiAgICAgIGlmKGRhdGEuYWN0aXZlVGFicyl7CiAgICAgICAgdGhpcy5hY3RpdmVUYWIgPSBkYXRhLmFjdGl2ZVRhYnM7CiAgICAgIH0KICAgICAgLyogaWYoZGF0YS5pZCAmJiBkYXRhLmlzV29yayl7CiAgICAgICAgdGhpcy5nZXRDb25maWdLZXkoIndvcmtPcmRlckRlZmF1bHRUYWIiKS50aGVuKHJlcyA9PnsKICAgICAgICAgIGlmKHJlcy5tc2cpewogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5mb3JtQWN0aXZlVGFicyA9IHJlcy5tc2c7CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSAqLwogICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdmbG93Um93RGF0YScpOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLnNldHRpbmcgPSBkYXRhOwogICAgICBpZiAoZGF0YS5zdWJGbG93VmlzaWJsZSkgewogICAgICAgIHRoaXMuc3ViRmxvd0luZm8oZGF0YSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLyoqCiAgICAgICAgICogb3BUeXBlCiAgICAgICAgICogLTEgLSDmiJHlj5HotbfnmoTmlrDlu7ov57yW6L6RCiAgICAgICAgICogMCAtIOaIkeWPkei1t+eahOivpuaDhQogICAgICAgICAqIDEgLSDlvoXlip7kuovlrpwKICAgICAgICAgKiAyIC0g5bey5Yqe5LqL5a6cCiAgICAgICAgICogMyAtIOaKhOmAgeS6i+WunAogICAgICAgICAqIDQgLSDmtYHnqIvnm5HmjqcKICAgICAgICAgKi8KICAgICAgICB0aGlzLmdldEJlZm9yZUluZm8oZGF0YSk7CiAgICAgIH0KCiAgICB9LAogICAgZ2V0QmVmb3JlSW5mbyhkYXRhKSB7CiAgICAgIEZsb3dCZWZvcmVJbmZvKGRhdGEuaWQgfHwgMCwgewogICAgICAgIHRhc2tOb2RlSWQ6IGRhdGEudGFza05vZGVJZCwKICAgICAgICB0YXNrT3BlcmF0b3JJZDogZGF0YS50YXNrSWQ/ZGF0YS50YXNrSWQ6ZGF0YS50YXNrSWQsCiAgICAgICAgZmxvd0lkOiBkYXRhLmZsb3dJZCwKICAgICAgfSkKICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICB0aGlzLmZsb3dGb3JtSW5mbyA9IHJlcy5kYXRhLmZsb3dGb3JtSW5mbzsKICAgICAgICAgIHRoaXMuZmxvd1Rhc2tJbmZvID0gcmVzLmRhdGEuZmxvd1Rhc2tJbmZvIHx8IHt9OwogICAgICAgICAgdGhpcy5mbG93VGVtcGxhdGVJbmZvID0gcmVzLmRhdGEuZmxvd1RlbXBsYXRlSW5mbzsKICAgICAgICAgIGNvbnN0IGZ1bGxOYW1lID0KICAgICAgICAgICAgZGF0YS5vcFR5cGUgPT0gIi0xIgogICAgICAgICAgICAgID8gdGhpcy5mbG93VGVtcGxhdGVJbmZvLmZ1bGxOYW1lCiAgICAgICAgICAgICAgOiB0aGlzLmZsb3dUYXNrSW5mby5mdWxsTmFtZTsKICAgICAgICAgIGRhdGEuZnVsbE5hbWUgPSBmdWxsTmFtZTsKICAgICAgICAgIHRoaXMuZnVsbE5hbWUgPSBmdWxsTmFtZTsKICAgICAgICAgIHRoaXMudGhpc1N0ZXAgPSB0aGlzLmZsb3dUYXNrSW5mby50aGlzU3RlcDsKICAgICAgICAgIHRoaXMuZmxvd1VyZ2VudCA9IHRoaXMuZmxvd1Rhc2tJbmZvLmZsb3dVcmdlbnQgfHwgMTsKICAgICAgICAgIGRhdGEudHlwZSA9IHRoaXMuZmxvd1RlbXBsYXRlSW5mby50eXBlOwogICAgICAgICAgZGF0YS5kcmFmdERhdGEgPSByZXMuZGF0YS5kcmFmdERhdGEgfHwgbnVsbDsKICAgICAgICAgIGRhdGEuZm9ybURhdGEgPSByZXMuZGF0YS5mb3JtRGF0YSB8fCB7fTsKICAgICAgICAgIGRhdGEuZm9ybUVuQ29kZSA9IHRoaXMuZmxvd0Zvcm1JbmZvLmVuQ29kZTsKICAgICAgICAgIGNvbnN0IGZvcm1VcmwgPQogICAgICAgICAgICB0aGlzLmZsb3dGb3JtSW5mby5mb3JtVHlwZSA9PSAyCiAgICAgICAgICAgICAgPyAid29ya0Zsb3cvd29ya0Zsb3dGb3JtL2R5bmFtaWNGb3JtIgogICAgICAgICAgICAgIDogdGhpcy5mbG93Rm9ybUluZm8udXJsQWRkcmVzcwogICAgICAgICAgICAgICAgPyB0aGlzLmZsb3dGb3JtSW5mby51cmxBZGRyZXNzLnJlcGxhY2UoL1xzKi9nLCAiIikKICAgICAgICAgICAgICAgIDogYHdvcmtGbG93L3dvcmtGbG93Rm9ybS8ke3RoaXMuZmxvd0Zvcm1JbmZvLmVuQ29kZX1gOwogICAgICAgICAgdGhpcy5jdXJyZW50VmlldyA9IChyZXNvbHZlKSA9PgogICAgICAgICAgICByZXF1aXJlKFtgQC92aWV3cy8ke2Zvcm1Vcmx9YF0sIHJlc29sdmUpOwogICAgICAgICAgdGhpcy5mbG93VGFza05vZGVMaXN0ID0gcmVzLmRhdGEuZmxvd1Rhc2tOb2RlTGlzdCB8fCBbXTsKICAgICAgICAgIHRoaXMuc2V0dGluZy5mbG93VGFza05vZGVMaXN0ID0gdGhpcy5mbG93VGFza05vZGVMaXN0OwogICAgICAgICAgdGhpcy5mbG93VGVtcGxhdGVKc29uID0gdGhpcy5mbG93VGFza0luZm8gJiYgdGhpcy5mbG93VGFza0luZm8uZmxvd1RlbXBsYXRlSnNvbiA/IEpTT04ucGFyc2UodGhpcy5mbG93VGFza0luZm8uZmxvd1RlbXBsYXRlSnNvbikgOiB0aGlzLmZsb3dUZW1wbGF0ZUluZm8uZmxvd1RlbXBsYXRlSnNvbj9KU09OLnBhcnNlKHRoaXMuZmxvd1RlbXBsYXRlSW5mby5mbG93VGVtcGxhdGVKc29uKTpudWxsOwogICAgICAgICAgLyp0aGlzLmZsb3dUZW1wbGF0ZUpzb24gPSB0aGlzLmZsb3dUZW1wbGF0ZUluZm8uZmxvd1RlbXBsYXRlSnNvbgogICAgICAgICAgICA/IEpTT04ucGFyc2UodGhpcy5mbG93VGVtcGxhdGVJbmZvLmZsb3dUZW1wbGF0ZUpzb24pCiAgICAgICAgICAgIDogbnVsbDsqLwogICAgICAgICAgdGhpcy5pc0NvbW1lbnQgPSB0aGlzLmZsb3dUZW1wbGF0ZUpzb24ucHJvcGVydGllcy5pc0NvbW1lbnQ7CiAgICAgICAgICB0aGlzLmlzU3VtbWFyeSA9IHRoaXMuZmxvd1RlbXBsYXRlSnNvbi5wcm9wZXJ0aWVzLmlzU3VtbWFyeTsKICAgICAgICAgIHRoaXMuc3VtbWFyeVR5cGUgPSB0aGlzLmZsb3dUZW1wbGF0ZUpzb24ucHJvcGVydGllcy5zdW1tYXJ5VHlwZTsKICAgICAgICAgIHRoaXMuZmxvd1Rhc2tPcGVyYXRvclJlY29yZExpc3QgPQogICAgICAgICAgICByZXMuZGF0YS5mbG93VGFza09wZXJhdG9yUmVjb3JkTGlzdCB8fCBbXTsKICAgICAgICAgIHRoaXMuZmxvd1Rhc2tPcGVyYXRvclJlY29yZExpc3QgPQogICAgICAgICAgICB0aGlzLmZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0LnJldmVyc2UoKTsKICAgICAgICAgIHRoaXMucHJvcGVydGllcyA9IHJlcy5kYXRhLmFwcHJvdmVyc1Byb3BlcnRpZXMgfHwge307CiAgICAgICAgICBpZih0aGlzLnByb3BlcnRpZXMgJiYgdGhpcy5wcm9wZXJ0aWVzLmZsb3dWYXJpYWJsZSl7CiAgICAgICAgICAgIGRhdGEuZmxvd1ZhcmlhYmxlID0gdGhpcy5wcm9wZXJ0aWVzLmZsb3dWYXJpYWJsZTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuY2FuZGlkYXRlRm9ybS5yZWplY3RUeXBlID0KICAgICAgICAgICAgdGhpcy5wcm9wZXJ0aWVzLnJlamVjdFR5cGUgPT0gMyA/IDEgOiB0aGlzLnByb3BlcnRpZXMucmVqZWN0VHlwZTsKICAgICAgICAgIHRoaXMuZW5kVGltZSA9CiAgICAgICAgICAgIHRoaXMuZmxvd1Rhc2tJbmZvLmNvbXBsZXRpb24gPT0gMTAwID8gdGhpcy5mbG93VGFza0luZm8uZW5kVGltZSA6IDA7CiAgICAgICAgICBkYXRhLmZvcm1Db25mID0gdGhpcy5mbG93Rm9ybUluZm8ucHJvcGVydHlKc29uOwogICAgICAgICAgaWYgKGRhdGEub3BUeXBlICE9IDEgJiYgZGF0YS5vcFR5cGUgIT0gIi0xIikgZGF0YS5yZWFkb25seSA9IHRydWU7CiAgICAgICAgICBkYXRhLmZvcm1PcGVyYXRlcyA9IHJlcy5kYXRhLmZvcm1PcGVyYXRlcyB8fCBbXTsKICAgICAgICAgIGlmIChkYXRhLm9wVHlwZSA9PSAwKSB7CiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5mb3JtT3BlcmF0ZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICBkYXRhLmZvcm1PcGVyYXRlc1tpXS53cml0ZSA9IGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICBkYXRhLmZsb3dUZW1wbGF0ZUpzb24gPSB0aGlzLmZsb3dUZW1wbGF0ZUpzb247CiAgICAgICAgICBpZiAodGhpcy5mbG93VGFza05vZGVMaXN0Lmxlbmd0aCkgewogICAgICAgICAgICBsZXQgYXNzaWduTm9kZUxpc3QgPSBbXTsKICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZsb3dUYXNrTm9kZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICBjb25zdCBub2RlSXRlbSA9IHRoaXMuZmxvd1Rhc2tOb2RlTGlzdFtpXTsKICAgICAgICAgICAgICBkYXRhLm9wVHlwZSA9PSA0ICYmCiAgICAgICAgICAgICAgbm9kZUl0ZW0udHlwZSA9PSAxICYmCiAgICAgICAgICAgICAgbm9kZUl0ZW0ubm9kZVR5cGUgPT09ICJhcHByb3ZlciIgJiYKICAgICAgICAgICAgICBhc3NpZ25Ob2RlTGlzdC5wdXNoKG5vZGVJdGVtKTsKICAgICAgICAgICAgICBjb25zdCBsb29wID0gKGRhdGEpID0+IHsKICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpKSBkYXRhLmZvckVhY2goKGQpID0+IGxvb3AoZCkpOwogICAgICAgICAgICAgICAgaWYgKGRhdGEubm9kZUlkID09PSBub2RlSXRlbS5ub2RlQ29kZSkgewogICAgICAgICAgICAgICAgICBpZiAobm9kZUl0ZW0udHlwZSA9PSAwKSBkYXRhLnN0YXRlID0gInN0YXRlLXBhc3QiOwogICAgICAgICAgICAgICAgICBpZiAobm9kZUl0ZW0udHlwZSA9PSAxKSBkYXRhLnN0YXRlID0gInN0YXRlLWN1cnIiOwogICAgICAgICAgICAgICAgICBpZiAoCiAgICAgICAgICAgICAgICAgICAgbm9kZUl0ZW0ubm9kZVR5cGUgPT09ICJhcHByb3ZlciIgfHwKICAgICAgICAgICAgICAgICAgICBub2RlSXRlbS5ub2RlVHlwZSA9PT0gInN0YXJ0IiB8fAogICAgICAgICAgICAgICAgICAgIG5vZGVJdGVtLm5vZGVUeXBlID09PSAic3ViRmxvdyIKICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgIGRhdGEuY29udGVudCA9IG5vZGVJdGVtLnVzZXJOYW1lOwogICAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoZGF0YS5jb25kaXRpb25Ob2RlcyAmJiBBcnJheS5pc0FycmF5KGRhdGEuY29uZGl0aW9uTm9kZXMpKQogICAgICAgICAgICAgICAgICBsb29wKGRhdGEuY29uZGl0aW9uTm9kZXMpOwogICAgICAgICAgICAgICAgaWYgKGRhdGEuY2hpbGROb2RlKSBsb29wKGRhdGEuY2hpbGROb2RlKTsKICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIGxvb3AodGhpcy5mbG93VGVtcGxhdGVKc29uKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmFzc2lnbk5vZGVMaXN0ID0gYXNzaWduTm9kZUxpc3Q7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmZsb3dUZW1wbGF0ZUpzb24uc3RhdGUgPSAic3RhdGUtY3VyciI7CiAgICAgICAgICB9CiAgICAgICAgICBkYXRhLmZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0ID0gdGhpcy5mbG93VGFza09wZXJhdG9yUmVjb3JkTGlzdDsKICAgICAgICAgIHRoaXMuaW5pdEJ0bkxpc3QoKTsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtICYmIHRoaXMuJHJlZnMuZm9ybS5pbml0KGRhdGEpOwogICAgICAgICAgICAgIGlmICghdGhpcy4kcmVmcy5mb3JtKQogICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybSAmJiB0aGlzLiRyZWZzLmZvcm0uaW5pdChkYXRhKTsKICAgICAgICAgICAgICAgIH0sIDUwMCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSwgNTAwKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KQogICAgICAgIC5maW5hbGx5KCgpID0+IHsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICB0aGlzLmxvb3BXb3JkQnRucygpOwogICAgICAgICAgfSw1MDApOwogICAgICAgIH0pOwogICAgfSwKICAgIGluaXRCdG5MaXN0KCkgewogICAgICBjb25zdCBsaXN0ID0gW107CiAgICAgIGNvbnN0IHNldHRpbmcgPSB0aGlzLnNldHRpbmc7CiAgICAgIGNvbnN0IG9wVHlwZSA9IHRoaXMuc2V0dGluZy5vcFR5cGU7CiAgICAgIGNvbnN0IHByb3BlcnRpZXMgPSB0aGlzLnByb3BlcnRpZXM7CiAgICAgIGNvbnN0IGZsb3dUYXNrSW5mbyA9IHRoaXMuZmxvd1Rhc2tJbmZvOwogICAgICBpZiAob3BUeXBlID09ICItMSIgJiYgIXNldHRpbmcuaGlkZUNhbmNlbEJ0bikKICAgICAgICAvL2xpc3QucHVzaCh7IGxhYmVsOiBwcm9wZXJ0aWVzLnNhdmVCdG5UZXh0IHx8ICLmmoIg5a2YIiwga2V5OiAic2F2ZSIgfSk7CiAgICAgICAgaWYgKAogICAgICAgICAgb3BUeXBlID09IDAgJiYKICAgICAgICAgIHNldHRpbmcuc3RhdHVzID09IDEgJiYKICAgICAgICAgIChwcm9wZXJ0aWVzLmhhc1Jldm9rZUJ0biB8fCBwcm9wZXJ0aWVzLmhhc1Jldm9rZUJ0biA9PT0gdW5kZWZpbmVkKQogICAgICAgICkKICAgICAgICAgIGxpc3QucHVzaCh7CiAgICAgICAgICAgIGxhYmVsOiBwcm9wZXJ0aWVzLnJldm9rZUJ0blRleHQgfHwgIuaSpCDlm54iLAogICAgICAgICAgICBrZXk6ICJyZXZva2UiLAogICAgICAgICAgfSk7CiAgICAgIGlmICgKICAgICAgICBvcFR5cGUgIT0gNCAmJgogICAgICAgIHNldHRpbmcuaWQgJiYKICAgICAgICBwcm9wZXJ0aWVzLmhhc1ByaW50QnRuICYmCiAgICAgICAgcHJvcGVydGllcy5wcmludElkCiAgICAgICkKICAgICAgICBsaXN0LnB1c2goeyBsYWJlbDogcHJvcGVydGllcy5wcmludEJ0blRleHQgfHwgIuaJkyDljbAiLCBrZXk6ICJwcmludCIgfSk7CiAgICAgIGlmIChvcFR5cGUgPT0gMSkgewogICAgICAgIGlmIChwcm9wZXJ0aWVzLmhhc1RyYW5zZmVyQnRuKQogICAgICAgICAgbGlzdC5wdXNoKHsKICAgICAgICAgICAgbGFiZWw6IHByb3BlcnRpZXMudHJhbnNmZXJCdG5UZXh0IHx8ICLovawg5a6hIiwKICAgICAgICAgICAga2V5OiAidHJhbnNmZXIiLAogICAgICAgICAgfSk7CiAgICAgICAgaWYgKHByb3BlcnRpZXMuaGFzU2F2ZUJ0bikKICAgICAgICAgIGxpc3QucHVzaCh7CiAgICAgICAgICAgIGxhYmVsOiBwcm9wZXJ0aWVzLnNhdmVCdG5UZXh0IHx8ICLmmoIg5a2YIiwKICAgICAgICAgICAga2V5OiAic2F2ZUF1ZGl0IiwKICAgICAgICAgIH0pOwogICAgICAgIGlmIChwcm9wZXJ0aWVzLmhhc1JlamVjdEJ0bikKICAgICAgICAgIGxpc3QucHVzaCh7CiAgICAgICAgICAgIGxhYmVsOiBwcm9wZXJ0aWVzLnJlamVjdEJ0blRleHQgfHwgIumAgCDlm54iLAogICAgICAgICAgICBrZXk6ICJyZWplY3QiLAogICAgICAgICAgfSk7CiAgICAgICAgaWYgKHByb3BlcnRpZXMuaGFzRnJlZUFwcHJvdmVyQnRuKQogICAgICAgICAgbGlzdC5wdXNoKHsKICAgICAgICAgICAgbGFiZWw6IHByb3BlcnRpZXMuaGFzRnJlZUFwcHJvdmVyQnRuVGV4dCB8fCAi5YqgIOetviIsCiAgICAgICAgICAgIGtleTogImhhc0ZyZWVBcHByb3ZlciIsCiAgICAgICAgICB9KTsKICAgICAgfQogICAgICBpZiAob3BUeXBlID09IDQpIHsKICAgICAgICBpZiAoZmxvd1Rhc2tJbmZvLmNvbXBsZXRpb24gPT0gMTAwKQogICAgICAgICAgbGlzdC5wdXNoKHsgbGFiZWw6ICLlpI0g5rS7Iiwga2V5OiAicmVzdXJnZW5jZSIgfSk7CiAgICAgICAgaWYgKAogICAgICAgICAgZmxvd1Rhc2tJbmZvLmNvbXBsZXRpb24gPiAwICYmCiAgICAgICAgICBmbG93VGFza0luZm8uY29tcGxldGlvbiA8IDEwMCAmJgogICAgICAgICAgIWZsb3dUYXNrSW5mby5yZWplY3REYXRhSWQgJiYKICAgICAgICAgIChzZXR0aW5nLnN0YXR1cyA9PSAxIHx8IHNldHRpbmcuc3RhdHVzID09IDMpCiAgICAgICAgKQogICAgICAgICAgbGlzdC5wdXNoKHsgbGFiZWw6ICLlj5gg5pu0Iiwga2V5OiAicmVzdXJnZW5jZSIgfSk7CiAgICAgICAgaWYgKHNldHRpbmcuc3RhdHVzID09IDEgJiYgdGhpcy5hc3NpZ25Ob2RlTGlzdC5sZW5ndGgpCiAgICAgICAgICBsaXN0LnB1c2goeyBsYWJlbDogIuaMhyDmtL4iLCBrZXk6ICJhc3NpZ24iIH0pOwogICAgICAgIGlmIChmbG93VGFza0luZm8uc3RhdHVzID09IDEpCiAgICAgICAgICBsaXN0LnB1c2goeyBsYWJlbDogIuaMgiDotbciLCBrZXk6ICJzdXNwZW5kIiB9KTsKICAgICAgICBpZiAoZmxvd1Rhc2tJbmZvLnN0YXR1cyA9PSA2ICYmICFmbG93VGFza0luZm8uc3VzcGVuZCkKICAgICAgICAgIGxpc3QucHVzaCh7IGxhYmVsOiAi5oGiIOWkjSIsIGtleTogInJlY292ZXJ5IiB9KTsKICAgICAgfQogICAgICB0aGlzLm1vcmVCdG5MaXN0ID0gbGlzdDsKICAgIH0sCiAgICBzdWJGbG93SW5mbyhkYXRhKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLmFjdGl2ZVRhYiA9ICIwIjsKICAgICAgdGhpcy5zdWJGbG93VmlzaWJsZSA9IHRydWU7CiAgICAgIHN1YkZsb3dJbmZvKGRhdGFbMF0uaWQpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgdGhpcy5zdWJGbG93SW5mb0xpc3QgPSByZXMuZGF0YSB8fCBbXTsKICAgICAgICAgIHRoaXMuc3ViRmxvd1RhYiA9IHRoaXMuc3ViRmxvd0luZm9MaXN0WzBdLmZsb3dUYXNrSW5mby5pZDsKICAgICAgICAgIHRoaXMuZmxvd1VyZ2VudCA9CiAgICAgICAgICAgIHRoaXMuc3ViRmxvd0luZm9MaXN0WzBdLmZsb3dUYXNrSW5mby5mbG93VXJnZW50IHx8IDE7CiAgICAgICAgICB0aGlzLmZ1bGxOYW1lID0gdGhpcy5zdWJGbG93SW5mb0xpc3RbMF0uZmxvd1Rhc2tJbmZvLmZ1bGxOYW1lOwogICAgICAgICAgdGhpcy5mbG93VGFza09wZXJhdG9yUmVjb3JkTGlzdCA9CiAgICAgICAgICAgIHRoaXMuc3ViRmxvd0luZm9MaXN0WzBdLmZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0IHx8IFtdOwogICAgICAgICAgdGhpcy5mbG93VGFza09wZXJhdG9yUmVjb3JkTGlzdCA9CiAgICAgICAgICAgIHRoaXMuZmxvd1Rhc2tPcGVyYXRvclJlY29yZExpc3QucmV2ZXJzZSgpOwogICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuc3ViRmxvd0luZm9MaXN0Lmxlbmd0aDsgaW5kZXgrKykgewogICAgICAgICAgICBsZXQgZWxlbWVudCA9IHRoaXMuc3ViRmxvd0luZm9MaXN0W2luZGV4XTsKICAgICAgICAgICAgZWxlbWVudC5mbG93VGVtcGxhdGVJbmZvLmZsb3dUZW1wbGF0ZUpzb24gPSBlbGVtZW50LmZsb3dUZW1wbGF0ZUluZm8KICAgICAgICAgICAgICA/IEpTT04ucGFyc2UoZWxlbWVudC5mbG93VGVtcGxhdGVJbmZvLmZsb3dUZW1wbGF0ZUpzb24pCiAgICAgICAgICAgICAgOiB7fTsKICAgICAgICAgICAgaWYgKGVsZW1lbnQuZmxvd1Rhc2tOb2RlTGlzdC5sZW5ndGgpIHsKICAgICAgICAgICAgICBsZXQgYXNzaWduTm9kZUxpc3QgPSBbXTsKICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGVsZW1lbnQuZmxvd1Rhc2tOb2RlTGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgICAgY29uc3Qgbm9kZUl0ZW0gPSBlbGVtZW50LmZsb3dUYXNrTm9kZUxpc3RbaV07CiAgICAgICAgICAgICAgICBkYXRhLm9wVHlwZSA9PSA0ICYmCiAgICAgICAgICAgICAgICBub2RlSXRlbS50eXBlID09IDEgJiYKICAgICAgICAgICAgICAgIG5vZGVJdGVtLm5vZGVUeXBlID09PSAiYXBwcm92ZXIiICYmCiAgICAgICAgICAgICAgICBhc3NpZ25Ob2RlTGlzdC5wdXNoKG5vZGVJdGVtKTsKICAgICAgICAgICAgICAgIGNvbnN0IGxvb3AgPSAoZGF0YSkgPT4gewogICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSkgZGF0YS5mb3JFYWNoKChkKSA9PiBsb29wKGQpKTsKICAgICAgICAgICAgICAgICAgaWYgKGRhdGEubm9kZUlkID09PSBub2RlSXRlbS5ub2RlQ29kZSkgewogICAgICAgICAgICAgICAgICAgIGlmIChub2RlSXRlbS50eXBlID09IDApIGRhdGEuc3RhdGUgPSAic3RhdGUtcGFzdCI7CiAgICAgICAgICAgICAgICAgICAgaWYgKG5vZGVJdGVtLnR5cGUgPT0gMSkgZGF0YS5zdGF0ZSA9ICJzdGF0ZS1jdXJyIjsKICAgICAgICAgICAgICAgICAgICBpZiAoCiAgICAgICAgICAgICAgICAgICAgICBub2RlSXRlbS5ub2RlVHlwZSA9PT0gImFwcHJvdmVyIiB8fAogICAgICAgICAgICAgICAgICAgICAgbm9kZUl0ZW0ubm9kZVR5cGUgPT09ICJzdGFydCIgfHwKICAgICAgICAgICAgICAgICAgICAgIG5vZGVJdGVtLm5vZGVUeXBlID09PSAic3ViRmxvdyIKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICBkYXRhLmNvbnRlbnQgPSBub2RlSXRlbS51c2VyTmFtZTsKICAgICAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgaWYgKGRhdGEuY29uZGl0aW9uTm9kZXMgJiYgQXJyYXkuaXNBcnJheShkYXRhLmNvbmRpdGlvbk5vZGVzKSkKICAgICAgICAgICAgICAgICAgICBsb29wKGRhdGEuY29uZGl0aW9uTm9kZXMpOwogICAgICAgICAgICAgICAgICBpZiAoZGF0YS5jaGlsZE5vZGUpIGxvb3AoZGF0YS5jaGlsZE5vZGUpOwogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIGxvb3AoZWxlbWVudC5mbG93VGVtcGxhdGVJbmZvLmZsb3dUZW1wbGF0ZUpzb24pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBlbGVtZW50LmFzc2lnbk5vZGVMaXN0ID0gYXNzaWduTm9kZUxpc3Q7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgZWxlbWVudC5mbG93VGVtcGxhdGVJbmZvLmZsb3dUZW1wbGF0ZUpzb24uc3RhdGUgPSAic3RhdGUtY3VyciI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgbGV0IHRlbXBsYXRlSnNvbiA9IHRoaXMuc3ViRmxvd0luZm9MaXN0WzBdLmZsb3dUYXNrSW5mbwogICAgICAgICAgICAgIC5mbG93VGVtcGxhdGVKc29uCiAgICAgICAgICAgICAgPyBKU09OLnBhcnNlKAogICAgICAgICAgICAgICAgdGhpcy5zdWJGbG93SW5mb0xpc3RbMF0uZmxvd1Rhc2tJbmZvLmZsb3dUZW1wbGF0ZUpzb24KICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgOiBudWxsOwogICAgICAgICAgICB0aGlzLmlzQ29tbWVudCA9IHRlbXBsYXRlSnNvbi5wcm9wZXJ0aWVzLmlzQ29tbWVudDsKICAgICAgICAgICAgdGhpcy5pc1N1bW1hcnkgPSB0ZW1wbGF0ZUpzb24ucHJvcGVydGllcy5pc1N1bW1hcnk7CiAgICAgICAgICAgIHRoaXMuc3VtbWFyeVR5cGUgPSB0ZW1wbGF0ZUpzb24ucHJvcGVydGllcy5zdW1tYXJ5VHlwZTsKICAgICAgICAgICAgdGhpcy5zZXR0aW5nLmlkID0gdGhpcy5zdWJGbG93SW5mb0xpc3RbMF0uZmxvd1Rhc2tJbmZvLmlkOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgfSwKICAgIHByaW50QnJvd3NlSGFuZGxlKGlkKSB7CiAgICAgIHRoaXMucHJpbnRUZW1wbGF0ZUlkID0gaWQ7CiAgICAgIHRoaXMucHJpbnREaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMucHJpbnRCcm93c2VWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBwcmludERpYWxvZygpIHsKICAgICAgdGhpcy5wcmludERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5wcmludERpYWxvZy5pbml0KHRoaXMucHJvcGVydGllcy5wcmludElkKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlTW9yZShlKSB7CiAgICAgIGlmIChlID09ICJyZXZva2UiKSByZXR1cm4gdGhpcy5hY3Rpb25MYXVuY2hlcigicmV2b2tlIik7CiAgICAgIGlmIChlID09ICJ0cmFuc2ZlciIpIHJldHVybiB0aGlzLmFjdGlvbkxhdW5jaGVyKCJ0cmFuc2ZlciIpOwogICAgICBpZiAoZSA9PSAic2F2ZUF1ZGl0IikgcmV0dXJuIHRoaXMuZXZlbnRMYXVuY2hlcigic2F2ZUF1ZGl0Iik7CiAgICAgIGlmIChlID09ICJyZWplY3QiKSByZXR1cm4gdGhpcy5ldmVudFJlY2VpdmVyKHt9LCAicmVqZWN0Iik7CiAgICAgIGlmIChlID09ICJyZXN1cmdlbmNlIikgcmV0dXJuIHRoaXMuZmxvd1Jlc3VyZ2VuY2UoKTsKICAgICAgaWYgKGUgPT0gImFzc2lnbiIpIHJldHVybiB0aGlzLmFjdGlvbkxhdW5jaGVyKCJhc3NpZ24iKTsKICAgICAgaWYgKGUgPT0gImNvbW1lbnQiKSByZXR1cm4gdGhpcy5hZGRDb21tZW50KCk7CiAgICAgIGlmIChlID09ICJwcmludCIpIHJldHVybiB0aGlzLnByaW50RGlhbG9nKCk7CiAgICAgIGlmIChlID09ICJzdXNwZW5kIikgcmV0dXJuIHRoaXMuc3VzcGVuZCgpOwogICAgICBpZiAoZSA9PSAicmVjb3ZlcnkiKSByZXR1cm4gdGhpcy5yZWNvdmVyeSgpOwogICAgICB0aGlzLmV2ZW50TGF1bmNoZXIoZSk7CiAgICB9LAogICAgc3VzcGVuZCgpIHsKICAgICAgdGhpcy5zdXNwZW5kVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLnN1c3BlbmREaWFsb2cuaW5pdCh0aGlzLnNldHRpbmcuaWQpOwogICAgICB9KTsKICAgIH0sCiAgICByZWNvdmVyeSgpIHsKICAgICAgbGV0IGRhdGEgPSB7CiAgICAgICAgaGFuZGxlT3BpbmlvbjogIiIsCiAgICAgICAgZmlsZUxpc3Q6IFtdLAogICAgICB9OwogICAgICByZXN0b3JlKHRoaXMuc2V0dGluZy5pZCwgZGF0YSkKICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLm1zZywKICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICBkdXJhdGlvbjogMTUwMCwKICAgICAgICAgICAgb25DbG9zZTogKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJGVtaXQoImNsb3NlIiwgdHJ1ZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRyZWZzLnN1c3BlbmREaWFsb2cuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgfSwKICAgIHN1c3BlbmRSZWNlaXZlcihkYXRhRm9ybSkgewogICAgICBzdXNwZW5kKHRoaXMuc2V0dGluZy5pZCwgZGF0YUZvcm0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogcmVzLm1zZywKICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgIGR1cmF0aW9uOiAxNTAwLAogICAgICAgICAgb25DbG9zZTogKCkgPT4gewogICAgICAgICAgICB0aGlzLiRlbWl0KCJjbG9zZSIsIHRydWUpOwogICAgICAgICAgfSwKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgZXZlbnRMYXVuY2hlcihldmVudFR5cGUpIHsKICAgICAgdGhpcy4kcmVmcy5mb3JtICYmCiAgICAgIHRoaXMuJHJlZnMuZm9ybS5kYXRhRm9ybVN1Ym1pdChldmVudFR5cGUsIHRoaXMuZmxvd1VyZ2VudCk7CiAgICB9LAogICAgZXZlbnRSZWNlaXZlcihmb3JtRGF0YSwgZXZlbnRUeXBlKSB7CiAgICAgIHRoaXMuZm9ybURhdGEgPSBmb3JtRGF0YTsKICAgICAgdGhpcy5mb3JtRGF0YS5mbG93SWQgPSB0aGlzLnNldHRpbmcuZmxvd0lkOwogICAgICB0aGlzLmZvcm1EYXRhLmlkID0gdGhpcy5zZXR0aW5nLmlkOwogICAgICB0aGlzLmV2ZW50VHlwZSA9IGV2ZW50VHlwZTsKICAgICAgaWYgKGV2ZW50VHlwZSA9PT0gInNhdmUiIHx8IGV2ZW50VHlwZSA9PT0gInN1Ym1pdCIpIHsKICAgICAgICByZXR1cm4gdGhpcy5zdWJtaXRPclNhdmUoKTsKICAgICAgfQogICAgICBpZiAoZXZlbnRUeXBlID09PSAic2F2ZUF1ZGl0IikgewogICAgICAgIHJldHVybiB0aGlzLnNhdmVBdWRpdCgpOwogICAgICB9CiAgICAgIGlmIChldmVudFR5cGUgPT09ICJoYXNGcmVlQXBwcm92ZXIiKSB7CiAgICAgICAgcmV0dXJuICh0aGlzLmhhc0ZyZWVBcHByb3ZlclZpc2libGUgPSB0cnVlKTsKICAgICAgfQogICAgICBpZiAoZXZlbnRUeXBlID09PSAiYXVkaXQiIHx8IGV2ZW50VHlwZSA9PT0gInJlamVjdCIpIHsKICAgICAgICB0aGlzLmhhbmRsZUlkID0gIiI7CiAgICAgICAgdGhpcy5jYW5kaWRhdGVGb3JtLmhhbmRsZU9waW5pb24gPSAiIjsKICAgICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uZmlsZUxpc3QgPSBbXTsKICAgICAgICB0aGlzLmNvcHlJZHMgPSBbXTsKICAgICAgICB0aGlzLmlzVmFsaWRhdGUgPSBmYWxzZTsKICAgICAgICAvLyBpZiAodGhpcy5wcm9wZXJ0aWVzLmhhc1NpZ24pIHRoaXMuc2lnbkltZyA9IHRoaXMudXNlckluZm8uc2lnbkltZzsKICAgICAgICBpZiAoZXZlbnRUeXBlID09PSAicmVqZWN0IikgewogICAgICAgICAgUmVqZWN0TGlzdCh0aGlzLnNldHRpbmcudGFza0lkP3RoaXMuc2V0dGluZy50YXNrSWQ6dGhpcy5zZXR0aW5nLmlkKQogICAgICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5zaG93UmVqZWN0ID0gcmVzLmRhdGEuaXNMYXN0QXBwcm87CiAgICAgICAgICAgICAgdGhpcy5yZWplY3RMaXN0ID0gcmVzLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICAgICAgICBpZih0aGlzLnByb3BlcnRpZXMucmVqZWN0U3RlcCAhPT0gJzInKXsKICAgICAgICAgICAgICAgIGlmKHRoaXMucmVqZWN0TGlzdCAmJiB0aGlzLnJlamVjdExpc3QubGVuZ3RoID09PSAxKXsKICAgICAgICAgICAgICAgICAgdGhpcy5jYW5kaWRhdGVGb3JtLnJlamVjdFN0ZXAgPSBbdGhpcy5yZWplY3RMaXN0WzBdLm5vZGVDb2RlXTsKICAgICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0ucmVqZWN0U3RlcCA9IFtdOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoCiAgICAgICAgICAgICAgICAhdGhpcy5wcm9wZXJ0aWVzLmhhc1NpZ24gJiYKICAgICAgICAgICAgICAgICF0aGlzLnByb3BlcnRpZXMuaGFzT3BpbmlvbiAmJgogICAgICAgICAgICAgICAgIXRoaXMucHJvcGVydGllcy5pc0N1c3RvbUNvcHkgJiYKICAgICAgICAgICAgICAgICF0aGlzLnNob3dSZWplY3QKICAgICAgICAgICAgICApIHsKICAgICAgICAgICAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhumAgOWbnuivpeWuoeaJueWNle+8jOaYr+WQpue7p+e7re+8nyIsICLmj5DnpLoiLCB7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZUFwcHJvdmFsKCk7CiAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRoaXMuaXNWYWxpZGF0ZSA9IHRydWU7CiAgICAgICAgICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKHt9KTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgdGhpcy5jYW5kaWRhdGVMb2FkaW5nID0gdHJ1ZTsKICAgICAgICBDYW5kaWRhdGVzKHRoaXMuc2V0dGluZy50YXNrSWQ/dGhpcy5zZXR0aW5nLnRhc2tJZDp0aGlzLmZsb3dUYXNrSW5mby50aGlzT3BlcmF0b3JJZCwgdGhpcy5mb3JtRGF0YSkKICAgICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgbGV0IGRhdGEgPSByZXMuZGF0YTsKICAgICAgICAgICAgdGhpcy5jYW5kaWRhdGVUeXBlID0gZGF0YS50eXBlOwogICAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5jYW5kaWRhdGVGb3JtLmJyYW5jaExpc3QgPSBbXTsKICAgICAgICAgICAgdGhpcy5icmFuY2hMaXN0ID0gW107CiAgICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT0gMSkgewogICAgICAgICAgICAgIHRoaXMuYnJhbmNoTGlzdCA9IHJlcy5kYXRhLmxpc3QuZmlsdGVyKChvKSA9PiBvLmlzQnJhbmNoRmxvdyk7CiAgICAgICAgICAgICAgbGV0IGxpc3QgPSByZXMuZGF0YS5saXN0LmZpbHRlcigKICAgICAgICAgICAgICAgIChvKSA9PiAhby5pc0JyYW5jaEZsb3cgJiYgby5pc0NhbmRpZGF0ZXMKICAgICAgICAgICAgICApOwogICAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlRm9ybS5jYW5kaWRhdGVMaXN0ID0gbGlzdC5tYXAoKG8pID0+ICh7CiAgICAgICAgICAgICAgICAuLi5vLAogICAgICAgICAgICAgICAgaXNEZWZhdWx0OiB0cnVlLAogICAgICAgICAgICAgICAgbGFiZWw6ICLlrqHmibnkuroiLAogICAgICAgICAgICAgICAgdmFsdWU6IFtdLAogICAgICAgICAgICAgICAgcnVsZXM6IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGDlrqHmibnkurrkuI3og73kuLrnqbpgLAogICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJjbGljayIsCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgaXNTZWxmRGVwdDogby5pc1NlbGZEZXB0CiAgICAgICAgICAgICAgfSkpOwogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbImNhbmRpZGF0ZUZvcm0iXS5yZXNldEZpZWxkcygpOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuaXNWYWxpZGF0ZSA9IHRydWU7CiAgICAgICAgICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT0gMikgewogICAgICAgICAgICAgIGxldCBsaXN0ID0gcmVzLmRhdGEubGlzdC5maWx0ZXIoKG8pID0+IG8uaXNDYW5kaWRhdGVzKTsKICAgICAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdCA9IGxpc3QubWFwKChvKSA9PiAoewogICAgICAgICAgICAgICAgLi4ubywKICAgICAgICAgICAgICAgIGxhYmVsOiAi5a6h5om55Lq6IiwKICAgICAgICAgICAgICAgIHZhbHVlOiBbXSwKICAgICAgICAgICAgICAgIHJ1bGVzOiBbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBg5a6h5om55Lq65LiN6IO95Li656m6YCwKICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiY2xpY2siLAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kcmVmc1siY2FuZGlkYXRlRm9ybSJdLnJlc2V0RmllbGRzKCk7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5pc1ZhbGlkYXRlID0gdHJ1ZTsKICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSB0cnVlOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlRm9ybS5jYW5kaWRhdGVMaXN0ID0gW107CiAgICAgICAgICAgICAgaWYgKAogICAgICAgICAgICAgICAgIXRoaXMucHJvcGVydGllcy5oYXNTaWduICYmCiAgICAgICAgICAgICAgICAhdGhpcy5wcm9wZXJ0aWVzLmhhc09waW5pb24gJiYKICAgICAgICAgICAgICAgICF0aGlzLnByb3BlcnRpZXMuaGFzRnJlZUFwcHJvdmVyICYmCiAgICAgICAgICAgICAgICAhdGhpcy5wcm9wZXJ0aWVzLmlzQ3VzdG9tQ29weQogICAgICAgICAgICAgICkgewogICAgICAgICAgICAgICAgbGV0IGNvbmZpcm1Nc2cgPSAi5q2k5pON5L2c5bCG6YCa6L+H6K+l5a6h5om55Y2V77yM5piv5ZCm57un57ut77yfIjsKICAgICAgICAgICAgICAgIGlmKHRoaXMucHJvcGVydGllcyAmJiB0aGlzLnByb3BlcnRpZXMuc3VibWl0QnRuVGV4dC5pbmRleE9mKCfmj5DkuqQnKSAhPT0gLTEpewogICAgICAgICAgICAgICAgICBjb25maXJtTXNnID0gIuatpOaTjeS9nOWwhuaPkOS6pOivpeWuoeaJueWNle+8jOaYr+WQpue7p+e7re+8nyI7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB0aGlzLiRjb25maXJtKGNvbmZpcm1Nc2csICLmj5DnpLoiLCB7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZUFwcHJvdmFsKCk7CiAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRoaXMuaXNWYWxpZGF0ZSA9IHRydWU7CiAgICAgICAgICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBvbkJyYW5jaENoYW5nZSh2YWwpIHsKICAgICAgY29uc3QgZGVmYXVsdExpc3QgPSB0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdC5maWx0ZXIoCiAgICAgICAgKG8pID0+IG8uaXNEZWZhdWx0CiAgICAgICk7CiAgICAgIGlmICghdmFsLmxlbmd0aCkgcmV0dXJuICh0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdCA9IGRlZmF1bHRMaXN0KTsKICAgICAgbGV0IGxpc3QgPSBbXTsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpbm5lcjogZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLmJyYW5jaExpc3QubGVuZ3RoOyBqKyspIHsKICAgICAgICAgIGxldCBvID0gdGhpcy5icmFuY2hMaXN0W2pdOwogICAgICAgICAgaWYgKHZhbFtpXSA9PT0gby5ub2RlSWQgJiYgby5pc0NhbmRpZGF0ZXMpIHsKICAgICAgICAgICAgbGlzdC5wdXNoKHsKICAgICAgICAgICAgICAuLi5vLAogICAgICAgICAgICAgIGxhYmVsOiAi5a6h5om55Lq6IiwKICAgICAgICAgICAgICB2YWx1ZTogW10sCiAgICAgICAgICAgICAgcnVsZXM6IFsKICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6IGDlrqHmibnkurrkuI3og73kuLrnqbpgLCB0cmlnZ2VyOiAiY2xpY2siIH0sCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGJyZWFrIGlubmVyOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdCA9IFsuLi5kZWZhdWx0TGlzdCwgLi4ubGlzdF07CiAgICB9LAogICAgc2F2ZUF1ZGl0KCkgewogICAgICB0aGlzLmFsbEJ0bkRpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgU2F2ZUF1ZGl0KHRoaXMuc2V0dGluZy50YXNrSWQgfHwgdGhpcy5zZXR0aW5nLmlkIHx8IDAsIHRoaXMuZm9ybURhdGEpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgZHVyYXRpb246IDE1MDAsCiAgICAgICAgICAgIG9uQ2xvc2U6ICgpID0+IHsKICAgICAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmFsbEJ0bkRpc2FibGVkID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy4kZW1pdCgiY2xvc2UiLCB0cnVlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuYWxsQnRuRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgfSwKICAgIHN1Ym1pdE9yU2F2ZSgpIHsKICAgICAgdGhpcy5mb3JtRGF0YS5zdGF0dXMgPSB0aGlzLmV2ZW50VHlwZSA9PT0gInN1Ym1pdCIgPyAwIDogMTsKICAgICAgdGhpcy5mb3JtRGF0YS5mbG93VXJnZW50ID0gdGhpcy5mbG93VXJnZW50OwogICAgICBpZiAodGhpcy5zZXR0aW5nLmRlbGVnYXRlVXNlckxpc3QpIHsKICAgICAgICAvL+WPl+WnlOaJmOS6uuS4jeS4uuepuueahOaXtuWAmei1sOWnlOaJmOWIm+W7uua1geeoiwogICAgICAgIHRoaXMuZm9ybURhdGEuZGVsZWdhdGVVc2VyTGlzdCA9IHRoaXMuc2V0dGluZy5kZWxlZ2F0ZVVzZXJMaXN0OwogICAgICB9CgogICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJzYXZlIikgcmV0dXJuIHRoaXMuaGFuZGxlUmVxdWVzdCgpOwogICAgICB0aGlzLmNhbmRpZGF0ZUxvYWRpbmcgPSB0cnVlOwogICAgICBDYW5kaWRhdGVzKDAsIHRoaXMuZm9ybURhdGEpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgbGV0IGRhdGEgPSByZXMuZGF0YTsKICAgICAgICAgIHRoaXMuY2FuZGlkYXRlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgdGhpcy5jYW5kaWRhdGVUeXBlID0gZGF0YS50eXBlOwogICAgICAgICAgaWYgKGRhdGEudHlwZSA9PSAxKSB7CiAgICAgICAgICAgIHRoaXMuYnJhbmNoTGlzdCA9IHJlcy5kYXRhLmxpc3QuZmlsdGVyKChvKSA9PiBvLmlzQnJhbmNoRmxvdyk7CiAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlTGlzdCA9IHJlcy5kYXRhLmxpc3QuZmlsdGVyKAogICAgICAgICAgICAgIChvKSA9PiAhby5pc0JyYW5jaEZsb3cgJiYgby5pc0NhbmRpZGF0ZXMKICAgICAgICAgICAgKTsKICAgICAgICAgICAgdGhpcy5jYW5kaWRhdGVWaXNpYmxlID0gdHJ1ZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09IDIpIHsKICAgICAgICAgICAgdGhpcy5icmFuY2hMaXN0ID0gW107CiAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlTGlzdCA9IHJlcy5kYXRhLmxpc3QuZmlsdGVyKChvKSA9PiBvLmlzQ2FuZGlkYXRlcyk7CiAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBpZiAodGhpcy5wcm9wZXJ0aWVzLmlzQ3VzdG9tQ29weSkgewogICAgICAgICAgICAgIHRoaXMuYnJhbmNoTGlzdCA9IFtdOwogICAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlTGlzdCA9IFtdOwogICAgICAgICAgICAgIHRoaXMuY2FuZGlkYXRlVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuJGNvbmZpcm0oIuaCqOehruWumuimgeaPkOS6pOW9k+WJjea1geeoi+WQlywg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICAgICAgICB9KQogICAgICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUmVxdWVzdCgpOwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVSZXF1ZXN0KGNhbmRpZGF0ZURhdGEpIHsKICAgICAgaWYgKGNhbmRpZGF0ZURhdGEpIHRoaXMuZm9ybURhdGEgPSB7IC4uLnRoaXMuZm9ybURhdGEsIC4uLmNhbmRpZGF0ZURhdGEgfTsKICAgICAgdGhpcy5mb3JtRGF0YS5jYW5kaWRhdGVUeXBlID0gdGhpcy5jYW5kaWRhdGVUeXBlOwogICAgICBpZiAoIXRoaXMuZm9ybURhdGEuaWQpIGRlbGV0ZSB0aGlzLmZvcm1EYXRhLmlkOwogICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJzYXZlIikgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5hbGxCdG5EaXNhYmxlZCA9IHRydWU7CiAgICAgIGNvbnN0IGZvcm1NZXRob2QgPSB0aGlzLmZvcm1EYXRhLmlkID8gVXBkYXRlIDogQ3JlYXRlOwogICAgICBmb3JtTWV0aG9kKHRoaXMuZm9ybURhdGEpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gcmVzLmRhdGE7CiAgICAgICAgICBpZiAoZXJyb3JEYXRhICYmIEFycmF5LmlzQXJyYXkoZXJyb3JEYXRhKSAmJiBlcnJvckRhdGEubGVuZ3RoKSB7CiAgICAgICAgICAgIHRoaXMuZXJyb3JOb2RlTGlzdCA9IGVycm9yRGF0YTsKICAgICAgICAgICAgdGhpcy5lcnJvclZpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmFsbEJ0bkRpc2FibGVkID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMubXNnLAogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICBkdXJhdGlvbjogMTUwMCwKICAgICAgICAgICAgICBvbkNsb3NlOiAoKSA9PiB7CiAgICAgICAgICAgICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJzYXZlIikgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLmNhbmRpZGF0ZVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuYWxsQnRuRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCJjbG9zZSIsIHRydWUpOwogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gInNhdmUiKSB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuYWxsQnRuRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuZXJyb3JWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICBsZXQgY2FuZGlkYXRlRm9ybVJlZiA9IHRoaXMuJHJlZnMuY2FuZGlkYXRlRm9ybTsKICAgICAgICAgIGlmKGNhbmRpZGF0ZUZvcm1SZWYpewogICAgICAgICAgICBjYW5kaWRhdGVGb3JtUmVmLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRDYW5kaWRhdGUoZGF0YSkgewogICAgICB0aGlzLmhhbmRsZVJlcXVlc3QoZGF0YSk7CiAgICB9LAogICAgYWN0aW9uTGF1bmNoZXIoZXZlbnRUeXBlKSB7CiAgICAgIHRoaXMuZXZlbnRUeXBlID0gZXZlbnRUeXBlOwogICAgICBpZiAoCiAgICAgICAgKGV2ZW50VHlwZSA9PT0gInJldm9rZSIgfHwgZXZlbnRUeXBlID09PSAicmVjYWxsIikgJiYKICAgICAgICAhdGhpcy5wcm9wZXJ0aWVzLmhhc09waW5pb24gJiYKICAgICAgICAhdGhpcy5wcm9wZXJ0aWVzLmhhc1NpZ24KICAgICAgKSB7CiAgICAgICAgY29uc3QgdGl0bGUgPQogICAgICAgICAgdGhpcy5ldmVudFR5cGUgPT0gInJldm9rZSIKICAgICAgICAgICAgPyAi5q2k5pON5L2c5bCG5pKk5Zue6K+l5rWB56iL77yM5piv5ZCm57un57ut77yfIgogICAgICAgICAgICA6ICLmraTmk43kvZzlsIbmkqTlm57or6XlrqHmibnljZXvvIzmmK/lkKbnu6fnu63vvJ8iOwogICAgICAgIHRoaXMuJGNvbmZpcm0odGl0bGUsICLmj5DnpLoiLCB7CiAgICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgICAgfSkKICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5hY3Rpb25SZWNlaXZlcigpOwogICAgICAgICAgfSkKICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuc2hvd0FjdGlvbkRpYWxvZygpOwogICAgfSwKICAgIHNob3dBY3Rpb25EaWFsb2coKSB7CiAgICAgIHRoaXMuYWN0aW9uVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLmFjdGlvbkRpYWxvZy5pbml0KHRoaXMucHJvcGVydGllcywgdGhpcy5ldmVudFR5cGUpOwogICAgICB9KTsKICAgIH0sCiAgICBhY3Rpb25SZWNlaXZlcihxdWVyeSkgewogICAgICBpZiAoIXF1ZXJ5KSB7CiAgICAgICAgcXVlcnkgPSB7CiAgICAgICAgICBoYW5kbGVPcGluaW9uOiAiIiwKICAgICAgICAgIHNpZ25JbWc6ICIiLAogICAgICAgICAgZmlsZUxpc3Q6IFtdLAogICAgICAgIH07CiAgICAgIH0KICAgICAgY29uc3QgaWQgPQogICAgICAgIHRoaXMuZXZlbnRUeXBlID09ICJyZXZva2UiID8gdGhpcy5zZXR0aW5nLmlkIDogdGhpcy5zZXR0aW5nLnRhc2tJZDsKICAgICAgY29uc3QgYWN0aW9uTWV0aG9kID0gdGhpcy5nZXRBY3Rpb25NZXRob2QoKTsKICAgICAgdGhpcy5hcHByb3ZhbEJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICBhY3Rpb25NZXRob2QoaWQsIHF1ZXJ5KQogICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgIHRoaXMuYXBwcm92YWxCdG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICBtZXNzYWdlOiByZXMubXNnLAogICAgICAgICAgICBkdXJhdGlvbjogMTAwMCwKICAgICAgICAgICAgb25DbG9zZTogKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJGVtaXQoImNsb3NlIiwgdHJ1ZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRyZWZzLmFjdGlvbkRpYWxvZy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB0aGlzLmFwcHJvdmFsQnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgfSwKICAgIGdldEFjdGlvbk1ldGhvZCgpIHsKICAgICAgaWYgKHRoaXMuZXZlbnRUeXBlID09PSAidHJhbnNmZXIiKSByZXR1cm4gVHJhbnNmZXI7CiAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gImFzc2lnbiIpIHJldHVybiBBc3NpZ247CiAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gInJldm9rZSIpIHJldHVybiBSZXZva2U7CiAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gInJlY2FsbCIpIHJldHVybiBSZWNhbGw7CiAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gImNhbmNlbCIpIHJldHVybiBDYW5jZWw7CiAgICB9LAogICAgcHJlc3MoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuaPkOekuuivpeiKgueCueWwveW/q+WkhOeQhu+8jOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBQcmVzcyh0aGlzLnNldHRpbmcuaWQpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLm1zZywKICAgICAgICAgICAgICBkdXJhdGlvbjogMTAwMCwKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgaGFuZGxlRXJyb3IoZGF0YSkgewogICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJzdWJtaXQiKSB7CiAgICAgICAgdGhpcy5mb3JtRGF0YS5lcnJvclJ1bGVVc2VyTGlzdCA9IGRhdGE7CiAgICAgICAgdGhpcy5oYW5kbGVSZXF1ZXN0KCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLmV2ZW50VHlwZSA9PT0gImF1ZGl0IiB8fCB0aGlzLmV2ZW50VHlwZSA9PT0gInJlamVjdCIpIHsKICAgICAgICB0aGlzLmhhbmRsZUFwcHJvdmFsKGRhdGEpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJyZXN1cmdlbmNlIikgewogICAgICAgIHRoaXMuaGFuZGxlUmVzdXJnZW5jZShkYXRhKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVBcHByb3ZhbChlcnJvclJ1bGVVc2VyTGlzdCkgewogICAgICBjb25zdCBoYW5kbGVSZXF1ZXN0ID0gKCkgPT4gewogICAgICAgIGlmICh0aGlzLnByb3BlcnRpZXMuaGFzU2lnbiAmJiAhdGhpcy5zaWduSW1nKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogIuivt+etvuWQjSIsCiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgbGV0IHF1ZXJ5ID0gewogICAgICAgICAgaGFuZGxlT3BpbmlvbjogdGhpcy5jYW5kaWRhdGVGb3JtLmhhbmRsZU9waW5pb24sCiAgICAgICAgICBmaWxlTGlzdDogdGhpcy5jYW5kaWRhdGVGb3JtLmZpbGVMaXN0LAogICAgICAgICAgLi4udGhpcy5mb3JtRGF0YSwKICAgICAgICAgIGVuQ29kZTogdGhpcy5zZXR0aW5nLmVuQ29kZSwKICAgICAgICAgIHNpZ25JbWc6IHRoaXMuc2lnbkltZywKICAgICAgICAgIGNvcHlJZHM6IHRoaXMuY29weUlkcy5qb2luKCIsIiksCiAgICAgICAgICBicmFuY2hMaXN0OiB0aGlzLmNhbmRpZGF0ZUZvcm0uYnJhbmNoTGlzdCwKICAgICAgICAgIGNhbmRpZGF0ZVR5cGU6IHRoaXMuY2FuZGlkYXRlVHlwZSwKICAgICAgICAgIHJlamVjdFR5cGU6IHRoaXMuY2FuZGlkYXRlRm9ybS5yZWplY3RUeXBlLAogICAgICAgIH07CiAgICAgICAgaWYgKHRoaXMuZXZlbnRUeXBlID09PSAicmVqZWN0Iil7CiAgICAgICAgICBpZih0aGlzLmNhbmRpZGF0ZUZvcm0ucmVqZWN0U3RlcCBpbnN0YW5jZW9mIEFycmF5KXsKICAgICAgICAgICAgcXVlcnkucmVqZWN0U3RlcCA9IHRoaXMuY2FuZGlkYXRlRm9ybS5yZWplY3RTdGVwLmpvaW4oIiwiKTsKICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgcXVlcnkucmVqZWN0U3RlcCA9IHRoaXMuY2FuZGlkYXRlRm9ybS5yZWplY3RTdGVwOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICBpZiAoZXJyb3JSdWxlVXNlckxpc3QpIHF1ZXJ5LmVycm9yUnVsZVVzZXJMaXN0ID0gZXJyb3JSdWxlVXNlckxpc3Q7CiAgICAgICAgaWYgKHRoaXMuY2FuZGlkYXRlRm9ybS5jYW5kaWRhdGVMaXN0Lmxlbmd0aCkgewogICAgICAgICAgbGV0IGNhbmRpZGF0ZUxpc3QgPSB7fTsKICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5jYW5kaWRhdGVGb3JtLmNhbmRpZGF0ZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgY2FuZGlkYXRlTGlzdFt0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdFtpXS5ub2RlSWRdID0KICAgICAgICAgICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uY2FuZGlkYXRlTGlzdFtpXS52YWx1ZTsKICAgICAgICAgIH0KICAgICAgICAgIHF1ZXJ5LmNhbmRpZGF0ZUxpc3QgPSBjYW5kaWRhdGVMaXN0OwogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5ldmVudFR5cGUgPT09ICJhdWRpdCIgJiYgdGhpcy5wcm9wZXJ0aWVzLmhhc0ZyZWVBcHByb3ZlcikgewogICAgICAgICAgcXVlcnkgPSB7IGZyZWVBcHByb3ZlclVzZXJJZDogdGhpcy5oYW5kbGVJZCwgLi4ucXVlcnkgfTsKICAgICAgICB9CiAgICAgICAgY29uc3QgYXBwcm92YWxNZXRob2QgPSB0aGlzLmV2ZW50VHlwZSA9PT0gImF1ZGl0IiA/IEF1ZGl0IDogUmVqZWN0OwogICAgICAgIHRoaXMuYXBwcm92YWxCdG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgICBhcHByb3ZhbE1ldGhvZCh0aGlzLnNldHRpbmcudGFza0lkP3RoaXMuc2V0dGluZy50YXNrSWQ6dGhpcy5mbG93VGFza0luZm8udGhpc09wZXJhdG9ySWQsIHF1ZXJ5KQogICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICBjb25zdCBlcnJvckRhdGEgPSByZXMuZGF0YTsKICAgICAgICAgICAgaWYgKGVycm9yRGF0YSAmJiBBcnJheS5pc0FycmF5KGVycm9yRGF0YSkgJiYgZXJyb3JEYXRhLmxlbmd0aCkgewogICAgICAgICAgICAgIHRoaXMuZXJyb3JOb2RlTGlzdCA9IGVycm9yRGF0YTsKICAgICAgICAgICAgICB0aGlzLmVycm9yVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgICAgdGhpcy5hcHByb3ZhbEJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAwMCwKICAgICAgICAgICAgICAgIG9uQ2xvc2U6ICgpID0+IHsKICAgICAgICAgICAgICAgICAgdGhpcy5hcHByb3ZhbEJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoImNsb3NlIiwgdHJ1ZSk7CiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5hcHByb3ZhbEJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICB9OwogICAgICBpZiAoIXRoaXMuaXNWYWxpZGF0ZSkgcmV0dXJuIGhhbmRsZVJlcXVlc3QoKTsKICAgICAgdGhpcy4kcmVmc1siY2FuZGlkYXRlRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaGFuZGxlUmVxdWVzdCgpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgYWRkQ29tbWVudCgpIHsKICAgICAgdGhpcy4kcmVmcy5jb21tZW50ICYmIHRoaXMuJHJlZnMuY29tbWVudC5zaG93Q29tbWVudERpYWxvZygpOwogICAgfSwKICAgIHNldFBhZ2VMb2FkKHZhbCkgewogICAgICB0aGlzLmxvYWRpbmcgPSAhIXZhbDsKICAgIH0sCiAgICBzZXRDYW5kaWRhdGVMb2FkKHZhbCkgewogICAgICB0aGlzLmNhbmRpZGF0ZUxvYWRpbmcgPSAhIXZhbDsKICAgICAgdGhpcy5hbGxCdG5EaXNhYmxlZCA9ICEhdmFsOwogICAgfSwKICAgIHNldExvYWQodmFsKSB7CiAgICAgIHRoaXMuYnRuTG9hZGluZyA9ICEhdmFsOwogICAgfSwKICAgIGhhbmRsZUZsb3dVcmdlbnQoZSkgewogICAgICB0aGlzLmZsb3dVcmdlbnQgPSBlOwogICAgfSwKICAgIHNob3dUeXBlQnRuKGJ0bk5hbWUsc3RhdGUpewogICAgICBpZih0aGlzLnNldHRpbmcgJiYgdGhpcy5zZXR0aW5nLnJvdyAmJiB0aGlzLnNldHRpbmcucm93LmhhbmRsZVN0YXRlICYmIHRoaXMuc2V0dGluZy5yb3cuaGFuZGxlU3RhdGUgPj0gc3RhdGUpewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIGxldCByZWNvcmRzID0gdGhpcy5zZXR0aW5nLmZsb3dUYXNrT3BlcmF0b3JSZWNvcmRMaXN0OwogICAgICBpZighcmVjb3JkcyB8fCByZWNvcmRzLmxlbmd0aCA8IDEpewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICByZXR1cm4gcmVjb3Jkc1swXS5oYW5kbGVTdGF0dXMgPT0gMCAmJiByZWNvcmRzWzBdLm5vZGVOYW1lID09PSBidG5OYW1lOwogICAgfSwKICAgIGFjdGl2ZVRhYkNsaWNrKGUpewogICAgICBpZihlLm5hbWUgPT09ICc5OScpewogICAgICAgIHRoaXMubG9vcFdvcmRCdG5zKCk7CiAgICAgIH0KICAgIH0sCiAgICBsb29wV29yZEJ0bnMoKXsKICAgICAgLyppZih0aGlzLnNldHRpbmcgJiYgdGhpcy5zZXR0aW5nLnJvdyAmJiB0aGlzLnNldHRpbmcucm93Lm5vZGVQcm9wZXJ0aWVzKXsKICAgICAgICBsZXQgbm9kZVByb3BlcnRpZXMgPSBKU09OLnBhcnNlKHRoaXMuc2V0dGluZy5yb3cubm9kZVByb3BlcnRpZXMpOwogICAgICAgIGlmKG5vZGVQcm9wZXJ0aWVzLndvcmRFeHBvcnQpewogICAgICAgICAgbGV0IGFyciA9IG5ldyBTZXQoKTsKICAgICAgICAgIGZvcihsZXQgaSA9IDA7IGkgPCBub2RlUHJvcGVydGllcy53b3JkRXhwb3J0Lmxlbmd0aDsgaSsrKXsKICAgICAgICAgICAgaWYobm9kZVByb3BlcnRpZXMud29yZEV4cG9ydFtpXS5mdGxOYW1lKXsKICAgICAgICAgICAgICBhcnIuYWRkKG5vZGVQcm9wZXJ0aWVzLndvcmRFeHBvcnRbaV0uZnRsTmFtZS5yZXBsYWNlKCcuZnRsJywnJykpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmV4cG9ydEJ0bkFyciA9IEFycmF5LmZyb20oYXJyKTsKCiAgICAgICAgICBsZXQgYXJyMiA9IG5ldyBTZXQoKTsKICAgICAgICAgIGlmKHRoaXMuZmxvd1Rhc2tJbmZvICYmIHRoaXMuZmxvd1Rhc2tJbmZvLmZsb3dUZW1wbGF0ZUpzb24pewogICAgICAgICAgICB0aGlzLmxvb3BGdGxOYW1lKEpTT04ucGFyc2UodGhpcy5mbG93VGFza0luZm8uZmxvd1RlbXBsYXRlSnNvbiksYXJyMik7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmV4cG9ydEJ0bkFyciA9IEFycmF5LmZyb20oYXJyKTsKICAgICAgICAgIC8hKnRoaXMuZXhwb3J0QnRuQXJyID0gbm9kZVByb3BlcnRpZXMud29yZEV4cG9ydC5maWx0ZXIoaXRlbSA9PiBpdGVtLmZ0bE5hbWUpLm1hcChpdGVtID0+IHsKICAgICAgICAgICAgaWYoaXRlbS5mdGxOYW1lKXsKICAgICAgICAgICAgICByZXR1cm4gaXRlbS5mdGxOYW1lLnJlcGxhY2UoJy5mdGwnLCcnKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkqIS8KCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIGlmKHRoaXMuZXhwb3J0QnRuQXJyICYmIHRoaXMuZXhwb3J0QnRuQXJyLmxlbmd0aD4wKXsKICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRXb3JkQnRuID0gdGhpcy5leHBvcnRCdG5BcnJbMF07CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9Ki8KICAgICAgbGV0IGFyciA9IG5ldyBTZXQoKTsKICAgICAgbGV0IGN1cnJlbnROb2RlSW5mbyA9IHsKICAgICAgICBjdXJyZW50Tm9kZUZ0bE5hbWU6IG51bGwsCiAgICAgIH07CiAgICAgIGlmKHRoaXMuZmxvd1Rhc2tJbmZvICYmIHRoaXMuZmxvd1Rhc2tJbmZvLnRoaXNTdGVwSWQpewogICAgICAgIGN1cnJlbnROb2RlSW5mby5jdXJyZW50Tm9kZSA9IHRoaXMuZmxvd1Rhc2tJbmZvLnRoaXNTdGVwSWQuc3BsaXQoJywnKVswXTsKICAgICAgfQogICAgICBpZih0aGlzLmZsb3dUZW1wbGF0ZUpzb24pewogICAgICAgIHRoaXMubG9vcEZ0bE5hbWUodGhpcy5mbG93VGVtcGxhdGVKc29uLGFycixjdXJyZW50Tm9kZUluZm8pOwogICAgICB9CiAgICAgIHRoaXMuZXhwb3J0QnRuQXJyID0gQXJyYXkuZnJvbShhcnIpOwogICAgICAvKnRoaXMuZXhwb3J0QnRuQXJyID0gbm9kZVByb3BlcnRpZXMud29yZEV4cG9ydC5maWx0ZXIoaXRlbSA9PiBpdGVtLmZ0bE5hbWUpLm1hcChpdGVtID0+IHsKICAgICAgICBpZihpdGVtLmZ0bE5hbWUpewogICAgICAgICAgcmV0dXJuIGl0ZW0uZnRsTmFtZS5yZXBsYWNlKCcuZnRsJywnJyk7CiAgICAgICAgfQogICAgICB9KSovCgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYodGhpcy5leHBvcnRCdG5BcnIgJiYgdGhpcy5leHBvcnRCdG5BcnIubGVuZ3RoPjApewogICAgICAgICAgdGhpcy5jdXJyZW50V29yZEJ0biA9IHRoaXMuZXhwb3J0QnRuQXJyWzBdOwogICAgICAgICAgaWYoY3VycmVudE5vZGVJbmZvLmN1cnJlbnROb2RlRnRsTmFtZSl7CiAgICAgICAgICAgIGxldCBtYXRjaCA9IHRoaXMuZXhwb3J0QnRuQXJyLmZpbmQoaXRlbSA9PiBpdGVtID09PSBjdXJyZW50Tm9kZUluZm8uY3VycmVudE5vZGVGdGxOYW1lKTsKICAgICAgICAgICAgaWYobWF0Y2gpewogICAgICAgICAgICAgIHRoaXMuY3VycmVudFdvcmRCdG4gPSBtYXRjaDsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBsb29wRnRsTmFtZShkYXRhLGFycixjdXJyZW50Tm9kZUluZm8pewogICAgICBpZihkYXRhKXsKICAgICAgICBsZXQgcHJvcGVydGllcyA9IGRhdGEucHJvcGVydGllczsKICAgICAgICBpZihwcm9wZXJ0aWVzKXsKICAgICAgICAgIGxldCBub2RlUHJvcGVydGllcyA9IHByb3BlcnRpZXMubm9kZVByb3BlcnRpZXM7CiAgICAgICAgICBpZihub2RlUHJvcGVydGllcyl7CiAgICAgICAgICAgIGxldCB3b3JkRXhwb3J0TGlzdCA9IFtdOwogICAgICAgICAgICBpZih0eXBlb2Ygbm9kZVByb3BlcnRpZXMgPT09ICdzdHJpbmcnKXsKICAgICAgICAgICAgICB3b3JkRXhwb3J0TGlzdCA9IEpTT04ucGFyc2Uobm9kZVByb3BlcnRpZXMpLndvcmRFeHBvcnQ7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICB3b3JkRXhwb3J0TGlzdCA9IG5vZGVQcm9wZXJ0aWVzLndvcmRFeHBvcnQ7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYod29yZEV4cG9ydExpc3QgJiYgd29yZEV4cG9ydExpc3QubGVuZ3RoPjApewogICAgICAgICAgICAgIHdvcmRFeHBvcnRMaXN0LmZvckVhY2god29yZEV4cG9ydCA9PiB7CiAgICAgICAgICAgICAgICBsZXQgZnRsTmFtZSA9IHdvcmRFeHBvcnQuZnRsTmFtZTsKICAgICAgICAgICAgICAgIGlmKGZ0bE5hbWUpewogICAgICAgICAgICAgICAgICBhcnIuYWRkKGZ0bE5hbWUucmVwbGFjZSgnLmZ0bCcsJycpKTsKICAgICAgICAgICAgICAgICAgaWYoZGF0YS5ub2RlSWQgPT09IGN1cnJlbnROb2RlSW5mby5jdXJyZW50Tm9kZSl7CiAgICAgICAgICAgICAgICAgICAgY3VycmVudE5vZGVJbmZvLmN1cnJlbnROb2RlRnRsTmFtZSA9IGZ0bE5hbWUucmVwbGFjZSgnLmZ0bCcsJycpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgbGV0IGNvbmRpdGlvbk5vZGVzID0gZGF0YS5jb25kaXRpb25Ob2RlczsKICAgICAgICBpZihjb25kaXRpb25Ob2RlcyAmJiBjb25kaXRpb25Ob2Rlcy5sZW5ndGggPiAwKXsKICAgICAgICAgIGNvbmRpdGlvbk5vZGVzLmZvckVhY2goY29uZGl0aW9uSXRlbSA9PiB0aGlzLmxvb3BGdGxOYW1lKGNvbmRpdGlvbkl0ZW0sYXJyLGN1cnJlbnROb2RlSW5mbykpOwogICAgICAgIH0KICAgICAgICBsZXQgY2hpbGROb2RlID0gZGF0YS5jaGlsZE5vZGU7CiAgICAgICAgaWYoY2hpbGROb2RlKXsKICAgICAgICAgIHRoaXMubG9vcEZ0bE5hbWUoY2hpbGROb2RlLGFycixjdXJyZW50Tm9kZUluZm8pOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIHdvcmRCdG5TZWxlY3RlZChsYWJlbCl7CiAgICAgIGlmKCFsYWJlbCB8fCAnMCcgPT09IGxhYmVsIHx8IDAgPT09IGxhYmVsKXsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYodGhpcy5zZXR0aW5nICYmIHRoaXMuc2V0dGluZy5yb3cgJiYgdGhpcy5zZXR0aW5nLnJvdy5pZCl7CiAgICAgICAgZ2V0T3JkZXIodGhpcy5zZXR0aW5nLnJvdy5pZCkudGhlbihiYXNlUmVzID0+IHsKICAgICAgICAgIGxldCBkYXRhID0gey4uLmJhc2VSZXMuZGF0YX07CiAgICAgICAgICB0aGlzLmN1cnJlbnRXb3JkRm9ybSA9IHsuLi5kYXRhLC4uLnRoaXMuY3VycmVudFdvcmRGb3JtfQogICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgICAgaWYodGhpcy5jdXJyZW50V29yZEZvcm0gJiYgT2JqZWN0LmtleXModGhpcy5jdXJyZW50V29yZEZvcm0pLmxlbmd0aD4wKXsKICAgICAgICAgICAgdGhpcy53b3JkUHJldmlld0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICBsZXQgZGF0YSA9IHsuLi50aGlzLmN1cnJlbnRXb3JkRm9ybX07CiAgICAgICAgICAgIGRhdGEuZnRsTmFtZSA9IGxhYmVsKycuZnRsJzsKICAgICAgICAgICAgZGF0YS5jdXJUYXJnZXREZXB0ID0gdGhpcy53b3JkVGFyZ2V0RGVwdDsKICAgICAgICAgICAgY29tbW9uR2V0V29yZEJ5VGVtcChkYXRhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMud29yZFByZXZpZXcgJiYgdGhpcy4kcmVmcy53b3JkUHJldmlldy5pbml0KG5ldyBCbG9iKFtyZXNdLCB7dHlwZTogImFwcGxpY2F0aW9uL3BkZiJ9KSkKICAgICAgICAgICAgICAgIH0sMzAwKQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgICAgICB0aGlzLndvcmRQcmV2aWV3TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICB9KQogICAgICAgICAgfWVsc2UgewogICAgICAgICAgICBpZighdGhpcy5zZXR0aW5nIHx8ICF0aGlzLnNldHRpbmcucm93IHx8ICF0aGlzLnNldHRpbmcucm93LmlkIHx8ICF0aGlzLnNldHRpbmcuaWQpewogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLndvcmRQcmV2aWV3TG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgIGNvbW1vbkdldFdvcmQoe2Z0bE5hbWU6IGxhYmVsKycuZnRsJyxpZDp0aGlzLnNldHRpbmcucm93LmlkLGN1clRhcmdldERlcHQ6IHRoaXMud29yZFRhcmdldERlcHR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMud29yZFByZXZpZXcgJiYgdGhpcy4kcmVmcy53b3JkUHJldmlldy5pbml0KG5ldyBCbG9iKFtyZXNdLCB7dHlwZTogImFwcGxpY2F0aW9uL3BkZiJ9KSkKICAgICAgICAgICAgICAgIH0sMzAwKQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgICAgICB0aGlzLndvcmRQcmV2aWV3TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH1lbHNlIHsKICAgICAgICBpZih0aGlzLmN1cnJlbnRXb3JkRm9ybSAmJiBPYmplY3Qua2V5cyh0aGlzLmN1cnJlbnRXb3JkRm9ybSkubGVuZ3RoPjApewogICAgICAgICAgdGhpcy53b3JkUHJldmlld0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgbGV0IGRhdGEgPSB7Li4udGhpcy5jdXJyZW50V29yZEZvcm19OwogICAgICAgICAgZGF0YS5mdGxOYW1lID0gbGFiZWwrJy5mdGwnOwogICAgICAgICAgY29tbW9uR2V0V29yZEJ5VGVtcChkYXRhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMud29yZFByZXZpZXcgJiYgdGhpcy4kcmVmcy53b3JkUHJldmlldy5pbml0KG5ldyBCbG9iKFtyZXNdLCB7dHlwZTogImFwcGxpY2F0aW9uL3BkZiJ9KSkKICAgICAgICAgICAgICB9LDMwMCkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgICAgdGhpcy53b3JkUHJldmlld0xvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pCiAgICAgICAgfWVsc2UgewogICAgICAgICAgaWYoIXRoaXMuc2V0dGluZyB8fCAhdGhpcy5zZXR0aW5nLnJvdyB8fCAhdGhpcy5zZXR0aW5nLnJvdy5pZCB8fCAhdGhpcy5zZXR0aW5nLmlkKXsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy53b3JkUHJldmlld0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgY29tbW9uR2V0V29yZCh7ZnRsTmFtZTogbGFiZWwrJy5mdGwnLGlkOnRoaXMuc2V0dGluZy5yb3cuaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMud29yZFByZXZpZXcgJiYgdGhpcy4kcmVmcy53b3JkUHJldmlldy5pbml0KG5ldyBCbG9iKFtyZXNdLCB7dHlwZTogImFwcGxpY2F0aW9uL3BkZiJ9KSkKICAgICAgICAgICAgICB9LDMwMCkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgICAgdGhpcy53b3JkUHJldmlld0xvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9CgogICAgICAvKiBpZih0aGlzLmN1cnJlbnRXb3JkRm9ybSAmJiBPYmplY3Qua2V5cyh0aGlzLmN1cnJlbnRXb3JkRm9ybSkubGVuZ3RoPjApewogICAgICAgIHRoaXMud29yZFByZXZpZXdMb2FkaW5nID0gdHJ1ZTsKICAgICAgICBsZXQgZGF0YSA9IHsuLi50aGlzLmN1cnJlbnRXb3JkRm9ybX07CiAgICAgICAgZGF0YS5mdGxOYW1lID0gbGFiZWwrJy5mdGwnOwogICAgICAgIGNvbW1vbkdldFdvcmRCeVRlbXAoZGF0YSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRyZWZzLndvcmRQcmV2aWV3ICYmIHRoaXMuJHJlZnMud29yZFByZXZpZXcuaW5pdChuZXcgQmxvYihbcmVzXSwge3R5cGU6ICJhcHBsaWNhdGlvbi9wZGYifSkpCiAgICAgICAgICAgIH0sMzAwKQogICAgICAgICAgfSkKICAgICAgICB9KS5maW5hbGx5KCgpPT57CiAgICAgICAgICB0aGlzLndvcmRQcmV2aWV3TG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pCiAgICAgIH1lbHNlIHsKICAgICAgICBpZighdGhpcy5zZXR0aW5nIHx8ICF0aGlzLnNldHRpbmcucm93IHx8ICF0aGlzLnNldHRpbmcucm93LmlkIHx8ICF0aGlzLnNldHRpbmcuaWQpewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICB0aGlzLndvcmRQcmV2aWV3TG9hZGluZyA9IHRydWU7CiAgICAgICAgY29tbW9uR2V0V29yZCh7ZnRsTmFtZTogbGFiZWwrJy5mdGwnLGlkOnRoaXMuc2V0dGluZy5yb3cuaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJHJlZnMud29yZFByZXZpZXcgJiYgdGhpcy4kcmVmcy53b3JkUHJldmlldy5pbml0KG5ldyBCbG9iKFtyZXNdLCB7dHlwZTogImFwcGxpY2F0aW9uL3BkZiJ9KSkKICAgICAgICAgICAgfSwzMDApCiAgICAgICAgICB9KQogICAgICAgIH0pLmZpbmFsbHkoKCk9PnsKICAgICAgICAgIHRoaXMud29yZFByZXZpZXdMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfSkKICAgICAgfSAqLwogICAgfSwKICAgIG9uQ29tbW9uV29yZHNTZWxlY3RlZCh2YWwpewogICAgICB0aGlzLmNhbmRpZGF0ZUZvcm0uaGFuZGxlT3BpbmlvbiA9IHZhbDsKICAgIH0sCiAgICBmb3JtVGFic0NsaWNrKGVsKXsKICAgICAgaWYoIjIiID09PSBlbC5uYW1lKXsKICAgICAgICB0aGlzLiRldmVudEJ1cy4kZW1pdCgnZ2V0V29ya0Zvcm0nLDEpOwogICAgICAgIC8vdGhpcy5sb29wV29yZEJ0bnMoKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVNob3dXb3JkKCl7CiAgICAgIHRoaXMuY3VycmVudFdvcmRGb3JtID0gdGhpcy4kcmVmcy5mb3JtICYmIHRoaXMuJHJlZnMuZm9ybS5zZW5kRGF0YUZvcm0gJiYgdGhpcy4kcmVmcy5mb3JtLnNlbmREYXRhRm9ybSgpOwogICAgICB0aGlzLndvcmRCdG5TZWxlY3RlZCh0aGlzLmN1cnJlbnRXb3JkQnRuKTsKICAgIH0sCiAgICByZXBvcnREYXRhQ2hhbmdlKHZhbCl7CiAgICAgIGlmKHZhbCAmJiB2YWwubGVuZ3RoPjApewogICAgICAgIHRoaXMud29yZFRhcmdldERlcHRPcHRpb25zID0gdmFsLmZpbHRlcihpdGVtID0+IGl0ZW0uZm9ybURhdGEgJiYgaXRlbS5mb3JtRGF0YS5sZW5ndGg+MCkubWFwKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgbGFiZWw6IGl0ZW0uZGVwdE5hbWUsCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLmRlcHRJZCwKICAgICAgICAgICAga2V5OiBpdGVtLmRlcHRJZAogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIGlmKHRoaXMud29yZFRhcmdldERlcHRPcHRpb25zICYmIHRoaXMud29yZFRhcmdldERlcHRPcHRpb25zLmxlbmd0aD4wKXsKICAgICAgICAgIGlmKCF0aGlzLndvcmRUYXJnZXREZXB0KXsKICAgICAgICAgICAgdGhpcy53b3JkVGFyZ2V0RGVwdCA9IHRoaXMud29yZFRhcmdldERlcHRPcHRpb25zWzBdLnZhbHVlOwogICAgICAgICAgfWVsc2UgewogICAgICAgICAgICBsZXQgbWF0Y2ggPSB0aGlzLndvcmRUYXJnZXREZXB0T3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gdGhpcy53b3JkVGFyZ2V0RGVwdCk7CiAgICAgICAgICAgIGlmKCFtYXRjaCl7CiAgICAgICAgICAgICAgdGhpcy53b3JkVGFyZ2V0RGVwdCA9IHRoaXMud29yZFRhcmdldERlcHRPcHRpb25zWzBdLnZhbHVlOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgd29yZFRhcmdldERlcHRDaGFuZ2UodmFsKXsKICAgICAgdGhpcy5oYW5kbGVTaG93V29yZCgpOwogICAgfSwKICAgIHJlamVjdENoYW5nZSh2YWwpewogICAgICBpZih2YWwgJiYgdmFsLmxlbmd0aD4wKXsKICAgICAgICBsZXQgY3VyTm9kZSA9IHRoaXMucmVqZWN0TGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5ub2RlQ29kZSA9PT0gdmFsWzBdKTsKICAgICAgICBpZighY3VyTm9kZSl7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHRoaXMucmVqZWN0TGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYoaXRlbS5zb3J0Q29kZSA9PT0gY3VyTm9kZS5zb3J0Q29kZSl7CiAgICAgICAgICAgIGl0ZW0uZGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgaXRlbS5kaXNhYmxlZCA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMucmVqZWN0TGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS5kaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["FlowBox.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4rBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FlowBox.vue", "sourceRoot": "src/views/zeroCode/workFlow/components", "sourcesContent": ["<template>\n  <!-- <transition name=\"el-zoom-in-center\"> -->\n  <div class=\"flow-form-main\">\n    <div class=\"JNPF-common-page-header\">\n      <div v-if=\"setting.fromForm\">{{ title }}</div>\n      <el-page-header @back=\"goBack\" v-else>\n        <template slot=\"content\">\n          <div class=\"JNPF-page-header-content\">{{ title }}</div>\n        </template>\n      </el-page-header>\n      <template v-if=\"!loading || title\">\n        <el-dropdown\n          placement=\"bottom\"\n          @command=\"handleFlowUrgent\"\n          trigger=\"click\"\n          v-show=\"setting.opType == '-1'\"\n        >\n          <div class=\"flow-urgent-value\" style=\"cursor: pointer\">\n            <span\n              :style=\"{ 'background-color': flowUrgentList[selectState]?flowUrgentList[selectState].color:'' }\"\n              class=\"color-box\"\n            ></span>\n            <span :style=\"{ color: flowUrgentList[selectState]?flowUrgentList[selectState].color:'' }\">\n              {{ flowUrgentList[selectState].name }}</span\n            >\n          </div>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item\n              v-for=\"(item, index) in flowUrgentList\"\n              :key=\"'flowUrgent' + index\"\n              :command=\"item.state\"\n            >\n              <span\n                :style=\"{ 'background-color': item.color }\"\n                class=\"color-box\"\n              >\n              </span>\n              {{ item.name }}\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n        <!--        <div class=\"flow-urgent-value\" v-show=\"setting.opType !== '-1'\">\n                  <span\n                    :style=\"{ 'background-color': flowUrgentList[selectState].color }\"\n                    class=\"color-box\"\n                  ></span>\n                  <span :style=\"{ color: flowUrgentList[selectState].color }\">{{\n                    flowUrgentList[selectState].name\n                  }}</span>\n                </div>-->\n      </template>\n      <div class=\"options\" v-if=\"!subFlowVisible\">\n        <!--        <el-dropdown\n                  class=\"dropdown\"\n                  placement=\"bottom\"\n                  @command=\"handleMore\"\n                  v-if=\"moreBtnList.length\"\n                >\n                  <el-button style=\"width: 70px\" :disabled=\"allBtnDisabled\">\n                    更 多1<i class=\"el-icon-arrow-down el-icon&#45;&#45;right\"></i>\n                  </el-button>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item\n                      class=\"dropdown-item\"\n                      v-for=\"(item, index) in moreBtnList\"\n                      :key=\"'moreBtn'+index\"\n                      :command=\"item.key\"\n                      >{{ item.label }}</el-dropdown-item\n                    >\n                  </el-dropdown-menu>\n                </el-dropdown>-->\n        <el-button\n          v-if=\"properties.hasSaveBtn && !setting.readOnly\"\n          type=\"primary\"\n          @click=\"eventLauncher('saveAudit')\"\n          :loading=\"candidateLoading\"\n          :disabled=\"allBtnDisabled\"\n        >\n          {{ properties.saveBtnText || \"暂 存\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == '-1'\"\n          type=\"primary\"\n          @click=\"eventLauncher('submit')\"\n          :loading=\"candidateLoading\"\n          :disabled=\"allBtnDisabled\"\n        >\n          {{ properties.submitBtnText || \"提 交\" }}</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"eventLauncher('audit')\"\n          :loading=\"candidateLoading\"\n          v-if=\"setting.opType == 1 && properties.hasAuditBtn\"\n        >{{ properties.auditBtnText || \"通 过\" }}\n        </el-button>\n        <el-button\n          type=\"danger\"\n          @click=\"eventLauncher('reject')\"\n          :loading=\"candidateLoading\"\n          v-if=\"setting.opType == 1 && properties.hasRejectBtn\"\n        >{{ properties.rejectBtnText || \"退 回\" }}\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"press()\"\n          v-if=\"\n            setting.opType == 0 &&\n            setting.status == 1 &&\n            (properties.hasPressBtn || properties.hasPressBtn === undefined)\n          \"\n        >\n          {{ properties.pressBtnText || \"催 办\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == 2 && properties.hasRevokeBtn\"\n          @click=\"actionLauncher('recall')\"\n        >{{ properties.revokeBtnText || \"撤 回\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == 4 && setting.status == 1\"\n          @click=\"actionLauncher('cancel')\"\n        >\n          终 止</el-button\n        >\n        <el-button\n          @click=\"goBack()\"\n          v-if=\"!setting.hideCancelBtn\"\n          :disabled=\"allBtnDisabled\"\n        >\n          取消\n        </el-button>\n      </div>\n    </div>\n    <div\n      class=\"approve-result\"\n      v-if=\"\n        (setting.opType == 0 || setting.opType == 4) &&\n        activeTab === '0' &&\n        !subFlowVisible\n      \"\n    >\n      <div\n        class=\"approve-result-img\"\n        :class=\"flowTaskInfo.status | flowStatus()\"\n      ></div>\n    </div>\n    <el-tabs class=\"JNPF-el_tabs center_tabs work_order_flow\" v-model=\"activeTab\" @tab-click=\"activeTabClick\">\n      <el-tab-pane\n        label=\"表单信息\"\n        v-loading=\"loading\"\n        v-if=\"!setting.readOnly && setting.opType != '4' && !subFlowVisible\"\n      >\n        <div class=\"center_tabs_pane\">\n          <div class=\"form-container\">\n            <component\n              :is=\"currentView\"\n              @close=\"goBack\"\n              ref=\"form\"\n              @eventReceiver=\"eventReceiver\"\n              @setLoad=\"setLoad\"\n              @setCandidateLoad=\"setCandidateLoad\"\n              @setPageLoad=\"setPageLoad\"\n              @reportDataChange=\"reportDataChange\"\n            />\n          </div>\n          <div class=\"word-preview-container\" v-if=\"exportBtnArr && exportBtnArr.length>0\">\n            <div class=\"word-preview-btns\">\n              <!--          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                          <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                        </el-radio-group>-->\n              <div class=\"btns\">\n                <el-tabs v-model=\"currentWordBtn\">\n                  <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                </el-tabs>\n              </div>\n              <div class=\"target-select\">\n                <el-select v-model=\"wordTargetDept\" placeholder=\"请选择\" @change=\"wordTargetDeptChange\">\n                  <el-option\n                    v-for=\"item in wordTargetDeptOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </div>\n            </div>\n            <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n              <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n              <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n            </div>\n          </div>\n        </div>\n        <!--        <el-tabs v-model=\"formActiveTabs\" @tab-click=\"formTabsClick\">\n                  <el-tab-pane label=\"表单\" name=\"1\">\n                    <div class=\"center_tabs_pane\">\n                      <component\n                        :is=\"currentView\"\n                        @close=\"goBack\"\n                        ref=\"form\"\n                        @eventReceiver=\"eventReceiver\"\n                        @setLoad=\"setLoad\"\n                        @setCandidateLoad=\"setCandidateLoad\"\n                        @setPageLoad=\"setPageLoad\"\n                      />\n                    </div>\n                  </el-tab-pane>\n                  <el-tab-pane label=\"预览1\" name=\"2\">\n                    <div class=\"center_tabs_pane\">\n                      <div class=\"word-preview-btns\">\n                        &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                    <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                  </el-radio-group>&ndash;&gt;\n                        <el-tabs v-model=\"currentWordBtn\">\n                          <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                        </el-tabs>\n                      </div>\n                      <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                        <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                        <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                      </div>\n                    </div>\n                  </el-tab-pane>\n                </el-tabs>-->\n      </el-tab-pane>\n      <el-tab-pane label=\"填报信息\" v-loading=\"loading\" v-if=\"setting.row && setting.readonly\">\n        <div class=\"center_tabs_pane\">\n          <div style=\"flex: 1;height: 100%;overflow-y: auto\">\n<!--            <form-record ref=\"form\" :current-setting=\"setting\" />-->\n            <work-flow ref=\"form\" :current-setting=\"setting\" @reportDataChange=\"reportDataChange\" />\n          </div>\n          <!--          <div style=\"width: 8px;flex: none;background-color: #F3F3F3\"></div>-->\n          <div style=\"width: 40%;margin-left: 8px;border-left: 8px solid #f3f3f3;height: 100%;\" v-if=\"exportBtnArr && exportBtnArr.length>0\">\n            <div class=\"word-preview-btns\">\n              <div class=\"btns\">\n                <el-tabs v-model=\"currentWordBtn\">\n                  <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                </el-tabs>\n              </div>\n              <div class=\"target-select\">\n                <el-select v-model=\"wordTargetDept\" placeholder=\"请选择\" @change=\"wordTargetDeptChange\">\n                  <el-option\n                    v-for=\"item in wordTargetDeptOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </div>\n            </div>\n            <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n              <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n              <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n            </div>\n          </div>\n        </div>\n        <!--        <el-tabs v-model=\"formActiveTabs\">\n                  <el-tab-pane label=\"表单\" name=\"1\">\n                    <div class=\"center_tabs_pane\">\n                      <div style=\"width: 50%\">\n                        <form-record :current-setting=\"setting\" />\n                      </div>\n                      <div style=\"width: 50%\">\n                        <div class=\"word-preview-btns\">\n                          &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                      <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                    </el-radio-group>&ndash;&gt;\n                          <el-tabs v-model=\"currentWordBtn\">\n                            <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                          </el-tabs>\n                        </div>\n                        <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                          <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                          <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                        </div>\n                      </div>\n                    </div>\n                  </el-tab-pane>\n        &lt;!&ndash;          <el-tab-pane label=\"预览2\" name=\"2\">\n                    <div class=\"center_tabs_pane\">\n                      <div class=\"word-preview-btns\">\n                        &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                    <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                  </el-radio-group>&ndash;&gt;\n                        <el-tabs v-model=\"currentWordBtn\">\n                          <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                        </el-tabs>\n                      </div>\n                      <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                        <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                        <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                      </div>\n                    </div>\n                  </el-tab-pane>&ndash;&gt;\n                </el-tabs>-->\n      </el-tab-pane>\n      <el-tab-pane\n        label=\"审批记录\"\n        v-loading=\"loading\"\n      >\n        <div class=\"type_select\">\n          <div class=\"type_btn\">\n            <!--            <div class=\"type_btn_item\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 0\" :class=\"recordType === 0 ? 'btn_active' : ''\">流程记录</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-for=\"(item, index) in recordBtnArr\" :key=\"'type_btn_item' + item.id\">\n                          <el-button type=\"primary\" plain @click=\"recordType = index+1\" :class=\"recordType === index+1 ? 'btn_active' : ''\">{{item.nodeName?(item.nodeName=='开始'?'创建通报':item.nodeName):''}}</el-button>\n                        </div>-->\n            <!--            <div class=\"type_btn_item\" v-if=\"setting.id\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 1\" :class=\"recordType === 1 ? 'btn_active' : ''\">创建通报</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('审批告知单',1)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 2\" :class=\"recordType === 2 ? 'btn_active' : ''\">审核告知单</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('处置通报',2)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 3\" :class=\"recordType === 3 ? 'btn_active' : ''\">处置通报</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('审核反馈单',3)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 4\" :class=\"recordType === 4 ? 'btn_active' : ''\">审核反馈单</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('验证',4)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 5\" :class=\"recordType === 5 ? 'btn_active' : ''\">提交验证</el-button>\n                        </div>-->\n          </div>\n        </div>\n        <recordList\n          v-if=\"recordType === 0\"\n          :list=\"flowTaskOperatorRecordList\"\n          :endTime=\"endTime\"\n          :flowId=\"setting.flowId\"\n          :opType=\"setting.opType?parseInt(setting.opType):-1\"\n        />\n        <informRecord v-if=\"recordType === 1\" :current-setting=\"setting\"/>\n        <informSignRecord v-if=\"recordType === 2\" :current-setting=\"setting\"/>\n        <feedbackRecord v-if=\"recordType === 3\" :current-setting=\"setting\"/>\n        <feedbackSignRecord v-if=\"recordType === 4\" :current-setting=\"setting\"/>\n        <checkRecord v-if=\"recordType === 5\" :current-setting=\"setting\"/>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"流程信息\" v-loading=\"loading\">\n        <template v-if=\"!subFlowVisible\">\n          <Process\n            :setting=\"setting\"\n            :conf=\"flowTemplateJson\"\n            v-if=\"flowTemplateJson.nodeId\"\n            @subFlow=\"subFlow\"\n          />\n        </template>\n        <template v-else>\n          <el-tabs v-model=\"subFlowTab\" @tab-click=\"activeClick\" type=\"card\">\n            <el-tab-pane\n              v-for=\"(item, index) in subFlowInfoList\"\n              :key=\"'subFlowTab' + index\"\n              :label=\"item.flowTaskInfo.fullName\"\n              :name=\"item.flowTaskInfo.id\"\n            >\n              <Process :conf=\"item.flowTemplateInfo.flowTemplateJson\" />\n            </el-tab-pane>\n          </el-tabs>\n        </template>\n      </el-tab-pane>\n\n      <el-tab-pane\n        label=\"审批汇总\"\n        v-if=\"setting.opType != '-1' && isSummary\"\n        v-loading=\"loading\"\n        name=\"recordSummary\"\n      >\n        <RecordSummary\n          :id=\"setting.id\"\n          :summaryType=\"summaryType\"\n          ref=\"recordSummary\"\n        />\n      </el-tab-pane>\n      <el-tab-pane\n        label=\"流程评论\"\n        v-if=\"setting.opType != '-1' && isComment\"\n        v-loading=\"loading\"\n        name=\"comment\"\n      >\n        <Comment :id=\"setting.id\" ref=\"comment\" />\n      </el-tab-pane>\n    </el-tabs>\n    <el-dialog\n      :title=\"eventType === 'audit' ? auditText : '审批退回'\"\n      :close-on-click-modal=\"false\"\n      :visible.sync=\"visible\"\n      class=\"JNPF-dialog JNPF-dialog_center\"\n      lock-scroll\n      append-to-body\n      :before-close=\"beforeClose\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"candidateForm\"\n        :model=\"candidateForm\"\n        :label-width=\"\n          candidateForm.candidateList.length || branchList.length\n            ? '130px'\n            : '80px'\n        \"\n      >\n        <template v-if=\"eventType === 'audit'\">\n          <el-form-item\n            label=\"分支选择\"\n            prop=\"branchList\"\n            v-if=\"branchList.length\"\n            :rules=\"[\n              { required: true, message: `分支不能为空`, trigger: 'change' },\n            ]\"\n          >\n            <el-select\n              v-model=\"candidateForm.branchList\"\n              multiple\n              placeholder=\"请选择审批分支\"\n              clearable\n              @change=\"onBranchChange\"\n            >\n              <el-option\n                v-for=\"item in branchList\"\n                :key=\"'branch' + item.nodeId\"\n                :label=\"item.nodeName\"\n                :value=\"item.nodeId\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            :label=\"item.nodeName + item.label\"\n            :prop=\"'candidateList.' + i + '.value'\"\n            v-for=\"(item, i) in candidateForm.candidateList\"\n            :key=\"'candidateList' + i\"\n            :rules=\"item.rules\"\n          >\n            <candidate-user-select\n              v-model=\"item.value\"\n              multiple\n              :placeholder=\"'请选择' + item.label\"\n              :taskId=\"setting.taskId\"\n              :formData=\"formData\"\n              :nodeId=\"item.nodeId\"\n              v-if=\"item.hasCandidates\"\n            />\n            <user-select\n              v-model=\"item.value\"\n              multiple\n              :placeholder=\"'请选择' + item.label\"\n              title=\"候选人员\"\n              :is-self-dept=\"item.isSelfDept\"\n              v-else\n            />\n          </el-form-item>\n        </template>\n        <template\n          v-if=\"properties.rejectType && eventType !== 'audit' && showReject\"\n        >\n          <el-form-item label=\"退回节点\" prop=\"rejectStep\" :rules=\"[\n              { required: true, message: `节点不能为空`, trigger: 'blur' },\n            ]\">\n            <el-select\n              v-model=\"candidateForm.rejectStep\"\n              placeholder=\"请选择退回节点\"\n              @change=\"rejectChange\"\n              multiple\n            >\n              <el-option\n                v-for=\"item in rejectList\"\n                :key=\"'rejectStep'+item.nodeCode\"\n                :label=\"item.nodeName\"\n                :value=\"item.nodeCode\"\n                :disabled=\"item.disabled\"\n              >\n              </el-option>\n            </el-select>\n          </el-form-item>\n          <template v-if=\"properties.rejectType == 3\">\n            <el-form-item prop=\"rejectRadio\">\n              <el-radio-group\n                v-model=\"candidateForm.rejectType\"\n                class=\"form-item-content\"\n              >\n                <el-radio :label=\"1\"\n                >重新审批\n                  <el-tooltip\n                    content=\"若流程为A->B->C,C退回至A，则C->A->B->C\"\n                    placement=\"top\"\n                  >\n                    <i class=\"el-icon-question tooltip-question\"></i>\n                  </el-tooltip>\n                </el-radio>\n                <el-radio :label=\"2\"\n                >直接提交给我\n                  <el-tooltip\n                    content=\"若流程为A->B->C,C退回至A，则C->A->C\"\n                    placement=\"top\"\n                  >\n                    <i class=\"el-icon-question tooltip-question\"></i>\n                  </el-tooltip>\n                </el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </template>\n        </template>\n        <template v-if=\"properties.hasOpinion\">\n          <el-form-item label=\"审批意见\" prop=\"handleOpinion\">\n            <el-input\n              v-model=\"candidateForm.handleOpinion\"\n              placeholder=\"请输入审批意见\"\n              type=\"textarea\"\n              :rows=\"4\"\n              maxlength=\"2000\"\n              show-word-limit\n            />\n            <!--            <CommonWordsDialog ref=\"commonWordsDialog\" @change=\"common\" />-->\n            <common-words v-if=\"visible && setting && setting.isWork\" :eventType=\"eventType\" @selected=\"onCommonWordsSelected\" />\n          </el-form-item>\n          <el-form-item label=\"审批附件\" prop=\"fileList\">\n            <JNPF-UploadFz v-model=\"candidateForm.fileList\" :limit=\"3\" />\n          </el-form-item>\n        </template>\n        <el-form-item label=\"手写签名\" required v-if=\"properties.hasSign\">\n          <div class=\"sign-main\">\n            <img :src=\"signImg\" alt=\"\" v-if=\"signImg\" class=\"sign-img\" />\n            <div @click=\"addSign\" class=\"sign-style\">\n              <i class=\"icon-ym icon-ym-signature add-sign\"></i>\n              <span class=\"sign-title\" v-if=\"!signImg\">手写签名</span>\n            </div>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"抄送人员\" v-if=\"properties.isCustomCopy && eventType === 'audit'\">\n          <user-select v-model=\"copyIds\" placeholder=\"请选择\" multiple />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"visible = false\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleApproval()\"\n          :loading=\"approvalBtnLoading\"\n        >\n          确定\n        </el-button>\n      </span>\n    </el-dialog>\n    <!-- 流程节点变更复活对话框 -->\n    <el-dialog\n      :title=\"flowTaskInfo.completion == 100 ? '复活' : '变更'\"\n      :close-on-click-modal=\"false\"\n      :visible.sync=\"resurgenceVisible\"\n      class=\"JNPF-dialog JNPF-dialog_center\"\n      lock-scroll\n      append-to-body\n      width=\"600px\"\n    >\n      <el-form\n        label-width=\"80px\"\n        :model=\"resurgenceForm\"\n        :rules=\"resurgenceRules\"\n        ref=\"resurgenceForm\"\n      >\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活节点' : '变更节点'\"\n          prop=\"taskNodeId\"\n        >\n          <el-select\n            v-model=\"resurgenceForm.taskNodeId\"\n            :placeholder=\"\n              flowTaskInfo.completion == 100\n                ? '请选择复活节点'\n                : '请选择变更节点'\n            \"\n          >\n            <el-option\n              v-for=\"item in resurgenceNodeList\"\n              :key=\"'taskNode' + item.id\"\n              :label=\"item.nodeName\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活意见' : '变更意见'\"\n          prop=\"handleOpinion\"\n        >\n          <el-row>\n            <el-col :span=\"24\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"resurgenceForm.handleOpinion\"\n                placeholder=\"请填写意见\"\n                :rows=\"4\"\n              />\n            </el-col>\n          </el-row>\n        </el-form-item>\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活附件' : '变更附件'\"\n          prop=\"fileList\"\n        >\n          <JNPF-UploadFz v-model=\"resurgenceForm.fileList\" :limit=\"3\" />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"resurgenceVisible = false\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleResurgence()\"\n          :loading=\"resurgenceBtnLoading\"\n        >\n          确定\n        </el-button>\n      </span>\n    </el-dialog>\n    <print-browse\n      :visible.sync=\"printBrowseVisible\"\n      :id=\"printTemplateId\"\n      :formId=\"setting.id\"\n      :fullName=\"setting.fullName\"\n    />\n    <candidate-form\n      :visible.sync=\"candidateVisible\"\n      :candidateList=\"candidateList\"\n      :branchList=\"branchList\"\n      :taskId=\"setting.taskId?setting.taskId:setting.id\"\n      :formData=\"formData\"\n      @submitCandidate=\"submitCandidate\"\n      :isCustomCopy=\"properties.isCustomCopy\"\n      ref=\"candidateForm\"\n    />\n    <error-form\n      :visible.sync=\"errorVisible\"\n      :nodeList=\"errorNodeList\"\n      @submit=\"handleError\"\n    />\n    <actionDialog\n      v-if=\"actionVisible\"\n      ref=\"actionDialog\"\n      :assignNodeList=\"assignNodeList\"\n      @submit=\"actionReceiver\"\n    />\n    <SuspendDialog\n      v-if=\"suspendVisible\"\n      ref=\"suspendDialog\"\n      @submit=\"suspendReceiver\"\n    />\n    <HasFreeApprover\n      :visible.sync=\"hasFreeApproverVisible\"\n      :taskId=\"setting.taskId\"\n      :formData=\"formData\"\n      :properties=\"properties\"\n      @close=\"approverDialog\"\n    />\n    <SignImgDialog\n      v-if=\"signVisible\"\n      ref=\"SignImg\"\n      :lineWidth=\"3\"\n      :userInfo=\"userInfo\"\n      :isDefault=\"1\"\n      @close=\"signDialog\"\n    />\n    <FlowBox\n      v-if=\"flowBoxVisible\"\n      ref=\"FlowBox\"\n      @close=\"flowBoxVisible = false\"\n    />\n    <PrintDialog\n      v-if=\"printDialogVisible\"\n      ref=\"printDialog\"\n      @change=\"printBrowseHandle\"\n    >\n    </PrintDialog>\n\n    <!-- 查看流程 开始 -->\n    <!--    <el-dialog title=\"查看流程\" :visible.sync=\"dialogFlowProcessVisible\" :modal-append-to-body=\"false\">\n          <template v-if=\"!subFlowVisible\">\n            <Process\n              :conf=\"flowTemplateJson\"\n              v-if=\"flowTemplateJson.nodeId\"\n              @subFlow=\"subFlow\"\n            />\n          </template>\n          <template v-else>\n            <el-tabs v-model=\"subFlowTab\" @tab-click=\"activeClick\" type=\"card\">\n              <el-tab-pane\n                v-for=\"(item, index) in subFlowInfoList\"\n                :key=\"'subFlowTab' + index\"\n                :label=\"item.flowTaskInfo.fullName\"\n                :name=\"item.flowTaskInfo.id\"\n              >\n                <Process :conf=\"item.flowTemplateInfo.flowTemplateJson\" />\n              </el-tab-pane>\n            </el-tabs>\n          </template>\n        </el-dialog>-->\n    <!-- 查看流程 结束 -->\n\n  </div>\n  <!-- </transition> -->\n</template>\n\n<script>\nimport WordPreview from \"@/components/WordPreview/index.vue\";\nimport PrintDialog from \"@/components/PrintDialog\";\nimport SignImgDialog from \"@/components/SignImgDialog\";\nimport informRecord from \"./FlowRecord/informRecord.vue\";\nimport informSignRecord from \"./FlowRecord/informSignRecord.vue\";\nimport feedbackRecord from \"./FlowRecord/feedbackRecord.vue\";\nimport feedbackSignRecord from \"./FlowRecord/feedbackSignRecord.vue\";\nimport checkRecord from \"./FlowRecord/checkRecord.vue\";\nimport formRecord from './FlowRecord/formRecord.vue';\nimport workFlow from '@/views/todoItem/todo/work_flow.vue';\nimport {\n  Assign,\n  Audit,\n  Cancel,\n  Candidates,\n  FlowBeforeInfo,\n  Recall,\n  Reject,\n  RejectList,\n  restore,\n  Resurgence,\n  ResurgenceList,\n  SaveAudit,\n  subFlowInfo,\n  suspend,\n  Transfer,\n} from \"@/api/lowCode/FlowBefore\";\nimport {Press, Revoke} from \"@/api/lowCode/FlowLaunch\";\nimport {Create, Update} from \"@/api/lowCode/workFlowForm\";\nimport recordList from \"./RecordList\";\nimport Comment from \"./Comment\";\nimport RecordSummary from \"./RecordSummary\";\nimport CandidateForm from \"./CandidateForm\";\nimport ErrorForm from \"./ErrorForm\";\nimport CandidateUserSelect from \"./CandidateUserSelect\";\nimport Process from \"@/components/Process/Preview\";\nimport PrintBrowse from \"@/components/PrintBrowse\";\nimport ActionDialog from \"@/views/workFlow/components/ActionDialog\";\nimport HasFreeApprover from \"./HasFreeApprover\";\nimport SuspendDialog from \"./SuspendDialog\";\nimport CommonWordsDialog from \"./CommonWordsDialog\";\nimport {mapGetters} from \"vuex\";\nimport {commonGetWord, commonGetWordByTemp,getOrder} from \"@/api/tool/work\";\nimport CommonWords from \"@/views/zeroCode/workFlow/components/commonWords.vue\";\n\nexport default {\n  name: \"FlowBox\",\n  components: {\n    CommonWords,\n    PrintDialog,\n    SignImgDialog,\n    HasFreeApprover,\n    recordList,\n    Process,\n    PrintBrowse,\n    Comment,\n    RecordSummary,\n    CandidateForm,\n    CandidateUserSelect,\n    ErrorForm,\n    ActionDialog,\n    SuspendDialog,\n    CommonWordsDialog,\n    informRecord,\n    informSignRecord,\n    feedbackRecord,\n    feedbackSignRecord,\n    checkRecord,\n    formRecord,\n    WordPreview,\n    workFlow\n  },\n  data() {\n    return {\n      dialogFlowProcessVisible: false,\n      printTemplateId: \"\",\n      printDialogVisible: false,\n      subFlowTab: \"\",\n      resurgenceVisible: false,\n      actionVisible: false,\n      resurgenceForm: {\n        taskNodeId: \"\",\n        handleOpinion: \"\",\n        fileList: [],\n      },\n      resurgenceRules: {\n        taskNodeId: [\n          {\n            required: true,\n            message: \"请选择节点\",\n            trigger: \"change\",\n          },\n        ],\n      },\n      previewVisible: false,\n      assignNodeList: [],\n      resurgenceNodeList: [],\n      currentView: \"\",\n      previewTitle: \"\",\n      formData: {},\n      setting: {},\n      monitorList: [\n        {\n          fullName: \"1\",\n          flowName: \"1\",\n          startTime: \"1\",\n          userName: \"1\",\n          thisStep: \"1\",\n        },\n        {\n          fullName: \"1\",\n          flowName: \"1\",\n          startTime: \"1\",\n          userName: \"1\",\n          thisStep: \"1\",\n        },\n      ],\n      flowFormInfo: {},\n      flowTemplateInfo: {},\n      flowTaskInfo: {},\n      flowTaskNodeList: [],\n      flowTemplateJson: {},\n      flowTaskOperatorRecordList: [],\n      properties: {},\n      endTime: 0,\n      suspendVisible: false,\n      visible: false,\n      handleId: \"\",\n      activeTab: \"0\",\n      isComment: false,\n      isSummary: false,\n      summaryType: 0,\n      loading: false,\n      btnLoading: false,\n      approvalBtnLoading: false,\n      resurgenceBtnLoading: false,\n      candidateLoading: false,\n      candidateVisible: false,\n      hasFreeApproverVisible: false,\n      signVisible: false,\n      candidateType: 1,\n      branchList: [],\n      candidateList: [],\n      candidateForm: {\n        branchList: [],\n        candidateList: [],\n        fileList: [],\n        handleOpinion: \"\",\n        rejectStep: \"\",\n        rejectType: 1,\n      },\n      printBrowseVisible: false,\n      rejectList: [],\n      showReject: false,\n      eventType: \"\",\n      signImg: \"\",\n      copyIds: [],\n      fullName: \"\",\n      thisStep: \"\",\n      allBtnDisabled: false,\n      flowUrgent: 1,\n      flowUrgentList: [\n        { name: \"普通\", color: \"#409EFF\", state: 1 },\n        { name: \"重要\", color: \"#E6A23C\", state: 2 },\n        { name: \"紧急\", color: \"#F56C6C\", state: 3 },\n      ],\n      errorVisible: false,\n      errorNodeList: [],\n      isValidate: false,\n      moreBtnList: [],\n      subFlowVisible: false,\n      flowBoxVisible: false,\n      subFlowInfoList: [],\n      commonWordsVisible: false,\n      recordType: 0,\n      lastRecord: {},\n      recordBtnArr: [],\n      exportBtnArr: [],\n      currentWordBtn: null,\n      wordPreviewLoading: false,\n      wordSrc: null,\n      isSelfDept: false,\n      formActiveTabs: '1',\n      currentWordForm: null,\n      wordTargetDept: null,\n      wordTargetDeptOptions: [],\n    };\n  },\n  computed: {\n    title() {\n      if ([2, 3, 4].includes(this.setting.opType)) return this.fullName;\n      return this.thisStep\n        ? this.fullName + \"/\" + this.thisStep\n        : this.fullName;\n    },\n    selectState() {\n      const index = this.flowUrgentList.findIndex(\n        (c) => this.flowUrgent === c.state\n      );\n      return index;\n    },\n    ...mapGetters([\"userInfo\"]),\n    auditText(){\n      let title = '审批通过';\n      if(this.properties && this.properties.submitBtnText && this.properties.submitBtnText.indexOf('提交') !== -1){\n        title = '提交';\n      }\n      return title;\n    },\n  },\n  created() {\n    this.$eventBus.$on('sendWorkForm', (val) => {\n      this.currentWordForm = val;\n      this.wordBtnSelected(this.currentWordBtn);\n    })\n    this.$eventBus.$on('setFormActiveTabs', (val) => {\n      this.formActiveTabs = val;\n    })\n  },\n  watch: {\n    activeTab(val) {\n      if (val === \"comment\") {\n        this.$refs.comment && this.$refs.comment.init();\n        this.moreBtnList.push({ label: \"评 论\", key: \"comment\" });\n      } else {\n        this.moreBtnList = this.moreBtnList.filter((o) => o.key != \"comment\");\n      }\n      if (val === \"recordSummary\") {\n        this.$refs.recordSummary && this.$refs.recordSummary.init();\n      }\n    },\n    flowTaskOperatorRecordList(val){\n      this.recordBtnArr = [];\n      if(val && val.length>0){\n        for (let i = val.length - 1; i >= 0; i--) {\n          let o = val[i];\n          let matchItem = this.recordBtnArr.find((i) => i.nodeCode == o.nodeCode);\n          if(!matchItem){\n            this.recordBtnArr.push(o);\n          }else {\n            //替换\n            this.recordBtnArr.splice(this.recordBtnArr.indexOf(matchItem),1,o);\n          }\n        }\n      }\n    },\n    flowUrgent(val){\n      let match = this.flowUrgentList.find((c) => c.state === val);\n      if(!match){\n        this.flowUrgent = 1;\n      }\n    },\n    currentWordBtn: {\n      immediate: true,\n      handler(val) {\n        if(val){\n          this.handleShowWord();\n        }\n      },\n    },\n    formActiveTabs(val){\n      this.currentWordBtn = null;\n      if(val === \"2\"){\n        this.loopWordBtns();\n      }\n    },\n  },\n  methods: {\n    common(val) {\n      this.commonWordsVisible = false;\n      if (val) {\n        if (this.resurgenceVisible) {\n          this.resurgenceForm.handleOpinion += val.commonWordsText;\n        } else {\n          this.candidateForm.handleOpinion += val.commonWordsText;\n        }\n      }\n    },\n    beforeClose() {\n      this.visible = false;\n      this.$refs.commonWordsDialog.close();\n    },\n    addSign() {\n      this.signVisible = true;\n      this.$nextTick(() => {\n        this.$refs.SignImg.init();\n      });\n    },\n    signDialog(val) {\n      this.signVisible = false;\n      if (val) {\n        this.signImg = val;\n      }\n    },\n    approverDialog(needClose) {\n      if (needClose) this.$emit(\"close\", true);\n    },\n    activeClick() {\n      let data =\n        this.subFlowInfoList.filter(\n          (o) => o.flowTaskInfo.id == this.subFlowTab\n        ) || [];\n      if (data.length) {\n        this.fullName = data[0].flowTaskInfo.fullName;\n        this.flowTaskOperatorRecordList =\n          data[0].flowTaskOperatorRecordList || [];\n        let templateJson = data[0].flowTaskInfo.flowTemplateJson\n          ? JSON.parse(data[0].flowTaskInfo.flowTemplateJson)\n          : null;\n        this.isComment = templateJson.properties.isComment;\n        this.isSummary = templateJson.properties.isSummary;\n        this.summaryType = templateJson.properties.summaryType;\n        this.flowUrgent = data[0].flowTaskInfo.flowUrgent || 1;\n        this.setting.id = data[0].flowTaskInfo.id;\n      }\n    },\n    subFlow(enCode) {\n      let flowTaskNodeList = this.flowTaskNodeList.filter(\n        (res) => res.nodeCode == enCode\n      );\n      if (!flowTaskNodeList.length) return;\n      if (\n        !flowTaskNodeList[0].type ||\n        flowTaskNodeList[0].nodeType != \"subFlow\"\n      )\n        return;\n      let item = {\n        subFlowVisible: true,\n        ...flowTaskNodeList,\n        ...this.setting,\n      };\n      this.flowBoxVisible = true;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(item);\n      });\n    },\n    handleResurgence(errorRuleUserList) {\n      this.$refs[\"resurgenceForm\"].validate((valid) => {\n        if (!valid) return;\n        let query = {\n          ...this.resurgenceForm,\n          taskId: this.setting.taskId,\n          resurgence: this.flowTaskInfo.completion == 100,\n        };\n        if (errorRuleUserList) query.errorRuleUserList = errorRuleUserList;\n        this.resurgenceBtnLoading = true;\n        Resurgence(query)\n          .then((res) => {\n            const errorData = res.data;\n            if (errorData && Array.isArray(errorData) && errorData.length) {\n              this.errorNodeList = errorData;\n              this.eventType = \"resurgence\";\n              this.errorVisible = true;\n              this.resurgenceBtnLoading = false;\n            } else {\n              this.$message({\n                type: \"success\",\n                message: res.msg,\n                duration: 1000,\n                onClose: () => {\n                  this.resurgenceBtnLoading = false;\n                  this.visible = false;\n                  this.errorVisible = false;\n                  this.$emit(\"close\", true);\n                },\n              });\n            }\n          })\n          .catch(() => {\n            this.resurgenceBtnLoading = false;\n          });\n      });\n    },\n    flowResurgence() {\n      this.resurgenceVisible = true;\n      ResurgenceList(this.setting.taskId).then((res) => {\n        this.resurgenceNodeList = res.data;\n      });\n    },\n    goBack(isRefresh) {\n      this.$emit(\"close\", isRefresh);\n    },\n    init(data) {\n      this.activeTab = \"0\";\n      if(data.activeTabs){\n        this.activeTab = data.activeTabs;\n      }\n      /* if(data.id && data.isWork){\n        this.getConfigKey(\"workOrderDefaultTab\").then(res =>{\n          if(res.msg){\n            this.$nextTick(() => {\n              this.formActiveTabs = res.msg;\n            })\n          }\n        })\n      } */\n      sessionStorage.removeItem('flowRowData');\n      this.loading = true;\n      this.setting = data;\n      if (data.subFlowVisible) {\n        this.subFlowInfo(data);\n      } else {\n        /**\n         * opType\n         * -1 - 我发起的新建/编辑\n         * 0 - 我发起的详情\n         * 1 - 待办事宜\n         * 2 - 已办事宜\n         * 3 - 抄送事宜\n         * 4 - 流程监控\n         */\n        this.getBeforeInfo(data);\n      }\n\n    },\n    getBeforeInfo(data) {\n      FlowBeforeInfo(data.id || 0, {\n        taskNodeId: data.taskNodeId,\n        taskOperatorId: data.taskId?data.taskId:data.taskId,\n        flowId: data.flowId,\n      })\n        .then((res) => {\n          this.flowFormInfo = res.data.flowFormInfo;\n          this.flowTaskInfo = res.data.flowTaskInfo || {};\n          this.flowTemplateInfo = res.data.flowTemplateInfo;\n          const fullName =\n            data.opType == \"-1\"\n              ? this.flowTemplateInfo.fullName\n              : this.flowTaskInfo.fullName;\n          data.fullName = fullName;\n          this.fullName = fullName;\n          this.thisStep = this.flowTaskInfo.thisStep;\n          this.flowUrgent = this.flowTaskInfo.flowUrgent || 1;\n          data.type = this.flowTemplateInfo.type;\n          data.draftData = res.data.draftData || null;\n          data.formData = res.data.formData || {};\n          data.formEnCode = this.flowFormInfo.enCode;\n          const formUrl =\n            this.flowFormInfo.formType == 2\n              ? \"workFlow/workFlowForm/dynamicForm\"\n              : this.flowFormInfo.urlAddress\n                ? this.flowFormInfo.urlAddress.replace(/\\s*/g, \"\")\n                : `workFlow/workFlowForm/${this.flowFormInfo.enCode}`;\n          this.currentView = (resolve) =>\n            require([`@/views/${formUrl}`], resolve);\n          this.flowTaskNodeList = res.data.flowTaskNodeList || [];\n          this.setting.flowTaskNodeList = this.flowTaskNodeList;\n          this.flowTemplateJson = this.flowTaskInfo && this.flowTaskInfo.flowTemplateJson ? JSON.parse(this.flowTaskInfo.flowTemplateJson) : this.flowTemplateInfo.flowTemplateJson?JSON.parse(this.flowTemplateInfo.flowTemplateJson):null;\n          /*this.flowTemplateJson = this.flowTemplateInfo.flowTemplateJson\n            ? JSON.parse(this.flowTemplateInfo.flowTemplateJson)\n            : null;*/\n          this.isComment = this.flowTemplateJson.properties.isComment;\n          this.isSummary = this.flowTemplateJson.properties.isSummary;\n          this.summaryType = this.flowTemplateJson.properties.summaryType;\n          this.flowTaskOperatorRecordList =\n            res.data.flowTaskOperatorRecordList || [];\n          this.flowTaskOperatorRecordList =\n            this.flowTaskOperatorRecordList.reverse();\n          this.properties = res.data.approversProperties || {};\n          if(this.properties && this.properties.flowVariable){\n            data.flowVariable = this.properties.flowVariable;\n          }\n          this.candidateForm.rejectType =\n            this.properties.rejectType == 3 ? 1 : this.properties.rejectType;\n          this.endTime =\n            this.flowTaskInfo.completion == 100 ? this.flowTaskInfo.endTime : 0;\n          data.formConf = this.flowFormInfo.propertyJson;\n          if (data.opType != 1 && data.opType != \"-1\") data.readonly = true;\n          data.formOperates = res.data.formOperates || [];\n          if (data.opType == 0) {\n            for (let i = 0; i < data.formOperates.length; i++) {\n              data.formOperates[i].write = false;\n            }\n          }\n          data.flowTemplateJson = this.flowTemplateJson;\n          if (this.flowTaskNodeList.length) {\n            let assignNodeList = [];\n            for (let i = 0; i < this.flowTaskNodeList.length; i++) {\n              const nodeItem = this.flowTaskNodeList[i];\n              data.opType == 4 &&\n              nodeItem.type == 1 &&\n              nodeItem.nodeType === \"approver\" &&\n              assignNodeList.push(nodeItem);\n              const loop = (data) => {\n                if (Array.isArray(data)) data.forEach((d) => loop(d));\n                if (data.nodeId === nodeItem.nodeCode) {\n                  if (nodeItem.type == 0) data.state = \"state-past\";\n                  if (nodeItem.type == 1) data.state = \"state-curr\";\n                  if (\n                    nodeItem.nodeType === \"approver\" ||\n                    nodeItem.nodeType === \"start\" ||\n                    nodeItem.nodeType === \"subFlow\"\n                  )\n                    data.content = nodeItem.userName;\n                  return;\n                }\n                if (data.conditionNodes && Array.isArray(data.conditionNodes))\n                  loop(data.conditionNodes);\n                if (data.childNode) loop(data.childNode);\n              };\n              loop(this.flowTemplateJson);\n            }\n            this.assignNodeList = assignNodeList;\n          } else {\n            this.flowTemplateJson.state = \"state-curr\";\n          }\n          data.flowTaskOperatorRecordList = this.flowTaskOperatorRecordList;\n          this.initBtnList();\n          setTimeout(() => {\n            this.$nextTick(() => {\n              this.$refs.form && this.$refs.form.init(data);\n              if (!this.$refs.form)\n                setTimeout(() => {\n                  this.$refs.form && this.$refs.form.init(data);\n                }, 500);\n            });\n          }, 500);\n        })\n        .catch(() => {\n          this.loading = false;\n        })\n        .finally(() => {\n          setTimeout(() => {\n            this.loopWordBtns();\n          },500);\n        });\n    },\n    initBtnList() {\n      const list = [];\n      const setting = this.setting;\n      const opType = this.setting.opType;\n      const properties = this.properties;\n      const flowTaskInfo = this.flowTaskInfo;\n      if (opType == \"-1\" && !setting.hideCancelBtn)\n        //list.push({ label: properties.saveBtnText || \"暂 存\", key: \"save\" });\n        if (\n          opType == 0 &&\n          setting.status == 1 &&\n          (properties.hasRevokeBtn || properties.hasRevokeBtn === undefined)\n        )\n          list.push({\n            label: properties.revokeBtnText || \"撤 回\",\n            key: \"revoke\",\n          });\n      if (\n        opType != 4 &&\n        setting.id &&\n        properties.hasPrintBtn &&\n        properties.printId\n      )\n        list.push({ label: properties.printBtnText || \"打 印\", key: \"print\" });\n      if (opType == 1) {\n        if (properties.hasTransferBtn)\n          list.push({\n            label: properties.transferBtnText || \"转 审\",\n            key: \"transfer\",\n          });\n        if (properties.hasSaveBtn)\n          list.push({\n            label: properties.saveBtnText || \"暂 存\",\n            key: \"saveAudit\",\n          });\n        if (properties.hasRejectBtn)\n          list.push({\n            label: properties.rejectBtnText || \"退 回\",\n            key: \"reject\",\n          });\n        if (properties.hasFreeApproverBtn)\n          list.push({\n            label: properties.hasFreeApproverBtnText || \"加 签\",\n            key: \"hasFreeApprover\",\n          });\n      }\n      if (opType == 4) {\n        if (flowTaskInfo.completion == 100)\n          list.push({ label: \"复 活\", key: \"resurgence\" });\n        if (\n          flowTaskInfo.completion > 0 &&\n          flowTaskInfo.completion < 100 &&\n          !flowTaskInfo.rejectDataId &&\n          (setting.status == 1 || setting.status == 3)\n        )\n          list.push({ label: \"变 更\", key: \"resurgence\" });\n        if (setting.status == 1 && this.assignNodeList.length)\n          list.push({ label: \"指 派\", key: \"assign\" });\n        if (flowTaskInfo.status == 1)\n          list.push({ label: \"挂 起\", key: \"suspend\" });\n        if (flowTaskInfo.status == 6 && !flowTaskInfo.suspend)\n          list.push({ label: \"恢 复\", key: \"recovery\" });\n      }\n      this.moreBtnList = list;\n    },\n    subFlowInfo(data) {\n      this.loading = false;\n      this.activeTab = \"0\";\n      this.subFlowVisible = true;\n      subFlowInfo(data[0].id)\n        .then((res) => {\n          this.subFlowInfoList = res.data || [];\n          this.subFlowTab = this.subFlowInfoList[0].flowTaskInfo.id;\n          this.flowUrgent =\n            this.subFlowInfoList[0].flowTaskInfo.flowUrgent || 1;\n          this.fullName = this.subFlowInfoList[0].flowTaskInfo.fullName;\n          this.flowTaskOperatorRecordList =\n            this.subFlowInfoList[0].flowTaskOperatorRecordList || [];\n          this.flowTaskOperatorRecordList =\n            this.flowTaskOperatorRecordList.reverse();\n          for (let index = 0; index < this.subFlowInfoList.length; index++) {\n            let element = this.subFlowInfoList[index];\n            element.flowTemplateInfo.flowTemplateJson = element.flowTemplateInfo\n              ? JSON.parse(element.flowTemplateInfo.flowTemplateJson)\n              : {};\n            if (element.flowTaskNodeList.length) {\n              let assignNodeList = [];\n              for (let i = 0; i < element.flowTaskNodeList.length; i++) {\n                const nodeItem = element.flowTaskNodeList[i];\n                data.opType == 4 &&\n                nodeItem.type == 1 &&\n                nodeItem.nodeType === \"approver\" &&\n                assignNodeList.push(nodeItem);\n                const loop = (data) => {\n                  if (Array.isArray(data)) data.forEach((d) => loop(d));\n                  if (data.nodeId === nodeItem.nodeCode) {\n                    if (nodeItem.type == 0) data.state = \"state-past\";\n                    if (nodeItem.type == 1) data.state = \"state-curr\";\n                    if (\n                      nodeItem.nodeType === \"approver\" ||\n                      nodeItem.nodeType === \"start\" ||\n                      nodeItem.nodeType === \"subFlow\"\n                    )\n                      data.content = nodeItem.userName;\n                    return;\n                  }\n                  if (data.conditionNodes && Array.isArray(data.conditionNodes))\n                    loop(data.conditionNodes);\n                  if (data.childNode) loop(data.childNode);\n                };\n                loop(element.flowTemplateInfo.flowTemplateJson);\n              }\n              element.assignNodeList = assignNodeList;\n            } else {\n              element.flowTemplateInfo.flowTemplateJson.state = \"state-curr\";\n            }\n            let templateJson = this.subFlowInfoList[0].flowTaskInfo\n              .flowTemplateJson\n              ? JSON.parse(\n                this.subFlowInfoList[0].flowTaskInfo.flowTemplateJson\n              )\n              : null;\n            this.isComment = templateJson.properties.isComment;\n            this.isSummary = templateJson.properties.isSummary;\n            this.summaryType = templateJson.properties.summaryType;\n            this.setting.id = this.subFlowInfoList[0].flowTaskInfo.id;\n          }\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    },\n    printBrowseHandle(id) {\n      this.printTemplateId = id;\n      this.printDialogVisible = false;\n      this.printBrowseVisible = true;\n    },\n    printDialog() {\n      this.printDialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.printDialog.init(this.properties.printId);\n      });\n    },\n    handleMore(e) {\n      if (e == \"revoke\") return this.actionLauncher(\"revoke\");\n      if (e == \"transfer\") return this.actionLauncher(\"transfer\");\n      if (e == \"saveAudit\") return this.eventLauncher(\"saveAudit\");\n      if (e == \"reject\") return this.eventReceiver({}, \"reject\");\n      if (e == \"resurgence\") return this.flowResurgence();\n      if (e == \"assign\") return this.actionLauncher(\"assign\");\n      if (e == \"comment\") return this.addComment();\n      if (e == \"print\") return this.printDialog();\n      if (e == \"suspend\") return this.suspend();\n      if (e == \"recovery\") return this.recovery();\n      this.eventLauncher(e);\n    },\n    suspend() {\n      this.suspendVisible = true;\n      this.$nextTick(() => {\n        this.$refs.suspendDialog.init(this.setting.id);\n      });\n    },\n    recovery() {\n      let data = {\n        handleOpinion: \"\",\n        fileList: [],\n      };\n      restore(this.setting.id, data)\n        .then((res) => {\n          this.$message({\n            message: res.msg,\n            type: \"success\",\n            duration: 1500,\n            onClose: () => {\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.$refs.suspendDialog.btnLoading = false;\n        });\n    },\n    suspendReceiver(dataForm) {\n      suspend(this.setting.id, dataForm).then((res) => {\n        this.$message({\n          message: res.msg,\n          type: \"success\",\n          duration: 1500,\n          onClose: () => {\n            this.$emit(\"close\", true);\n          },\n        });\n      });\n    },\n    eventLauncher(eventType) {\n      this.$refs.form &&\n      this.$refs.form.dataFormSubmit(eventType, this.flowUrgent);\n    },\n    eventReceiver(formData, eventType) {\n      this.formData = formData;\n      this.formData.flowId = this.setting.flowId;\n      this.formData.id = this.setting.id;\n      this.eventType = eventType;\n      if (eventType === \"save\" || eventType === \"submit\") {\n        return this.submitOrSave();\n      }\n      if (eventType === \"saveAudit\") {\n        return this.saveAudit();\n      }\n      if (eventType === \"hasFreeApprover\") {\n        return (this.hasFreeApproverVisible = true);\n      }\n      if (eventType === \"audit\" || eventType === \"reject\") {\n        this.handleId = \"\";\n        this.candidateForm.handleOpinion = \"\";\n        this.candidateForm.fileList = [];\n        this.copyIds = [];\n        this.isValidate = false;\n        // if (this.properties.hasSign) this.signImg = this.userInfo.signImg;\n        if (eventType === \"reject\") {\n          RejectList(this.setting.taskId?this.setting.taskId:this.setting.id)\n            .then((res) => {\n              this.showReject = res.data.isLastAppro;\n              this.rejectList = res.data.list || [];\n              if(this.properties.rejectStep !== '2'){\n                if(this.rejectList && this.rejectList.length === 1){\n                  this.candidateForm.rejectStep = [this.rejectList[0].nodeCode];\n                }else{\n                  this.candidateForm.rejectStep = [];\n                }\n              }\n              if (\n                !this.properties.hasSign &&\n                !this.properties.hasOpinion &&\n                !this.properties.isCustomCopy &&\n                !this.showReject\n              ) {\n                this.$confirm(\"此操作将退回该审批单，是否继续？\", \"提示\", {\n                  type: \"warning\",\n                })\n                  .then(() => {\n                    this.handleApproval();\n                  })\n                  .catch(() => {});\n                return;\n              }\n              this.isValidate = true;\n              this.visible = true;\n            })\n            .catch({});\n          return;\n        }\n        this.candidateLoading = true;\n        Candidates(this.setting.taskId?this.setting.taskId:this.flowTaskInfo.thisOperatorId, this.formData)\n          .then((res) => {\n            let data = res.data;\n            this.candidateType = data.type;\n            this.candidateLoading = false;\n            this.candidateForm.branchList = [];\n            this.branchList = [];\n            if (data.type == 1) {\n              this.branchList = res.data.list.filter((o) => o.isBranchFlow);\n              let list = res.data.list.filter(\n                (o) => !o.isBranchFlow && o.isCandidates\n              );\n              this.candidateForm.candidateList = list.map((o) => ({\n                ...o,\n                isDefault: true,\n                label: \"审批人\",\n                value: [],\n                rules: [\n                  {\n                    required: true,\n                    message: `审批人不能为空`,\n                    trigger: \"click\",\n                  },\n                ],\n                isSelfDept: o.isSelfDept\n              }));\n              this.$nextTick(() => {\n                this.$refs[\"candidateForm\"].resetFields();\n              });\n              this.isValidate = true;\n              this.visible = true;\n            } else if (data.type == 2) {\n              let list = res.data.list.filter((o) => o.isCandidates);\n              this.candidateForm.candidateList = list.map((o) => ({\n                ...o,\n                label: \"审批人\",\n                value: [],\n                rules: [\n                  {\n                    required: true,\n                    message: `审批人不能为空`,\n                    trigger: \"click\",\n                  },\n                ],\n              }));\n              this.$nextTick(() => {\n                this.$refs[\"candidateForm\"].resetFields();\n              });\n              this.isValidate = true;\n              this.visible = true;\n            } else {\n              this.candidateForm.candidateList = [];\n              if (\n                !this.properties.hasSign &&\n                !this.properties.hasOpinion &&\n                !this.properties.hasFreeApprover &&\n                !this.properties.isCustomCopy\n              ) {\n                let confirmMsg = \"此操作将通过该审批单，是否继续？\";\n                if(this.properties && this.properties.submitBtnText.indexOf('提交') !== -1){\n                  confirmMsg = \"此操作将提交该审批单，是否继续？\";\n                }\n                this.$confirm(confirmMsg, \"提示\", {\n                  type: \"warning\",\n                })\n                  .then(() => {\n                    this.handleApproval();\n                  })\n                  .catch(() => {});\n                return;\n              }\n              this.isValidate = true;\n              this.visible = true;\n            }\n          })\n          .catch(() => {\n            this.candidateLoading = false;\n          });\n      }\n    },\n    onBranchChange(val) {\n      const defaultList = this.candidateForm.candidateList.filter(\n        (o) => o.isDefault\n      );\n      if (!val.length) return (this.candidateForm.candidateList = defaultList);\n      let list = [];\n      for (let i = 0; i < val.length; i++) {\n        inner: for (let j = 0; j < this.branchList.length; j++) {\n          let o = this.branchList[j];\n          if (val[i] === o.nodeId && o.isCandidates) {\n            list.push({\n              ...o,\n              label: \"审批人\",\n              value: [],\n              rules: [\n                { required: true, message: `审批人不能为空`, trigger: \"click\" },\n              ],\n            });\n            break inner;\n          }\n        }\n      }\n      this.candidateForm.candidateList = [...defaultList, ...list];\n    },\n    saveAudit() {\n      this.allBtnDisabled = true;\n      this.btnLoading = true;\n      SaveAudit(this.setting.taskId || this.setting.id || 0, this.formData)\n        .then((res) => {\n          this.$message({\n            message: res.msg,\n            type: \"success\",\n            duration: 1500,\n            onClose: () => {\n              this.btnLoading = false;\n              this.allBtnDisabled = false;\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.allBtnDisabled = false;\n          this.btnLoading = false;\n        });\n    },\n    submitOrSave() {\n      this.formData.status = this.eventType === \"submit\" ? 0 : 1;\n      this.formData.flowUrgent = this.flowUrgent;\n      if (this.setting.delegateUserList) {\n        //受委托人不为空的时候走委托创建流程\n        this.formData.delegateUserList = this.setting.delegateUserList;\n      }\n\n      if (this.eventType === \"save\") return this.handleRequest();\n      this.candidateLoading = true;\n      Candidates(0, this.formData)\n        .then((res) => {\n          let data = res.data;\n          this.candidateLoading = false;\n          this.candidateType = data.type;\n          if (data.type == 1) {\n            this.branchList = res.data.list.filter((o) => o.isBranchFlow);\n            this.candidateList = res.data.list.filter(\n              (o) => !o.isBranchFlow && o.isCandidates\n            );\n            this.candidateVisible = true;\n          } else if (data.type == 2) {\n            this.branchList = [];\n            this.candidateList = res.data.list.filter((o) => o.isCandidates);\n            this.candidateVisible = true;\n          } else {\n            if (this.properties.isCustomCopy) {\n              this.branchList = [];\n              this.candidateList = [];\n              this.candidateVisible = true;\n              return;\n            }\n            this.$confirm(\"您确定要提交当前流程吗, 是否继续?\", \"提示\", {\n              type: \"warning\",\n            })\n              .then(() => {\n                this.handleRequest();\n              })\n              .catch(() => {});\n          }\n        })\n        .catch(() => {\n          this.candidateLoading = false;\n        });\n    },\n    handleRequest(candidateData) {\n      if (candidateData) this.formData = { ...this.formData, ...candidateData };\n      this.formData.candidateType = this.candidateType;\n      if (!this.formData.id) delete this.formData.id;\n      if (this.eventType === \"save\") this.btnLoading = true;\n      this.allBtnDisabled = true;\n      const formMethod = this.formData.id ? Update : Create;\n      formMethod(this.formData)\n        .then((res) => {\n          const errorData = res.data;\n          if (errorData && Array.isArray(errorData) && errorData.length) {\n            this.errorNodeList = errorData;\n            this.errorVisible = true;\n            this.allBtnDisabled = false;\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"success\",\n              duration: 1500,\n              onClose: () => {\n                if (this.eventType === \"save\") this.btnLoading = false;\n                this.candidateVisible = false;\n                this.allBtnDisabled = false;\n                this.errorVisible = false;\n                this.$emit(\"close\", true);\n              },\n            });\n          }\n        })\n        .catch(() => {\n          if (this.eventType === \"save\") this.btnLoading = false;\n          this.allBtnDisabled = false;\n          this.errorVisible = false;\n          let candidateFormRef = this.$refs.candidateForm;\n          if(candidateFormRef){\n            candidateFormRef.btnLoading = false;\n          }\n        });\n    },\n    submitCandidate(data) {\n      this.handleRequest(data);\n    },\n    actionLauncher(eventType) {\n      this.eventType = eventType;\n      if (\n        (eventType === \"revoke\" || eventType === \"recall\") &&\n        !this.properties.hasOpinion &&\n        !this.properties.hasSign\n      ) {\n        const title =\n          this.eventType == \"revoke\"\n            ? \"此操作将撤回该流程，是否继续？\"\n            : \"此操作将撤回该审批单，是否继续？\";\n        this.$confirm(title, \"提示\", {\n          type: \"warning\",\n        })\n          .then(() => {\n            this.actionReceiver();\n          })\n          .catch(() => {});\n        return;\n      }\n      this.showActionDialog();\n    },\n    showActionDialog() {\n      this.actionVisible = true;\n      this.$nextTick(() => {\n        this.$refs.actionDialog.init(this.properties, this.eventType);\n      });\n    },\n    actionReceiver(query) {\n      if (!query) {\n        query = {\n          handleOpinion: \"\",\n          signImg: \"\",\n          fileList: [],\n        };\n      }\n      const id =\n        this.eventType == \"revoke\" ? this.setting.id : this.setting.taskId;\n      const actionMethod = this.getActionMethod();\n      this.approvalBtnLoading = true;\n      actionMethod(id, query)\n        .then((res) => {\n          this.approvalBtnLoading = false;\n          this.$message({\n            type: \"success\",\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.$refs.actionDialog.btnLoading = false;\n          this.approvalBtnLoading = false;\n        });\n    },\n    getActionMethod() {\n      if (this.eventType === \"transfer\") return Transfer;\n      if (this.eventType === \"assign\") return Assign;\n      if (this.eventType === \"revoke\") return Revoke;\n      if (this.eventType === \"recall\") return Recall;\n      if (this.eventType === \"cancel\") return Cancel;\n    },\n    press() {\n      this.$confirm(\"此操作将提示该节点尽快处理，是否继续?\", \"提示\", {\n        type: \"warning\",\n      })\n        .then(() => {\n          Press(this.setting.id).then((res) => {\n            this.$message({\n              type: \"success\",\n              message: res.msg,\n              duration: 1000,\n            });\n          });\n        })\n        .catch(() => {});\n    },\n    handleError(data) {\n      if (this.eventType === \"submit\") {\n        this.formData.errorRuleUserList = data;\n        this.handleRequest();\n        return;\n      }\n      if (this.eventType === \"audit\" || this.eventType === \"reject\") {\n        this.handleApproval(data);\n        return;\n      }\n      if (this.eventType === \"resurgence\") {\n        this.handleResurgence(data);\n        return;\n      }\n    },\n    handleApproval(errorRuleUserList) {\n      const handleRequest = () => {\n        if (this.properties.hasSign && !this.signImg) {\n          this.$message({\n            message: \"请签名\",\n            type: \"error\",\n          });\n          return;\n        }\n        let query = {\n          handleOpinion: this.candidateForm.handleOpinion,\n          fileList: this.candidateForm.fileList,\n          ...this.formData,\n          enCode: this.setting.enCode,\n          signImg: this.signImg,\n          copyIds: this.copyIds.join(\",\"),\n          branchList: this.candidateForm.branchList,\n          candidateType: this.candidateType,\n          rejectType: this.candidateForm.rejectType,\n        };\n        if (this.eventType === \"reject\"){\n          if(this.candidateForm.rejectStep instanceof Array){\n            query.rejectStep = this.candidateForm.rejectStep.join(\",\");\n          }else {\n            query.rejectStep = this.candidateForm.rejectStep;\n          }\n        }\n        if (errorRuleUserList) query.errorRuleUserList = errorRuleUserList;\n        if (this.candidateForm.candidateList.length) {\n          let candidateList = {};\n          for (let i = 0; i < this.candidateForm.candidateList.length; i++) {\n            candidateList[this.candidateForm.candidateList[i].nodeId] =\n              this.candidateForm.candidateList[i].value;\n          }\n          query.candidateList = candidateList;\n        }\n        if (this.eventType === \"audit\" && this.properties.hasFreeApprover) {\n          query = { freeApproverUserId: this.handleId, ...query };\n        }\n        const approvalMethod = this.eventType === \"audit\" ? Audit : Reject;\n        this.approvalBtnLoading = true;\n        approvalMethod(this.setting.taskId?this.setting.taskId:this.flowTaskInfo.thisOperatorId, query)\n          .then((res) => {\n            const errorData = res.data;\n            if (errorData && Array.isArray(errorData) && errorData.length) {\n              this.errorNodeList = errorData;\n              this.errorVisible = true;\n              this.approvalBtnLoading = false;\n            } else {\n              this.$message({\n                type: \"success\",\n                message: res.msg,\n                duration: 1000,\n                onClose: () => {\n                  this.approvalBtnLoading = false;\n                  this.visible = false;\n                  this.errorVisible = false;\n                  this.$emit(\"close\", true);\n                },\n              });\n            }\n          })\n          .catch(() => {\n            this.approvalBtnLoading = false;\n          });\n      };\n      if (!this.isValidate) return handleRequest();\n      this.$refs[\"candidateForm\"].validate((valid) => {\n        if (valid) {\n          handleRequest();\n        }\n      });\n    },\n    addComment() {\n      this.$refs.comment && this.$refs.comment.showCommentDialog();\n    },\n    setPageLoad(val) {\n      this.loading = !!val;\n    },\n    setCandidateLoad(val) {\n      this.candidateLoading = !!val;\n      this.allBtnDisabled = !!val;\n    },\n    setLoad(val) {\n      this.btnLoading = !!val;\n    },\n    handleFlowUrgent(e) {\n      this.flowUrgent = e;\n    },\n    showTypeBtn(btnName,state){\n      if(this.setting && this.setting.row && this.setting.row.handleState && this.setting.row.handleState >= state){\n        return true;\n      }\n      let records = this.setting.flowTaskOperatorRecordList;\n      if(!records || records.length < 1){\n        return false;\n      }\n      return records[0].handleStatus == 0 && records[0].nodeName === btnName;\n    },\n    activeTabClick(e){\n      if(e.name === '99'){\n        this.loopWordBtns();\n      }\n    },\n    loopWordBtns(){\n      /*if(this.setting && this.setting.row && this.setting.row.nodeProperties){\n        let nodeProperties = JSON.parse(this.setting.row.nodeProperties);\n        if(nodeProperties.wordExport){\n          let arr = new Set();\n          for(let i = 0; i < nodeProperties.wordExport.length; i++){\n            if(nodeProperties.wordExport[i].ftlName){\n              arr.add(nodeProperties.wordExport[i].ftlName.replace('.ftl',''));\n            }\n          }\n          this.exportBtnArr = Array.from(arr);\n\n          let arr2 = new Set();\n          if(this.flowTaskInfo && this.flowTaskInfo.flowTemplateJson){\n            this.loopFtlName(JSON.parse(this.flowTaskInfo.flowTemplateJson),arr2);\n          }\n          this.exportBtnArr = Array.from(arr);\n          /!*this.exportBtnArr = nodeProperties.wordExport.filter(item => item.ftlName).map(item => {\n            if(item.ftlName){\n              return item.ftlName.replace('.ftl','');\n            }\n          })*!/\n\n          this.$nextTick(() => {\n            if(this.exportBtnArr && this.exportBtnArr.length>0){\n              this.currentWordBtn = this.exportBtnArr[0];\n            }\n          })\n        }\n      }*/\n      let arr = new Set();\n      let currentNodeInfo = {\n        currentNodeFtlName: null,\n      };\n      if(this.flowTaskInfo && this.flowTaskInfo.thisStepId){\n        currentNodeInfo.currentNode = this.flowTaskInfo.thisStepId.split(',')[0];\n      }\n      if(this.flowTemplateJson){\n        this.loopFtlName(this.flowTemplateJson,arr,currentNodeInfo);\n      }\n      this.exportBtnArr = Array.from(arr);\n      /*this.exportBtnArr = nodeProperties.wordExport.filter(item => item.ftlName).map(item => {\n        if(item.ftlName){\n          return item.ftlName.replace('.ftl','');\n        }\n      })*/\n\n      this.$nextTick(() => {\n        if(this.exportBtnArr && this.exportBtnArr.length>0){\n          this.currentWordBtn = this.exportBtnArr[0];\n          if(currentNodeInfo.currentNodeFtlName){\n            let match = this.exportBtnArr.find(item => item === currentNodeInfo.currentNodeFtlName);\n            if(match){\n              this.currentWordBtn = match;\n            }\n          }\n        }\n      })\n    },\n    loopFtlName(data,arr,currentNodeInfo){\n      if(data){\n        let properties = data.properties;\n        if(properties){\n          let nodeProperties = properties.nodeProperties;\n          if(nodeProperties){\n            let wordExportList = [];\n            if(typeof nodeProperties === 'string'){\n              wordExportList = JSON.parse(nodeProperties).wordExport;\n            }else {\n              wordExportList = nodeProperties.wordExport;\n            }\n            if(wordExportList && wordExportList.length>0){\n              wordExportList.forEach(wordExport => {\n                let ftlName = wordExport.ftlName;\n                if(ftlName){\n                  arr.add(ftlName.replace('.ftl',''));\n                  if(data.nodeId === currentNodeInfo.currentNode){\n                    currentNodeInfo.currentNodeFtlName = ftlName.replace('.ftl','');\n                  }\n                }\n              })\n            }\n          }\n        }\n\n        let conditionNodes = data.conditionNodes;\n        if(conditionNodes && conditionNodes.length > 0){\n          conditionNodes.forEach(conditionItem => this.loopFtlName(conditionItem,arr,currentNodeInfo));\n        }\n        let childNode = data.childNode;\n        if(childNode){\n          this.loopFtlName(childNode,arr,currentNodeInfo);\n        }\n      }\n    },\n    wordBtnSelected(label){\n      if(!label || '0' === label || 0 === label){\n        return;\n      }\n      if(this.setting && this.setting.row && this.setting.row.id){\n        getOrder(this.setting.row.id).then(baseRes => {\n          let data = {...baseRes.data};\n          this.currentWordForm = {...data,...this.currentWordForm}\n        }).finally(() => {\n          if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n            this.wordPreviewLoading = true;\n            let data = {...this.currentWordForm};\n            data.ftlName = label+'.ftl';\n            data.curTargetDept = this.wordTargetDept;\n            commonGetWordByTemp(data).then(res => {\n              this.$nextTick(() => {\n                setTimeout(() => {\n                  this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n                },300)\n              })\n            }).finally(()=>{\n              this.wordPreviewLoading = false;\n            })\n          }else {\n            if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n              return;\n            }\n            this.wordPreviewLoading = true;\n            commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id,curTargetDept: this.wordTargetDept}).then(res => {\n              this.$nextTick(() => {\n                setTimeout(() => {\n                  this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n                },300)\n              })\n            }).finally(()=>{\n              this.wordPreviewLoading = false;\n            })\n          }\n        })\n      }else {\n        if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n          this.wordPreviewLoading = true;\n          let data = {...this.currentWordForm};\n          data.ftlName = label+'.ftl';\n          commonGetWordByTemp(data).then(res => {\n            this.$nextTick(() => {\n              setTimeout(() => {\n                this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n              },300)\n            })\n          }).finally(()=>{\n            this.wordPreviewLoading = false;\n          })\n        }else {\n          if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n            return;\n          }\n          this.wordPreviewLoading = true;\n          commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id}).then(res => {\n            this.$nextTick(() => {\n              setTimeout(() => {\n                this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n              },300)\n            })\n          }).finally(()=>{\n            this.wordPreviewLoading = false;\n          })\n        }\n      }\n\n      /* if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n        this.wordPreviewLoading = true;\n        let data = {...this.currentWordForm};\n        data.ftlName = label+'.ftl';\n        commonGetWordByTemp(data).then(res => {\n          this.$nextTick(() => {\n            setTimeout(() => {\n              this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n            },300)\n          })\n        }).finally(()=>{\n          this.wordPreviewLoading = false;\n        })\n      }else {\n        if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n          return;\n        }\n        this.wordPreviewLoading = true;\n        commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id}).then(res => {\n          this.$nextTick(() => {\n            setTimeout(() => {\n              this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n            },300)\n          })\n        }).finally(()=>{\n          this.wordPreviewLoading = false;\n        })\n      } */\n    },\n    onCommonWordsSelected(val){\n      this.candidateForm.handleOpinion = val;\n    },\n    formTabsClick(el){\n      if(\"2\" === el.name){\n        this.$eventBus.$emit('getWorkForm',1);\n        //this.loopWordBtns();\n      }\n    },\n    handleShowWord(){\n      this.currentWordForm = this.$refs.form && this.$refs.form.sendDataForm && this.$refs.form.sendDataForm();\n      this.wordBtnSelected(this.currentWordBtn);\n    },\n    reportDataChange(val){\n      if(val && val.length>0){\n        this.wordTargetDeptOptions = val.filter(item => item.formData && item.formData.length>0).map(item => {\n          return {\n            label: item.deptName,\n            value: item.deptId,\n            key: item.deptId\n          }\n        });\n        if(this.wordTargetDeptOptions && this.wordTargetDeptOptions.length>0){\n          if(!this.wordTargetDept){\n            this.wordTargetDept = this.wordTargetDeptOptions[0].value;\n          }else {\n            let match = this.wordTargetDeptOptions.find(item => item.value === this.wordTargetDept);\n            if(!match){\n              this.wordTargetDept = this.wordTargetDeptOptions[0].value;\n            }\n          }\n        }\n      }\n    },\n    wordTargetDeptChange(val){\n      this.handleShowWord();\n    },\n    rejectChange(val){\n      if(val && val.length>0){\n        let curNode = this.rejectList.find(item => item.nodeCode === val[0]);\n        if(!curNode){\n          return;\n        }\n        this.rejectList.forEach(item => {\n          if(item.sortCode === curNode.sortCode){\n            item.disabled = false;\n          }else {\n            item.disabled = true;\n          }\n        })\n      }else {\n        this.rejectList.forEach(item => {\n          item.disabled = false;\n        })\n      }\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.flow-form-main {\n  position: absolute;\n  background-color: #fff;\n  left: 0;\n  top: 0;\n  z-index: 100;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  .JNPF-el_tabs {\n    height: calc(100% - 62px);\n    background: #f2f4f8;\n  }\n}\n\n.color-box {\n  width: 7px;\n  height: 7px;\n  display: inline-block;\n  border-radius: 50%;\n}\n.flow-urgent-value {\n  display: flex;\n  align-items: center;\n  span:first-child {\n    margin: 0 3px 0 10px;\n  }\n}\n\n.options {\n  .dropdown {\n    margin-right: 10px;\n  }\n  .el-button {\n    min-width: 70px;\n  }\n}\n.dropdown-item {\n  min-width: 70px;\n  text-align: center;\n}\n.subFlow_tabs {\n  // ::v-deep .el-tabs__item {\n  //   text-align: center;\n  // }\n  // ::v-deep .el-tabs__content {\n  //   padding: 0px 0 15px;\n  // }\n  height: 100%;\n  overflow: auto;\n  overflow-x: hidden;\n  /* padding: 0 10px 10px; */\n}\n.commonWords-button {\n  margin-top: 57px;\n}\n.JNPF-page-header-content {\n  max-width: 40vw;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.type_btn{\n  display: flex;\n  padding-bottom: 10px;\n  padding-left: 10px;\n  >.type_btn_item:not(:first-child){\n    margin-left: 10px;\n  }\n  .btn_active{\n    background: #1890ff;\n    border-color: #1890ff;\n    color: #FFFFFF;\n  }\n}\n\n::v-deep .el-dialog__body{\n  .flow-container{\n    .scale-slider{\n      position: absolute !important;\n      display: none !important;\n    }\n  }\n}\n\n.word-preview-btns{\n  text-align: center;\n  margin-top: 10px;\n  display: flex;\n  align-items: center;\n  .btns{\n    width: 70%;\n  }\n  .target-select{\n    flex: 1;\n    padding-right: 5px;\n  }\n  ::v-deep .el-tabs__nav-scroll{\n    .el-tabs__active-bar{\n      background-color: #383838;\n      height: 0;\n    }\n    .el-tabs__item{\n      font-size: 18px;\n      font-weight: 700;\n      color: #A1A1A1;\n    }\n    .el-tabs__item.is-active{\n      color: #383838;\n      border-color: #383838;\n      border-bottom-width: 3px;\n    }\n  }\n}\n\n.tool-btns{\n  margin-top: 5px;\n}\n\n::v-deep .center_tabs{\n  .el-tabs__content{\n    background: #fff;\n    margin-top: 8px;\n    position: relative;\n    .el-tabs__header{\n      position: relative;\n    }\n  }\n  .center_tabs_pane{\n    margin-top: 20px;\n    display: flex;\n    height: 100%;\n    overflow: hidden !important;\n\n    .tabs-pane-title{\n      font-size: 18px;\n      text-align: center;\n    }\n  }\n  > .el-tabs__header{\n    /*padding-bottom: 10px;\n    border-bottom: 1px solid #dcdfe6;*/\n  }\n  /*.center_tabs_pane::before{\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 2px;\n    background: #DCDFE6;\n    transform: translateY(-10px);\n  }*/\n}\n\n.work_order_flow{\n  .el-tab-pane{\n    padding: 0 0 10px !important;\n    overflow: hidden !important;\n  }\n}\n\n.form-container {\n  flex: 1;\n  height: 100%;\n  overflow-y: auto;\n  &::-webkit-scrollbar-track {\n    background: #f2f4f8 !important; /* 轨道颜色 */\n  }\n}\n\n.word-preview-container {\n  width: 40%;\n  height: 100%;\n  border-left: 3px solid #f2f4f8;\n}\n</style>\n"]}]}