package com.ruoyi.external.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.validation.Validator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.external.domain.ExternalAttackMiniProgram;
import com.ruoyi.external.mapper.ExternalAttackMiniProgramMapper;
import com.ruoyi.external.model.ExternalAttackMiniProgramExcelForm;
import com.ruoyi.external.service.IExternalAttackMiniProgramService;
import com.ruoyi.external.util.ExternalAttackValidationUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信小程序Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
@Slf4j
@Service
public class ExternalAttackMiniProgramServiceImpl implements IExternalAttackMiniProgramService
{
    // 每批次查询的最大元素数量
    private static final int BATCH_SIZE = 500;

    @Autowired
    private ExternalAttackMiniProgramMapper externalAttackMiniProgramMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper userMapper;

    private Long defaultDeptId = 100L;

    @PostConstruct
    public void initDefaultDeptId() {
        SysDept defaultDept = deptMapper.selectDeptByParentId(0L);
        if (Objects.nonNull(defaultDept)) {
            this.defaultDeptId = Objects.nonNull(defaultDept.getDeptId()) ? defaultDept.getDeptId() : 100L;
        }
    }

    /**
     * 查询微信小程序
     *
     * @param id 微信小程序主键
     * @return 微信小程序
     */
    @Override
    public ExternalAttackMiniProgram selectExternalAttackMiniProgramById(Long id)
    {
        return externalAttackMiniProgramMapper.selectExternalAttackMiniProgramById(id);
    }

    /**
     * 批量查询微信小程序
     *
     * @param ids 微信小程序主键集合
     * @return 微信小程序集合
     */
    @Override
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramByIds(Long[] ids)
    {
        return externalAttackMiniProgramMapper.selectExternalAttackMiniProgramByIds(ids);
    }

    /**
     * 查询微信小程序列表
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 微信小程序
     */
    @Override
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramList(ExternalAttackMiniProgram externalAttackMiniProgram)
    {
        return externalAttackMiniProgramMapper.selectExternalAttackMiniProgramList(externalAttackMiniProgram);
    }

    /**
     * 新增微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    @Override
    public int insertExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram)
    {
        // 录入类型为手动时，才校验，因为数据同步时，已有去重逻辑
        if (Objects.equals(externalAttackMiniProgram.getEntryType(),2)) {
            validateMiniProgramUniqueness(externalAttackMiniProgram, null);
        }

        externalAttackMiniProgram.setCreateTime(DateUtils.getNowDate());
        // 如果没有deptId，则设置为默认部门id(定级部门)
        if (Objects.isNull(externalAttackMiniProgram.getDeptId())) {
            externalAttackMiniProgram.setDeptId(defaultDeptId);
        }
        return externalAttackMiniProgramMapper.insertExternalAttackMiniProgram(externalAttackMiniProgram);
    }

    /**
     * 修改微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    @Override
    public int updateExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram)
    {
        validateMiniProgramUniqueness(externalAttackMiniProgram, externalAttackMiniProgram.getId());


        externalAttackMiniProgram.setUpdateTime(DateUtils.getNowDate());
        return externalAttackMiniProgramMapper.updateExternalAttackMiniProgram(externalAttackMiniProgram);
    }


    /**
     * 检查 微信小程序 是否已存在（排除指定 id）
     * xxAppId非空的校验唯一性,空的就不校验唯一
     *
     * @param record
     * @param excludeId
     * @param excludeId
     */
    private void validateMiniProgramUniqueness(ExternalAttackMiniProgram record, Long excludeId) {
        String miniProgramAppId = record.getMiniProgramAppId();
        String miniProgramName = record.getMiniProgramName();
        if (externalAttackMiniProgramMapper.checkMiniProgramExistence(miniProgramAppId, miniProgramName, excludeId) > 0) {
            throw new ServiceException("微信小程序已存在");
        }
    }


    /**
     * 校验微信小程序是否存在
     *
     * @param appId
     * @param miniProgramName
     * @return
     */
    private boolean validateMiniProgram(String appId, String miniProgramName) {
        return externalAttackMiniProgramMapper.checkMiniProgramExistence(appId, miniProgramName, null) > 0;
    }


    /**
     * 删除微信小程序信息
     *
     * @param id 微信小程序主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackMiniProgramById(Long id)
    {
        return externalAttackMiniProgramMapper.deleteExternalAttackMiniProgramById(id);
    }


    /**
     * 批量删除微信小程序
     *
     * @param ids 需要删除的微信小程序主键
     * @return 结果
     */
    @Override
    public int deleteExternalAttackMiniProgramByIds(Long[] ids)
    {
        return externalAttackMiniProgramMapper.deleteExternalAttackMiniProgramByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExternalAttackMiniProgram(List<ExternalAttackMiniProgramExcelForm> miniProgramExcelFormList, boolean updateSupport, String operName) {
        if (CollUtil.isEmpty(miniProgramExcelFormList)){
            throw new ServiceException("导入微信小程序数据不能为空！");
        }
        // 检查导入数据条数是否超过500条
        if (miniProgramExcelFormList.size() > 500) {
            throw new ServiceException("单次导入数据条数不能超过500条！");
        }
        // 查询部门列表
        List<SysDept> queryDeptList = deptMapper.selectDeptList(new SysDept());
        Map<String, SysDept> deptMap = queryDeptList.stream().collect(Collectors.toMap(
                SysDept::getDeptName,
                Function.identity(),
                (dept1, dept2) -> { // mergeFunction, 解决键冲突的方法
                    // 按ID大小比较
                    return dept1.getDeptId() < dept2.getDeptId() ? dept1 : dept2;
                }));
        // 查询用户列表
        List<SysUser> sysUsers = userMapper.selectUserList(new SysUser());
        Map<String, Long> userMap = sysUsers.stream().collect(Collectors.toMap(
                user -> user.getNickName() + "-" + user.getPhonenumber(), // 新的键生成方式
                SysUser::getUserId, // 值为 SysUser 的 userId
                (id1, id2) -> { // 解决键冲突的方法
                    // 如果两个用户有相同的键，则选择一个值，这里简单地返回第一个值
                    return id1;
                }
        ));

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        //记录新增和更新域名
        List<ExternalAttackMiniProgram> insertProgramList = new ArrayList<>();
        List<ExternalAttackMiniProgram> updateProgramList = new ArrayList<>();
        //检查新增微信小程序的App ID否有重复的
        HashSet<String> miniProgramAppIds = new HashSet<>();
        //先检查小程序APP ID是否存在和校验数据是否正确
        for (ExternalAttackMiniProgramExcelForm miniProgramExcelForm : miniProgramExcelFormList)
        {
            try
            {
                if (!miniProgramAppIds.contains(StrUtil.format("{}-{}", miniProgramExcelForm.getMiniProgramName(), miniProgramExcelForm.getMiniProgramAppId())))
                {
                    BeanValidators.validateWithException(validator, miniProgramExcelForm);
                    // 域名重复
                    if (this.validateMiniProgram(miniProgramExcelForm.getMiniProgramAppId(), miniProgramExcelForm.getMiniProgramName())){
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、小程序名称 "+miniProgramExcelForm.getMiniProgramName()+" 小程序APP ID " + miniProgramExcelForm.getMiniProgramAppId() + " 的数据已存在");
                    }else if(!deptMap.containsKey(miniProgramExcelForm.getDeptName())){
                        // 校验部门
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、小程序APP ID " + miniProgramExcelForm.getMiniProgramAppId() + " 输入的所属部门不正确");
                    }else {
                        String responsiblePerson = miniProgramExcelForm.getResponsiblePersonName();
                        Pair<Boolean, String> booleanStringPair = Pair.of(true, "默认校验通过");
                        if(StrUtil.isNotBlank(responsiblePerson)){
                            booleanStringPair = ExternalAttackValidationUtils.checkResponsibilityExists(sysUsers, responsiblePerson);
                        }
                        if (!booleanStringPair.getKey()) {
                            // 校验负责人输入格式是否正确/负责人是否存在
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、小程序APP ID " + miniProgramExcelForm.getMiniProgramAppId() + booleanStringPair.getValue());
                        } else {
                            ExternalAttackMiniProgram externalAttackMiniProgram = new ExternalAttackMiniProgram();
                            BeanUtils.copyBeanProp(externalAttackMiniProgram, miniProgramExcelForm);
                            externalAttackMiniProgram.setDeptId(deptMap.get(miniProgramExcelForm.getDeptName()).getDeptId());
                            String userIds = ExternalAttackValidationUtils.getUserIds(responsiblePerson, userMap);
                            externalAttackMiniProgram.setResponsiblePerson(userIds);
                            externalAttackMiniProgram.setCreateBy(operName);
                            // 设置小程序录入类型为2手动
                            externalAttackMiniProgram.setEntryType(2);
                            insertProgramList.add(externalAttackMiniProgram);
                            successMsg.append("<br/>" + successNum + "、小程序APP ID " + externalAttackMiniProgram.getMiniProgramAppId() + " 导入成功");
                            miniProgramAppIds.add(StrUtil.format("{}-{}", miniProgramExcelForm.getMiniProgramName(), miniProgramExcelForm.getMiniProgramAppId()));
                        }
                    }
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、小程序名称 "+miniProgramExcelForm.getMiniProgramName()+" 小程序APP ID " + miniProgramExcelForm.getMiniProgramAppId() + " 已存在或重复添加");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、小程序APP ID " + miniProgramExcelForm.getMiniProgramAppId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            //插入域名数据
            for (ExternalAttackMiniProgram externalAttackMiniProgram : insertProgramList) {
                this.insertExternalAttackMiniProgram(externalAttackMiniProgram);
                successNum++;
            }
            if (updateSupport){
                for (ExternalAttackMiniProgram externalAttackMiniProgram : updateProgramList) {
                    this.updateExternalAttackMiniProgram(externalAttackMiniProgram);
                    successNum++;
                }
            }
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    @Override
    public List<ExternalAttackMiniProgram> selectByMiniProgramAppIds(Set<String> appIds) {
        List<ExternalAttackMiniProgram> result = new ArrayList<>();
        int totalSize = appIds.size();
        int batchSize = BATCH_SIZE;
        // 将 Set 转换为 List，以便支持 subList 操作
        List<String> appIdsList = new ArrayList<>(appIds);
        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<String> batchAppIds = appIdsList.subList(i, end);
            result.addAll(externalAttackMiniProgramMapper.selectByAppIds(batchAppIds));
        }
        return result;
    }

    @Override
    public int countNum() {
        return externalAttackMiniProgramMapper.countNum();
    }

    @Override
    public void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId) {
         if (CollUtil.isEmpty(uniqueKeys)) {
            return;
        }
        externalAttackMiniProgramMapper.deleteByEntryTypeAndAppId(uniqueKeys, deviceConfigId);
    }

}
