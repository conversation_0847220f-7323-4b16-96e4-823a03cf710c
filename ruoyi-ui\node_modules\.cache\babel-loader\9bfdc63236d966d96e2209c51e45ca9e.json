{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\loophole\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\loophole\\index.vue", "mtime": 1756794280289}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_assetFrailty", "require", "_createWork", "_interopRequireDefault", "_newAddloophole", "_FlowBox", "_index", "_<PERSON><PERSON><PERSON>n", "_index2", "_LeakScanDialog", "_assetFrailty2", "_wpresult", "_monitor", "_vulnResult", "_deptSelect", "_FlowEngine", "_user", "_threat", "_index3", "name", "components", "Webvuln", "DeptSelect", "LeakScanDialog", "CronInput", "CreateWork", "newAddloophole", "FlowBox", "FlowTemplateSelect", "SystemList", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "props", "severity", "type", "Number", "toParams", "Object", "dicts", "data", "listType", "hostRisk", "webRisk", "vulnerabilityScanning", "totalNumberOfRisks", "hostRiskList", "title", "img", "num", "webRiskList", "userList", "showHandleDialog", "handleForm", "id", "handleDesc", "handleRules", "showAll", "flowVisible", "editable", "loopholeData", "showAddloophole", "showHandleBatchDialog", "DealStatusData", "loading", "ids", "rows", "single", "multiple", "handleStateOptions", "label", "value", "handleStateOption", "flowStateOptions", "queryParams", "category", "hostIp", "handleState", "hostPort", "dealStatus", "domainId", "pageNum", "pageSize", "total", "assetFrailtyList", "deptOptions", "handleStateList", "rickLevelList", "typeList", "openDialog", "vulnId", "workDialog", "gapId", "workId", "flowTemplateSelectVisible", "currentFlowData", "scanStrategyVisible", "editForm", "form", "jobGroup", "jobType", "cronExpression", "period", "status", "cronTransfer", "isDisabled", "created", "$route", "query", "$emit", "initDept", "initData", "handleQuery", "watch", "handler", "val", "deep", "immediate", "newVal", "referenceId", "methods", "_this", "listUser", "res", "getHandleStateVulnStat", "for<PERSON>ach", "e", "handle_state", "obj1", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "count", "push", "getRickLevelVulnStat", "getHostVulnTypeList", "_this2", "getDepts", "handleStateFormatter", "row", "column", "cellValue", "index", "match", "find", "item", "disposer<PERSON><PERSON><PERSON><PERSON>", "userId", "nick<PERSON><PERSON>", "syncStatus<PERSON><PERSON><PERSON><PERSON>", "dict", "synchronization_status", "handleAddJob", "_this3", "length", "ips", "split", "invokeIp", "join", "job<PERSON>ame", "Date", "getTime", "invoke<PERSON><PERSON><PERSON>", "from", "_objectSpread2", "$modal", "confirm", "addJob", "getList", "msgSuccess", "catch", "submitHandleForm", "_this4", "handleVuln", "$message", "success", "showHandle", "assetName", "showHandleBatch", "_toConsumableArray2", "filter", "error", "submitHandleBatchForm", "_this5", "handleBatchVuln", "handleAdd", "handleEdit", "handleScan", "weakPw", "handleDelete", "_this6", "vulnNames", "delDeal", "canceloophole", "confirmVulnDeal", "reset<PERSON><PERSON>y", "$props", "handleStatus", "$refs", "systemList1", "resetSelection", "systemList2", "_this7", "tempQueryParams", "handleType", "getVulnDealList", "response", "selectable", "handleExport", "download", "concat", "handleDetail", "flowStateFormatter", "createWork", "closeWork", "flowTemplateSelectChange", "_this8", "flowId", "$nextTick", "init", "addOrUpdateFlowHandle", "flowState", "_this9", "formType", "opType", "isWork", "workType", "eventType", "originType", "getConfigKey", "msg", "getFlowEngineInfo", "finally", "addOrUpdateFlowHandleBatch", "_this10", "_this11", "FlowEngineInfo", "flowTemplateJson", "JSON", "parse", "handleSelectionChange", "selection", "map", "colse<PERSON>low", "isrRefresh", "handleApplicationTagShow", "applicationList", "result", "unique", "arr", "i", "j", "splice"], "sources": ["src/views/frailty/loophole/index.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"100px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"漏洞名称\" prop=\"title\">\n                <el-input\n                  v-model=\"queryParams.title\"\n                  placeholder=\"请输入内容\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"漏洞类型\" prop=\"category\">\n                <!--<el-input-->\n                <!--v-model=\"queryParams.category\"-->\n                <!--placeholder=\"请输入内容\"-->\n                <!--clearable-->\n                <!--@keyup.enter.native=\"handleQuery\"-->\n                <!--/>-->\n                <el-select\n                  v-model=\"queryParams.category\"\n                  placeholder=\"请选择漏洞类型\"\n                  :popper-append-to-body=\"false\"\n                >\n                  <el-option\n                    v-for=\"dict in typeList\"\n                    :key=\"dict.category\"\n                    :label=\"dict.category\"\n                    :value=\"dict.category\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"影响资产主IP\" prop=\"hostIp\">\n                <el-input\n                  v-model=\"queryParams.hostIp\"\n                  placeholder=\"请输入内容\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button\n                >\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置\n                </el-button\n                >\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-show=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <!--                <el-select\n                                  v-model=\"queryParams.deptId\"\n                                  placeholder=\"请选择所属部门\"\n                                  filterable\n                                  clearable\n                                >\n                                  <el-option\n                                    v-for=\"item in deptOptions\"\n                                    :key=\"item.deptId\"\n                                    :label=\"item.deptName\"\n                                    :value=\"item.deptId\"\n                                  ></el-option>\n                                </el-select>-->\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\" prop=\"title\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\" prop=\"title\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-show=\"showAll\" :gutter=\"10\">\n<!--            <el-col :span=\"24\" v-if=\"rickLevelList.length\">\n              <el-form-item label=\"漏洞等级\">\n                <SystemList\n                  ref=\"systemList2\"\n                  :systemTypes=\"rickLevelList\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.severity\"\n                />\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\" v-if=\"handleStateList.length\">\n              <el-form-item label=\"处置状态\">\n                <SystemList\n                  ref=\"systemList1\"\n                  :systemTypes=\"handleStateList\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.handleState\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">主机漏洞列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['frailty:loophole:add']\"\n                >新增\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"addOrUpdateFlowHandleBatch(null,null)\"\n                  v-hasPermi=\"['frailty:loophole:edit']\"\n                >创建通报\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"showHandleBatch\"\n                  v-hasPermi=\"['frailty:loophole:edit']\"\n                >批量处置\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"single\"\n                  @click=\"handleAddJob\"\n                  v-hasPermi=\"['frailty:loophole:check']\"\n                >复查\n                </el-button>\n              </el-col>\n              <!--<el-col :span=\"1.5\">-->\n              <!--<el-button-->\n              <!--class=\"btn1\"-->\n              <!--size=\"small\"-->\n              <!--@click=\"handleScan\"-->\n              <!--&gt;IP漏洞扫描-->\n              <!--</el-button>-->\n              <!--</el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table height=\"100%\" v-loading=\"loading\" :data=\"assetFrailtyList\" ref=\"multipleTable\"\n                  @selection-change=\"handleSelectionChange\">\n          <el-table-column\n            type=\"selection\"\n            width=\"55\">\n          </el-table-column>\n          <el-table-column\n            label=\"漏洞名称\"\n            prop=\"title\"\n            min-width=\"300\"\n            show-overflow-tooltip\n          />\n          <el-table-column\n            label=\"漏洞类型\"\n            prop=\"category\"\n            width=\"150\"\n          >\n            <template slot-scope=\"scope\">\n              <dict-tag\n                :options=\"dict.type.loophole_category\"\n                :value=\"scope.row.category\"\n              />\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"漏洞等级\"\n            prop=\"severity\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.severity == 0\" type=\"info\">未知</el-tag>\n              <el-tag v-if=\"scope.row.severity == 1\" type=\"success\"\n              >低危\n              </el-tag\n              >\n              <el-tag v-if=\"scope.row.severity == 2\" type=\"primary\"\n              >中危\n              </el-tag\n              >\n              <el-tag v-if=\"scope.row.severity == 3\" type=\"warning\"\n              >高危\n              </el-tag\n              >\n              <el-tooltip v-if=\"scope.row.severity === 4\" placement=\"top-end\" content=\"可入侵漏洞\" effect=\"light\">\n                <el-tag type=\"danger\">严重</el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"影响资产主IP\" prop=\"hostIp\" width=\"220\" show-overflow-tooltip>\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.hostIp }}</span>\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 9\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 10\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"端口\"\n            prop=\"hostPort\"\n            width=\"120\"\n          />\n          <el-table-column\n            label=\"所属部门\"\n            prop=\"deptName\"\n            align=\"center\"\n            width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.deptName ? unique(scope.row.deptName.split(',')).join(',') : '-' }}</span>\n            </template>\n          </el-table-column>\n          <!--<el-table-column-->\n          <!--label=\"协议\"-->\n          <!--prop=\"protocol\"-->\n          <!--width=\"100\"-->\n          <!--/>-->\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"120\" :formatter=\"disposerFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"120\" :formatter=\"handleStateFormatter\"/>\n          <!--<el-table-column-->\n          <!--label=\"通报状态\"-->\n\n          <!--prop=\"flowState\"-->\n          <!--width=\"120\"-->\n          <!--:formatter=\"flowStateFormatter\"-->\n          <!--/>-->\n\n          <el-table-column\n            label=\"数据来源\"\n            prop=\"dataSource\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.dataSource == '1'\">探测</span>\n              <span v-else-if=\"scope.row.dataSource == '2'\">手动</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"发现次数\"\n            prop=\"scanNum\"\n            width=\"100\"\n          />\n          <!--        <el-table-column label=\"发现漏洞次数\"  width=\"120\" prop=\"scanNum\"/>-->\n          <el-table-column\n            label=\"最近漏洞时间\"\n            prop=\"updateTime\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.updateTime, \"{y}-{m}-{d} {h}:{i}:{s}\") }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"120\" :formatter=\"syncStatusFormatter\" />\n          <el-table-column\n            label=\"操作\"\n            width=\"300\"\n            fixed=\"right\"\n            class-name=\"small-padding fixed-width\"\n            :show-overflow-tooltip=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:query']\"\n              >详情\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId == null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleEdit(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId == null && !(scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:remove']\"\n              >删除\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"showHandle(scope.row)\"\n                         v-hasPermi=\"['frailty:loophole:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                v-if=\"scope.row.flowState == null && (scope.row.handleState === '2' || scope.row.handleState === '0')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"addOrUpdateFlowHandle(null, null, scope.row)\"\n                v-hasPermi=\"['frailty:loophole:edit']\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"批量处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleBatchForm\">确 定</el-button>\n        <el-button @click=\"showHandleBatchDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <el-dialog\n      v-if=\"workDialog\"\n      title=\"创建通报\"\n      :visible.sync=\"workDialog\"\n      width=\"80%\"\n      append-to-body\n    >\n      <create-work\n        v-if=\"workDialog\"\n        :work-type=\"'0'\"\n        :m-id=\"gapId\"\n        @closeWork=\"closeWork\"\n      />\n    </el-dialog>\n    <el-dialog\n      v-if=\"showAddloophole\"\n      :title=\"title\"\n      :visible.sync=\"showAddloophole\"\n      class=\"loop_dialog\"\n      width=\"60%\"\n      append-to-body\n    >\n      <new-addloophole\n        v-if=\"showAddloophole\"\n        :loophole-data=\"loopholeData\"\n        :editable=\"editable\"\n        @cancel=\"canceloophole()\"\n        @confirm=\"confirmVulnDeal()\"\n      />\n    </el-dialog>\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"colseFlow\"/>\n    <flow-template-select\n      :show.sync=\"flowTemplateSelectVisible\"\n      @change=\"flowTemplateSelectChange\"\n    />\n    <LeakScanDialog\n      :title=\"title\"\n      :edit-form=\"editForm\"\n      edit-title=\"IP漏洞扫描\"\n      :is-disabled=\"isDisabled\"\n      @getList=\"getList\"\n      :scan-strategy-visible.sync=\"scanStrategyVisible\"/>\n    <div v-if=\"listType === 2\">\n      <webvuln/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  delDeal,\n  getVulnDealList, handleVuln,\n  updateVulnDeal,\n} from \"@/api/monitor2/assetFrailty\";\nimport CreateWork from \"../../todoItem/todo/createWork\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'\nimport FlowTemplateSelect from \"@/components/FlowTemplateSelect/index.vue\";\nimport {updateAlarm} from \"@/api/threaten/threatenWarn\";\nimport CronInput from '@/components/CronInput/index.vue'\nimport LeakScanDialog from \"@/views/safe/server/components/LeakScanDialog.vue\";\nimport {handleBatchVuln} from '../../../api/monitor2/assetFrailty'\nimport {getDepts} from '../../../api/monitor2/wpresult'\nimport {addJob} from '../../../api/safe/monitor'\nimport {getHandleStateVulnStat, getHostVulnTypeList, getRickLevelVulnStat} from '../../../api/monitor2/vulnResult'\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport { listUser } from \"@/api/system/user\";\nimport { getVulnerabilityRiskHeadCount } from \"@/api/threat/threat\";\nimport Webvuln from \"@/views/frailty/webvuln/index.vue\";\n\nexport default {\n  name: \"index\",\n  components: {\n    Webvuln,\n    DeptSelect, LeakScanDialog, CronInput, CreateWork, newAddloophole, FlowBox, FlowTemplateSelect,\n    SystemList: () => import('../../../components/SystemList')\n  },\n  props: {\n    severity:{\n      type: Number,\n      default: null\n    },\n    toParams: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  dicts: [\"loophole_category\", \"synchronization_status\"],\n  data() {\n    return {\n      listType:1,\n      hostRisk:{}, // 主机风险对象\n      webRisk:{}, // web风险对象\n      vulnerabilityScanning:{}, // 漏洞扫描对象\n      totalNumberOfRisks:0, // 风险总数\n      hostRiskList: [\n        {\n          severity:4,\n          title:\"可入侵漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      webRiskList: [\n        {\n          severity:4,\n          title:\"严重漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: ''\n      },\n      handleRules: {},\n      showAll: false,\n      flowVisible: false,\n      editable: true,\n      loopholeData: null,\n      showAddloophole: false,\n      showHandleBatchDialog: false,\n      title: '',\n      DealStatusData: {},\n      loading: false,\n      // 选中数组\n      ids: [],\n      // 选中数组对象\n      rows: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      handleStateOption: [\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        }\n      ],\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      queryParams: {\n        title: '',\n        category: '',\n        severity: '',\n        hostIp: '',\n        handleState: null,\n        hostPort: '',\n        dealStatus: '',\n        domainId: '',\n        pageNum: 1,\n        pageSize: 10\n      },\n      total: 10,\n      assetFrailtyList: [],\n      deptOptions: [],\n      handleStateList: [],\n      rickLevelList: [],\n      typeList: [],\n      openDialog: false,\n      vulnId: null,\n      workDialog: false,\n      gapId: '',\n      workId: '',\n      flowTemplateSelectVisible: false,\n      currentFlowData: null,\n      scanStrategyVisible: false,\n      editForm: {},\n      form: {\n        jobGroup: 'ASSET_SCAN',\n        jobType: 1,\n        cronExpression: '* * * * * ?',\n        period: 0,\n        status: '0',\n        cronTransfer: '立即执行'\n      },\n      isDisabled: false\n    }\n  },\n  created() {\n    if (this.$route.query.hostIp) {\n      this.queryParams.hostIp = this.$route.query.hostIp\n    }\n    if (this.$route.query.severity) {\n      this.queryParams.severity = this.$route.query.severity;\n      this.$emit('severityChange','ip'+this.$route.query.severity);\n    }\n    if (this.$route.query.handleState) {\n      this.queryParams.handleState = this.$route.query.handleState\n    }\n    this.initDept();\n    this.initData();\n    this.handleQuery();\n  },\n  watch: {\n    '$route.query': {\n      handler(val) {\n        if (val) {\n          this.queryParams.domainId = val.domainId\n        }\n      },\n      deep: true,\n      immediate: true\n    },\n    severity : {\n      handler(val) {\n        if (val) {\n          this.queryParams.severity = val\n        }else {\n          this.queryParams.severity = null\n        }\n        this.handleQuery()\n      },\n      immediate: false\n    },\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.referenceId){\n          this.queryParams.referenceId = newVal.referenceId\n          this.handleQuery()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    initData() {\n      /*getVulnerabilityRiskHeadCount().then(res => {\n        if (res.data){\n          this.hostRisk = res.data.hostRisk;\n          this.webRisk = res.data.webRisk;\n          this.vulnerabilityScanning = res.data.vulnerabilityScanning;\n          this.totalNumberOfRisks = res.data.totalNumberOfRisks;\n          //遍历hostRiskList\n          this.hostRiskList.forEach(e => {\n           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n           e.num = num[0].num\n          })\n          this.webRiskList.forEach(e => {\n            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n            e.num = num[0].num\n          })\n        }\n      })*/\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n      this.handleStateList = []\n      this.rickLevelList = []\n      this.typeList = []\n      getHandleStateVulnStat().then(res => {\n        res.data.forEach(e => {\n          if (e.handle_state === 0) {\n            const obj1 = {\n              dictValue: 0,\n              dictLabel: '未处置',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 1) {\n            const obj1 = {\n              dictValue: 1,\n              dictLabel: '已处置',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 2) {\n            const obj1 = {\n              dictValue: 2,\n              dictLabel: '忽略',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 3) {\n            const obj1 = {\n              dictValue: 3,\n              dictLabel: '处置中',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n        })\n      })\n      getRickLevelVulnStat().then(res => {\n        res.data.forEach(e => {\n          /*if (e.severity === 0) {\n            const obj1 = {\n              dictValue: 0,\n              dictLabel: '未知',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }*/\n          if (e.severity === 1) {\n            const obj1 = {\n              dictValue: 1,\n              dictLabel: '低危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 2) {\n            const obj1 = {\n              dictValue: 2,\n              dictLabel: '中危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 3) {\n            const obj1 = {\n              dictValue: 3,\n              dictLabel: '高危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 4) {\n            const obj1 = {\n              dictValue: 4,\n              dictLabel: '严重',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n        })\n      })\n\n      getHostVulnTypeList().then(res => {\n        this.typeList = res.data\n      })\n    },\n    initDept() {\n      getDepts(this.handleForm).then(res => {\n        this.deptOptions = res.data\n      })\n    },\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n    syncStatusFormatter(row, column, cellValue, index){\n      const label = this.dict.type.synchronization_status.find(item => item.value === cellValue)\n      return label ? label.label : ''\n    },\n    handleAddJob() {\n      if (!this.single) {\n        if (this.rows.length > 0) {\n          let ips = this.rows[0].hostIp.split(/\\n/g);\n          // 调用目标字符串\n          this.form.invokeIp = ips.join(';')\n          this.form.jobName = this.rows[0].hostIp + '漏扫复查任务_' + new Date().getTime();\n        }\n        this.form.invokeTarget = 'HostVulnScan.scan(\\'${jobId}\\',\\'' + this.form.jobName + '|' + this.form.invokeIp + '|1|1|1' + '\\')'\n\n        const from = {...this.form }\n        this.$modal.confirm('是否确认创建【' + this.rows[0].hostIp + '】的漏扫复查任务？').then(function () {\n          return addJob(from);\n        }).then(() => {\n          this.getList();\n          this.$modal.msgSuccess(\"复核任务新增成功\");\n        }).catch(() => {\n        });\n      }\n    },\n    submitHandleForm() {\n      handleVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    showHandle(row) {\n      this.handleForm = {};\n      this.handleForm = {...row};\n      if (this.handleForm.handleState === '0') {\n        this.handleForm.handleState = null\n      }\n      this.handleForm.assetName = ''\n      this.showHandleDialog = true;\n    },\n    showHandleBatch() {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState === '0' || item.handleState === '2' || item.handleState === null);\n      if (rows.length < this.rows.length) {\n        this.$message.error('选择中有已处置或处置中事件，无法批量处置');\n        return false;\n      }\n      this.handleForm = {};\n      if (rows.length === 1) {\n        if (rows[0].handleState === '2') {\n          this.handleForm = rows[0]\n        }\n      }\n      // this.handleForm.id=row.id;\n      this.handleForm.ids = this.ids;\n      this.showHandleBatchDialog = true;\n    },\n    submitHandleBatchForm() {\n      handleBatchVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleBatchDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    handleAdd() {\n      this.showAddloophole = true;\n      this.editable = true;\n      this.loopholeData = null;\n      this.title = '新增主机漏洞事件';\n    },\n    handleEdit(row) {\n      this.showAddloophole = true;\n      this.editable = true;\n      this.loopholeData = row;\n      this.title = '修改主机漏洞事件';\n    },\n    handleScan() {\n      this.title = '添加任务';\n      this.editForm = {}\n      this.editForm.jobType = 1;\n      this.editForm.weakPw = '1';\n      this.editForm.status = '0';\n      this.editForm.cronExpression = '* * * * * ?';\n      this.editForm.period = 0;\n      this.editForm.cronTransfer = '立即执行';\n      this.scanStrategyVisible = true;\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const vulnNames = row.title;\n      this.$modal\n        .confirm(\"是否确认删除漏洞名称为【\" + vulnNames + \"】的数据项？\")\n        .then(function () {\n          return delDeal(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.initData();\n          this.$modal.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {\n        });\n    },\n    canceloophole() {\n      this.showAddloophole = false;\n    },\n    confirmVulnDeal() {\n      this.showAddloophole = false;\n      this.getList();\n      this.initData();\n    },\n    // updateDealStatus(row){\n    //   this.DealStatusData=row;\n    //   this.$modal.confirm('是否确定更新漏洞处理状态？', '提示').then(() => {\n    //     return updateVulnDeal(this.DealStatusData).then(()=>{\n    //       this.$message.success('修改成功');\n    //       // this.handleQuery();\n    //     }).catch(()=>{\n    //       this.$message.warning('调用接口超时');\n    //     });\n    //   }).catch(() => {\n    //     this.$message.info('已取消');\n    //     this.handleQuery();\n    //   });\n    // },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n      this.total = 0;\n      this.getList();\n    },\n    resetQuery() {\n      this.queryParams = {\n        title: '',\n        category: '',\n        severity: this.$props.severity,\n        hostIp: '',\n        referenceId: '',\n        handleState: null,\n        hostPort: '',\n        handleStatus: '',\n        domainId: '',\n        pageNum: 1,\n        pageSize: 10\n      };\n      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();\n      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();\n      this.getList();\n    },\n    getList() {\n      this.loading = true;\n      let tempQueryParams = {...(this.queryParams), handleType: ''};\n      getVulnDealList(tempQueryParams).then(response => {\n        this.assetFrailtyList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    selectable(row) {\n      if (row.handleState === '1' || row.handleState === '3') {\n        return false\n      } else {\n        return true\n      }\n    },\n    handleExport() {\n      this.download('/monitor2/vulndeal/export', {\n        ...this.queryParams\n      }, `IP漏洞_${new Date().getTime()}.xlsx`)\n    },\n    handleDetail(row) {\n      this.showAddloophole = true;\n      this.editable = false;\n      this.loopholeData = row;\n      this.title = '查看主机漏洞事件';\n    },\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    createWork(row) {\n      this.gapId = row.id\n      this.workDialog = true\n    },\n    closeWork() {\n      this.workDialog = false\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        // flowtemplatejson id\n        // flowId: '564350702671937349',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '0';\n      data.row.eventType = 1;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if(flowId){\n          this.getFlowEngineInfo(flowId);\n        }else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    addOrUpdateFlowHandleBatch(id, flowState) {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState === '0' || item.handleState === null);\n      if (!rows || rows.length < 1) {\n        this.$message.error('未选择未处置事件，无法批量创建通报');\n        return false;\n      }\n      let data = {\n        id: id || '',\n        // flowtemplatejson id\n        // flowId: '564350702671937349',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        rows: rows,\n        isWork: true\n      }\n      data.rows[0].workType = '0';\n      data.rows[0].eventType = 1;\n      data.originType = 'event';\n      this.currentFlowData = data;\n\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if(flowId){\n          this.getFlowEngineInfo(flowId);\n        }else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val){\n      FlowEngineInfo(val).then(res => {\n        if(res.data && res.data.flowTemplateJson){\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if(!data[0].flowId){\n            this.$message.error('该流程模板异常,请重新选择');\n          }else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n    },\n    colseFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n      this.initData();\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n    unique(arr) {\n      for (let i = 0; i < arr.length; i++) {\n        for (let j = i + 1; j < arr.length; j++) {\n          if (arr[i] === arr[j]) {\n            //如果第一个等同于第二个，splice方法删除第二个\n            arr.splice(j, 1);\n\n            j--;\n          }\n        }\n      }\n      return arr;\n    },\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n::v-deep.el-select {\n  width: 100%;\n\n  .el-select-dropdown {\n    position: absolute;\n    top: 30px !important;\n    left: 5px;\n    .el-scrollbar {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n  }\n}\n\n.loop_dialog {\n  height: 90vh;\n  overflow: hidden;\n  ::v-deep .el-dialog {\n    height: 100%;\n    .el-dialog__body {\n      height: calc(100% - 110px);\n      padding: 10px 20px 0;\n      overflow: auto;\n    }\n  }\n}\n\n.asset-tag {\n  margin-left: 5px;\n  max-width: 35%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAkfA,IAAAA,aAAA,GAAAC,OAAA;AAKA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,QAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,eAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAV,OAAA;AACA,IAAAW,QAAA,GAAAX,OAAA;AACA,IAAAY,WAAA,GAAAZ,OAAA;AACA,IAAAa,WAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,WAAA,GAAAd,OAAA;AACA,IAAAe,KAAA,GAAAf,OAAA;AACA,IAAAgB,OAAA,GAAAhB,OAAA;AACA,IAAAiB,OAAA,GAAAf,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAkB,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,eAAA;IACAC,UAAA,EAAAA,mBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,SAAA,EAAAA,eAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,kBAAA,EAAAA,cAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjC,OAAA;MAAA;IAAA;EACA;EACAkC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAJ,OAAA;IACA;IACAK,QAAA;MACAF,IAAA,EAAAG,MAAA;MACAN,OAAA,WAAAA,SAAA;IACA;EACA;EACAO,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;MAAA;MACAC,OAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,YAAA,GACA;QACAZ,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,EACA;MACAC,WAAA,GACA;QACAhB,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,GACA;QACAf,QAAA;QACAa,KAAA;QACAC,GAAA,EAAAjD,OAAA;QACAkD,GAAA;MACA,EACA;MACAE,QAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,EAAA;QACAC,UAAA;MACA;MACAC,WAAA;MACAC,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,YAAA;MACAC,eAAA;MACAC,qBAAA;MACAf,KAAA;MACAgB,cAAA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,IAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACAC,kBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,iBAAA,GACA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAE,gBAAA,GACA;QACAH,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAG,WAAA;QACA3B,KAAA;QACA4B,QAAA;QACAzC,QAAA;QACA0C,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;MACAC,UAAA;MACAC,KAAA;MACAC,MAAA;MACAC,yBAAA;MACAC,eAAA;MACAC,mBAAA;MACAC,QAAA;MACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,cAAA;QACAC,MAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAhC,MAAA;MACA,KAAAF,WAAA,CAAAE,MAAA,QAAA+B,MAAA,CAAAC,KAAA,CAAAhC,MAAA;IACA;IACA,SAAA+B,MAAA,CAAAC,KAAA,CAAA1E,QAAA;MACA,KAAAwC,WAAA,CAAAxC,QAAA,QAAAyE,MAAA,CAAAC,KAAA,CAAA1E,QAAA;MACA,KAAA2E,KAAA,+BAAAF,MAAA,CAAAC,KAAA,CAAA1E,QAAA;IACA;IACA,SAAAyE,MAAA,CAAAC,KAAA,CAAA/B,WAAA;MACA,KAAAH,WAAA,CAAAG,WAAA,QAAA8B,MAAA,CAAAC,KAAA,CAAA/B,WAAA;IACA;IACA,KAAAiC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,KAAAzC,WAAA,CAAAM,QAAA,GAAAmC,GAAA,CAAAnC,QAAA;QACA;MACA;MACAoC,IAAA;MACAC,SAAA;IACA;IACAnF,QAAA;MACAgF,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,KAAAzC,WAAA,CAAAxC,QAAA,GAAAiF,GAAA;QACA;UACA,KAAAzC,WAAA,CAAAxC,QAAA;QACA;QACA,KAAA8E,WAAA;MACA;MACAK,SAAA;IACA;IACAhF,QAAA;MACA6E,OAAA,WAAAA,QAAAI,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,WAAA;UACA,KAAA7C,WAAA,CAAA6C,WAAA,GAAAD,MAAA,CAAAC,WAAA;UACA,KAAAP,WAAA;QACA;MACA;MACAK,SAAA;IACA;EACA;EACAG,OAAA;IACAT,QAAA,WAAAA,SAAA;MAAA,IAAAU,KAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA,IAAAC,cAAA;QAAAzC,OAAA;QAAAC,QAAA;MAAA,GAAApD,IAAA,WAAA6F,GAAA;QACA,IAAAA,GAAA,CAAAzD,IAAA;UACAuD,KAAA,CAAAtE,QAAA,GAAAwE,GAAA,CAAAzD,IAAA;QACA;MACA;MACA,KAAAoB,eAAA;MACA,KAAAC,aAAA;MACA,KAAAC,QAAA;MACA,IAAAoC,kCAAA,IAAA9F,IAAA,WAAA6F,GAAA;QACAA,GAAA,CAAAnF,IAAA,CAAAqF,OAAA,WAAAC,CAAA;UACA,IAAAA,CAAA,CAAAC,YAAA;YACA,IAAAC,IAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAnC,eAAA,CAAA8C,IAAA,CAAAJ,IAAA;UACA;UACA,IAAAF,CAAA,CAAAC,YAAA;YACA,IAAAC,IAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAnC,eAAA,CAAA8C,IAAA,CAAAJ,IAAA;UACA;UACA,IAAAF,CAAA,CAAAC,YAAA;YACA,IAAAC,KAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAnC,eAAA,CAAA8C,IAAA,CAAAJ,KAAA;UACA;UACA,IAAAF,CAAA,CAAAC,YAAA;YACA,IAAAC,KAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAnC,eAAA,CAAA8C,IAAA,CAAAJ,KAAA;UACA;QACA;MACA;MACA,IAAAK,gCAAA,IAAAvG,IAAA,WAAA6F,GAAA;QACAA,GAAA,CAAAnF,IAAA,CAAAqF,OAAA,WAAAC,CAAA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA,IAAAA,CAAA,CAAA5F,QAAA;YACA,IAAA8F,IAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAlC,aAAA,CAAA6C,IAAA,CAAAJ,IAAA;UACA;UACA,IAAAF,CAAA,CAAA5F,QAAA;YACA,IAAA8F,KAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAlC,aAAA,CAAA6C,IAAA,CAAAJ,KAAA;UACA;UACA,IAAAF,CAAA,CAAA5F,QAAA;YACA,IAAA8F,KAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAlC,aAAA,CAAA6C,IAAA,CAAAJ,KAAA;UACA;UACA,IAAAF,CAAA,CAAA5F,QAAA;YACA,IAAA8F,KAAA;cACAC,SAAA;cACAC,SAAA;cACAC,KAAA,EAAAL,CAAA,CAAA7E;YACA;YACAwE,KAAA,CAAAlC,aAAA,CAAA6C,IAAA,CAAAJ,KAAA;UACA;QACA;MACA;MAEA,IAAAM,+BAAA,IAAAxG,IAAA,WAAA6F,GAAA;QACAF,KAAA,CAAAjC,QAAA,GAAAmC,GAAA,CAAAnF,IAAA;MACA;IACA;IACAsE,QAAA,WAAAA,SAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,kBAAA,OAAAnF,UAAA,EAAAvB,IAAA,WAAA6F,GAAA;QACAY,MAAA,CAAAlD,WAAA,GAAAsC,GAAA,CAAAnF,IAAA;MACA;IACA;IACAiG,oBAAA,WAAAA,qBAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,KAAA;MACA,IAAA5H,IAAA;MACA,IAAA6H,KAAA,QAAAzE,kBAAA,CAAA0E,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzE,KAAA,IAAAqE,SAAA;MAAA;MACA,IAAAE,KAAA;QACA7H,IAAA,GAAA6H,KAAA,CAAAxE,KAAA;MACA;MACA,OAAArD,IAAA;IACA;IACAgI,iBAAA,WAAAA,kBAAAP,GAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,KAAA;MACA,IAAA5H,IAAA;MACA,IAAA2H,SAAA;QACA,KAAAzF,QAAA,CAAA0E,OAAA,WAAAC,CAAA;UACA,IAAAA,CAAA,CAAAoB,MAAA,IAAAN,SAAA;YACA3H,IAAA,GAAA6G,CAAA,CAAAqB,QAAA;UACA;QACA;QACA,OAAAlI,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IACAmI,mBAAA,WAAAA,oBAAAV,GAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,KAAA;MACA,IAAAvE,KAAA,QAAA+E,IAAA,CAAAlH,IAAA,CAAAmH,sBAAA,CAAAP,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzE,KAAA,KAAAqE,SAAA;MAAA;MACA,OAAAtE,KAAA,GAAAA,KAAA,CAAAA,KAAA;IACA;IACAiF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,UAAArF,MAAA;QACA,SAAAD,IAAA,CAAAuF,MAAA;UACA,IAAAC,GAAA,QAAAxF,IAAA,IAAAU,MAAA,CAAA+E,KAAA;UACA;UACA,KAAAzD,IAAA,CAAA0D,QAAA,GAAAF,GAAA,CAAAG,IAAA;UACA,KAAA3D,IAAA,CAAA4D,OAAA,QAAA5F,IAAA,IAAAU,MAAA,mBAAAmF,IAAA,GAAAC,OAAA;QACA;QACA,KAAA9D,IAAA,CAAA+D,YAAA,8CAAA/D,IAAA,CAAA4D,OAAA,cAAA5D,IAAA,CAAA0D,QAAA;QAEA,IAAAM,IAAA,OAAAC,cAAA,CAAAnI,OAAA,WAAAkE,IAAA;QACA,KAAAkE,MAAA,CAAAC,OAAA,kBAAAnG,IAAA,IAAAU,MAAA,gBAAA9C,IAAA;UACA,WAAAwI,eAAA,EAAAJ,IAAA;QACA,GAAApI,IAAA;UACA0H,MAAA,CAAAe,OAAA;UACAf,MAAA,CAAAY,MAAA,CAAAI,UAAA;QACA,GAAAC,KAAA,cACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,wBAAA,OAAAvH,UAAA,EAAAvB,IAAA,WAAA6F,GAAA;QACAgD,MAAA,CAAAE,QAAA,CAAAC,OAAA;QACAH,MAAA,CAAAtH,UAAA;QACAsH,MAAA,CAAAvH,gBAAA;QACAuH,MAAA,CAAAJ,OAAA;QACAI,MAAA,CAAA5D,QAAA;MACA;IACA;IACAgE,UAAA,WAAAA,WAAArC,GAAA;MACA,KAAArF,UAAA;MACA,KAAAA,UAAA,OAAA8G,cAAA,CAAAnI,OAAA,MAAA0G,GAAA;MACA,SAAArF,UAAA,CAAAwB,WAAA;QACA,KAAAxB,UAAA,CAAAwB,WAAA;MACA;MACA,KAAAxB,UAAA,CAAA2H,SAAA;MACA,KAAA5H,gBAAA;IACA;IACA6H,eAAA,WAAAA,gBAAA;MACA,IAAA/G,IAAA,OAAAgH,mBAAA,CAAAlJ,OAAA,OAAAkC,IAAA;MACAA,IAAA,GAAAA,IAAA,CAAAiH,MAAA,WAAAnC,IAAA;QAAA,OAAAA,IAAA,CAAAnE,WAAA,YAAAmE,IAAA,CAAAnE,WAAA,YAAAmE,IAAA,CAAAnE,WAAA;MAAA;MACA,IAAAX,IAAA,CAAAuF,MAAA,QAAAvF,IAAA,CAAAuF,MAAA;QACA,KAAAoB,QAAA,CAAAO,KAAA;QACA;MACA;MACA,KAAA/H,UAAA;MACA,IAAAa,IAAA,CAAAuF,MAAA;QACA,IAAAvF,IAAA,IAAAW,WAAA;UACA,KAAAxB,UAAA,GAAAa,IAAA;QACA;MACA;MACA;MACA,KAAAb,UAAA,CAAAY,GAAA,QAAAA,GAAA;MACA,KAAAH,qBAAA;IACA;IACAuH,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,8BAAA,OAAAlI,UAAA,EAAAvB,IAAA,WAAA6F,GAAA;QACA2D,MAAA,CAAAT,QAAA,CAAAC,OAAA;QACAQ,MAAA,CAAAjI,UAAA;QACAiI,MAAA,CAAAxH,qBAAA;QACAwH,MAAA,CAAAf,OAAA;QACAe,MAAA,CAAAvE,QAAA;MACA;IACA;IACAyE,SAAA,WAAAA,UAAA;MACA,KAAA3H,eAAA;MACA,KAAAF,QAAA;MACA,KAAAC,YAAA;MACA,KAAAb,KAAA;IACA;IACA0I,UAAA,WAAAA,WAAA/C,GAAA;MACA,KAAA7E,eAAA;MACA,KAAAF,QAAA;MACA,KAAAC,YAAA,GAAA8E,GAAA;MACA,KAAA3F,KAAA;IACA;IACA2I,UAAA,WAAAA,WAAA;MACA,KAAA3I,KAAA;MACA,KAAAkD,QAAA;MACA,KAAAA,QAAA,CAAAG,OAAA;MACA,KAAAH,QAAA,CAAA0F,MAAA;MACA,KAAA1F,QAAA,CAAAM,MAAA;MACA,KAAAN,QAAA,CAAAI,cAAA;MACA,KAAAJ,QAAA,CAAAK,MAAA;MACA,KAAAL,QAAA,CAAAO,YAAA;MACA,KAAAR,mBAAA;IACA;IACA4F,YAAA,WAAAA,aAAAlD,GAAA;MAAA,IAAAmD,MAAA;MACA,IAAA5H,GAAA,GAAAyE,GAAA,CAAApF,EAAA;MACA,IAAAwI,SAAA,GAAApD,GAAA,CAAA3F,KAAA;MACA,KAAAqH,MAAA,CACAC,OAAA,kBAAAyB,SAAA,aACAhK,IAAA;QACA,WAAAiK,qBAAA,EAAA9H,GAAA;MACA,GACAnC,IAAA;QACA+J,MAAA,CAAAtB,OAAA;QACAsB,MAAA,CAAA9E,QAAA;QACA8E,MAAA,CAAAzB,MAAA,CAAAI,UAAA;MACA,GACAC,KAAA,cACA;IACA;IACAuB,aAAA,WAAAA,cAAA;MACA,KAAAnI,eAAA;IACA;IACAoI,eAAA,WAAAA,gBAAA;MACA,KAAApI,eAAA;MACA,KAAA0G,OAAA;MACA,KAAAxD,QAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAO,OAAA;MACA,KAAAP,WAAA,CAAAQ,QAAA;MACA,KAAAC,KAAA;MACA,KAAAoF,OAAA;IACA;IACA2B,UAAA,WAAAA,WAAA;MACA,KAAAxH,WAAA;QACA3B,KAAA;QACA4B,QAAA;QACAzC,QAAA,OAAAiK,MAAA,CAAAjK,QAAA;QACA0C,MAAA;QACA2C,WAAA;QACA1C,WAAA;QACAC,QAAA;QACAsH,YAAA;QACApH,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAmH,KAAA,CAAAC,WAAA,SAAAD,KAAA,CAAAC,WAAA,CAAAC,cAAA;MACA,KAAAF,KAAA,CAAAG,WAAA,SAAAH,KAAA,CAAAG,WAAA,CAAAD,cAAA;MACA,KAAAhC,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAkC,MAAA;MACA,KAAAzI,OAAA;MACA,IAAA0I,eAAA,OAAAvC,cAAA,CAAAnI,OAAA,MAAAmI,cAAA,CAAAnI,OAAA,WAAA0C,WAAA;QAAAiI,UAAA;MAAA;MACA,IAAAC,6BAAA,EAAAF,eAAA,EAAA5K,IAAA,WAAA+K,QAAA;QACAJ,MAAA,CAAArH,gBAAA,GAAAyH,QAAA,CAAA3I,IAAA;QACAuI,MAAA,CAAAtH,KAAA,GAAA0H,QAAA,CAAA1H,KAAA;QACAsH,MAAA,CAAAzI,OAAA;MACA;IACA;IACA8I,UAAA,WAAAA,WAAApE,GAAA;MACA,IAAAA,GAAA,CAAA7D,WAAA,YAAA6D,GAAA,CAAA7D,WAAA;QACA;MACA;QACA;MACA;IACA;IACAkI,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,kCAAA7C,cAAA,CAAAnI,OAAA,MACA,KAAA0C,WAAA,qBAAAuI,MAAA,CACA,IAAAlD,IAAA,GAAAC,OAAA;IACA;IACAkD,YAAA,WAAAA,aAAAxE,GAAA;MACA,KAAA7E,eAAA;MACA,KAAAF,QAAA;MACA,KAAAC,YAAA,GAAA8E,GAAA;MACA,KAAA3F,KAAA;IACA;IACAoK,kBAAA,WAAAA,mBAAAzE,GAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,KAAA;MACA,IAAA5H,IAAA;MACA,IAAA6H,KAAA,QAAArE,gBAAA,CAAAsE,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzE,KAAA,IAAAqE,SAAA;MAAA;MACA,IAAAE,KAAA;QACA7H,IAAA,GAAA6H,KAAA,CAAAxE,KAAA;MACA;MACA,OAAArD,IAAA;IACA;IACAmM,UAAA,WAAAA,WAAA1E,GAAA;MACA,KAAA9C,KAAA,GAAA8C,GAAA,CAAApF,EAAA;MACA,KAAAqC,UAAA;IACA;IACA0H,SAAA,WAAAA,UAAA;MACA,KAAA1H,UAAA;IACA;IACA2H,wBAAA,WAAAA,yBAAAnG,GAAA;MAAA,IAAAoG,MAAA;MACA,KAAAzH,yBAAA;MACA,KAAApC,WAAA;MACA,KAAAqC,eAAA,CAAAyH,MAAA,GAAArG,GAAA;MACA,KAAAsG,SAAA;QACAF,MAAA,CAAAlB,KAAA,CAAA5K,OAAA,CAAAiM,IAAA,CAAAH,MAAA,CAAAxH,eAAA;MACA;IACA;IACA4H,qBAAA,WAAAA,sBAAArK,EAAA,EAAAsK,SAAA,EAAAlF,GAAA;MAAA,IAAAmF,MAAA;MACA,IAAArL,IAAA;QACAc,EAAA,EAAAA,EAAA;QACA;QACA;QACAwK,QAAA;QACAC,MAAA,EAAAH,SAAA;QACArH,MAAA,EAAAqH,SAAA;QACAlF,GAAA,EAAAA,GAAA;QACAsF,MAAA;MACA;MACAxL,IAAA,CAAAkG,GAAA,CAAAuF,QAAA;MACAzL,IAAA,CAAAkG,GAAA,CAAAwF,SAAA;MACA1L,IAAA,CAAA2L,UAAA;MACA,KAAApI,eAAA,GAAAvD,IAAA;MACA,KAAAwB,OAAA;MACA,KAAAoK,YAAA,2BAAAtM,IAAA,WAAA6F,GAAA;QACA,IAAA6F,MAAA,GAAA7F,GAAA,CAAA0G,GAAA;QACA,IAAAb,MAAA;UACAK,MAAA,CAAAS,iBAAA,CAAAd,MAAA;QACA;UACAK,MAAA,CAAA/H,yBAAA;QACA;MACA,GAAAyI,OAAA;QACAV,MAAA,CAAA7J,OAAA;MACA;IACA;IACAwK,0BAAA,WAAAA,2BAAAlL,EAAA,EAAAsK,SAAA;MAAA,IAAAa,OAAA;MACA,IAAAvK,IAAA,OAAAgH,mBAAA,CAAAlJ,OAAA,OAAAkC,IAAA;MACAA,IAAA,GAAAA,IAAA,CAAAiH,MAAA,WAAAnC,IAAA;QAAA,OAAAA,IAAA,CAAAnE,WAAA,YAAAmE,IAAA,CAAAnE,WAAA;MAAA;MACA,KAAAX,IAAA,IAAAA,IAAA,CAAAuF,MAAA;QACA,KAAAoB,QAAA,CAAAO,KAAA;QACA;MACA;MACA,IAAA5I,IAAA;QACAc,EAAA,EAAAA,EAAA;QACA;QACA;QACAwK,QAAA;QACAC,MAAA,EAAAH,SAAA;QACArH,MAAA,EAAAqH,SAAA;QACA1J,IAAA,EAAAA,IAAA;QACA8J,MAAA;MACA;MACAxL,IAAA,CAAA0B,IAAA,IAAA+J,QAAA;MACAzL,IAAA,CAAA0B,IAAA,IAAAgK,SAAA;MACA1L,IAAA,CAAA2L,UAAA;MACA,KAAApI,eAAA,GAAAvD,IAAA;MAEA,KAAAwB,OAAA;MACA,KAAAoK,YAAA,2BAAAtM,IAAA,WAAA6F,GAAA;QACA,IAAA6F,MAAA,GAAA7F,GAAA,CAAA0G,GAAA;QACA,IAAAb,MAAA;UACAiB,OAAA,CAAAH,iBAAA,CAAAd,MAAA;QACA;UACAiB,OAAA,CAAA3I,yBAAA;QACA;MACA,GAAAyI,OAAA;QACAE,OAAA,CAAAzK,OAAA;MACA;IACA;IACAsK,iBAAA,WAAAA,kBAAAnH,GAAA;MAAA,IAAAuH,OAAA;MACA,IAAAC,0BAAA,EAAAxH,GAAA,EAAArF,IAAA,WAAA6F,GAAA;QACA,IAAAA,GAAA,CAAAnF,IAAA,IAAAmF,GAAA,CAAAnF,IAAA,CAAAoM,gBAAA;UACA,IAAApM,IAAA,GAAAqM,IAAA,CAAAC,KAAA,CAAAnH,GAAA,CAAAnF,IAAA,CAAAoM,gBAAA;UACA,KAAApM,IAAA,IAAAgL,MAAA;YACAkB,OAAA,CAAA7D,QAAA,CAAAO,KAAA;UACA;YACAsD,OAAA,CAAA3I,eAAA,CAAAyH,MAAA,GAAAhL,IAAA,IAAAgL,MAAA;YACAkB,OAAA,CAAAhL,WAAA;YACAgL,OAAA,CAAAjB,SAAA;cACAiB,OAAA,CAAArC,KAAA,CAAA5K,OAAA,CAAAiM,IAAA,CAAAgB,OAAA,CAAA3I,eAAA;YACA;UACA;QACA;MACA,GAAAwI,OAAA;QACAG,OAAA,CAAA1K,OAAA;MACA;IACA;IACA;IACA+K,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/K,GAAA,GAAA+K,SAAA,CAAAC,GAAA,WAAAjG,IAAA;QAAA,OAAAA,IAAA,CAAA1F,EAAA;MAAA;MACA,KAAAa,MAAA,GAAA6K,SAAA,CAAAvF,MAAA;MACA,KAAArF,QAAA,IAAA4K,SAAA,CAAAvF,MAAA;MACA,KAAAvF,IAAA,GAAA8K,SAAA;IACA;IACAE,SAAA,WAAAA,UAAAC,UAAA;MACA,KAAAzL,WAAA;MACA,IAAAyL,UAAA,OAAA5E,OAAA;MACA,KAAAxD,QAAA;IACA;IACAqI,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAA5F,MAAA;QACA;MACA;MACA,IAAA6F,MAAA,GAAAD,eAAA,IAAArE,SAAA;MACA,IAAAqE,eAAA,CAAA5F,MAAA;QACA6F,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IACAC,MAAA,WAAAA,OAAAC,GAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAD,GAAA,CAAA/F,MAAA,EAAAgG,CAAA;QACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAAF,GAAA,CAAA/F,MAAA,EAAAiG,CAAA;UACA,IAAAF,GAAA,CAAAC,CAAA,MAAAD,GAAA,CAAAE,CAAA;YACA;YACAF,GAAA,CAAAG,MAAA,CAAAD,CAAA;YAEAA,CAAA;UACA;QACA;MACA;MACA,OAAAF,GAAA;IACA;EACA;AACA", "ignoreList": []}]}