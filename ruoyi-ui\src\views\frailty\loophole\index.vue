<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="100px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="漏洞名称" prop="title">
                <el-input
                  v-model="queryParams.title"
                  placeholder="请输入内容"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞类型" prop="category">
                <!--<el-input-->
                <!--v-model="queryParams.category"-->
                <!--placeholder="请输入内容"-->
                <!--clearable-->
                <!--@keyup.enter.native="handleQuery"-->
                <!--/>-->
                <el-select
                  v-model="queryParams.category"
                  placeholder="请选择漏洞类型"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="dict in typeList"
                    :key="dict.category"
                    :label="dict.category"
                    :value="dict.category"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="影响资产主IP" prop="hostIp">
                <el-input
                  v-model="queryParams.hostIp"
                  placeholder="请输入内容"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询
                </el-button
                >
                <el-button class="btn2" size="small" @click="resetQuery"
                >重置
                </el-button
                >
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="所属部门" prop="deptId">
                <!--                <el-select
                                  v-model="queryParams.deptId"
                                  placeholder="请选择所属部门"
                                  filterable
                                  clearable
                                >
                                  <el-option
                                    v-for="item in deptOptions"
                                    :key="item.deptId"
                                    :label="item.deptName"
                                    :value="item.deptId"
                                  ></el-option>
                                </el-select>-->
                <dept-select v-model="queryParams.deptId"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处置人" prop="title">
                <el-input
                  v-model="queryParams.disposer"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="同步状态" prop="title">
                <el-select v-model="queryParams.synchronizationStatus" placeholder="请选择同步状态" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.synchronization_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="showAll" :gutter="10">
<!--            <el-col :span="24" v-if="rickLevelList.length">
              <el-form-item label="漏洞等级">
                <SystemList
                  ref="systemList2"
                  :systemTypes="rickLevelList"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.severity"
                />
              </el-form-item>
            </el-col>-->
            <el-col :span="24" v-if="handleStateList.length">
              <el-form-item label="处置状态">
                <SystemList
                  ref="systemList1"
                  :systemTypes="handleStateList"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.handleState"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">主机漏洞列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAdd"
                  v-hasPermi="['frailty:loophole:add']"
                >新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="multiple"
                  @click="addOrUpdateFlowHandleBatch(null,null)"
                  v-hasPermi="['frailty:loophole:edit']"
                >创建通报
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="multiple"
                  @click="showHandleBatch"
                  v-hasPermi="['frailty:loophole:edit']"
                >批量处置
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="single"
                  @click="handleAddJob"
                  v-hasPermi="['frailty:loophole:check']"
                >复查
                </el-button>
              </el-col>
              <!--<el-col :span="1.5">-->
              <!--<el-button-->
              <!--class="btn1"-->
              <!--size="small"-->
              <!--@click="handleScan"-->
              <!--&gt;IP漏洞扫描-->
              <!--</el-button>-->
              <!--</el-col>-->
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table height="100%" v-loading="loading" :data="assetFrailtyList" ref="multipleTable"
                  @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column
            label="漏洞名称"
            prop="title"
            min-width="300"
            show-overflow-tooltip
          />
          <el-table-column
            label="漏洞类型"
            prop="category"
            width="150"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.loophole_category"
                :value="scope.row.category"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="漏洞等级"
            prop="severity"
            width="100"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.severity == 0" type="info">未知</el-tag>
              <el-tag v-if="scope.row.severity == 1" type="success"
              >低危
              </el-tag
              >
              <el-tag v-if="scope.row.severity == 2" type="primary"
              >中危
              </el-tag
              >
              <el-tag v-if="scope.row.severity == 3" type="warning"
              >高危
              </el-tag
              >
              <el-tooltip v-if="scope.row.severity === 4" placement="top-end" content="可入侵漏洞" effect="light">
                <el-tag type="danger">严重</el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="影响资产主IP" prop="hostIp" width="220" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.hostIp }}</span>
              <el-tooltip placement="bottom-end" effect="light"
                          v-if="scope.row.businessApplications && scope.row.businessApplications.length > 0">
                <div slot="content">
                  <div v-for="(item,tagIndex) in scope.row.businessApplications" :key="item.assetId"
                       class="overflow-tag" v-if="tagIndex <= 9">
                    <el-tag type="primary"><span>{{ item.assetName }}</span></el-tag>
                  </div>
                  <div v-if="scope.row.businessApplications.length > 10">
                    <el-tag type="primary"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag">
                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="端口"
            prop="hostPort"
            width="120"
          />
          <el-table-column
            label="所属部门"
            prop="deptName"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.deptName ? unique(scope.row.deptName.split(',')).join(',') : '-' }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column-->
          <!--label="协议"-->
          <!--prop="protocol"-->
          <!--width="100"-->
          <!--/>-->
          <el-table-column label="处置人" prop="disposer" width="120" :formatter="disposerFormatter"/>
          <el-table-column label="处置状态" prop="handleState" width="120" :formatter="handleStateFormatter"/>
          <!--<el-table-column-->
          <!--label="通报状态"-->

          <!--prop="flowState"-->
          <!--width="120"-->
          <!--:formatter="flowStateFormatter"-->
          <!--/>-->

          <el-table-column
            label="数据来源"
            prop="dataSource"
            width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.dataSource == '1'">探测</span>
              <span v-else-if="scope.row.dataSource == '2'">手动</span>
            </template>
          </el-table-column>
          <el-table-column
            label="发现次数"
            prop="scanNum"
            width="100"
          />
          <!--        <el-table-column label="发现漏洞次数"  width="120" prop="scanNum"/>-->
          <el-table-column
            label="最近漏洞时间"
            prop="updateTime"
            width="160"
          >
            <template slot-scope="scope">
              {{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <el-table-column label="同步状态" prop="synchronizationStatus" width="120" :formatter="syncStatusFormatter" />
          <el-table-column
            label="操作"
            width="300"
            fixed="right"
            class-name="small-padding fixed-width"
            :show-overflow-tooltip="false"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
                v-hasPermi="['frailty:loophole:query']"
              >详情
              </el-button>
              <el-button
                v-if="scope.row.workId == null && !(scope.row.handleState === '1' || scope.row.handleState === '3')"
                size="mini"
                type="text"
                @click="handleEdit(scope.row)"
                v-hasPermi="['frailty:loophole:edit']"
              >编辑
              </el-button>
              <el-button
                v-if="scope.row.workId == null && !(scope.row.handleState === '3')"
                size="mini"
                type="text"
                class="table-delBtn"
                @click="handleDelete(scope.row)"
                v-hasPermi="['frailty:loophole:remove']"
              >删除
              </el-button>
              <el-button v-if="scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')"
                         size="mini"
                         type="text"
                         @click="showHandle(scope.row)"
                         v-hasPermi="['frailty:loophole:edit']"
              >处置
              </el-button>
              <el-button
                v-if="scope.row.flowState == null && (scope.row.handleState === '2' || scope.row.handleState === '0')"
                size="mini"
                type="text"
                @click="addOrUpdateFlowHandle(null, null, scope.row)"
                v-hasPermi="['frailty:loophole:edit']"
              >创建通报
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 处置威胁情报对话框! -->
    <el-dialog
      title="快速处置"
      :visible.sync="showHandleDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit
                    placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleForm">确 定</el-button>
        <el-button @click="showHandleDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 处置威胁情报对话框! -->
    <el-dialog
      title="批量处置"
      :visible.sync="showHandleBatchDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit
                    placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleBatchForm">确 定</el-button>
        <el-button @click="showHandleBatchDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="workDialog"
      title="创建通报"
      :visible.sync="workDialog"
      width="80%"
      append-to-body
    >
      <create-work
        v-if="workDialog"
        :work-type="'0'"
        :m-id="gapId"
        @closeWork="closeWork"
      />
    </el-dialog>
    <el-dialog
      v-if="showAddloophole"
      :title="title"
      :visible.sync="showAddloophole"
      class="loop_dialog"
      width="60%"
      append-to-body
    >
      <new-addloophole
        v-if="showAddloophole"
        :loophole-data="loopholeData"
        :editable="editable"
        @cancel="canceloophole()"
        @confirm="confirmVulnDeal()"
      />
    </el-dialog>
    <FlowBox v-if="flowVisible" ref="FlowBox" @close="colseFlow"/>
    <flow-template-select
      :show.sync="flowTemplateSelectVisible"
      @change="flowTemplateSelectChange"
    />
    <LeakScanDialog
      :title="title"
      :edit-form="editForm"
      edit-title="IP漏洞扫描"
      :is-disabled="isDisabled"
      @getList="getList"
      :scan-strategy-visible.sync="scanStrategyVisible"/>
    <div v-if="listType === 2">
      <webvuln/>
    </div>
  </div>
</template>

<script>
import {
  delDeal,
  getVulnDealList, handleVuln,
  updateVulnDeal,
} from "@/api/monitor2/assetFrailty";
import CreateWork from "../../todoItem/todo/createWork";
import newAddloophole from "@/views/frailty/loophole/newAddloophole.vue";
import FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'
import FlowTemplateSelect from "@/components/FlowTemplateSelect/index.vue";
import {updateAlarm} from "@/api/threaten/threatenWarn";
import CronInput from '@/components/CronInput/index.vue'
import LeakScanDialog from "@/views/safe/server/components/LeakScanDialog.vue";
import {handleBatchVuln} from '../../../api/monitor2/assetFrailty'
import {getDepts} from '../../../api/monitor2/wpresult'
import {addJob} from '../../../api/safe/monitor'
import {getHandleStateVulnStat, getHostVulnTypeList, getRickLevelVulnStat} from '../../../api/monitor2/vulnResult'
import DeptSelect from '@/views/components/select/deptSelect.vue'
import {FlowEngineInfo} from "@/api/lowCode/FlowEngine";
import { listUser } from "@/api/system/user";
import { getVulnerabilityRiskHeadCount } from "@/api/threat/threat";
import Webvuln from "@/views/frailty/webvuln/index.vue";

export default {
  name: "index",
  components: {
    Webvuln,
    DeptSelect, LeakScanDialog, CronInput, CreateWork, newAddloophole, FlowBox, FlowTemplateSelect,
    SystemList: () => import('../../../components/SystemList')
  },
  props: {
    severity:{
      type: Number,
      default: null
    },
    toParams: {
      type: Object,
      default: () => {}
    }
  },
  dicts: ["loophole_category", "synchronization_status"],
  data() {
    return {
      listType:1,
      hostRisk:{}, // 主机风险对象
      webRisk:{}, // web风险对象
      vulnerabilityScanning:{}, // 漏洞扫描对象
      totalNumberOfRisks:0, // 风险总数
      hostRiskList: [
        {
          severity:4,
          title:"可入侵漏洞",
          img:require('@/assets/images/keruqin.png'),
          num:0
        },
        {
          severity:3,
          title:"高危漏洞",
          img: require('@/assets/images/gaowei.png'),
          num:0
        },
        {
          severity:2,
          title:"中危漏洞",
          img: require('@/assets/images/zhongwei.png'),
          num:0
        },
        {
          severity:1,
          title:"低危漏洞",
          img: require('@/assets/images/diwei.png'),
          num:0
        },
      ],
      webRiskList: [
        {
          severity:4,
          title:"严重漏洞",
          img:require('@/assets/images/keruqin.png'),
          num:0
        },
        {
          severity:3,
          title:"高危漏洞",
          img: require('@/assets/images/gaowei.png'),
          num:0
        },
        {
          severity:2,
          title:"中危漏洞",
          img: require('@/assets/images/zhongwei.png'),
          num:0
        },
        {
          severity:1,
          title:"低危漏洞",
          img: require('@/assets/images/diwei.png'),
          num:0
        },
      ],
      userList: [],
      showHandleDialog: false,
      handleForm: {
        id: '',
        handleDesc: ''
      },
      handleRules: {},
      showAll: false,
      flowVisible: false,
      editable: true,
      loopholeData: null,
      showAddloophole: false,
      showHandleBatchDialog: false,
      title: '',
      DealStatusData: {},
      loading: false,
      // 选中数组
      ids: [],
      // 选中数组对象
      rows: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      handleStateOptions: [
        {
          label: '未处置',
          value: '0'
        },
        {
          label: '已处置',
          value: '1'
        },
        {
          label: '忽略',
          value: '2'
        },
        {
          label: '处置中',
          value: '3'
        }
      ],
      handleStateOption: [
        {
          label: '已处置',
          value: '1'
        },
        {
          label: '忽略',
          value: '2'
        }
      ],
      flowStateOptions: [
        {
          label: '待审核',
          value: 0
        },
        {
          label: '待处置',
          value: 1
        },
        {
          label: '待审核',
          value: 2
        },
        {
          label: '待验证',
          value: 3
        },
        {
          label: '已完成',
          value: 4
        },
        {
          label: '待提交',
          value: -1
        },
        {
          label: '未分配',
          value: 99
        }
      ],
      queryParams: {
        title: '',
        category: '',
        severity: '',
        hostIp: '',
        handleState: null,
        hostPort: '',
        dealStatus: '',
        domainId: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 10,
      assetFrailtyList: [],
      deptOptions: [],
      handleStateList: [],
      rickLevelList: [],
      typeList: [],
      openDialog: false,
      vulnId: null,
      workDialog: false,
      gapId: '',
      workId: '',
      flowTemplateSelectVisible: false,
      currentFlowData: null,
      scanStrategyVisible: false,
      editForm: {},
      form: {
        jobGroup: 'ASSET_SCAN',
        jobType: 1,
        cronExpression: '* * * * * ?',
        period: 0,
        status: '0',
        cronTransfer: '立即执行'
      },
      isDisabled: false
    }
  },
  created() {
    if (this.$route.query.hostIp) {
      this.queryParams.hostIp = this.$route.query.hostIp
    }
    if (this.$route.query.severity) {
      this.queryParams.severity = this.$route.query.severity;
      this.$emit('severityChange','ip'+this.$route.query.severity);
    }
    if (this.$route.query.handleState) {
      this.queryParams.handleState = this.$route.query.handleState
    }
    this.initDept();
    this.initData();
    this.handleQuery();
  },
  watch: {
    '$route.query': {
      handler(val) {
        if (val) {
          this.queryParams.domainId = val.domainId
        }
      },
      deep: true,
      immediate: true
    },
    severity : {
      handler(val) {
        if (val) {
          this.queryParams.severity = val
        }else {
          this.queryParams.severity = null
        }
        this.handleQuery()
      },
      immediate: false
    },
    toParams: {
      handler(newVal) {
        if(newVal && newVal.referenceId){
          this.queryParams.referenceId = newVal.referenceId
          this.handleQuery()
        }
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      /*getVulnerabilityRiskHeadCount().then(res => {
        if (res.data){
          this.hostRisk = res.data.hostRisk;
          this.webRisk = res.data.webRisk;
          this.vulnerabilityScanning = res.data.vulnerabilityScanning;
          this.totalNumberOfRisks = res.data.totalNumberOfRisks;
          //遍历hostRiskList
          this.hostRiskList.forEach(e => {
           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});
           e.num = num[0].num
          })
          this.webRiskList.forEach(e => {
            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});
            e.num = num[0].num
          })
        }
      })*/
      listUser({pageNum:1,pageSize:1000}).then(res=>{
        if (res.rows){
          this.userList = res.rows
        }
      })
      this.handleStateList = []
      this.rickLevelList = []
      this.typeList = []
      getHandleStateVulnStat().then(res => {
        res.data.forEach(e => {
          if (e.handle_state === 0) {
            const obj1 = {
              dictValue: 0,
              dictLabel: '未处置',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if (e.handle_state === 1) {
            const obj1 = {
              dictValue: 1,
              dictLabel: '已处置',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if (e.handle_state === 2) {
            const obj1 = {
              dictValue: 2,
              dictLabel: '忽略',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if (e.handle_state === 3) {
            const obj1 = {
              dictValue: 3,
              dictLabel: '处置中',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
        })
      })
      getRickLevelVulnStat().then(res => {
        res.data.forEach(e => {
          /*if (e.severity === 0) {
            const obj1 = {
              dictValue: 0,
              dictLabel: '未知',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }*/
          if (e.severity === 1) {
            const obj1 = {
              dictValue: 1,
              dictLabel: '低危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if (e.severity === 2) {
            const obj1 = {
              dictValue: 2,
              dictLabel: '中危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if (e.severity === 3) {
            const obj1 = {
              dictValue: 3,
              dictLabel: '高危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if (e.severity === 4) {
            const obj1 = {
              dictValue: 4,
              dictLabel: '严重',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
        })
      })

      getHostVulnTypeList().then(res => {
        this.typeList = res.data
      })
    },
    initDept() {
      getDepts(this.handleForm).then(res => {
        this.deptOptions = res.data
      })
    },
    handleStateFormatter(row, column, cellValue, index) {
      let name = '未处置';
      let match = this.handleStateOptions.find(item => item.value == cellValue);
      if (match) {
        name = match.label;
      }
      return name;
    },
    disposerFormatter(row, column, cellValue, index){
      let name = '';
      if (cellValue){
        this.userList.forEach(e => {
          if (e.userId == cellValue){
            name = e.nickName
          }
        })
        return name;
      }
      return name;
    },
    syncStatusFormatter(row, column, cellValue, index){
      const label = this.dict.type.synchronization_status.find(item => item.value === cellValue)
      return label ? label.label : ''
    },
    handleAddJob() {
      if (!this.single) {
        if (this.rows.length > 0) {
          let ips = this.rows[0].hostIp.split(/\n/g);
          // 调用目标字符串
          this.form.invokeIp = ips.join(';')
          this.form.jobName = this.rows[0].hostIp + '漏扫复查任务_' + new Date().getTime();
        }
        this.form.invokeTarget = 'HostVulnScan.scan(\'${jobId}\',\'' + this.form.jobName + '|' + this.form.invokeIp + '|1|1|1' + '\')'

        const from = {...this.form }
        this.$modal.confirm('是否确认创建【' + this.rows[0].hostIp + '】的漏扫复查任务？').then(function () {
          return addJob(from);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("复核任务新增成功");
        }).catch(() => {
        });
      }
    },
    submitHandleForm() {
      handleVuln(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleDialog = false;
        this.getList();
        this.initData();
      })
    },
    showHandle(row) {
      this.handleForm = {};
      this.handleForm = {...row};
      if (this.handleForm.handleState === '0') {
        this.handleForm.handleState = null
      }
      this.handleForm.assetName = ''
      this.showHandleDialog = true;
    },
    showHandleBatch() {
      let rows = [...this.rows];
      rows = rows.filter(item => item.handleState === '0' || item.handleState === '2' || item.handleState === null);
      if (rows.length < this.rows.length) {
        this.$message.error('选择中有已处置或处置中事件，无法批量处置');
        return false;
      }
      this.handleForm = {};
      if (rows.length === 1) {
        if (rows[0].handleState === '2') {
          this.handleForm = rows[0]
        }
      }
      // this.handleForm.id=row.id;
      this.handleForm.ids = this.ids;
      this.showHandleBatchDialog = true;
    },
    submitHandleBatchForm() {
      handleBatchVuln(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleBatchDialog = false;
        this.getList();
        this.initData();
      })
    },
    handleAdd() {
      this.showAddloophole = true;
      this.editable = true;
      this.loopholeData = null;
      this.title = '新增主机漏洞事件';
    },
    handleEdit(row) {
      this.showAddloophole = true;
      this.editable = true;
      this.loopholeData = row;
      this.title = '修改主机漏洞事件';
    },
    handleScan() {
      this.title = '添加任务';
      this.editForm = {}
      this.editForm.jobType = 1;
      this.editForm.weakPw = '1';
      this.editForm.status = '0';
      this.editForm.cronExpression = '* * * * * ?';
      this.editForm.period = 0;
      this.editForm.cronTransfer = '立即执行';
      this.scanStrategyVisible = true;
    },
    handleDelete(row) {
      const ids = row.id;
      const vulnNames = row.title;
      this.$modal
        .confirm("是否确认删除漏洞名称为【" + vulnNames + "】的数据项？")
        .then(function () {
          return delDeal(ids);
        })
        .then(() => {
          this.getList();
          this.initData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
    },
    canceloophole() {
      this.showAddloophole = false;
    },
    confirmVulnDeal() {
      this.showAddloophole = false;
      this.getList();
      this.initData();
    },
    // updateDealStatus(row){
    //   this.DealStatusData=row;
    //   this.$modal.confirm('是否确定更新漏洞处理状态？', '提示').then(() => {
    //     return updateVulnDeal(this.DealStatusData).then(()=>{
    //       this.$message.success('修改成功');
    //       // this.handleQuery();
    //     }).catch(()=>{
    //       this.$message.warning('调用接口超时');
    //     });
    //   }).catch(() => {
    //     this.$message.info('已取消');
    //     this.handleQuery();
    //   });
    // },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.total = 0;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        title: '',
        category: '',
        severity: this.$props.severity,
        hostIp: '',
        referenceId: '',
        handleState: null,
        hostPort: '',
        handleStatus: '',
        domainId: '',
        pageNum: 1,
        pageSize: 10
      };
      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();
      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();
      this.getList();
    },
    getList() {
      this.loading = true;
      let tempQueryParams = {...(this.queryParams), handleType: ''};
      getVulnDealList(tempQueryParams).then(response => {
        this.assetFrailtyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    selectable(row) {
      if (row.handleState === '1' || row.handleState === '3') {
        return false
      } else {
        return true
      }
    },
    handleExport() {
      this.download('/monitor2/vulndeal/export', {
        ...this.queryParams
      }, `IP漏洞_${new Date().getTime()}.xlsx`)
    },
    handleDetail(row) {
      this.showAddloophole = true;
      this.editable = false;
      this.loopholeData = row;
      this.title = '查看主机漏洞事件';
    },
    flowStateFormatter(row, column, cellValue, index) {
      let name = '未分配';
      let match = this.flowStateOptions.find(item => item.value == cellValue);
      if (match) {
        name = match.label;
      }
      return name;
    },
    createWork(row) {
      this.gapId = row.id
      this.workDialog = true
    },
    closeWork() {
      this.workDialog = false
    },
    flowTemplateSelectChange(val) {
      this.flowTemplateSelectVisible = false;
      this.flowVisible = true;
      this.currentFlowData.flowId = val;
      this.$nextTick(() => {
        this.$refs.FlowBox.init(this.currentFlowData)
      })
    },
    addOrUpdateFlowHandle(id, flowState, row) {
      let data = {
        id: id || '',
        // flowtemplatejson id
        // flowId: '564350702671937349',
        formType: 1,
        opType: flowState ? 0 : '-1',
        status: flowState,
        row: row,
        isWork: true
      }
      data.row.workType = '0';
      data.row.eventType = 1;
      data.originType = 'event';
      this.currentFlowData = data;
      this.loading = true;
      this.getConfigKey("default.flowTemplateId").then(res => {
        let flowId = res.msg;
        if(flowId){
          this.getFlowEngineInfo(flowId);
        }else {
          this.flowTemplateSelectVisible = true;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    addOrUpdateFlowHandleBatch(id, flowState) {
      let rows = [...this.rows];
      rows = rows.filter(item => item.handleState === '0' || item.handleState === null);
      if (!rows || rows.length < 1) {
        this.$message.error('未选择未处置事件，无法批量创建通报');
        return false;
      }
      let data = {
        id: id || '',
        // flowtemplatejson id
        // flowId: '564350702671937349',
        formType: 1,
        opType: flowState ? 0 : '-1',
        status: flowState,
        rows: rows,
        isWork: true
      }
      data.rows[0].workType = '0';
      data.rows[0].eventType = 1;
      data.originType = 'event';
      this.currentFlowData = data;

      this.loading = true;
      this.getConfigKey("default.flowTemplateId").then(res => {
        let flowId = res.msg;
        if(flowId){
          this.getFlowEngineInfo(flowId);
        }else {
          this.flowTemplateSelectVisible = true;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    getFlowEngineInfo(val){
      FlowEngineInfo(val).then(res => {
        if(res.data && res.data.flowTemplateJson){
          let data = JSON.parse(res.data.flowTemplateJson);
          if(!data[0].flowId){
            this.$message.error('该流程模板异常,请重新选择');
          }else {
            this.currentFlowData.flowId = data[0].flowId;
            this.flowVisible = true;
            this.$nextTick(() => {
              this.$refs.FlowBox.init(this.currentFlowData);
            });
          }
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
      this.rows = selection;
    },
    colseFlow(isrRefresh) {
      this.flowVisible = false
      if (isrRefresh) this.getList();
      this.initData();
    },
    handleApplicationTagShow(applicationList) {
      if (!applicationList || applicationList.length < 1) {
        return '';
      }
      let result = applicationList[0].assetName;
      if (applicationList.length > 1) {
        result += '...';
      }
      return result;
    },
    unique(arr) {
      for (let i = 0; i < arr.length; i++) {
        for (let j = i + 1; j < arr.length; j++) {
          if (arr[i] === arr[j]) {
            //如果第一个等同于第二个，splice方法删除第二个
            arr.splice(j, 1);

            j--;
          }
        }
      }
      return arr;
    },
  }
}
</script>
<style lang="scss" scoped>
::v-deep.el-select {
  width: 100%;

  .el-select-dropdown {
    position: absolute;
    top: 30px !important;
    left: 5px;
    .el-scrollbar {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

.loop_dialog {
  height: 90vh;
  overflow: hidden;
  ::v-deep .el-dialog {
    height: 100%;
    .el-dialog__body {
      height: calc(100% - 110px);
      padding: 10px 20px 0;
      overflow: auto;
    }
  }
}

.asset-tag {
  margin-left: 5px;
  max-width: 35%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.overflow-tag:not(:first-child) {
  margin-top: 5px;
}
</style>
