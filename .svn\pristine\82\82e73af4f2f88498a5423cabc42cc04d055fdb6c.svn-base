package com.ruoyi.safe.mapper;

import java.util.List;
import com.ruoyi.safe.domain.FfsafeHostEvents;
import org.apache.ibatis.annotations.Param;

/**
 * 主机事件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface FfsafeHostEventsMapper
{
    /**
     * 查询主机事件
     *
     * @param id 主机事件主键
     * @return 主机事件
     */
    public FfsafeHostEvents selectFfsafeHostEventsById(Long id);

    /**
     * 批量查询主机事件
     *
     * @param ids 主机事件主键集合
     * @return 主机事件集合
     */
    public List<FfsafeHostEvents> selectFfsafeHostEventsByIds(Long[] ids);

    /**
     * 查询主机事件列表
     *
     * @param ffsafeHostEvents 主机事件
     * @return 主机事件集合
     */
    public List<FfsafeHostEvents> selectFfsafeHostEventsList(FfsafeHostEvents ffsafeHostEvents);

    /**
     * 新增主机事件
     *
     * @param ffsafeHostEvents 主机事件
     * @return 结果
     */
    public int insertFfsafeHostEvents(FfsafeHostEvents ffsafeHostEvents);

    /**
     * 修改主机事件
     *
     * @param ffsafeHostEvents 主机事件
     * @return 结果
     */
    public int updateFfsafeHostEvents(FfsafeHostEvents ffsafeHostEvents);

    /**
     * 删除主机事件
     *
     * @param id 主机事件主键
     * @return 结果
     */
    public int deleteFfsafeHostEventsById(Long id);

    /**
     * 批量删除主机事件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFfsafeHostEventsByIds(Long[] ids);

    void saveBatch(@Param("saveList") List<FfsafeHostEvents> saveList);

    void truncateTable();

    void deleteFfsafeHostEventsByDeviceConfigId(Long id);
}
