<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.work.mapper.WorkHwTaskMapper">

    <resultMap type="cn.anmte.aqsoc.work.domain.WorkHwTask" id="WorkHwTaskResult">
        <result property="id"    column="id"    />
        <result property="workId"    column="work_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="stageClass"    column="stage_class"    />
        <result property="content"    column="content"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="manageUser"    column="manage_user"    />
        <result property="operateWorkId"    column="operate_work_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="manageUserName" column="manage_user_name" />
        <result property="operateWorkName" column="operate_work_name" />
        <result property="flowTaskId" column="flow_task_id" />
    </resultMap>

    <sql id="selectWorkHwTaskVo">
        SELECT
            t1.id,
            t1.work_id,
            t1.task_name,
            t1.stage_class,
            t1.content,
            t1.start_time,
            t1.end_time,
            t1.complete_time,
            t1.manage_user,
            t1.operate_work_id,
            t1.create_time,
            t1.create_by,
            t1.update_time,
            t1.update_by,
            t1.flow_task_id,
            su.nick_name AS manage_user_name,
            tow.work_name AS operate_work_name
        FROM
            work_hw_task t1
            LEFT JOIN sys_user su ON su.user_id=t1.manage_user
            LEFT JOIN tbl_operate_work tow ON tow.id = t1.operate_work_id
    </sql>

    <select id="selectWorkHwTaskList" parameterType="WorkHwTask" resultMap="WorkHwTaskResult">
        <include refid="selectWorkHwTaskVo"/>
        <where>
            <if test="workId != null "> and t1.work_id = #{workId}</if>
            <if test="taskName != null  and taskName != ''"> and t1.task_name like concat('%', #{taskName}, '%')</if>
            <if test="stageClass != null  and stageClass != ''"> and t1.stage_class = #{stageClass}</if>
            <if test="startTime != null "> and t1.start_time >= #{startTime}</if>
            <if test="endTime != null "> and t1.end_time &lt;= #{endTime}</if>
            <if test="completeTime != null "> and t1.complete_time = #{completeTime}</if>
            <if test="manageUser != null "> and t1.manage_user = #{manageUser}</if>
            <if test="operateWorkId != null "> and t1.operate_work_id = #{operateWorkId}</if>
            <if test="flowTaskId != null and flowTaskId != ''"> and t1.flow_task_id = #{flowTaskId}</if>
        </where>
    </select>

    <select id="selectWorkHwTaskById" parameterType="Long" resultMap="WorkHwTaskResult">
        <include refid="selectWorkHwTaskVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectWorkHwTaskByIds" parameterType="Long" resultMap="WorkHwTaskResult">
        <include refid="selectWorkHwTaskVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertWorkHwTask" parameterType="WorkHwTask" useGeneratedKeys="true" keyProperty="id">
        insert into work_hw_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workId != null">work_id,</if>
            <if test="taskName != null">task_name,</if>
            <if test="stageClass != null">stage_class,</if>
            <if test="content != null">content,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="manageUser != null">manage_user,</if>
            <if test="operateWorkId != null">operate_work_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="flowTaskId != null and flowTaskId != ''">flow_task_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workId != null">#{workId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="stageClass != null">#{stageClass},</if>
            <if test="content != null">#{content},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="manageUser != null">#{manageUser},</if>
            <if test="operateWorkId != null">#{operateWorkId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="flowTaskId != null and flowTaskId != ''">#{flowTaskId},</if>
        </trim>
    </insert>

    <update id="updateWorkHwTask" parameterType="WorkHwTask">
        update work_hw_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="workId != null">work_id = #{workId},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="stageClass != null">stage_class = #{stageClass},</if>
            <if test="content != null">content = #{content},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="manageUser != null">manage_user = #{manageUser},</if>
            <if test="operateWorkId != null">operate_work_id = #{operateWorkId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="flowTaskId != null and flowTaskId != ''">flow_task_id = #{flowTaskId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkHwTaskById" parameterType="Long">
        delete from work_hw_task where id = #{id}
    </delete>

    <delete id="deleteWorkHwTaskByIds" parameterType="String">
        delete from work_hw_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
