{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue?vue&type=style&index=0&id=95131482&scoped=true&lang=css", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue", "mtime": 1756710899571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiLi4vLi4vLi4vc3R5bGVzL3RyYWNrLmNzcyI7Cg=="}, {"version": 3, "sources": ["work_flow.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAulDA", "file": "work_flow.vue", "sourceRoot": "src/views/todoItem/todo", "sourcesContent": ["<template>\n  <div class=\"main\" ref=\"main\">\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"base_content\">\n      <div class=\"title\"><i class=\"el-icon-info\" /> 基础信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"!isHide('workName')\">\n            <el-form-item label=\"通报名称\" prop=\"workName\" ref=\"workName\">\n              <el-input size=\"small\" :disabled=\"!isWrite('ruleForm') || !isWrite('workName')\" v-model.trim=\"ruleForm.workName\" placeholder=\"请填写通报名称\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('period')\" style=\"height: 59px\">\n            <el-form-item label=\"期号\" prop=\"period\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" :required=\"isRequired('period')\">\n              <el-input-number style=\"width: 100%\" size=\"small\" v-model=\"ruleForm.period\" controls-position=\"right\" :min=\"1\" :max=\"999999\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" @change=\"refreshWord\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\" v-if=\"isReadOrNull('issue')\">\n            <el-form-item label=\"期刊\" prop=\"issue\" :disabled=\"!isWrite('ruleForm') || !isWrite('issue')\">\n              <el-input :disabled=\"true\" size=\"small\" v-model.trim=\"ruleForm.issue\" placeholder=\"\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('remark6') || isWriteOrNull('remark6')\">\n            <el-form-item label=\"通报类型\" prop=\"remark6\" ref=\"remark6\">\n              <el-select size=\"small\" v-model=\"ruleForm.remark6\" placeholder=\"请选择通报类型\" :disabled=\"!isWrite('ruleForm') || !isWrite('remark6')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in reportOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('urgency')\">\n            <el-form-item label=\"紧急程度\" prop=\"urgency\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\">\n              <el-select size=\"small\" v-model=\"ruleForm.urgency\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in urgencyOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('severityLevel')\">\n            <el-form-item label=\"严重程度\" prop=\"severityLevel\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\">\n              <el-select size=\"small\" v-model=\"ruleForm.severityLevel\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in severityLevelOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('isPublic')\">\n            <el-form-item label=\"是否公开\" prop=\"isPublic\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\">\n              <el-select size=\"small\" v-model=\"ruleForm.isPublic\" :default-first-option=\"true\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in isPublicOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"time_content\">\n      <div class=\"title\"><i class=\"el-icon-time\" /> 时间信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"timeForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n<!--          <el-col :span=\"8\" v-if=\"!isHide('eventCreateTime')\">\n            <el-form-item label=\"发现时间\" prop=\"eventCreateTime\" ref=\"eventCreateTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('eventCreateTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.eventCreateTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('reportDate')\">\n            <el-form-item label=\"通报日期\" prop=\"reportDate\" :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\">\n              <el-date-picker\n                v-model=\"ruleForm.reportDate\"\n                @change=\"refreshWord\"\n                type=\"datetime\"\n                style=\"width: 100%\"\n                :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"请选择通报日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"!isHide('expectCompleteTime')\">\n            <el-form-item label=\"计划完成\" prop=\"expectCompleteTime\" ref=\"expectCompleteTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('expectCompleteTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.expectCompleteTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"person_content\">\n      <div class=\"title\"><i class=\"el-icon-user-solid\" /> 人员信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"personForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"isReadOrNull('signed')\">\n            <el-form-item label=\"签发\" prop=\"signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" :required=\"isRequired('signed')\">\n              <el-input size=\"small\" v-model=\"ruleForm.signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('proofread')\">\n            <el-form-item label=\"核稿\" prop=\"proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" :required=\"isRequired('proofread')\">\n              <el-input size=\"small\" v-model=\"ruleForm.proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('editor')\">\n            <el-form-item label=\"编辑\" prop=\"editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" :required=\"isRequired('editor')\">\n              <el-input size=\"small\" v-model=\"ruleForm.editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <!-- 告知单 开始 -->\n    <div class=\"ext_content\" v-if=\"isRead('informForm')\">\n      <div class=\"title\">\n        <i class=\"el-icon-s-order\" /> 通报内容\n      </div>\n      <el-form :model=\"informForm\" :rules=\"informRules\" ref=\"informForm\" label-width=\"120px\" size=\"medium\" class=\"demo-informForm\" style=\"margin-top: 10px\">\n<!--        <el-form-item label=\"事件来源\" prop=\"eventSource\" v-if=\"!isHide('eventSource')\" ref=\"eventSource\">\n          <el-input size=\"small\" v-model=\"informForm.eventSource\" placeholder=\"请填写事件来源\" maxlength=\"50\" :disabled=\"!isWriteOrNull('eventSource')\" />\n        </el-form-item>\n        <el-form-item label=\"事件类型\" prop=\"eventType\" v-if=\"!isHide('eventType')\" ref=\"eventType\">\n          <el-cascader size=\"small\" v-model=\"informForm.eventType\" :options=\"threatenDict\" clearable placeholder=\"请选择事件类型\" style=\"width: 100%\" :props=\"{ label: 'dictLabel', value: 'dictValue' }\" :disabled=\"!isWriteOrNull('eventType')\">\n            <template slot-scope=\"{ node, data }\">\n              <span>{{ data.dictLabel }}</span>\n              <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n            </template>\n          </el-cascader>\n          &lt;!&ndash;          <el-select v-model=\"informForm.eventType\" placeholder=\"请选择事件类型\">\n                      <el-option\n                        v-for=\"item in eventTypeOption\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>&ndash;&gt;\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"eventLevel\" v-if=\"!isHide('eventLevel')\" ref=\"eventLevel\">\n          <el-select size=\"small\" v-model=\"informForm.eventLevel\" placeholder=\"请选择事件级别\" :disabled=\"!isWriteOrNull('eventLevel')\">\n            <el-option\n              v-for=\"item in eventLevelOption\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-form-item>-->\n        <el-form-item label=\"事件通报\" prop=\"eventNotification\" ref=\"eventNotification\" v-if=\"!isHide('eventNotification')\" :required=\"isRequired('eventNotification')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.eventNotification\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写事件通报内容\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('eventNotification')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"处理建议\" prop=\"handleOpinion\" ref=\"handleOpinion\" v-if=\"!isHide('handleOpinion')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.handleOpinion\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写处理建议\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('handleOpinion')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述附件\" prop=\"describeFileUrl\" ref=\"describeFileUrl\" v-if=\"!isHide('describeFileUrl')\">\n          <file-upload v-model=\"informForm.describeFileUrl\"\n                       :disUpload=\"!isWrite('informForm') || !isWriteOrNull('describeFileUrl')\"\n                       :limit=\"5\"\n                       :file-type=\"['doc', 'docx', 'xlsx','xls', 'ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                       @change=\"refreshWord\"\n          />\n        </el-form-item>\n        <el-form-item label=\"发\" prop=\"remark8\" ref=\"remark8\" v-if=\"!isHideOrNull('remark8')\" :show-message=\"false\">\n          <el-input size=\"small\" type=\"text\" v-model=\"informForm.remark8\" placeholder=\"\" show-word-limit maxlength=\"50\" :disabled=\"!isWriteOrNull('remark8')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    <!-- 告知单 结束 -->\n\n    <!-- 告知单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('informFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 告知单审核签名签章 结束 -->\n\n    <!-- 通报对象 开始 -->\n    <div class=\"ext_content\" v-if=\"true\">\n      <div class=\"title\">\n        <i class=\"el-icon-aim\" /> 通报对象\n        <div class=\"title-right\"><div style=\"cursor: pointer;\" @click=\"addDept\" v-if=\"setting && setting.opType === '-1'\">+新增单位</div></div>\n      </div>\n      <report-target ref=\"reportTarget\" :dept-data-list=\"reportTargetForm\" :setting=\"setting\" style=\"margin-top: 10px;\" @refreshWord=\"refreshWord\" @activeNameChange=\"reportTargetActiveChange\"/>\n    </div>\n    <!-- 通报对象 结束 -->\n\n    <!-- 反馈单 开始 -->\n        <div class=\"ext_content\" v-if=\"isRead('feedbackForm') && reportTargetForm && reportTargetForm.length > 0\">\n          <div class=\"title\">\n            <i class=\"el-icon-s-order\" /> 反馈单信息\n          </div>\n          <el-form :model=\"currentDeptData\" :rules=\"feedbackRules\" ref=\"feedbackForm\" label-width=\"120px\" size=\"medium\" class=\"demo-feedbackForm\" style=\"margin-top: 10px\">\n            <el-form-item label=\"处置标题\" prop=\"remark7\" v-if=\"!isHideOrNull('remark7')\" :required=\"isRequired('remark7')\" :show-message=\"false\">\n              <el-input :disabled=\"!isWriteOrNull('remark7')\" size=\"small\" v-model=\"currentDeptData.handleTitle\" placeholder=\"请输入处置标题\" maxlength=\"50\" @blur=\"refreshWord\" />\n            </el-form-item>\n            <div style=\"display: flex;\" v-if=\"!isWrite('feedbackForm')\">\n    <!--          <el-form-item label=\"事件处理单文号\" prop=\"workNo\" v-if=\"!isHide('workNo')\" style=\"width: 50%\" ref=\"workNo\">\n                <el-input size=\"small\" v-model=\"feedbackForm.workNo\" placeholder=\"\" :disabled=\"true\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"反馈日期\" prop=\"feedbackDate\" v-if=\"!isHide('feedbackDate')\" style=\"width: 50%\" ref=\"feedbackDate\">\n                <el-input size=\"small\" v-model=\"feedbackForm.feedbackDate\" placeholder=\"\" :disabled=\"!isWriteOrNull('feedbackDate')\" @blur=\"refreshWord\"></el-input>\n              </el-form-item>-->\n            </div>\n            <el-form-item label=\"处理结果\" prop=\"eventDescription\" ref=\"eventDescription\" v-if=\"!isHide('eventDescription')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.eventDescription\" placeholder=\"请填写处理结果\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('eventDescription')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"处理情况\" prop=\"handleSituation\" ref=\"handleSituation\" v-if=\"!isHide('handleSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.handleSituation\" placeholder=\"请填写处理情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('handleSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"其他情况\" prop=\"otherSituation\" ref=\"otherSituation\" v-if=\"!isHide('otherSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.otherSituation\" placeholder=\"请填写其他情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('otherSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"附件\" prop=\"feedbackFileUrl\" ref=\"feedbackFileUrl\" v-if=\"!isHide('feedbackFileUrl')\">\n              <file-upload v-model=\"currentDeptData.feedbackFileUrl\"\n                           :disUpload=\"!isWrite('feedbackForm') || !isWriteOrNull('feedbackFileUrl')\"\n                           :limit=\"5\"\n                           :file-type=\"['doc','docx', 'xls', 'xlsx','ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                           @change=\"refreshWord\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n    <!-- 反馈单 结束 -->\n\n    <!-- 反馈单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('feedbackFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 反馈单审核签名签章 结束 -->\n\n    <!--  业务系统-资产选择  -->\n    <el-dialog v-if=\"applicationDialog\" title=选择资产 :visible.sync=\"applicationDialog\" class=\"application_dialog\" width=\"80%\" append-to-body>\n      <application-select v-if=\"applicationDialog\" ref=\"applicationSelect\" :value=\"currentApplicationSelect\" :isMultipleSelect=\"true\" :application-list=\"applicationList\" @cancel=\"applicationDialog = false\" @applicationSelected=\"applicationSelected\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitApplicationSelect\">确 定</el-button>\n        <el-button @click=\"applicationDialog = false\">取 消</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog v-if=\"openEventDetailDialog\" title=\"查看事件详情\" :visible.sync=\"openEventDetailDialog\" width=\"80%\" append-to-body>\n      <alarm-detail v-if=\"openEventDetailDialog && currentEventType == 3\" @openDetail=\"openDetail\" :asset-data=\"assetData\"/>\n      <new-addloophole v-if=\"openEventDetailDialog && currentEventType == 1\" :loophole-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n      <new-addwebvuln v-if=\"openEventDetailDialog && currentEventType == 2\" :webvuln-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n    </el-dialog>\n\n    <!--  事件选择弹窗-开始  -->\n    <select-event v-if=\"openEventSelectDialog\" :selected-event-ids=\"ruleForm.eventIds\" :dest-ips=\"ruleForm.associatedIps\" :open.sync=\"openEventSelectDialog\" :setting=\"setting\" :threaten-dict=\"threatenDict\" :current-event-type=\"parseInt(currentEventType)\" :severity-options=\"severityOptions\" @cancel=\"openEventSelectDialog=false\" @selected=\"handleEventSelected\"/>\n    <!--  事件选择弹窗-结束  -->\n  </div>\n</template>\n\n<script>\nimport { getAllUserListByDept,getAllUserList } from \"../../../api/system/user\"\nimport { addOrder } from \"../../../api/tool/work\";\nimport DeptSelect from \"../../components/select/deptSelect\";\nimport RelevancyGapInfo from \"./relevancyGapInfo\";\nimport RelevancyAlarmInfo from \"./relevancyAlarmInfo\";\nimport relevancyWebvulnInfo from \"./relevancyWebvulnInfo\";\nimport {listWebVuln} from \"@/api/monitor2/webvuln\"; //web漏洞\nimport {getVulnDealList} from \"@/api/monitor2/assetFrailty\"; //IP漏洞\nimport {listAlarm} from \"@/api/threaten/threatenWarn\"; //威胁事件\nimport comMixin from '@/views/zeroCode/workFlow/workFlowForm/mixin'\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getApplicationListByCondition,getBusinessInfo,getApplicationDetails} from \"@/api/safe/application\";\nimport { assetSelectList,getInfoByids,listByApplicationId } from \"@/api/assetSelect/assetSelect.js\";\nimport ApplicationSelect from \"./applicationSelect.vue\";\nimport SelectedEventList from \"./selected_event_list.vue\";\nimport SelectEvent from \"./select_event.vue\";\nimport AlarmDetail from \"@/views/basis/securityWarn/alarmDetail.vue\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport newAddwebvuln from \"@/views/frailty/webvuln/newAddwebvuln\";\nimport reportTarget from \"@/views/todoItem/todo/report_target.vue\";\nimport {listDept} from \"@/api/system/dept\";\n\nexport default {\n  name: \"createWork\",\n  mixins: [comMixin],\n  dicts: [\n    'loophole_category',\n    'threaten_alarm_type',\n    'work_order_report_type',\n    'work_order_urgency',\n    'work_order_public',\n    'work_order_severity_level',\n  ],\n  components: {AlarmDetail, ApplicationSelect, RelevancyAlarmInfo, RelevancyGapInfo, DeptSelect,relevancyWebvulnInfo,\n    newAddloophole,newAddwebvuln,SelectedEventList,SelectEvent,reportTarget},\n  props: {\n    workType: {\n      type: String,\n      require: true\n    },\n    mId: {\n      type: Number,\n      require: true\n    }\n  },\n  data() {\n    return {\n      componentName: 'createWork',\n      scrollCache: null,\n      openEventSelectDialog: false,\n      assetData: {},\n      openEventDetailDialog: false,\n      currentEventType: 0,\n      queryEventParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      eventListLoading: false,\n      eventList: [],\n      total: 0,\n      ruleForm: {\n        workName: null,\n        complateTime: null,\n        handleDept: null,\n        handleUser: null,\n        workType: null,\n        eventType: null,\n        associatedIps: [],\n      },\n      dataForm: {\n      },\n      dataRule:{},\n      selectedEvent: [],\n      tempSelectedEvent: [],\n      severityOptions: {\n        '1': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '2': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '3': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},\n        ],\n      },\n      threatenDict: [],\n      rules: {\n        workName: [\n          { required: true, message: '请输入通报名称', trigger: 'blur' },\n          { min: 3, max: 80, message: '长度在 3 到 80 个字符', trigger: 'blur' }\n        ],\n        applicationId: [\n          { required: false, message: '请选择业务系统', trigger: 'change' }\n        ],\n        eventCreateTime: [\n          { required: true, message: '请选择事件发生时间', trigger: 'change' }\n        ],\n        expectCompleteTime: [\n          { required: true, message: '计划完成时间', trigger: 'change' }\n        ],\n        handleDept: [\n          { required: true, message: '请选择处置单位', trigger: 'change' }\n        ],\n        handleUser: [\n          { required: true, message: '请选择处置人', trigger: 'change' }\n        ],\n        handleUserPhone: [\n          { required: false, message: '请输入处置人电话', trigger: 'blur' }\n        ],\n        remark6: [\n          { required: true, message: '请选择通报类型', trigger: 'change' }\n        ],\n      },\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      tableData: [],\n      alertTableData: [],\n      handleOption: [],\n      manageOption: [],\n      handleOptionCopy: [],\n      manageOptionCopy: [],\n      informForm: {},\n      informRules: {\n        eventSource: [\n          {required: true, message: '请输入事件来源', trigger: 'blur'}\n        ],\n        handleOpinion: [\n          {required: false, message: '请输入处理建议', trigger: 'blur'}\n        ],\n        eventNotification: [\n          {required: false, message: '请输入事件通报内容', trigger: 'blur'}\n        ],\n        describeFileUrl: [\n          {required: false, message: '请上传描述附件', trigger: 'change'}\n        ],\n        /*remark8: [\n          {required: true, message: '请输入', trigger: 'blur'}\n        ],*/\n      },\n      feedbackForm: {},\n      feedbackRules: {\n        eventDescription: [\n          {required: false, message: '请输入处理结果', trigger: 'blur'}\n        ],\n        handleSituation: [\n          {required: false, message: '请输入处理情况', trigger: 'blur'}\n        ]\n      },\n      applicationList: [],\n      applicationDialog: false,\n      macAipList: [],\n      assetIds: [],\n      currentApplicationSelect: null,\n      eventTypeOption: [\n        {\n          label: '漏洞',\n          value: 1\n        },\n        {\n          label: '后门',\n          value: 2\n        },\n        {\n          label: '外链',\n          value: 3\n        }\n      ],\n      eventLevelOption: [\n        {\n          label: 'Ⅰ级',\n          value: '1'\n        },\n        {\n          label: 'Ⅱ级',\n          value: '2'\n        },\n        {\n          label: 'Ⅲ级',\n          value: '3'\n        },\n        {\n          label: 'Ⅳ级',\n          value: '4'\n        },\n      ],\n      applicationInfo: {},\n      isChangeEventType: false,\n      eventTypeBtnKey: 0,\n      isFromEvent: false,\n      isChangeForm: false,\n      formOperatesRecord: [],\n      reportTargetForm: [],\n      reportTargetActive: '0',\n    }\n  },\n  watch: {\n    informForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    ruleForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    feedbackForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    'ruleForm.handleDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getUserList();\n        }\n      }\n    },\n    'ruleForm.manageDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getManageUserList();\n        }\n      }\n    },\n    'ruleForm.applicationId': {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        if(newVal && newVal != oldVal){\n          if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n            this.getApplicationDetails();\n          }\n        }\n      }\n    },\n    'setting.row': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length>0){\n          if(val.eventType && val.eventType != 0){\n            this.currentEventType = val.eventType;\n          }\n          if(val.createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val.createTime);\n          }\n          /*if(val.id){\n            this.$set(this.ruleForm, 'eventIds', [val.id]);\n          }*/\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          if(val.deptId || (val.eventType === 3 && val.deptIdStr)){\n            deptId = val.deptId?val.deptId:val.deptIdStr.split(',')[0];\n            deptName = val.deptName.split(',')[0];\n          }\n          this.reportTargetForm = [\n            {\n              deptName: deptName,\n              deptId: deptId,\n              handleTitle: null,\n              eventDescription: null,\n              handleSituation: null,\n              otherSituation: null,\n              feedbackFileUrl: null,\n              formData: [{\n                handleDept: deptId,\n                handleUser: '',\n                applicationId: val.businessApplications?val.businessApplications[0].assetId:null,\n                assetName: val.businessApplications?val.businessApplications[0].assetName:null,\n                loginUrl: val.businessApplications?val.businessApplications[0].url:null,\n                manager: val.businessApplications?parseInt(val.businessApplications[0].manager):null,\n                phone: val.businessApplications?val.businessApplications[0].managerPhone:null,\n                eventData: {\n                  'type1': val.eventType === 1?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type2': val.eventType === 2?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type3': val.eventType === 3?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type4': val.eventType === 4?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[]\n                }\n              }]\n            }\n          ];\n\n          //默认查业务系统\n          /*this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: this.setting && this.setting.row && this.setting.row.businessApplications && this.setting.row.businessApplications.length>0?this.setting.row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.rows': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && val.length>0){\n          if(val[0].eventType && val[0].eventType != 0){\n            this.currentEventType = val[0].eventType;\n          }\n          if(val[0].createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val[0].createTime);\n          }\n          let eventIds = val.map(item => item.id);\n          this.$set(this.ruleForm, 'eventIds', eventIds);\n          let row = val[0];\n          this.reportTargetForm = [];\n          let deptSet = new Set();\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          val.forEach(item => {\n            if(!item.deptId){\n              item.deptId = deptId;\n              item.deptName = deptName;\n            }\n            if(item.deptIdStr){\n              item.deptId = item.deptIdStr.split(',')[0];\n            }\n            deptSet.add({\n              deptName: item.deptName.split(',')[0],\n              deptId: item.deptId,\n            });\n          })\n          deptSet.forEach(item => {\n            let matchArr = val.filter(valItem => valItem.deptId === item.deptId);\n            let first = matchArr[0];\n            this.reportTargetForm.push(\n              {\n                deptName: item.deptName,\n                deptId: item.deptId,\n                handleTitle: null,\n                eventDescription: null,\n                handleSituation: null,\n                otherSituation: null,\n                feedbackFileUrl: null,\n                formData: [\n                  {\n                    handleDept: item.deptId,\n                    applicationId: first.businessApplications?first.businessApplications[0].assetId:null,\n                    assetName: first.businessApplications?first.businessApplications[0].assetName:null,\n                    loginUrl: first.businessApplications?first.businessApplications[0].url:null,\n                    manager: first.businessApplications?parseInt(first.businessApplications[0].manager):null,\n                    phone: first.businessApplications?first.businessApplications[0].managerPhone:null,\n                    eventData: {\n                      'type1': this.currentEventType===1?matchArr.map(item => ({type: 1, eventId: item.id})):[],\n                      'type2': this.currentEventType===2?matchArr.map(item => ({type: 2, eventId: item.id})):[],\n                      'type3': this.currentEventType===3?matchArr.map(item => ({type: 3, eventId: item.id})):[],\n                      'type4': this.currentEventType===4?matchArr.map(item => ({type: 4, eventId: item.id})):[]\n                    }\n                  }\n                ]\n              }\n            )\n          })\n          /*//默认查业务系统\n          this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: row.businessApplications && row.businessApplications.length>0?row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.formData': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length !== 0){\n          this.initForm();\n        }\n      }\n    },\n    'setting.flowTaskOperatorRecordList': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(!this.setting.flowTemplateJson){\n          return;\n        }\n        let allMatchNodes = [];\n        this.loopGetFlowNode(this.setting.flowTemplateJson,'end',allMatchNodes);\n        let allFormOperates = {};\n        allMatchNodes.map(item => item.properties.formOperates).forEach(item => {\n          item.forEach(arrItem => {\n            if(!allFormOperates[arrItem.id]){\n              allFormOperates[arrItem.id] = {\n                read: arrItem.read,\n                write: arrItem.write\n              }\n            }else {\n              if(!allFormOperates[arrItem.id].read){\n                allFormOperates[arrItem.id].read = arrItem.read;\n              }\n              if(!allFormOperates[arrItem.id].write){\n                allFormOperates[arrItem.id].write = arrItem.write;\n              }\n            }\n          })\n        });\n        this.formOperatesRecord = Object.entries(allFormOperates).map(([id, value]) => ({\n          id: id,\n          read: value.read,\n          write: value.write\n        }));\n      }\n    },\n    currentEventType: {\n      handler(newVal,oldVal){\n        this.$set(this.ruleForm,'workType',newVal);\n        /* if(newVal){\n          this.$nextTick(() => {\n            this.resetQuery();\n          })\n        } */\n      }\n    },\n    'ruleForm.handleUser': {\n      handler(newVal,oldVal){\n\n      }\n    },\n    reportTargetForm: {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        this.$emit('reportDataChange',newVal);\n      }\n    },\n  },\n  created() {\n    getMulTypeDict({\n      dictType:'threaten_alarm_type'\n    }).then(res=>{\n      this.threatenDict=res.data;\n      // TODO  暂定为获取两层  当前方法有问题 其它版本也有修改需要等待后续修改\n      /* if (this.threatenDict.length > 0 && this.threatenDict[0].children.length > 0) {\n        this.ruleForm.eventType = this.threatenDict[0].dictValue + '/' + this.threatenDict[0].children[0].dictValue\n      } */\n      this.threatenDict.forEach(item=>{\n        item.value=item.dictValue;\n        item.label=item.dictLabel;\n        item.children.forEach(cItem=>{\n          cItem.value=cItem.dictValue;\n          cItem.label=cItem.dictLabel;\n        })\n      })\n    });\n  },\n  mounted() {\n    this.$nextTick(() => {\n      //默认值\n      this.setFormDefault();\n    })\n  },\n  computed: {\n    categoryDict(){\n      if(this.currentEventType == 1 || this.currentEventType == 2){\n        return this.dict.type.loophole_category;\n      }else if(this.currentEventType == 3){\n        return this.threatenDict;\n      }\n    },\n    reportOptions(){\n      return this.dict.type.work_order_report_type;\n    },\n    urgencyOptions(){\n      return this.dict.type.work_order_urgency;\n    },\n    isPublicOptions(){\n      return this.dict.type.work_order_public;\n    },\n    severityLevelOptions(){\n      return this.dict.type.work_order_severity_level;\n    },\n    // 获取当前部门数据（安全访问）\n    currentDeptData() {\n      return this.reportTargetForm && this.reportTargetForm.length>0 ? this.reportTargetForm[parseInt(this.reportTargetActive)] : null;\n    }\n  },\n  methods: {\n    initForm(){\n      this.setFormData(this.ruleForm, 'applicationId');\n      this.currentApplicationSelect = {\n        application: {\n          assetId: this.setting.formData.applicationId,\n        },\n        selected: this.setting.formData.remark1?JSON.parse(this.setting.formData.remark1).map(se => {return {assetId: se}}):[],\n      }\n      this.setFormData(this.ruleForm, 'associatedIps');\n      this.setFormData(this.ruleForm, 'eventIds');\n      this.$set(this.queryEventParams,\"ids\",this.setting.formData.eventIds);\n      this.setFormData(this.ruleForm, 'workType');\n      this.currentEventType = this.setting.formData.workType;\n      this.setFormData(this.ruleForm, 'workName');\n      this.setFormData(this.ruleForm, 'loginUrl');\n      this.setFormData(this.ruleForm, 'handleDept');\n      this.setFormData(this.ruleForm, 'manageDept');\n      this.setFormData(this.ruleForm, 'handleDeptName');\n      this.setFormData(this.ruleForm, 'manageDeptName');\n      this.setFormData(this.ruleForm, 'handleUser');\n      this.$set(this.ruleForm,\"manageUser\",this.setting.formData.manageUser?parseInt(this.setting.formData.manageUser) : null);\n      this.setFormData(this.ruleForm, 'handleUserPhone');\n      this.setFormData(this.ruleForm, 'handleUserName');\n      this.setFormData(this.ruleForm, 'manageUserName');\n      this.setFormData(this.ruleForm, 'remark6');\n      this.setFormData(this.ruleForm, 'issue');\n      this.setFormData(this.ruleForm, 'expectCompleteTime');\n      this.setFormData(this.ruleForm, 'eventCreateTime');\n      this.setFormData(this.ruleForm, 'applicationName');\n      this.$set(this.ruleForm,\"urgency\",this.setting.formData.urgency?this.setting.formData.urgency.toString() : '1');\n      this.setFormData(this.ruleForm, 'isPublic');\n      this.setFormData(this.ruleForm, 'severityLevel');\n      this.setFormData(this.ruleForm, 'reportDate');\n      this.setFormData(this.ruleForm, 'period');\n      this.setFormData(this.ruleForm, 'signed');\n      this.setFormData(this.ruleForm, 'proofread');\n      this.setFormData(this.ruleForm, 'editor');\n\n      this.setFormData(this.informForm, 'describeFileUrl');\n      this.setFormData(this.informForm, 'eventLevel');\n      this.setFormData(this.informForm, 'eventNotification');\n      this.setFormData(this.informForm, 'eventSource');\n      // this.setFormData(this.informForm, 'workType');\n      this.setFormData(this.informForm, 'handleOpinion');\n      this.setFormData(this.informForm, 'eventType');\n      this.setFormData(this.informForm, 'remark8');\n      this.setFormData(this.feedbackForm, 'eventDescription');\n      this.setFormData(this.feedbackForm, 'handleSituation');\n      this.setFormData(this.feedbackForm, 'otherSituation');\n      this.setFormData(this.feedbackForm, 'workNo');\n      this.setFormData(this.feedbackForm, 'feedbackDate');\n      this.setFormData(this.feedbackForm, 'feedbackFileUrl');\n      this.setFormData(this.feedbackForm, 'remark7');\n      //通报对象赋值\n      this.reportTargetForm = this.setting.formData.reportTargetForm;\n\n      this.refreshWord();\n    },\n    setFormData(form, column){\n      let data = this.setting.formData[column];\n      if('eventType' === column){\n        if(data && data.indexOf(\"/\")!==-1){\n          data = data.split(\"/\");\n        }\n      }\n      this.$set(form, column, data);\n    },\n    setFormDefault(){\n      if(!this.ruleForm.urgency){\n        this.$set(this.ruleForm,'urgency','1');\n      }\n      if(!this.ruleForm.reportDate){\n        this.$set(this.ruleForm,'reportDate',this.jnpf.toDate(new Date(), 'yyyy-MM-dd HH:mm:ss'));\n      }\n      if(!this.ruleForm.signed){\n        this.$set(this.ruleForm,'signed','叶琛');\n      }\n      if(!this.ruleForm.proofread){\n        this.$set(this.ruleForm,'proofread','王亮');\n      }\n      if(!this.ruleForm.editor){\n        this.$set(this.ruleForm,'editor','付扬');\n      }\n    },\n    submitForm(formName) {\n      const that = this\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          that.ruleForm.businessId = that.mId\n          that.ruleForm.workType = that.workType\n          addOrder(that.ruleForm).then(res => {\n            if (res.code === 200) {\n              this.$message({\n                message: '通报创建成功',\n                type: 'success'\n              })\n              that.resetForm()\n            } else {\n              this.$message.error('通报创建失败')\n            }\n          })\n        } else {\n          console.log('error submit!!');\n          return false;\n        }\n      })\n    },\n    resetForm() {\n      this.$emit('closeWork')\n    },\n    openFocus() {\n      if (!this.ruleForm.handleDept) {\n        this.$message.warning('请先选择处理部门！');\n      }\n    },\n    handleQuery(){\n      this.queryEventParams.pageNum = 1;\n      this.total = 0;\n      this.getEventDataList();\n    },\n    resetQuery(){\n      this.queryEventParams={\n        pageNum: 1,\n        pageSize: this.queryEventParams.pageSize\n      };\n      this.getEventDataList();\n    },\n    handleEventDetail(row){\n      this.assetData= {...row};\n      this.openDetail(true);\n    },\n    openDetail(val){\n      this.openEventDetailDialog = val;\n    },\n    eventBtnClick(type,evt){\n      if(this.currentEventType != type && this.currentEventType != 0){\n        this.$confirm('切换事件类型将清空之前选择的事件, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.selectedEvent = [];\n          this.ruleForm.eventIds = [];\n          this.currentEventType = type;\n          this.$refs.eventList && this.$refs.eventList.clearSelection();\n          this.isChangeEventType = true;\n        }).catch(() => {\n          this.eventTypeBtnKey++;\n        });\n      }else {\n        if(this.currentEventType != 0 && this.currentEventType == type){\n          if(this.isFromEvent){\n            //从事件发起，不允许取消\n            return false;\n          }\n          this.$confirm('取消选中事件类型将清空之前选择的事件, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.eventList = [];\n            this.total = 0;\n            this.selectedEvent = [];\n            this.ruleForm.eventIds = [];\n            this.currentEventType = 0;\n            this.$refs.eventList && this.$refs.eventList.clearSelection();\n            this.isChangeEventType = true;\n            const target = evt.target;\n            if(target){\n              if(target.nodeName === 'SPAN'){\n                if(target.parentNode){\n                  target.parentNode.blur();\n                }\n              }\n              target.blur();\n            }\n          }).catch(() => {\n\n          });\n        }else {\n          this.currentEventType = type;\n        }\n      }\n    },\n    flowStateFormatter(row, column, cellValue, index){\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value==cellValue);\n      if(match){\n        name = match.label;\n      }\n      return name;\n    },\n    getEventDataList(){\n      if(!this.currentEventType && (!this.dataForm.eventIds || this.dataForm.eventIds.length<1)){\n        return false;\n      }\n      if(this.currentEventType == 0){\n        return false;\n      }\n      this.eventListLoading = true;\n      this.requestList().then(response => {\n        let resData = this.convertEventData(response.rows);\n        this.eventList = resData;\n        this.total= response.total;\n        this.eventListLoading = false;\n      })\n    },\n    //转换\n    convertEventData(srcDataList){\n      if(!srcDataList){\n        return [];\n      }\n      if(this.currentEventType == 3){\n        return srcDataList.map(item => {\n          return {\n            id: item.id,\n            title: item.threatenName,\n            category: item.threatenType,\n            severity: item.alarmLevel,\n            webUrl: item.destIp,\n            handleStatus: item.handleState,\n            workState: item.orderState,\n            dataSource: item.dataSource,\n            scanNum: item.alarmNum,\n            updateTime: item.updateTime,\n            createTime: item.createTime,\n            hostPort: item.destPort,\n            flowState: item.flowState\n          }\n        })\n      }else if(this.currentEventType == 1){\n        return srcDataList.map(item => {\n          item.webUrl = item.hostIp;\n          return item;\n        });\n      }else {\n        return srcDataList;\n      }\n\n    },\n    requestList(){\n      let queryParams = this.convertQueryParams(this.queryEventParams);\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return getVulnDealList(queryParams);\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return listWebVuln(queryParams);\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return listAlarm(queryParams);\n      }\n    },\n    convertQueryParams(srcParams){\n      if(!this.isRead('list_select')){\n        srcParams.ids = this.dataForm.eventIds;\n      }\n      if(this.currentEventType == 3){\n        return {\n          pageNum: srcParams.pageNum,\n          pageSize: srcParams.pageSize,\n          threatenName: srcParams.title,\n          threatenType: srcParams.category?srcParams.category.join('/'):null,\n          alarmLevel: srcParams.severity,\n          destIp: srcParams.webUrl,\n          destPort: srcParams.hostPort,\n          handleState: srcParams.handleStatus,\n          orderState: srcParams.workState,\n          dataSource: srcParams.dataSource,\n          createTime: srcParams.createTime,\n          ids: srcParams.ids\n        }\n      }else if(this.currentEventType == 1){\n        srcParams.hostIp = srcParams.webUrl;\n        return srcParams;\n      }else {\n        return srcParams;\n      }\n    },\n    handleEventSelected(val, event){\n      if(!this.ruleForm.eventIds){\n        this.ruleForm.eventIds = [];\n      }\n      let tempArr = [...this.ruleForm.eventIds];\n      tempArr.push(...val);\n      if (event) {\n        this.ruleForm.eventType = event\n      }\n      this.$set(this.ruleForm,'eventIds',tempArr);\n      this.selectedEvent = this.ruleForm.eventIds;\n    },\n    handleSeverityTag(severity,key){\n      if(!severity){\n        return '未知';\n      }\n      if(this.severityOptions[this.currentEventType.toString()]){\n        let matchItem = this.severityOptions[this.currentEventType.toString()].find(item => item.value == severity);\n        if(!matchItem){\n          return '未知';\n        }\n        return matchItem[key];\n      }\n      return '';\n    },\n    openApplicationSelect(){\n      /*getApplicationListByCondition({\n        // ipv4: this.getIpv4()\n        ipv4: null\n      }).then(res=>{\n        this.applicationList=res.data;\n        this.applicationDialog = true;\n      });*/\n      this.applicationDialog = true;\n    },\n    getIpv4(){\n      if(!this.setting.row && (!this.setting.rows || this.setting.rows.length<1)){\n        return null;\n      }\n      let row = this.setting.row || this.setting.rows[0];\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return row.hostIp;\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return row.webUrl;\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return row.destIp;\n      }else if(this.currentEventType == 4){\n        //弱口令漏洞\n        return row.hostIp;\n      }\n    },\n    applicationSelected(data){\n      this.currentApplicationSelect = data;\n      this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n      this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n      this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n      this.$set(this.ruleForm,'applicationId',data.application.assetId);\n      this.ruleForm.applicationName = data.application.assetName;\n      this.applicationDialog = false;\n      this.refreshWord();\n    },\n    submitApplicationSelect(){\n      this.$refs.applicationSelect.submit();\n    },\n    getApplicationDetails(){\n      let _that = this;\n      getApplicationDetails(this.ruleForm.applicationId).then(res => {\n        if(res.data.url){\n          this.$set(_that.ruleForm,'loginUrl',res.data.url);\n        }\n        if(res.data.dept_id){\n          this.$set(_that.ruleForm,'handleDept',res.data.dept_id);\n          this.$set(_that.ruleForm,'manageDept',res.data.dept_id);\n\n          this.$nextTick(() => {\n            this.getManageUserList();\n          })\n        }\n        this.applicationInfo = res.data;\n      })\n    },\n    eventTypeBtnDisable(){\n      if(\"-1\" == this.setting.opType){\n        return false;\n      }\n      if(this.setting.row && this.setting.row.eventType){\n        return true;\n      }\n      return false;\n    },\n    getManageUserList() {\n      // this.ruleForm.handleUser = ''\n      this.manageOption = []\n      if (this.ruleForm.manageDept) {\n        getAllUserList({\n          // deptId: this.ruleForm.manageDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.manageOption = res.rows;\n            this.manageOptionCopy = [...this.manageOption];\n            this.$nextTick(() => {\n              /*if(this.manageOption && !this.manageOption.find(manageUserItem => manageUserItem.userId == this.ruleForm.manageUser)){\n                this.ruleForm.manageUser = '';\n                this.ruleForm.handleUserPhone = '';\n              }*/\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  /*if(this.manageOption.find(manageUserItem => manageUserItem.userId == manager)){\n                    // this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                    this.$forceUpdate();\n                  }*/\n                  this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                  this.$forceUpdate();\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    getUserList() {\n      // this.ruleForm.handleUser = ''\n      this.handleOption = []\n      if (this.ruleForm.handleDept) {\n        getAllUserListByDept({\n          deptId: this.ruleForm.handleDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.handleOption = res.rows;\n            this.handleOptionCopy = [...this.handleOption];\n            this.$nextTick(() => {\n              if(this.handleOption && !this.handleOption.find(handleUserItem => handleUserItem.userId == this.ruleForm.handleUser)){\n                this.ruleForm.handleUser = '';\n                this.$set(this.ruleForm,'handleUser','');\n                // this.ruleForm.handleUserPhone = '';\n              }\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  if(this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                    if(this.handleOption && this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                      this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    }\n                  }\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    manageUserFilter(val){\n      if(val){\n        this.manageOption = this.manageOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.manageOption = [...this.manageOptionCopy];\n      }\n    },\n    handleUserFilter(val){\n      if(val){\n        this.handleOption = this.handleOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.handleOption = [...this.handleOptionCopy];\n      }\n    },\n    manageUserVisibleChange(){\n      this.manageOption = [...this.manageOptionCopy];\n    },\n    handleUserVisibleChange(){\n      this.handleOption = [...this.handleOptionCopy];\n    },\n    manageUserChange(row){\n      if(row){\n        let matchUser = this.manageOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserChange(row){\n      if(row){\n        let matchUser = this.handleOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserPhoneInput(){\n      this.$forceUpdate();\n    },\n    validateAllForm() {\n      /*if(this.isRead('list_select') && (this.currentEventType && this.currentEventType !== 0)){\n        if(!this.ruleForm.eventIds || this.ruleForm.eventIds.length < 1){\n          this.$modal.msgError('请选择关联事件');\n          this.$refs.event_list && this.$refs.event_list.scrollIntoView();\n          return new Promise((resolve, reject) => {\n            reject();\n          });\n        }\n      }*/\n\n      let validateForms = this.getValidateForm();\n      if(!validateForms || validateForms.length < 1){\n        return new Promise((resolve, reject) => {\n          resolve();\n        });\n      }\n      return Promise.all(validateForms);\n    },\n    validateForm(formName){\n      return new Promise((resolve, reject) => {\n        if(!this.$refs[formName]){\n          reject();\n        }\n        this.$refs[formName].validate((valid) => {\n          if (valid) {\n            resolve()\n          } else {\n            reject()\n          }\n        })\n      })\n    },\n    getValidateForm(){\n      let res = [];\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            res.push(this.validateForm(formName));\n          }\n        })\n      }\n      let refReportTarget = this.$refs.reportTarget;\n      if(refReportTarget){\n        res.push(...refReportTarget.validate());\n      }\n      return res;\n    },\n    getActiveForm(){\n      let res = [];\n      let flowVariable = this.setting.flowVariable;\n      if(flowVariable && flowVariable.length > 0){\n        let matchItem = flowVariable.find(item => item.key == 'formNames');\n        if(matchItem && matchItem.value){\n          let names = matchItem.value.split(',');\n          names.forEach(name => {\n            res.push(name);\n          })\n        }\n      }\n      return res;\n    },\n    handleColumn(column){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==column);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isRead(name){\n      let formOperates = [];\n      if(!this.setting.readonly){\n        formOperates = this.setting.formOperates;\n      }else {\n        formOperates = this.formOperatesRecord;\n      }\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isReadOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return false;\n      }\n    },\n    isWriteOrNull(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return false;\n      }\n    },\n    isWrite(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return true;\n      }\n    },\n    isHideOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return true;\n      }\n    },\n    isHide(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return false;\n      }\n    },\n    beforeSubmit(){\n      //动态表单\n      this.dataForm = {};\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            this.dataForm = {...this.dataForm, ...this[formName]};\n          }\n        })\n      }\n      if(this.setting.flowVariable){\n        let match = this.setting.flowVariable.find(item => item.key == 'workNoPrefix');\n        if(match){\n          this.dataForm.workNoPrefix = match.value;\n        }\n      }\n      /*      if (this.dataForm.eventType) {\n              this.dataForm.eventType = this.dataForm.eventType.join('/');\n            }*/\n      if(this.dataForm && this.dataForm.eventType){\n        if(Array.isArray(this.dataForm.eventType)){\n          this.dataForm.eventType = this.dataForm.eventType.join('/');\n        }\n      }else {\n        if(this.currentEventType === 4){\n          //弱口令\n          this.dataForm.eventType = '弱口令';\n        }\n      }\n\n      //通报对象\n      let reportTargetRef = this.$refs.reportTarget;\n      if(reportTargetRef){\n        this.dataForm.reportTargetForm = reportTargetRef.submitForm();\n      }\n      return this.dataForm;\n    },\n    isSelectAble(row,index){\n      if(this.setting && (!this.setting.originType || (this.setting.originType && this.setting.originType != 'event')) && (this.setting.row && this.setting.row.eventIds && this.setting.row.eventIds.find(item => item == row.id))){\n        return true;\n      }\n      return (!row.flowState || row.flowState == '99');\n    },\n    selectEventClick(){\n      if(!this.currentEventType){\n        this.$message.error('请先选择事件类型');\n        return false;\n      }\n      this.openEventSelectDialog = true;\n    },\n    isRequired(prop){\n      if(!this.formOperates){\n        return false;\n      }\n      let match = this.formOperates.find(item => item.id === prop);\n      if(match && match.required){\n        return true;\n      }\n      return false;\n    },\n    refreshWord(){\n      /* if(this.isChangeForm){\n        this.isChangeForm = false;\n        //暂存\n        // this.$eventBus.$emit('sendWorkForm', this.beforeSubmit());\n        this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n      } */\n      this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n    },\n    sendDataForm(){\n      //暂存\n      return this.beforeSubmit();\n    },\n    loopGetFlowNode(treeData,nodeId,arr){\n      if(!treeData){\n        return;\n      }\n      arr.push({\n        nodeId: treeData.nodeId,\n        properties: treeData.properties,\n        state: treeData.state,\n        type: treeData.type\n      })\n      if(treeData.nodeId === nodeId){\n        return;\n      }else {\n        return this.loopGetFlowNode(treeData.childNode,nodeId,arr);\n      }\n    },\n    addDept(){\n      this.$refs.reportTarget && this.$refs.reportTarget.addDept();\n    },\n    reportTargetActiveChange(val){\n      if(!val){\n        this.reportTargetActive = '0';\n      }else {\n        this.reportTargetActive = val;\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n@import \"../../../styles/track.css\";\n</style>\n<style lang=\"scss\" scoped>\n.main{\n  background-color: #F2F4F8;\n\n  > div{\n    background-color: #ffffff;\n    padding: 15px;\n  }\n  > div:not(:first-child){\n    margin-top: 0.8vh;\n  }\n\n  .base_content{\n    .ips{\n      display: flex;\n      > div{\n        background-color: #E7F2FF;\n        color: #0778FF;\n        border-width: 1px;\n        border-style: solid;\n        border-color: #0778FF;\n        border-radius: 3px;\n        height: 32px;\n        text-align: center;\n        overflow: hidden;\n        font-size: 0.5vw;\n      }\n      > div:not(:first-child){\n        margin-left: 1%;\n      }\n      .ips_item{\n        width: 31%;\n      }\n      .ips_item_overflow{\n        width: 7%;\n      }\n    }\n  }\n\n  .event_type_body{\n    > div:not(:first-child){\n      margin-top: 1.5vh;\n    }\n\n    .event_type_select{\n      display: flex;\n      .label{\n        align-content: center;\n      }\n      .event_type_btn{\n        display: flex;\n        margin-left: 20px;\n        > .event_type_btn_item:not(:first-child){\n          margin-left: 10px;\n        }\n\n        .btn_active{\n          background: #1890ff;\n          border-color: #1890ff;\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n\n  .title-right{\n    float: right;\n    font-size: 14px;\n    color: #6c6c6c;\n  }\n}\n</style>\n"]}]}