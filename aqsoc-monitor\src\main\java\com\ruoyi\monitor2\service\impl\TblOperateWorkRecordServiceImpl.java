package com.ruoyi.monitor2.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.domain.dto.TblOperateWorkRecordStatisticsDto;
import com.ruoyi.monitor2.mapper.TblOperateWorkRecordMapper;
import com.ruoyi.monitor2.service.ITblOperateWorkRecordService;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.system.domain.SysRoleDept;
import com.ruoyi.system.mapper.SysRoleDeptMapper;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 事务列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class TblOperateWorkRecordServiceImpl implements ITblOperateWorkRecordService
{
    @Autowired
    private TblOperateWorkRecordMapper tblOperateWorkRecordMapper;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private SysRoleDeptMapper roleDeptMapper;
    private static final String FLOW_TASK_URL = "/api/workflow/Engine/FlowTask";
    @Value("${server.port}")
    private String port;
    @Resource
    private TokenService tokenService;
    @Resource
    private ISysDictDataService  dictDataService;

    /**
     * 查询事务列表
     *
     * @param id 事务列表主键
     * @return 事务列表
     */
    @Override
    public TblOperateWorkRecord selectTblOperateWorkRecordById(Long id)
    {
        return tblOperateWorkRecordMapper.selectTblOperateWorkRecordById(id);
    }

    /**
     * 批量查询事务列表
     *
     * @param ids 事务列表主键集合
     * @return 事务列表集合
     */
    @Override
    public List<TblOperateWorkRecord> selectTblOperateWorkRecordByIds(Long[] ids)
    {
        return tblOperateWorkRecordMapper.selectTblOperateWorkRecordByIds(ids);
    }

    /**
     * 查询事务列表列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 事务列表
     */
    @Override
    public List<TblOperateWorkRecord> selectTblOperateWorkRecordList(TblOperateWorkRecord tblOperateWorkRecord)
    {
        handlePrem(tblOperateWorkRecord);
        PageUtils.startPage();
        return tblOperateWorkRecordMapper.selectTblOperateWorkRecordList(tblOperateWorkRecord);
    }

    /**
     * 新增事务列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 结果
     */
    @Override
    public int insertTblOperateWorkRecord(TblOperateWorkRecord tblOperateWorkRecord)
    {
        tblOperateWorkRecord.setCreateTime(DateUtils.getNowDate());
        return tblOperateWorkRecordMapper.insertTblOperateWorkRecord(tblOperateWorkRecord);
    }

    /**
     * 修改事务列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 结果
     */
    @Override
    public int updateTblOperateWorkRecord(TblOperateWorkRecord tblOperateWorkRecord)
    {
        return tblOperateWorkRecordMapper.updateTblOperateWorkRecord(tblOperateWorkRecord);
    }

    /**
     * 删除事务列表信息
     *
     * @param id 事务列表主键
     * @return 结果
     */
    @Override
    public int deleteTblOperateWorkRecordById(Long id)
    {
        return tblOperateWorkRecordMapper.deleteTblOperateWorkRecordById(id);
    }

    /**
     * 批量删除事务列表
     *
     * @param ids 需要删除的事务列表主键
     * @return 结果
     */
    @Override
    public int deleteTblOperateWorkRecordByIds(Long[] ids)
    {
        //删除flow_task
        List<TblOperateWorkRecord> tblOperateWorkRecords = selectTblOperateWorkRecordByIds(ids);
        if(CollUtil.isEmpty(tblOperateWorkRecords)){
            return 1;
        }
        String token = tokenService.createToken(SecurityUtils.getLoginUser());
        tblOperateWorkRecords.forEach(tblOperateWorkRecord -> {
            String delUrl = StrUtil.format("http://127.0.0.1:{}/proxy{}/delTask/{}",port,FLOW_TASK_URL,tblOperateWorkRecord.getFFlowtaskid());
            String delBody = HttpRequest.delete(delUrl).header("Authorization", token).body(new JSONObject().toString()).execute().body();
            JSONObject delRes = JSONObject.parseObject(delBody);
            if(delRes.getIntValue("code") != 200){
                throw new RuntimeException("删除流程失败:" + delRes.getString("msg"));
            }
        });
        return tblOperateWorkRecordMapper.deleteTblOperateWorkRecordByIds(ids);
    }

    @Override
    public int updateByFlowTaskId(TblOperateWorkRecord saveRecord) {
        return tblOperateWorkRecordMapper.updateTblOperateWorkRecord(saveRecord);
    }

    @Override
    public TblOperateWorkRecord getDetailsByTaskId(TblOperateWorkRecord tblOperateWorkRecord) {
        return tblOperateWorkRecordMapper.selectTblOperateWorkRecordByTaskId(tblOperateWorkRecord);
    }

    @Override
    public int countOperationalMattersNum(OverviewParams params) {
        return tblOperateWorkRecordMapper.countOperationalMattersNum(params);
    }

    @Override
    public int countOperateWorkRecordByStatus(TblOperateWorkRecord tblOperateWorkRecord) {
        return tblOperateWorkRecordMapper.countOperateWorkRecordByStatus(tblOperateWorkRecord);
    }

    @Override
    public void handlePrem(TblOperateWorkRecord workRecord){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //查看自己需要处置的
        workRecord.setFlowHandleUser(loginUser.getUserId().toString());

        if(loginUser.getUser().isAdmin()){
            //超管 看全部
            workRecord.setQueryAll(true);
            return;
        }
        SysUser user = loginUser.getUser();
        List<SysRole> roles = user.getRoles();
        if(CollUtil.isEmpty(roles)){
            //没有角色 只看自己
            workRecord.setCreateBy(loginUser.getUserId().toString());
            workRecord.setFlowHandleUser(user.getUserId().toString());
            workRecord.setOnlySelf(true);
            return;
        }
        boolean anyMatch = roles.stream().anyMatch(role -> "1".equals(role.getDataScope()));
        if(anyMatch){
            //有全部数据权限 看全部
            workRecord.setQueryAll(true);
            return;
        }
        workRecord.setCreateBy(loginUser.getUserId().toString());
        List<Long> customRoleIds = new ArrayList<>(); //自定义权限角色
        List<Long> deptTreeIds = new ArrayList<>(); //部门及子部门权限
        List<Long> deptIds = new ArrayList<>(); //部门权限
        roles.forEach(role -> {
            if("2".equals(role.getDataScope())){
                //自定义权限
                customRoleIds.add(role.getRoleId());
            }else if("3".equals(role.getDataScope())){
                //本部门权限
                deptIds.add(user.getDeptId());
            }else if("4".equals(role.getDataScope())){
                //本部门及以下权限
                deptTreeIds.add(user.getDeptId());
            }else if("5".equals(role.getDataScope())){
                //仅本人
                workRecord.setFlowHandleUser(user.getUserId().toString());
                workRecord.setOnlySelf(true);
            }
        });
        if(CollUtil.isNotEmpty(customRoleIds)){
            //查询自定义权限角色
            Page<Object> localPage = PageUtils.getLocalPage();
            if(localPage != null){
                PageUtils.clearPage();
            }
            List<SysRoleDept> roleDeptList = roleDeptMapper.selectListByRoleIds(CollUtil.distinct(customRoleIds));
            if(localPage != null){
                PageUtils.startPage();
            }
            if(CollUtil.isNotEmpty(roleDeptList)){
                roleDeptList.forEach(item -> deptIds.add(item.getDeptId()));
            }
        }
        if(CollUtil.isNotEmpty(deptTreeIds)){
            //查询本部门及以下部门
            Page<Object> localPage = PageUtils.getLocalPage();
            if(localPage != null){
                PageUtils.clearPage();
            }
            List<SysDept> deptList = deptService.selectDeptAndChildrenByIds(deptTreeIds);
            if(localPage != null){
                PageUtils.startPage();
            }
            if(CollUtil.isNotEmpty(deptList)){
                deptList.forEach(item -> deptIds.add(item.getDeptId()));
            }
        }

        workRecord.setHandleDeptIds(CollUtil.distinct(deptIds));
    }

    @Override
    public List<TblOperateWorkRecord> selectTblOperateWorkRecordListByFlowIds(TblOperateWorkRecord operateWorkRecord) {
        return tblOperateWorkRecordMapper.selectTblOperateWorkRecordList(operateWorkRecord);
    }
}
