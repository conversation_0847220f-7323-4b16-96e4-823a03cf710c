<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.external.mapper.ExternalAttackOfficialAccountMapper">

    <resultMap type="ExternalAttackOfficialAccount" id="ExternalAttackOfficialAccountResult">
        <result property="id"    column="id"    />
        <result property="officialAccountName"    column="official_account_name"    />
        <result property="wechatAccount"    column="wechat_account"    />
        <result property="officialAccountAppId"    column="official_account_app_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="discoveryTime"    column="discovery_time"    />
        <result property="entryType"    column="entry_type"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="officialAccountUpdateTime"    column="official_account_update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="accountType"    column="account_type"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="remark"    column="remark"    />
        <result property="deviceConfigId"    column="device_config_id"    />
        <result property="deptName" column="dept_name"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
    </resultMap>

    <sql id="selectExternalAttackOfficialAccountVo">
        select a.id, a.official_account_name, a.wechat_account, a.official_account_app_id, a.dept_id, a.discovery_time,a.entry_type, a.responsible_person, a.official_account_update_time, a.create_by, a.create_time, a.update_by, a.update_time, a.account_type, a.link_url, a.remark, a.device_config_id,
            GROUP_CONCAT(CONCAT(m.nick_name, '-', m.phonenumber) ORDER BY FIND_IN_SET(m.user_id, a.responsible_person) SEPARATOR ',') AS responsible_person_name, d.dept_name
        from external_attack_official_account a
        left join sys_user m ON FIND_IN_SET(m.user_id, a.responsible_person) > 0
        left join sys_dept d on a.dept_id=d.dept_id
    </sql>

    <select id="selectExternalAttackOfficialAccountList" parameterType="ExternalAttackOfficialAccount" resultMap="ExternalAttackOfficialAccountResult">
        <include refid="selectExternalAttackOfficialAccountVo"/>
        <where>
            <if test="officialAccountName != null  and officialAccountName != ''"> and a.official_account_name like concat('%', #{officialAccountName}, '%')</if>
            <if test="wechatAccount != null  and wechatAccount != ''"> and a.wechat_account like concat('%', #{wechatAccount}, '%')</if>
            <if test="officialAccountAppId != null  and officialAccountAppId != ''"> and a.official_account_app_id like concat('%', #{officialAccountAppId}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="deviceConfigId != null"> and a.device_config_id = #{deviceConfigId}</if>
        </where>
        GROUP BY a.id
        <if test="params != null">
            <if test="params.orderByColumn != null and params.isAsc != null">
                ORDER BY ${params.orderByColumn} ${params.isAsc}
            </if>
        </if>
    </select>

    <select id="selectExternalAttackOfficialAccountById" parameterType="Long" resultMap="ExternalAttackOfficialAccountResult">
        <include refid="selectExternalAttackOfficialAccountVo"/>
        where id = #{id}
    </select>

    <select id="selectExternalAttackOfficialAccountByIds" parameterType="Long" resultMap="ExternalAttackOfficialAccountResult">
        <include refid="selectExternalAttackOfficialAccountVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertExternalAttackOfficialAccount" parameterType="ExternalAttackOfficialAccount" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_official_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="officialAccountName != null">official_account_name,</if>
            <if test="wechatAccount != null">wechat_account,</if>
            <if test="officialAccountAppId != null">official_account_app_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="discoveryTime != null">discovery_time,</if>
            <if test="entryType != null">entry_type,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="officialAccountUpdateTime != null">official_account_update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="accountType != null">account_type,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="remark != null">remark,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="officialAccountName != null">#{officialAccountName},</if>
            <if test="wechatAccount != null">#{wechatAccount},</if>
            <if test="officialAccountAppId != null">#{officialAccountAppId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="discoveryTime != null">#{discoveryTime},</if>
            <if test="entryType != null">#{entryType},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="officialAccountUpdateTime != null">#{officialAccountUpdateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
         </trim>
    </insert>

    <update id="updateExternalAttackOfficialAccount" parameterType="ExternalAttackOfficialAccount">
        update external_attack_official_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="officialAccountName != null">official_account_name = #{officialAccountName},</if>
            <if test="wechatAccount != null">wechat_account = #{wechatAccount},</if>
            <if test="officialAccountAppId != null">official_account_app_id = #{officialAccountAppId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="officialAccountUpdateTime != null">official_account_update_time = #{officialAccountUpdateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExternalAttackOfficialAccountById" parameterType="Long">
        delete from external_attack_official_account where id = #{id}
    </delete>

    <delete id="deleteExternalAttackOfficialAccountByIds" parameterType="String">
        delete from external_attack_official_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="checkOfficialAccountExistence" resultType="int">
        SELECT COUNT(*) FROM external_attack_official_account
        <where>
            <if test="officialAccountAppId != null and officialAccountAppId != ''">
             AND official_account_app_id = #{officialAccountAppId}
            </if>
            and official_account_name = #{officialAccountName}
            <!-- 修改时排除指定 id -->
            <if test="id != null "> AND id != #{id} </if>
        </where>
    </select>

    <select id="selectByAppIds" parameterType="java.util.List" resultMap="ExternalAttackOfficialAccountResult">
        SELECT * FROM external_attack_official_account WHERE official_account_app_id IN
        <foreach item="id" index="index" collection="appIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        SELECT count(1) FROM external_attack_official_account
    </select>


    <delete id="deleteByEntryTypeAndAppId" parameterType="java.util.Set">
        DELETE FROM external_attack_official_account
        WHERE CONCAT(IFNULL(official_account_name,'null'), '-', IFNULL(official_account_app_id, 'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="deviceConfigId != null">
            AND device_config_id = #{deviceConfigId}
        </if>
    </delete>

    <!-- 选择性更新：只更新第三方接口返回的原有字段，保护用户维护的新增字段 -->
    <update id="selectiveUpdateByCondition" parameterType="ExternalAttackOfficialAccount">
        UPDATE external_attack_official_account
        <trim prefix="SET" suffixOverrides=",">
            <!-- 只更新第三方接口返回的原有字段 -->
            <if test="officialAccountName != null">official_account_name = #{officialAccountName},</if>
            <if test="wechatAccount != null">wechat_account = #{wechatAccount},</if>
            <if test="officialAccountAppId != null">official_account_app_id = #{officialAccountAppId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="officialAccountUpdateTime != null">official_account_update_time = #{officialAccountUpdateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <!-- 注意：不更新新增字段 wechat_id, account_type, link_url, remark -->
        </trim>
        WHERE CONCAT(IFNULL(official_account_name,'null'), '-', IFNULL(official_account_app_id, 'null')) = CONCAT(IFNULL(#{officialAccountName},'null'), '-', IFNULL(#{officialAccountAppId}, 'null'))
          AND entry_type = 1
    </update>

    <!-- 根据唯一键查询现有记录 -->
    <select id="selectByUniqueKey" parameterType="String" resultMap="ExternalAttackOfficialAccountResult">
        <include refid="selectExternalAttackOfficialAccountVo"/>
        WHERE CONCAT(IFNULL(a.official_account_name,'null'), '-', IFNULL(a.official_account_app_id, 'null')) = #{uniqueKey}
          AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量查询现有记录 -->
    <select id="selectByUniqueKeys" parameterType="java.util.Set" resultMap="ExternalAttackOfficialAccountResult">
        <include refid="selectExternalAttackOfficialAccountVo"/>
        WHERE CONCAT(IFNULL(a.official_account_name,'null'), '-', IFNULL(a.official_account_app_id, 'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量插入微信公众号 -->
    <insert id="batchInsertExternalAttackOfficialAccount" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_official_account (
            official_account_name, wechat_account, official_account_app_id, dept_id,
            discovery_time, entry_type, responsible_person, official_account_update_time,
            create_by, create_time, update_by, update_time, account_type, link_url, remark
        ) values
        <foreach collection="entityList" item="item" separator=",">
            (
                #{item.officialAccountName}, #{item.wechatAccount}, #{item.officialAccountAppId}, #{item.deptId},
                #{item.discoveryTime}, #{item.entryType}, #{item.responsiblePerson}, #{item.officialAccountUpdateTime},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime},
                #{item.accountType}, #{item.linkUrl}, #{item.remark}
            )
        </foreach>
    </insert>

</mapper>
