package com.ruoyi.external.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 微信小程序对象 external_attack_mini_program
 * 
 * <AUTHOR>
 * @date 2024-10-12
 */
@Data
public class ExternalAttackMiniProgram extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** 小程序名称 */
    @Excel(name = "小程序名称")
    private String miniProgramName;

    /** 账号原始ID */
    @Excel(name = "账号原始ID")
    private String originalAccountId;

    /** 小程序APP ID */
    @Excel(name = "小程序APP ID")
    private String miniProgramAppId;

    /** 所属部门 */
    private Long deptId;

    @Excel(name = "所属部门")
    private String deptName;


    /** 责任人及联系方式 */
    private String responsiblePerson;

    @Excel(name = "责任人及联系方式")
    private String responsiblePersonName;

    /** APP更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "APP更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date appUpdateTime;

    /** 发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date discoveryTime;

     /** 数据录入类型：1数据同步，2手动 */
    private Integer entryType;

    /** 微信号 */
    @Excel(name = "微信号")
    private String wechatId;

    /** 小程序类型，关联sys_dict_data.dict_value */
    @Excel(name = "小程序类型", dictType = "mini_program_type")
    private String programType;

    /** 小程序类型标签（字典翻译后的显示值） */
    private String programTypeLabel;

    /** 链接网址 */
    @Excel(name = "链接网址")
    private String linkUrl;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 设备配置ID */
    private Long deviceConfigId;

}
