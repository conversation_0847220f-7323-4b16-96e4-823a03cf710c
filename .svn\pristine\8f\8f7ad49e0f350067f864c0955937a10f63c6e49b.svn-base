package com.ruoyi.safe.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.component.FFSafeRequestComponent;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.FfsafeHostEvents;
import com.ruoyi.safe.domain.FfsafeIpfilterBlocking;
import com.ruoyi.safe.mapper.FfsafeHostEventsMapper;
import com.ruoyi.safe.service.IFfsafeHostEventsService;
import com.ruoyi.safe.service.IFfsafeIpfilterBlockingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 */
@Component
@Slf4j
public class FfsafeHostEventsTask implements SenderTask{
    @Resource
    private FFSafeRequestComponent ffSafeRequestComponent;
    @Resource
    private IFfsafeIpfilterBlockingService ffsafeIpfilterBlockingService;
    @Resource
    private IHandleDataSyncSender handleDataSyncSender;
    @Resource
    private ITblDeviceConfigService deviceConfigService;
    @Autowired
    private IFfsafeHostEventsService ffsafeHostEventsService;

    /*@PostConstruct
    public void init(){
        ThreadUtil.execute(this::syncToMiddle);
    }*/

    @Scheduled(fixedDelay = 5000*60)
    public void sync(){
        TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
        queryDeviceConfig.setStatus(1);
        List<TblDeviceConfig> list = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
        if(CollUtil.isEmpty(list)){
            return;
        }
        List<Runnable> tasks = new ArrayList<>();
        log.info("开始同步设备数据");
        list.forEach(deviceConfig -> {
            tasks.add(() -> {
                try {
                    FFSafeRequestComponent.deviceConfigThreadLocal.set(deviceConfig);
                    String resBody = ffSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.HOST_EVENTS_URL, null, null);
                    List<FfsafeHostEvents> saveList = new ArrayList<>();
                    if(StrUtil.isNotBlank(resBody) && JSON.isValidObject(resBody)){
                        JSONObject jsonObject = JSON.parseObject(resBody, JSONObject.class);
                        if (jsonObject != null && jsonObject.containsKey("data")){
                            List<JSONObject> blockingList = jsonObject.getJSONArray("data").toList(JSONObject.class);
                            if (CollUtil.isNotEmpty(blockingList)){
                                //根据探针id删除数据
                                ffsafeHostEventsService.deleteFfsafeHostEventsByDeviceConfigId(deviceConfig.getId());
                                blockingList.forEach(blocking -> {
                                    List<String> categoryName = blocking.getJSONArray("category_name").toList(String.class);
                                    if (CollUtil.isNotEmpty(categoryName)){
                                        //筛选出文件篡改的数据
                                        List<String> collect = categoryName.stream().filter(categoryNameStr -> categoryNameStr.equals("创建") ||
                                                categoryNameStr.equals("写入") || categoryNameStr.equals("删除") ||
                                                categoryNameStr.equals("重命名") || categoryNameStr.equals("权限变动")).collect(Collectors.toList());
                                        if (!CollUtil.isNotEmpty(collect)){
                                            FfsafeHostEvents ffsafeHostEvents = new FfsafeHostEvents();
                                            ffsafeHostEvents.setEventId(blocking.getLong("id"));
                                            ffsafeHostEvents.setHostIp(StrUtil.isNotBlank(blocking.getString("ip")) ? blocking.getString("ip") : null);
                                            ffsafeHostEvents.setMonitorItem(StrUtil.isNotBlank(blocking.getString("check_item")) ? blocking.getString("check_item") : null);
                                            ffsafeHostEvents.setOverallRisk(StrUtil.isNotBlank(blocking.getString("risk_level_text")) ? blocking.getString("risk_level_text") : null);
                                            ffsafeHostEvents.setCategory(categoryName.get(0));
                                            ffsafeHostEvents.setHandlingStatus(StrUtil.isNotBlank(blocking.getString("handle_status_text")) ? blocking.getString("handle_status_text") : null);
                                            ffsafeHostEvents.setAlarmTime(StrUtil.isNotBlank(blocking.getString("alert_time")) ? DateUtil.parse(blocking.getString("alert_time")) : null);
                                            ffsafeHostEvents.setDeviceConfigId(deviceConfig.getId());
                                            JSONObject params = new JSONObject();
                                            params.put("id", ffsafeHostEvents.getEventId());
                                            String hostEventDetail = ffSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.HOST_EVENTS_DETAIL_URL, params, null);
                                            if(StrUtil.isNotBlank(hostEventDetail) && JSON.isValidObject(hostEventDetail)){
                                                JSONObject hoseEventDetailObject = JSON.parseObject(hostEventDetail, JSONObject.class);
                                                if (hoseEventDetailObject != null && hoseEventDetailObject.containsKey("data")){
                                                    JSONObject data = hoseEventDetailObject.getJSONObject("data");
                                                    if (data != null){
                                                        JSONObject basicInfo = data.getJSONObject("basic_info");
                                                        if (basicInfo != null){
                                                            ffsafeHostEvents.setHostName(StrUtil.isNotBlank(basicInfo.getString("host_name")) ? basicInfo.getString("host_name") : null);
                                                            ffsafeHostEvents.setOperatingSystem(StrUtil.isNotBlank(basicInfo.getString("os")) ? basicInfo.getString("os") : null);
                                                            ffsafeHostEvents.setMd5Hash(StrUtil.isNotBlank(basicInfo.getString("md5")) ? basicInfo.getString("md5") : null);
                                                            ffsafeHostEvents.setSuspiciousFile(StrUtil.isNotBlank(basicInfo.getString("mal_file")) ? basicInfo.getString("mal_file") : null);
                                                            ffsafeHostEvents.setSha256Hash(StrUtil.isNotBlank(basicInfo.getString("sha256")) ? basicInfo.getString("sha256") : null);
                                                            ffsafeHostEvents.setFileCreateTime(StrUtil.isNotBlank(basicInfo.getString("file_create_time")) ? DateUtil.parse(basicInfo.getString("file_create_time")) : null);
                                                            ffsafeHostEvents.setFileModifyTime(StrUtil.isNotBlank(basicInfo.getString("file_modify_time")) ? DateUtil.parse(basicInfo.getString("file_modify_time")) : null);
                                                            if (data.getJSONArray("detect_infos") != null){
                                                                List<JSONObject> detectInfos = data.getJSONArray("detect_infos").toList(JSONObject.class);
                                                                if (CollUtil.isNotEmpty(detectInfos)){
                                                                    JSONObject detectInfoItem = detectInfos.get(0);
                                                                    List<String> detectEngine = detectInfoItem.getJSONArray("detect_engine").toList(String.class);
                                                                    if (CollUtil.isNotEmpty(detectEngine)){
                                                                        //将detectEngine变成以逗号分隔的字符串
                                                                        ffsafeHostEvents.setInspectionEngine(StrUtil.join(",", detectEngine));
                                                                    }
                                                                    ffsafeHostEvents.setAnomalyType(StrUtil.isNotBlank(detectInfoItem.getString("alert_name")) ? detectInfoItem.getString("alert_name") : null);
                                                                    ffsafeHostEvents.setDetectionInfo(StrUtil.isNotBlank(detectInfoItem.getString("detect_item")) ? detectInfoItem.getString("detect_item") : null);
                                                                    ffsafeHostEvents.setAiModel(StrUtil.isNotBlank(detectInfoItem.getString("detect_meta")) ? detectInfoItem.getString("detect_meta") : null);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            saveList.add(ffsafeHostEvents);
                                        }
                                    }
                                });
                            }
                        }
                    }
                    ffsafeHostEventsService.saveBatch(saveList);
                    log.info("保存{}条数据成功", saveList.size());
                } finally {
                    FFSafeRequestComponent.deviceConfigThreadLocal.remove();
                }
            });
        });
        Threads.batchAsyncExecute(tasks);
    }

    /**
     * 同步到中台
     */
    @Override
    public void syncToMiddle(){
        ThreadUtil.execute(() -> {
            log.info("非凡阻断中ip同步任务开始执行");
            List<FfsafeIpfilterBlocking> list = ffsafeIpfilterBlockingService.list(new LambdaQueryWrapper<FfsafeIpfilterBlocking>().eq(FfsafeIpfilterBlocking::getSyncStatus, 0)
                    .or().isNull(FfsafeIpfilterBlocking::getSyncStatus));
            if(CollUtil.isNotEmpty(list)){
                list.forEach(item -> {
                    SyncMessage<Object> message = new SyncMessage<>();
                    message.setDataType(DataTypeEnum.FF_SAFE_IP_FILTER_BLOCKING);
                    message.setOperationType(OperationTypeEnum.INSERT);
                    message.setData(item);
                    message.setTimestamp(System.currentTimeMillis());
                    handleDataSyncSender.sendDataSync(message);
                });
            }
            log.info("非凡阻断中ip同步任务执行结束");
        });
    }

    @Override
    public String getDataType() {
        return DataTypeEnum.FF_SAFE_IP_FILTER_BLOCKING.getCode();
    }
}
