<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TblCompanyMapper">

    <resultMap type="TblCompany" id="TblCompanyResult">
        <result property="dwid"    column="dwid"    />
        <result property="name"    column="name"    />
        <result property="bname"    column="bname"    />
        <result property="addr"    column="addr"    />
        <result property="nature"    column="nature"    />
        <result property="contacter"    column="contacter"    />
        <result property="phone"    column="phone"    />
        <result property="protocol1"    column="protocol1"    />
        <result property="protocol2"    column="protocol2"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblCompanyVo">
        select dwid, name, bname, addr, nature, contacter, phone, protocol1, protocol2, create_time, update_time from tbl_company
    </sql>

    <select id="selectTblCompanyList" parameterType="TblCompany" resultMap="TblCompanyResult">
        <include refid="selectTblCompanyVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="addr != null  and addr != ''"> and addr = #{addr}</if>
            <if test="contacter != null  and contacter != ''"> and contacter = #{contacter}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="protocol1 != null  and protocol1 != ''"> and protocol1 = #{protocol1}</if>
            <if test="protocol2 != null  and protocol2 != ''"> and protocol2 = #{protocol2}</if>
        </where>
    </select>

    <select id="selectTblCompanyByDwid" parameterType="Long" resultMap="TblCompanyResult">
        <include refid="selectTblCompanyVo"/>
        where dwid = #{dwid}
    </select>

    <select id="selectTblCompanyByDwids" parameterType="String" resultMap="TblCompanyResult">
        <include refid="selectTblCompanyVo"/>
        where dwid in
        <foreach item="dwid" collection="list" open="(" separator="," close=")">
            #{dwid}
        </foreach>
    </select>
    <select id="checkCanDeleteCompany" parameterType="long" resultType="int">
        select sum(t) from (
        select count(1) as t FROM tbl_company_doc where dwid =#{dwid}
        union select count(1) as t from tbl_company_emp where dwid =#{dwid}
        ) tmp
    </select>

    <select id="getcompanyNums" resultType="java.lang.Integer">
        select count(DISTINCT e.name) from tbl_company e
        <where>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <insert id="insertTblCompany" parameterType="TblCompany">
        insert into tbl_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dwid != null">dwid,</if>
            <if test="name != null">name,</if>
            <if test="addr != null">addr,</if>
            <if test="bname != null">bname,</if>
            <if test="nature != null">nature,</if>
            <if test="contacter != null">contacter,</if>
            <if test="phone != null">phone,</if>
            <if test="protocol1 != null">protocol1,</if>
            <if test="protocol2 != null">protocol2,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dwid != null">#{dwid},</if>
            <if test="name != null">#{name},</if>
            <if test="addr != null">#{addr},</if>
            <if test="bname != null">#{bname},</if>
            <if test="nature != null">#{nature},</if>
            <if test="contacter != null">#{contacter},</if>
            <if test="phone != null">#{phone},</if>
            <if test="protocol1 != null">#{protocol1},</if>
            <if test="protocol2 != null">#{protocol2},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblCompany" parameterType="TblCompany">
        update tbl_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="bname != null">bname = #{bname},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="contacter != null">contacter = #{contacter},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="protocol1 != null">protocol1 = #{protocol1},</if>
            <if test="protocol2 != null">protocol2 = #{protocol2},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dwid = #{dwid}
    </update>

    <delete id="deleteTblCompanyByDwid" parameterType="Long">
        delete from tbl_company where dwid = #{dwid}
    </delete>

    <delete id="deleteTblCompanyByDwids" parameterType="String">
        delete from tbl_company where dwid in
        <foreach item="dwid" collection="array" open="(" separator="," close=")">
            #{dwid}
        </foreach>
    </delete>

    <select id="selectEmployeeCountByCompany" resultType="CompanyEmpCountStatisticVo">
        SELECT
            c.name AS companyName,
            COUNT(e.eid) AS empCount
        FROM
            tbl_company_emp e
            INNER JOIN tbl_company c ON e.dwid = c.dwid
        GROUP BY
            c.dwid
    </select>
</mapper>
