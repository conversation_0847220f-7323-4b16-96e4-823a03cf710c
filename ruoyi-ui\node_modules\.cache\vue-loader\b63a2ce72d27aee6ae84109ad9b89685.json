{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue?vue&type=style&index=0&id=4f88bc19&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue", "mtime": 1756794280235}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgIi4uLy4uLy4uL2Fzc2V0cy9zdHlsZXMvdGFicy5zY3NzIjsKCi5kaXYtbWFpbi1jb250YWluZXIgewogIGhlaWdodDogY2FsYygxMDAlIC0gMTE5cHgpCn0KCi5kaXYtbWFpbi1jb250YWluZXItdGFicyB7CiAgaGVpZ2h0OiBjYWxjKDEwMCUgLSAxNjBweCk7CiAgbWFyZ2luLXRvcDogM3B4Owp9Cgo6OnYtZGVlcC5lbC1zZWxlY3QgewogIHdpZHRoOiAxMDAlOwogIC5lbC1zZWxlY3QtZHJvcGRvd24gewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAzMHB4ICFpbXBvcnRhbnQ7CiAgICBsZWZ0OiA1cHg7CiAgICAuZWwtc2Nyb2xsYmFyIHsKICAgICAgbWF4LWhlaWdodDogMzAwcHg7CiAgICAgIG92ZXJmbG93LXk6IGF1dG87CiAgICB9CiAgfQp9CgoubG9vcF9kaWFsb2cgewogIGhlaWdodDogOTB2aDsKICBvdmVyZmxvdzogaGlkZGVuOwogIDo6di1kZWVwIC5lbC1kaWFsb2cgewogICAgaGVpZ2h0OiAxMDAlOwogICAgLmVsLWRpYWxvZ19fYm9keSB7CiAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gMTEwcHgpOwogICAgICBwYWRkaW5nOiAxMHB4IDIwcHggMDsKICAgICAgb3ZlcmZsb3c6IGF1dG87CiAgICB9CiAgfQp9CgouYXNzZXQtdGFnIHsKICBtYXJnaW4tbGVmdDogNXB4OwogIG1heC13aWR0aDogMzUlOwogIG92ZXJmbG93OiBoaWRkZW47CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOwp9Cgoub3ZlcmZsb3ctdGFnOm5vdCg6Zmlyc3QtY2hpbGQpIHsKICBtYXJnaW4tdG9wOiA1cHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/frailty/index", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"div-main-top\">\n        <div class=\"div-main-top-one\">\n          <div class=\"main-top-one-left\"><img class=\"img-style\" src=\"../../../assets/images/fengxianzongshu.png\"></div>\n          <div class=\"main-top-one-right\">\n            <div class=\"main-top-one-right-top\">{{ totalNumberOfRisks }}</div>\n            <div class=\"main-top-one-right-bottom\">风险总数</div>\n          </div>\n        </div>\n        <div class=\"div-main-top-two\" :style=\"{borderColor: borderNum === 1 ? '#637fef' : '#fff'}\" tabindex=\"0\" role=\"button\" id=\"autoFocusBox\" @click=\"toLoophole()\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">主机风险</div>\n            <div class=\"top-title-right\">{{hostRisk.hostRiskNum}}</div>\n          </div>\n          <div class=\"main-top-two-bottom\">\n            <div :class=\"currentIndex === 'ip'+item.severity ? 'icons-title-count icons-title-count-active':'icons-title-count'\" v-for=\"(item,index) in hostRiskList\" @click.stop=\"toLoopholeByType(item.severity)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" :src=\"item.img\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ item.num }}</div>\n                <div class=\"icons-title-count-bottom\">{{ item.title }}</div>\n              </div>\n            </div>\n            <div :class=\"currentIndex == 'ip'+'4' ? 'icons-title-count icons-title-count-active':'icons-title-count'\" @click.stop=\"toWeakPassword()\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/ruokoling.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ hostRisk.weakPasswordsNum }}</div>\n                <div class=\"icons-title-count-bottom\">弱口令</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"div-main-top-three\" :style=\"{borderColor: borderNum === 2 ? '#637fef' : '#fff'}\" tabindex=\"0\" @click=\"toWebvulnPage()\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">Web风险</div>\n            <div class=\"top-title-right\">{{ webRisk.webRiskNum }}</div>\n          </div>\n          <div class=\"main-top-three-bottom\">\n            <div :class=\"currentIndex === 'web'+index ? 'top-three-bottom-body icons-title-count icons-title-count-active' : 'top-three-bottom-body icons-title-count'\" v-for=\"(item,index) in webRiskList\" @click.stop=\"toWebvulnByType(item.severity,index)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" :src=\"item.img\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ item.num }}</div>\n                <div class=\"icons-title-count-bottom\">{{ item.title }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"div-main-top-four\" :style=\"{borderColor: borderNum === 3 ? '#637fef' : '#fff'}\" tabindex=\"0\" @click=\"toMonitorIpPage(4)\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">漏扫任务</div>\n            <div class=\"top-title-right\">{{ vulnerabilityScanning.vulnerabilityScanningNum }}</div>\n          </div>\n          <div class=\"main-top-four-bottom\">\n            <div :class=\"currentIndex === 'monitorIp'+4 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'\" @click.stop=\"toMonitorIpPage(4)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/zhuji.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ vulnerabilityScanning.hostScanningNum }}</div>\n                <div class=\"icons-title-count-bottom\">主机漏扫</div>\n              </div>\n            </div>\n            <div :class=\"currentIndex === 'monitorWeb'+5 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'\" @click.stop=\"toMonitorWeb(5)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/weblousao.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ vulnerabilityScanning.webVulnerabilityScanningNum }}</div>\n                <div class=\"icons-title-count-bottom\">Web漏扫</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <el-tabs v-if=\"listType === 4 || listType === 5\" v-model=\"activeName\" class=\"tabs-style\">\n        <el-tab-pane :label=\"listType === 4 ? '主机漏扫任务' : 'Web漏扫任务'\" name=\"task\" />\n        <el-tab-pane :label=\"listType === 4 ? '主机漏扫记录' : 'Web漏扫记录'\" name=\"record\" />\n      </el-tabs>\n      <div :class=\"(listType === 4 || listType === 5) ? 'div-main-container-tabs' : 'div-main-container'\">\n        <index v-if=\"listType === 1\" :severity.sync=\"loopholeSeverity\" :toParams=\"toParams\" @severityChange=\"severityChange\"/>\n        <webvuln v-if=\"listType === 2\" :severity=\"webvulnSeverity\" :toParams=\"toParams\"/>\n        <Index2 v-if=\"listType  === 3\" :toParams=\"toParams\"/>\n        <Job v-if=\"listType === 4 && activeName === 'task'\" :toParams=\"toParams\"/>\n        <MonitorWeb v-if=\"listType === 5 && activeName === 'task'\" :toParams=\"toParams\"/>\n        <LeakyRecord\n          v-if=\"(listType === 4 || listType === 5) && activeName === 'record'\"\n          :list-type=\"listType\"\n          :toParams=\"toParams\"/>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CreateWork from \"../../todoItem/todo/createWork\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'\nimport FlowTemplateSelect from \"@/components/FlowTemplateSelect/index.vue\";\nimport CronInput from '@/components/CronInput/index.vue'\nimport LeakScanDialog from \"@/views/safe/server/components/LeakScanDialog.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport { getVulnerabilityRiskHeadCount } from \"@/api/threat/threat\";\nimport Webvuln from \"@/views/frailty/webvuln/index.vue\";\nimport Index from \"@/views/frailty/loophole/index.vue\";\nimport Index2 from \"@/views/frailty/weakPassword/index.vue\";\nimport Job from \"@/views/frailty/monitor/monitorIp.vue\";\nimport MonitorWeb from \"@/views/frailty/monitor/monitorWeb.vue\"\nimport LeakyRecord from \"@/views/frailty/monitor/leakyRecord.vue\"\nexport default {\n  components: {\n    Index,\n    Index2,\n    MonitorWeb,\n    Job,\n    Webvuln,\n    DeptSelect,\n    LeakScanDialog,\n    CronInput,\n    CreateWork,\n    newAddloophole,\n    FlowBox,\n    FlowTemplateSelect,\n    LeakyRecord,\n    SystemList: () => import('../../../components/SystemList')\n  },\n  dicts: [\"loophole_category\", \"synchronization_status\"],\n  data() {\n    return {\n      toParams: {},\n      webvulnSeverity:null,\n      loopholeSeverity:null,\n      listType:1,\n      borderNum: 1, // 保持选中固定值\n      hostRisk:{}, // 主机风险对象\n      webRisk:{}, // web风险对象\n      vulnerabilityScanning:{}, // 漏洞扫描对象\n      totalNumberOfRisks:0, // 风险总数\n      hostRiskList: [\n        {\n          severity:4,\n          title:\"可入侵漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      webRiskList: [\n        {\n          severity:4,\n          title:\"严重漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      activeName: 'task',\n      userList: [],\n      currentIndex: ''\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      if (!this.$route.query.type) {\n        let el = document.getElementById('autoFocusBox');\n        el.focus();\n      }\n    });\n  },\n  created() {\n    this.initData();\n  },\n  watch: {\n    $route: {\n      handler(newVal, oldVal) {\n        // 监听所有路由变化\n        if (newVal.query.referenceId) {\n          // 扣分详情跳转判断\n          if (newVal.query.type === '1') {\n            this.toLoophole();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          if (newVal.query.type === '2') {\n            this.toWebvulnPage();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          if (newVal.query.type === '3') {\n            this.toWeakPassword();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          this.$router.replace({});\n        }\n        if (newVal.query.type === '4') {\n          this.toMonitorIpPage(Number(newVal.query.type),{id: newVal.query.id})\n          const query = { ...this.$route.query }; // 复制当前查询对象\n          delete query.type;                      // 删除目标参数\n          delete query.id;                      // 删除目标参数\n          this.$router.replace({ query });         // 替换当前路由（URL更新）\n        } else if (newVal.query.type === '5') {\n          this.toMonitorWeb(Number(newVal.query.type),{\n            id: newVal.query.id,\n            cronTransfer: newVal.query.cronTransfer,\n            invokeTarget: newVal.query.invokeTarget,\n            jobName: newVal.query.jobName\n          })\n          const query = { ...this.$route.query }; // 复制当前查询对象\n          delete query.type;                      // 删除目标参数\n          delete query.id;                      // 删除目标参数\n          this.$router.replace({ query });         // 替换当前路由（URL更新）\n        }\n\n        if (newVal.query.type) {\n          this.$nextTick(() => {\n            // 精确选择当前组件内的目标元素\n            const targetElement = this.$el.querySelector('.div-main-top-four[tabindex]')\n            if (targetElement) {\n              // 先移除其他元素的焦点\n              document.activeElement?.blur?.()\n              // 添加延迟确保渲染完成\n              setTimeout(() => {\n                targetElement.focus()\n                // 添加自定义聚焦样式\n                targetElement.classList.add('force-focus')\n              }, 50)\n            }\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    toWeakPassword(){\n      this.currentIndex = 'ip'+'4';\n      this.listType = 3;\n      this.borderNum = 1;\n      //this.currentIndex = ''\n    },\n    toLoophole(){\n      this.loopholeSeverity = null;\n      this.listType = 1;\n      this.borderNum = 1;\n      this.currentIndex = '';\n    },\n    toLoopholeByType(type){\n      this.currentIndex = 'ip'+type;\n      this.loopholeSeverity = type;\n      this.listType = 1;\n      this.borderNum = 1;\n    },\n    toWebvulnPage(){\n      this.listType = 2;\n      this.borderNum = 2;\n      this.currentIndex = ''\n      this.webvulnSeverity = null;\n    },\n    toWebvulnByType(type,index){\n      this.webvulnSeverity = type;\n      this.currentIndex = 'web'+index;\n      this.listType = 2;\n      this.borderNum = 2;\n    },\n    toMonitorIpPage(index,params){\n      this.listType = index;\n      this.borderNum = 3;\n      this.currentIndex = 'monitorIp'+index;\n      this.toParams = params;\n      this.activeName = 'task'\n    },\n    toMonitorWeb(index,params){\n      this.listType = index;\n      this.borderNum = 3;\n      this.currentIndex = 'monitorWeb'+index;\n      this.toParams = params;\n      this.activeName = 'task'\n    },\n    initData() {\n      getVulnerabilityRiskHeadCount().then(res => {\n        if (res.data){\n          this.hostRisk = res.data.hostRisk;\n          this.webRisk = res.data.webRisk;\n          this.vulnerabilityScanning = res.data.vulnerabilityScanning;\n          this.totalNumberOfRisks = res.data.totalNumberOfRisks;\n          //遍历hostRiskList\n          this.hostRiskList.forEach(e => {\n           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n           if (num.length == 0){\n             e.num = 0\n           }else {\n             e.num = num[0].num\n           }\n          })\n          this.webRiskList.forEach(e => {\n            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n            if (num.length == 0){\n              e.num = 0\n            }else {\n              e.num = num[0].num\n            }\n          })\n        }\n      })\n    },\n    severityChange(currentIndex){\n      this.currentIndex = currentIndex;\n    },\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import \"../../../assets/styles/tabs.scss\";\n\n.div-main-container {\n  height: calc(100% - 119px)\n}\n\n.div-main-container-tabs {\n  height: calc(100% - 160px);\n  margin-top: 3px;\n}\n\n::v-deep.el-select {\n  width: 100%;\n  .el-select-dropdown {\n    position: absolute;\n    top: 30px !important;\n    left: 5px;\n    .el-scrollbar {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n  }\n}\n\n.loop_dialog {\n  height: 90vh;\n  overflow: hidden;\n  ::v-deep .el-dialog {\n    height: 100%;\n    .el-dialog__body {\n      height: calc(100% - 110px);\n      padding: 10px 20px 0;\n      overflow: auto;\n    }\n  }\n}\n\n.asset-tag {\n  margin-left: 5px;\n  max-width: 35%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n</style>\n"]}]}