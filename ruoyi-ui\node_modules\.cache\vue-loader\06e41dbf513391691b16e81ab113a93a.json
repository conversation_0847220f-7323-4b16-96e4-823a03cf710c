{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\loophole\\index.vue?vue&type=style&index=0&id=84a5ad5a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\loophole\\index.vue", "mtime": 1756794280289}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKOjp2LWRlZXAuZWwtc2VsZWN0IHsKICB3aWR0aDogMTAwJTsKCiAgLmVsLXNlbGVjdC1kcm9wZG93biB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDMwcHggIWltcG9ydGFudDsKICAgIGxlZnQ6IDVweDsKICAgIC5lbC1zY3JvbGxiYXIgewogICAgICBtYXgtaGVpZ2h0OiAzMDBweDsKICAgICAgb3ZlcmZsb3cteTogYXV0bzsKICAgIH0KICB9Cn0KCi5sb29wX2RpYWxvZyB7CiAgaGVpZ2h0OiA5MHZoOwogIG92ZXJmbG93OiBoaWRkZW47CiAgOjp2LWRlZXAgLmVsLWRpYWxvZyB7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICAuZWwtZGlhbG9nX19ib2R5IHsKICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSAxMTBweCk7CiAgICAgIHBhZGRpbmc6IDEwcHggMjBweCAwOwogICAgICBvdmVyZmxvdzogYXV0bzsKICAgIH0KICB9Cn0KCi5hc3NldC10YWcgewogIG1hcmdpbi1sZWZ0OiA1cHg7CiAgbWF4LXdpZHRoOiAzNSU7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7Cn0KCi5vdmVyZmxvdy10YWc6bm90KDpmaXJzdC1jaGlsZCkgewogIG1hcmdpbi10b3A6IDVweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqsCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/frailty/loophole", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"100px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"漏洞名称\" prop=\"title\">\n                <el-input\n                  v-model=\"queryParams.title\"\n                  placeholder=\"请输入内容\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"漏洞类型\" prop=\"category\">\n                <!--<el-input-->\n                <!--v-model=\"queryParams.category\"-->\n                <!--placeholder=\"请输入内容\"-->\n                <!--clearable-->\n                <!--@keyup.enter.native=\"handleQuery\"-->\n                <!--/>-->\n                <el-select\n                  v-model=\"queryParams.category\"\n                  placeholder=\"请选择漏洞类型\"\n                  :popper-append-to-body=\"false\"\n                >\n                  <el-option\n                    v-for=\"dict in typeList\"\n                    :key=\"dict.category\"\n                    :label=\"dict.category\"\n                    :value=\"dict.category\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"影响资产主IP\" prop=\"hostIp\">\n                <el-input\n                  v-model=\"queryParams.hostIp\"\n                  placeholder=\"请输入内容\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button\n                >\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置\n                </el-button\n                >\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-show=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <!--                <el-select\n                                  v-model=\"queryParams.deptId\"\n                                  placeholder=\"请选择所属部门\"\n                                  filterable\n                                  clearable\n                                >\n                                  <el-option\n                                    v-for=\"item in deptOptions\"\n                                    :key=\"item.deptId\"\n                                    :label=\"item.deptName\"\n                                    :value=\"item.deptId\"\n                                  ></el-option>\n                                </el-select>-->\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\" prop=\"title\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\" prop=\"title\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-show=\"showAll\" :gutter=\"10\">\n<!--            <el-col :span=\"24\" v-if=\"rickLevelList.length\">\n              <el-form-item label=\"漏洞等级\">\n                <SystemList\n                  ref=\"systemList2\"\n                  :systemTypes=\"rickLevelList\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.severity\"\n                />\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\" v-if=\"handleStateList.length\">\n              <el-form-item label=\"处置状态\">\n                <SystemList\n                  ref=\"systemList1\"\n                  :systemTypes=\"handleStateList\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.handleState\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">主机漏洞列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['frailty:loophole:add']\"\n                >新增\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"addOrUpdateFlowHandleBatch(null,null)\"\n                  v-hasPermi=\"['frailty:loophole:edit']\"\n                >创建通报\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"showHandleBatch\"\n                  v-hasPermi=\"['frailty:loophole:edit']\"\n                >批量处置\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"single\"\n                  @click=\"handleAddJob\"\n                  v-hasPermi=\"['frailty:loophole:check']\"\n                >复查\n                </el-button>\n              </el-col>\n              <!--<el-col :span=\"1.5\">-->\n              <!--<el-button-->\n              <!--class=\"btn1\"-->\n              <!--size=\"small\"-->\n              <!--@click=\"handleScan\"-->\n              <!--&gt;IP漏洞扫描-->\n              <!--</el-button>-->\n              <!--</el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table height=\"100%\" v-loading=\"loading\" :data=\"assetFrailtyList\" ref=\"multipleTable\"\n                  @selection-change=\"handleSelectionChange\">\n          <el-table-column\n            type=\"selection\"\n            width=\"55\">\n          </el-table-column>\n          <el-table-column\n            label=\"漏洞名称\"\n            prop=\"title\"\n            min-width=\"300\"\n            show-overflow-tooltip\n          />\n          <el-table-column\n            label=\"漏洞类型\"\n            prop=\"category\"\n            width=\"150\"\n          >\n            <template slot-scope=\"scope\">\n              <dict-tag\n                :options=\"dict.type.loophole_category\"\n                :value=\"scope.row.category\"\n              />\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"漏洞等级\"\n            prop=\"severity\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.severity == 0\" type=\"info\">未知</el-tag>\n              <el-tag v-if=\"scope.row.severity == 1\" type=\"success\"\n              >低危\n              </el-tag\n              >\n              <el-tag v-if=\"scope.row.severity == 2\" type=\"primary\"\n              >中危\n              </el-tag\n              >\n              <el-tag v-if=\"scope.row.severity == 3\" type=\"warning\"\n              >高危\n              </el-tag\n              >\n              <el-tooltip v-if=\"scope.row.severity === 4\" placement=\"top-end\" content=\"可入侵漏洞\" effect=\"light\">\n                <el-tag type=\"danger\">严重</el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"影响资产主IP\" prop=\"hostIp\" width=\"220\" show-overflow-tooltip>\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.hostIp }}</span>\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 9\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 10\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"端口\"\n            prop=\"hostPort\"\n            width=\"120\"\n          />\n          <el-table-column\n            label=\"所属部门\"\n            prop=\"deptName\"\n            align=\"center\"\n            width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.deptName ? unique(scope.row.deptName.split(',')).join(',') : '-' }}</span>\n            </template>\n          </el-table-column>\n          <!--<el-table-column-->\n          <!--label=\"协议\"-->\n          <!--prop=\"protocol\"-->\n          <!--width=\"100\"-->\n          <!--/>-->\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"120\" :formatter=\"disposerFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"120\" :formatter=\"handleStateFormatter\"/>\n          <!--<el-table-column-->\n          <!--label=\"通报状态\"-->\n\n          <!--prop=\"flowState\"-->\n          <!--width=\"120\"-->\n          <!--:formatter=\"flowStateFormatter\"-->\n          <!--/>-->\n\n          <el-table-column\n            label=\"数据来源\"\n            prop=\"dataSource\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.dataSource == '1'\">探测</span>\n              <span v-else-if=\"scope.row.dataSource == '2'\">手动</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"发现次数\"\n            prop=\"scanNum\"\n            width=\"100\"\n          />\n          <!--        <el-table-column label=\"发现漏洞次数\"  width=\"120\" prop=\"scanNum\"/>-->\n          <el-table-column\n            label=\"最近漏洞时间\"\n            prop=\"updateTime\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.updateTime, \"{y}-{m}-{d} {h}:{i}:{s}\") }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"120\" :formatter=\"syncStatusFormatter\" />\n          <el-table-column\n            label=\"操作\"\n            width=\"300\"\n            fixed=\"right\"\n            class-name=\"small-padding fixed-width\"\n            :show-overflow-tooltip=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:query']\"\n              >详情\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId == null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleEdit(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId == null && !(scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['frailty:loophole:remove']\"\n              >删除\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"showHandle(scope.row)\"\n                         v-hasPermi=\"['frailty:loophole:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                v-if=\"scope.row.flowState == null && (scope.row.handleState === '2' || scope.row.handleState === '0')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"addOrUpdateFlowHandle(null, null, scope.row)\"\n                v-hasPermi=\"['frailty:loophole:edit']\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"批量处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleBatchForm\">确 定</el-button>\n        <el-button @click=\"showHandleBatchDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <el-dialog\n      v-if=\"workDialog\"\n      title=\"创建通报\"\n      :visible.sync=\"workDialog\"\n      width=\"80%\"\n      append-to-body\n    >\n      <create-work\n        v-if=\"workDialog\"\n        :work-type=\"'0'\"\n        :m-id=\"gapId\"\n        @closeWork=\"closeWork\"\n      />\n    </el-dialog>\n    <el-dialog\n      v-if=\"showAddloophole\"\n      :title=\"title\"\n      :visible.sync=\"showAddloophole\"\n      class=\"loop_dialog\"\n      width=\"60%\"\n      append-to-body\n    >\n      <new-addloophole\n        v-if=\"showAddloophole\"\n        :loophole-data=\"loopholeData\"\n        :editable=\"editable\"\n        @cancel=\"canceloophole()\"\n        @confirm=\"confirmVulnDeal()\"\n      />\n    </el-dialog>\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"colseFlow\"/>\n    <flow-template-select\n      :show.sync=\"flowTemplateSelectVisible\"\n      @change=\"flowTemplateSelectChange\"\n    />\n    <LeakScanDialog\n      :title=\"title\"\n      :edit-form=\"editForm\"\n      edit-title=\"IP漏洞扫描\"\n      :is-disabled=\"isDisabled\"\n      @getList=\"getList\"\n      :scan-strategy-visible.sync=\"scanStrategyVisible\"/>\n    <div v-if=\"listType === 2\">\n      <webvuln/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  delDeal,\n  getVulnDealList, handleVuln,\n  updateVulnDeal,\n} from \"@/api/monitor2/assetFrailty\";\nimport CreateWork from \"../../todoItem/todo/createWork\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'\nimport FlowTemplateSelect from \"@/components/FlowTemplateSelect/index.vue\";\nimport {updateAlarm} from \"@/api/threaten/threatenWarn\";\nimport CronInput from '@/components/CronInput/index.vue'\nimport LeakScanDialog from \"@/views/safe/server/components/LeakScanDialog.vue\";\nimport {handleBatchVuln} from '../../../api/monitor2/assetFrailty'\nimport {getDepts} from '../../../api/monitor2/wpresult'\nimport {addJob} from '../../../api/safe/monitor'\nimport {getHandleStateVulnStat, getHostVulnTypeList, getRickLevelVulnStat} from '../../../api/monitor2/vulnResult'\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport { listUser } from \"@/api/system/user\";\nimport { getVulnerabilityRiskHeadCount } from \"@/api/threat/threat\";\nimport Webvuln from \"@/views/frailty/webvuln/index.vue\";\n\nexport default {\n  name: \"index\",\n  components: {\n    Webvuln,\n    DeptSelect, LeakScanDialog, CronInput, CreateWork, newAddloophole, FlowBox, FlowTemplateSelect,\n    SystemList: () => import('../../../components/SystemList')\n  },\n  props: {\n    severity:{\n      type: Number,\n      default: null\n    },\n    toParams: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  dicts: [\"loophole_category\", \"synchronization_status\"],\n  data() {\n    return {\n      listType:1,\n      hostRisk:{}, // 主机风险对象\n      webRisk:{}, // web风险对象\n      vulnerabilityScanning:{}, // 漏洞扫描对象\n      totalNumberOfRisks:0, // 风险总数\n      hostRiskList: [\n        {\n          severity:4,\n          title:\"可入侵漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      webRiskList: [\n        {\n          severity:4,\n          title:\"严重漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: ''\n      },\n      handleRules: {},\n      showAll: false,\n      flowVisible: false,\n      editable: true,\n      loopholeData: null,\n      showAddloophole: false,\n      showHandleBatchDialog: false,\n      title: '',\n      DealStatusData: {},\n      loading: false,\n      // 选中数组\n      ids: [],\n      // 选中数组对象\n      rows: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      handleStateOption: [\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        }\n      ],\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      queryParams: {\n        title: '',\n        category: '',\n        severity: '',\n        hostIp: '',\n        handleState: null,\n        hostPort: '',\n        dealStatus: '',\n        domainId: '',\n        pageNum: 1,\n        pageSize: 10\n      },\n      total: 10,\n      assetFrailtyList: [],\n      deptOptions: [],\n      handleStateList: [],\n      rickLevelList: [],\n      typeList: [],\n      openDialog: false,\n      vulnId: null,\n      workDialog: false,\n      gapId: '',\n      workId: '',\n      flowTemplateSelectVisible: false,\n      currentFlowData: null,\n      scanStrategyVisible: false,\n      editForm: {},\n      form: {\n        jobGroup: 'ASSET_SCAN',\n        jobType: 1,\n        cronExpression: '* * * * * ?',\n        period: 0,\n        status: '0',\n        cronTransfer: '立即执行'\n      },\n      isDisabled: false\n    }\n  },\n  created() {\n    if (this.$route.query.hostIp) {\n      this.queryParams.hostIp = this.$route.query.hostIp\n    }\n    if (this.$route.query.severity) {\n      this.queryParams.severity = this.$route.query.severity;\n      this.$emit('severityChange','ip'+this.$route.query.severity);\n    }\n    if (this.$route.query.handleState) {\n      this.queryParams.handleState = this.$route.query.handleState\n    }\n    this.initDept();\n    this.initData();\n    this.handleQuery();\n  },\n  watch: {\n    '$route.query': {\n      handler(val) {\n        if (val) {\n          this.queryParams.domainId = val.domainId\n        }\n      },\n      deep: true,\n      immediate: true\n    },\n    severity : {\n      handler(val) {\n        if (val) {\n          this.queryParams.severity = val\n        }else {\n          this.queryParams.severity = null\n        }\n        this.handleQuery()\n      },\n      immediate: false\n    },\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.referenceId){\n          this.queryParams.referenceId = newVal.referenceId\n          this.handleQuery()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    initData() {\n      /*getVulnerabilityRiskHeadCount().then(res => {\n        if (res.data){\n          this.hostRisk = res.data.hostRisk;\n          this.webRisk = res.data.webRisk;\n          this.vulnerabilityScanning = res.data.vulnerabilityScanning;\n          this.totalNumberOfRisks = res.data.totalNumberOfRisks;\n          //遍历hostRiskList\n          this.hostRiskList.forEach(e => {\n           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n           e.num = num[0].num\n          })\n          this.webRiskList.forEach(e => {\n            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n            e.num = num[0].num\n          })\n        }\n      })*/\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n      this.handleStateList = []\n      this.rickLevelList = []\n      this.typeList = []\n      getHandleStateVulnStat().then(res => {\n        res.data.forEach(e => {\n          if (e.handle_state === 0) {\n            const obj1 = {\n              dictValue: 0,\n              dictLabel: '未处置',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 1) {\n            const obj1 = {\n              dictValue: 1,\n              dictLabel: '已处置',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 2) {\n            const obj1 = {\n              dictValue: 2,\n              dictLabel: '忽略',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n          if (e.handle_state === 3) {\n            const obj1 = {\n              dictValue: 3,\n              dictLabel: '处置中',\n              count: e.num\n            }\n            this.handleStateList.push(obj1)\n          }\n        })\n      })\n      getRickLevelVulnStat().then(res => {\n        res.data.forEach(e => {\n          /*if (e.severity === 0) {\n            const obj1 = {\n              dictValue: 0,\n              dictLabel: '未知',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }*/\n          if (e.severity === 1) {\n            const obj1 = {\n              dictValue: 1,\n              dictLabel: '低危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 2) {\n            const obj1 = {\n              dictValue: 2,\n              dictLabel: '中危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 3) {\n            const obj1 = {\n              dictValue: 3,\n              dictLabel: '高危',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n          if (e.severity === 4) {\n            const obj1 = {\n              dictValue: 4,\n              dictLabel: '严重',\n              count: e.num\n            }\n            this.rickLevelList.push(obj1)\n          }\n        })\n      })\n\n      getHostVulnTypeList().then(res => {\n        this.typeList = res.data\n      })\n    },\n    initDept() {\n      getDepts(this.handleForm).then(res => {\n        this.deptOptions = res.data\n      })\n    },\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n    syncStatusFormatter(row, column, cellValue, index){\n      const label = this.dict.type.synchronization_status.find(item => item.value === cellValue)\n      return label ? label.label : ''\n    },\n    handleAddJob() {\n      if (!this.single) {\n        if (this.rows.length > 0) {\n          let ips = this.rows[0].hostIp.split(/\\n/g);\n          // 调用目标字符串\n          this.form.invokeIp = ips.join(';')\n          this.form.jobName = this.rows[0].hostIp + '漏扫复查任务_' + new Date().getTime();\n        }\n        this.form.invokeTarget = 'HostVulnScan.scan(\\'${jobId}\\',\\'' + this.form.jobName + '|' + this.form.invokeIp + '|1|1|1' + '\\')'\n\n        const from = {...this.form }\n        this.$modal.confirm('是否确认创建【' + this.rows[0].hostIp + '】的漏扫复查任务？').then(function () {\n          return addJob(from);\n        }).then(() => {\n          this.getList();\n          this.$modal.msgSuccess(\"复核任务新增成功\");\n        }).catch(() => {\n        });\n      }\n    },\n    submitHandleForm() {\n      handleVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    showHandle(row) {\n      this.handleForm = {};\n      this.handleForm = {...row};\n      if (this.handleForm.handleState === '0') {\n        this.handleForm.handleState = null\n      }\n      this.handleForm.assetName = ''\n      this.showHandleDialog = true;\n    },\n    showHandleBatch() {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState === '0' || item.handleState === '2' || item.handleState === null);\n      if (rows.length < this.rows.length) {\n        this.$message.error('选择中有已处置或处置中事件，无法批量处置');\n        return false;\n      }\n      this.handleForm = {};\n      if (rows.length === 1) {\n        if (rows[0].handleState === '2') {\n          this.handleForm = rows[0]\n        }\n      }\n      // this.handleForm.id=row.id;\n      this.handleForm.ids = this.ids;\n      this.showHandleBatchDialog = true;\n    },\n    submitHandleBatchForm() {\n      handleBatchVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleBatchDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    handleAdd() {\n      this.showAddloophole = true;\n      this.editable = true;\n      this.loopholeData = null;\n      this.title = '新增主机漏洞事件';\n    },\n    handleEdit(row) {\n      this.showAddloophole = true;\n      this.editable = true;\n      this.loopholeData = row;\n      this.title = '修改主机漏洞事件';\n    },\n    handleScan() {\n      this.title = '添加任务';\n      this.editForm = {}\n      this.editForm.jobType = 1;\n      this.editForm.weakPw = '1';\n      this.editForm.status = '0';\n      this.editForm.cronExpression = '* * * * * ?';\n      this.editForm.period = 0;\n      this.editForm.cronTransfer = '立即执行';\n      this.scanStrategyVisible = true;\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const vulnNames = row.title;\n      this.$modal\n        .confirm(\"是否确认删除漏洞名称为【\" + vulnNames + \"】的数据项？\")\n        .then(function () {\n          return delDeal(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.initData();\n          this.$modal.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {\n        });\n    },\n    canceloophole() {\n      this.showAddloophole = false;\n    },\n    confirmVulnDeal() {\n      this.showAddloophole = false;\n      this.getList();\n      this.initData();\n    },\n    // updateDealStatus(row){\n    //   this.DealStatusData=row;\n    //   this.$modal.confirm('是否确定更新漏洞处理状态？', '提示').then(() => {\n    //     return updateVulnDeal(this.DealStatusData).then(()=>{\n    //       this.$message.success('修改成功');\n    //       // this.handleQuery();\n    //     }).catch(()=>{\n    //       this.$message.warning('调用接口超时');\n    //     });\n    //   }).catch(() => {\n    //     this.$message.info('已取消');\n    //     this.handleQuery();\n    //   });\n    // },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n      this.total = 0;\n      this.getList();\n    },\n    resetQuery() {\n      this.queryParams = {\n        title: '',\n        category: '',\n        severity: this.$props.severity,\n        hostIp: '',\n        referenceId: '',\n        handleState: null,\n        hostPort: '',\n        handleStatus: '',\n        domainId: '',\n        pageNum: 1,\n        pageSize: 10\n      };\n      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();\n      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();\n      this.getList();\n    },\n    getList() {\n      this.loading = true;\n      let tempQueryParams = {...(this.queryParams), handleType: ''};\n      getVulnDealList(tempQueryParams).then(response => {\n        this.assetFrailtyList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    selectable(row) {\n      if (row.handleState === '1' || row.handleState === '3') {\n        return false\n      } else {\n        return true\n      }\n    },\n    handleExport() {\n      this.download('/monitor2/vulndeal/export', {\n        ...this.queryParams\n      }, `IP漏洞_${new Date().getTime()}.xlsx`)\n    },\n    handleDetail(row) {\n      this.showAddloophole = true;\n      this.editable = false;\n      this.loopholeData = row;\n      this.title = '查看主机漏洞事件';\n    },\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    createWork(row) {\n      this.gapId = row.id\n      this.workDialog = true\n    },\n    closeWork() {\n      this.workDialog = false\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        // flowtemplatejson id\n        // flowId: '564350702671937349',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '0';\n      data.row.eventType = 1;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if(flowId){\n          this.getFlowEngineInfo(flowId);\n        }else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    addOrUpdateFlowHandleBatch(id, flowState) {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState === '0' || item.handleState === null);\n      if (!rows || rows.length < 1) {\n        this.$message.error('未选择未处置事件，无法批量创建通报');\n        return false;\n      }\n      let data = {\n        id: id || '',\n        // flowtemplatejson id\n        // flowId: '564350702671937349',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        rows: rows,\n        isWork: true\n      }\n      data.rows[0].workType = '0';\n      data.rows[0].eventType = 1;\n      data.originType = 'event';\n      this.currentFlowData = data;\n\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if(flowId){\n          this.getFlowEngineInfo(flowId);\n        }else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val){\n      FlowEngineInfo(val).then(res => {\n        if(res.data && res.data.flowTemplateJson){\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if(!data[0].flowId){\n            this.$message.error('该流程模板异常,请重新选择');\n          }else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n    },\n    colseFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n      this.initData();\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n    unique(arr) {\n      for (let i = 0; i < arr.length; i++) {\n        for (let j = i + 1; j < arr.length; j++) {\n          if (arr[i] === arr[j]) {\n            //如果第一个等同于第二个，splice方法删除第二个\n            arr.splice(j, 1);\n\n            j--;\n          }\n        }\n      }\n      return arr;\n    },\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n::v-deep.el-select {\n  width: 100%;\n\n  .el-select-dropdown {\n    position: absolute;\n    top: 30px !important;\n    left: 5px;\n    .el-scrollbar {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n  }\n}\n\n.loop_dialog {\n  height: 90vh;\n  overflow: hidden;\n  ::v-deep .el-dialog {\n    height: 100%;\n    .el-dialog__body {\n      height: calc(100% - 110px);\n      padding: 10px 20px 0;\n      overflow: auto;\n    }\n  }\n}\n\n.asset-tag {\n  margin-left: 5px;\n  max-width: 35%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n</style>\n"]}]}