package cn.anmte.aqsoc.work.task;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.service.ITblOperateWorkRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-27 10:34
 */
@Component
@Slf4j
public class WorkHwInitTask {
    @Resource
    private IWorkHwTaskService workHwTaskService;
    @Resource
    private ITblOperateWorkRecordService operateWorkRecordService;

    @PostConstruct
    public void init(){
        ThreadUtil.execute(() -> {
            ThreadUtil.sleep(3000); //延迟3秒启动
            startDelayTask();
        });
    }

    private void startDelayTask() {
        DateTime nowDate = DateUtil.date();
        WorkHwTask query = new WorkHwTask();
        query.setStartTime(nowDate);
        List<WorkHwTask> list = workHwTaskService.selectWorkHwTaskList(query);
        if(CollUtil.isNotEmpty(list)){
            log.info("启动HW定时任务，数量:" + list.size());
            list.forEach(workHwTask -> workHwTaskService.startDelayTask(workHwTask));
        }
    }

    @Scheduled(fixedDelay = 1000*60*5)
    public void checkTask(){
        List<WorkHwTask> taskList = workHwTaskService.list(new LambdaQueryWrapper<WorkHwTask>().isNotNull(WorkHwTask::getFlowTaskId).isNull(WorkHwTask::getCompleteTime));
        if(CollUtil.isNotEmpty(taskList)){
            //查询是否已经完成
            DateTime nowDate = DateUtil.date();
            TblOperateWorkRecord operateWorkRecord = new TblOperateWorkRecord();
            operateWorkRecord.setQueryAll(true);
            operateWorkRecord.setQueryAllData(true);
            operateWorkRecord.setFlowTaskIdList(taskList.stream().map(WorkHwTask::getFlowTaskId).collect(Collectors.toList()));
            List<TblOperateWorkRecord> workRecordList = operateWorkRecordService.selectTblOperateWorkRecordListByFlowIds(operateWorkRecord);
            if(CollUtil.isNotEmpty(workRecordList)){
                taskList.forEach(task -> {
                    TblOperateWorkRecord workRecord = workRecordList.stream().filter(record -> record.getFFlowtaskid().equals(task.getFlowTaskId())).findFirst().orElse(null);
                    if(workRecord != null && workRecord.getFFlowstate() == 100){
                        //完成
                        task.setCompleteTime(workRecord.getUpdateTime());
                        workHwTaskService.updateById(task);
                    }
                });
            }
        }
    }
}
