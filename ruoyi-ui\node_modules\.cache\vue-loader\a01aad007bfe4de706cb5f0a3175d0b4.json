{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue?vue&type=template&id=dd0cb8d8&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue", "mtime": 1756710899653}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}