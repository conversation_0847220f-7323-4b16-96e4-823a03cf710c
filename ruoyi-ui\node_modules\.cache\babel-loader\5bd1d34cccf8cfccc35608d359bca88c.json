{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue", "mtime": 1756710899571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_work", "_deptSelect", "_interopRequireDefault", "_relevancyGapInfo", "_relevancyAlarmInfo", "_relevancyWebvulnInfo", "_webvuln", "_assetFrailty", "_<PERSON><PERSON><PERSON>n", "_mixin", "_data", "_application", "_assetSelect", "_applicationSelect", "_selected_event_list", "_select_event", "_alarmDetail", "_newAddloophole", "_newAddwebvuln", "_report_target", "_dept", "_default", "exports", "default", "name", "mixins", "comMixin", "dicts", "components", "AlarmDetail", "ApplicationSelect", "RelevancyAlarmInfo", "RelevancyGapInfo", "DeptSelect", "relevancyWebvulnInfo", "newAddloophole", "newAddwebvuln", "SelectedEventList", "SelectEvent", "reportTarget", "props", "workType", "type", "String", "mId", "Number", "data", "componentName", "scrollCache", "openEventSelectDialog", "assetData", "openEventDetailDialog", "currentEventType", "queryEventParams", "pageNum", "pageSize", "eventListLoading", "eventList", "total", "ruleForm", "workName", "complateTime", "handleDept", "handleUser", "eventType", "associatedIps", "dataForm", "dataRule", "selectedEvent", "tempSelectedEvent", "severityOptions", "label", "value", "threatenDict", "rules", "required", "message", "trigger", "min", "max", "applicationId", "eventCreateTime", "expectCompleteTime", "handleUserPhone", "remark6", "flowStateOptions", "tableData", "alertTableData", "handleOption", "manageOption", "handleOptionCopy", "manageOptionCopy", "informForm", "informRules", "eventSource", "handleOpinion", "eventNotification", "describeFileUrl", "feedbackForm", "feedbackRules", "eventDescription", "handleSituation", "applicationList", "applicationDialog", "macAipList", "assetIds", "currentApplicationSelect", "eventTypeOption", "eventLevelOption", "applicationInfo", "isChangeEventType", "eventTypeBtnKey", "isFromEvent", "isChangeForm", "formOperatesRecord", "reportTargetForm", "reportTargetActive", "watch", "handler", "newVal", "oldVal", "deep", "immediate", "val", "getUserList", "getManageUserList", "setting", "formData", "Object", "keys", "length", "getApplicationDetails", "createTime", "$set", "deptId", "sessionStorage", "getItem", "deptName", "deptIdStr", "split", "handleTitle", "otherSituation", "feedbackFileUrl", "businessApplications", "assetId", "assetName", "loginUrl", "url", "manager", "parseInt", "phone", "<PERSON><PERSON><PERSON>", "eventData", "eventId", "id", "_this", "eventIds", "map", "item", "row", "deptSet", "Set", "for<PERSON>ach", "add", "matchArr", "filter", "valItem", "first", "push", "initForm", "flowTemplateJson", "allMatchNodes", "loopGetFlowNode", "allFormOperates", "properties", "formOperates", "arrItem", "read", "write", "entries", "_ref", "_ref2", "_slicedToArray2", "$emit", "created", "_this2", "getMulTypeDict", "dictType", "then", "res", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "children", "cItem", "mounted", "_this3", "$nextTick", "set<PERSON><PERSON><PERSON><PERSON>ault", "computed", "categoryDict", "dict", "loophole_category", "reportOptions", "work_order_report_type", "urgencyOptions", "work_order_urgency", "isPublicOptions", "work_order_public", "severityLevelOptions", "work_order_severity_level", "currentDeptData", "methods", "setFormData", "application", "selected", "remark1", "JSON", "parse", "se", "manageUser", "urgency", "toString", "refreshWord", "form", "column", "indexOf", "reportDate", "jnpf", "toDate", "Date", "signed", "proofread", "editor", "submitForm", "formName", "_this4", "that", "$refs", "validate", "valid", "businessId", "addOrder", "code", "$message", "resetForm", "error", "console", "log", "openFocus", "warning", "handleQuery", "getEventDataList", "reset<PERSON><PERSON>y", "handleEventDetail", "_objectSpread2", "openDetail", "eventBtnClick", "evt", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "clearSelection", "catch", "target", "nodeName", "parentNode", "blur", "flowStateFormatter", "cellValue", "index", "match", "find", "_this6", "requestList", "response", "resData", "convertEventData", "rows", "srcDataList", "title", "<PERSON><PERSON><PERSON>", "category", "threatenType", "severity", "alarmLevel", "webUrl", "destIp", "handleStatus", "handleState", "workState", "orderState", "dataSource", "scanNum", "alarmNum", "updateTime", "hostPort", "destPort", "flowState", "hostIp", "queryParams", "convertQueryParams", "getVulnDealList", "listWebVuln", "listAlarm", "srcParams", "isRead", "ids", "join", "handleEventSelected", "event", "tempArr", "_toConsumableArray2", "apply", "handleSeverityTag", "key", "matchItem", "openApplicationSelect", "getIpv4", "applicationSelected", "ip", "self", "stringify", "applicationName", "submitApplicationSelect", "applicationSelect", "submit", "_this7", "_that", "dept_id", "eventTypeBtnDisable", "opType", "_this8", "manageDept", "getAllUserList", "$forceUpdate", "phonenumber", "_this9", "getAllUserListByDept", "handleUserItem", "userId", "manageUserFilter", "option", "userName", "nick<PERSON><PERSON>", "handleUserFilter", "manageUserVisibleChange", "handleUserVisibleChange", "manageUserChange", "matchUser", "handleUserChange", "handleUserPhoneInput", "validateAllForm", "validateForms", "getValidateForm", "Promise", "resolve", "reject", "all", "validateForm", "_this10", "_this11", "activeForms", "refReportTarget", "getActiveForm", "flowVariable", "names", "handleColumn", "matchOperate", "readonly", "isReadOrNull", "isWriteOrNull", "isWrite", "isHideOrNull", "hide", "isHide", "beforeSubmit", "_this12", "workNoPrefix", "Array", "isArray", "reportTargetRef", "isSelectAble", "originType", "selectEventClick", "isRequired", "prop", "$parent", "handleShowWord", "sendDataForm", "treeData", "nodeId", "arr", "state", "childNode", "addDept", "reportTargetActiveChange"], "sources": ["src/views/todoItem/todo/work_flow.vue"], "sourcesContent": ["<template>\n  <div class=\"main\" ref=\"main\">\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"base_content\">\n      <div class=\"title\"><i class=\"el-icon-info\" /> 基础信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"!isHide('workName')\">\n            <el-form-item label=\"通报名称\" prop=\"workName\" ref=\"workName\">\n              <el-input size=\"small\" :disabled=\"!isWrite('ruleForm') || !isWrite('workName')\" v-model.trim=\"ruleForm.workName\" placeholder=\"请填写通报名称\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('period')\" style=\"height: 59px\">\n            <el-form-item label=\"期号\" prop=\"period\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" :required=\"isRequired('period')\">\n              <el-input-number style=\"width: 100%\" size=\"small\" v-model=\"ruleForm.period\" controls-position=\"right\" :min=\"1\" :max=\"999999\" :disabled=\"!isWrite('ruleForm') || !isWrite('period')\" @change=\"refreshWord\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\" v-if=\"isReadOrNull('issue')\">\n            <el-form-item label=\"期刊\" prop=\"issue\" :disabled=\"!isWrite('ruleForm') || !isWrite('issue')\">\n              <el-input :disabled=\"true\" size=\"small\" v-model.trim=\"ruleForm.issue\" placeholder=\"\" maxlength=\"80\" @blur=\"refreshWord\" />\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('remark6') || isWriteOrNull('remark6')\">\n            <el-form-item label=\"通报类型\" prop=\"remark6\" ref=\"remark6\">\n              <el-select size=\"small\" v-model=\"ruleForm.remark6\" placeholder=\"请选择通报类型\" :disabled=\"!isWrite('ruleForm') || !isWrite('remark6')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in reportOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('urgency')\">\n            <el-form-item label=\"紧急程度\" prop=\"urgency\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\">\n              <el-select size=\"small\" v-model=\"ruleForm.urgency\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('urgency')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in urgencyOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('severityLevel')\">\n            <el-form-item label=\"严重程度\" prop=\"severityLevel\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\">\n              <el-select size=\"small\" v-model=\"ruleForm.severityLevel\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('severityLevel')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in severityLevelOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('isPublic')\">\n            <el-form-item label=\"是否公开\" prop=\"isPublic\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\">\n              <el-select size=\"small\" v-model=\"ruleForm.isPublic\" :default-first-option=\"true\" placeholder=\"请选择\" :clearable=\"true\" :disabled=\"!isWrite('ruleForm') || !isWrite('isPublic')\" @change=\"refreshWord\">\n                <el-option\n                  v-for=\"item in isPublicOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"time_content\">\n      <div class=\"title\"><i class=\"el-icon-time\" /> 时间信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"timeForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n<!--          <el-col :span=\"8\" v-if=\"!isHide('eventCreateTime')\">\n            <el-form-item label=\"发现时间\" prop=\"eventCreateTime\" ref=\"eventCreateTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('eventCreateTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.eventCreateTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\" v-if=\"isReadOrNull('reportDate')\">\n            <el-form-item label=\"通报日期\" prop=\"reportDate\" :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\">\n              <el-date-picker\n                v-model=\"ruleForm.reportDate\"\n                @change=\"refreshWord\"\n                type=\"datetime\"\n                style=\"width: 100%\"\n                :disabled=\"!isWrite('ruleForm') || !isWrite('reportDate')\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"请选择通报日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"!isHide('expectCompleteTime')\">\n            <el-form-item label=\"计划完成\" prop=\"expectCompleteTime\" ref=\"expectCompleteTime\">\n              <el-date-picker\n                :disabled=\"!isWrite('ruleForm') || !isWrite('expectCompleteTime')\"\n                size=\"small\"\n                style=\"width: 100%\"\n                v-model=\"ruleForm.expectCompleteTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <div class=\"base_content\" v-if=\"isRead('ruleForm')\" ref=\"person_content\">\n      <div class=\"title\"><i class=\"el-icon-user-solid\" /> 人员信息</div>\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"personForm\" label-width=\"120px\" size=\"medium\" class=\"demo-ruleForm\" style=\"margin-top: 10px\">\n        <el-row :gutter=\"15\">\n          <el-col :span=\"8\" v-if=\"isReadOrNull('signed')\">\n            <el-form-item label=\"签发\" prop=\"signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" :required=\"isRequired('signed')\">\n              <el-input size=\"small\" v-model=\"ruleForm.signed\" :disabled=\"!isWrite('ruleForm') || !isWrite('signed')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('proofread')\">\n            <el-form-item label=\"核稿\" prop=\"proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" :required=\"isRequired('proofread')\">\n              <el-input size=\"small\" v-model=\"ruleForm.proofread\" :disabled=\"!isWrite('ruleForm') || !isWrite('proofread')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" v-if=\"isReadOrNull('editor')\">\n            <el-form-item label=\"编辑\" prop=\"editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" :required=\"isRequired('editor')\">\n              <el-input size=\"small\" v-model=\"ruleForm.editor\" :disabled=\"!isWrite('ruleForm') || !isWrite('editor')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </div>\n\n    <!-- 告知单 开始 -->\n    <div class=\"ext_content\" v-if=\"isRead('informForm')\">\n      <div class=\"title\">\n        <i class=\"el-icon-s-order\" /> 通报内容\n      </div>\n      <el-form :model=\"informForm\" :rules=\"informRules\" ref=\"informForm\" label-width=\"120px\" size=\"medium\" class=\"demo-informForm\" style=\"margin-top: 10px\">\n<!--        <el-form-item label=\"事件来源\" prop=\"eventSource\" v-if=\"!isHide('eventSource')\" ref=\"eventSource\">\n          <el-input size=\"small\" v-model=\"informForm.eventSource\" placeholder=\"请填写事件来源\" maxlength=\"50\" :disabled=\"!isWriteOrNull('eventSource')\" />\n        </el-form-item>\n        <el-form-item label=\"事件类型\" prop=\"eventType\" v-if=\"!isHide('eventType')\" ref=\"eventType\">\n          <el-cascader size=\"small\" v-model=\"informForm.eventType\" :options=\"threatenDict\" clearable placeholder=\"请选择事件类型\" style=\"width: 100%\" :props=\"{ label: 'dictLabel', value: 'dictValue' }\" :disabled=\"!isWriteOrNull('eventType')\">\n            <template slot-scope=\"{ node, data }\">\n              <span>{{ data.dictLabel }}</span>\n              <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n            </template>\n          </el-cascader>\n          &lt;!&ndash;          <el-select v-model=\"informForm.eventType\" placeholder=\"请选择事件类型\">\n                      <el-option\n                        v-for=\"item in eventTypeOption\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>&ndash;&gt;\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"eventLevel\" v-if=\"!isHide('eventLevel')\" ref=\"eventLevel\">\n          <el-select size=\"small\" v-model=\"informForm.eventLevel\" placeholder=\"请选择事件级别\" :disabled=\"!isWriteOrNull('eventLevel')\">\n            <el-option\n              v-for=\"item in eventLevelOption\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-form-item>-->\n        <el-form-item label=\"事件通报\" prop=\"eventNotification\" ref=\"eventNotification\" v-if=\"!isHide('eventNotification')\" :required=\"isRequired('eventNotification')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.eventNotification\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写事件通报内容\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('eventNotification')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"处理建议\" prop=\"handleOpinion\" ref=\"handleOpinion\" v-if=\"!isHide('handleOpinion')\">\n          <el-input size=\"small\" type=\"textarea\" v-model=\"informForm.handleOpinion\" :autosize=\"{ minRows: 5}\" placeholder=\"请填写处理建议\" show-word-limit maxlength=\"2000\"\n                    :disabled=\"!isWriteOrNull('handleOpinion')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述附件\" prop=\"describeFileUrl\" ref=\"describeFileUrl\" v-if=\"!isHide('describeFileUrl')\">\n          <file-upload v-model=\"informForm.describeFileUrl\"\n                       :disUpload=\"!isWrite('informForm') || !isWriteOrNull('describeFileUrl')\"\n                       :limit=\"5\"\n                       :file-type=\"['doc', 'docx', 'xlsx','xls', 'ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                       @change=\"refreshWord\"\n          />\n        </el-form-item>\n        <el-form-item label=\"发\" prop=\"remark8\" ref=\"remark8\" v-if=\"!isHideOrNull('remark8')\" :show-message=\"false\">\n          <el-input size=\"small\" type=\"text\" v-model=\"informForm.remark8\" placeholder=\"\" show-word-limit maxlength=\"50\" :disabled=\"!isWriteOrNull('remark8')\" @blur=\"refreshWord\"></el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    <!-- 告知单 结束 -->\n\n    <!-- 告知单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('informFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 告知单审核签名签章 结束 -->\n\n    <!-- 通报对象 开始 -->\n    <div class=\"ext_content\" v-if=\"true\">\n      <div class=\"title\">\n        <i class=\"el-icon-aim\" /> 通报对象\n        <div class=\"title-right\"><div style=\"cursor: pointer;\" @click=\"addDept\" v-if=\"setting && setting.opType === '-1'\">+新增单位</div></div>\n      </div>\n      <report-target ref=\"reportTarget\" :dept-data-list=\"reportTargetForm\" :setting=\"setting\" style=\"margin-top: 10px;\" @refreshWord=\"refreshWord\" @activeNameChange=\"reportTargetActiveChange\"/>\n    </div>\n    <!-- 通报对象 结束 -->\n\n    <!-- 反馈单 开始 -->\n        <div class=\"ext_content\" v-if=\"isRead('feedbackForm') && reportTargetForm && reportTargetForm.length > 0\">\n          <div class=\"title\">\n            <i class=\"el-icon-s-order\" /> 反馈单信息\n          </div>\n          <el-form :model=\"currentDeptData\" :rules=\"feedbackRules\" ref=\"feedbackForm\" label-width=\"120px\" size=\"medium\" class=\"demo-feedbackForm\" style=\"margin-top: 10px\">\n            <el-form-item label=\"处置标题\" prop=\"remark7\" v-if=\"!isHideOrNull('remark7')\" :required=\"isRequired('remark7')\" :show-message=\"false\">\n              <el-input :disabled=\"!isWriteOrNull('remark7')\" size=\"small\" v-model=\"currentDeptData.handleTitle\" placeholder=\"请输入处置标题\" maxlength=\"50\" @blur=\"refreshWord\" />\n            </el-form-item>\n            <div style=\"display: flex;\" v-if=\"!isWrite('feedbackForm')\">\n    <!--          <el-form-item label=\"事件处理单文号\" prop=\"workNo\" v-if=\"!isHide('workNo')\" style=\"width: 50%\" ref=\"workNo\">\n                <el-input size=\"small\" v-model=\"feedbackForm.workNo\" placeholder=\"\" :disabled=\"true\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"反馈日期\" prop=\"feedbackDate\" v-if=\"!isHide('feedbackDate')\" style=\"width: 50%\" ref=\"feedbackDate\">\n                <el-input size=\"small\" v-model=\"feedbackForm.feedbackDate\" placeholder=\"\" :disabled=\"!isWriteOrNull('feedbackDate')\" @blur=\"refreshWord\"></el-input>\n              </el-form-item>-->\n            </div>\n            <el-form-item label=\"处理结果\" prop=\"eventDescription\" ref=\"eventDescription\" v-if=\"!isHide('eventDescription')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.eventDescription\" placeholder=\"请填写处理结果\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('eventDescription')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"处理情况\" prop=\"handleSituation\" ref=\"handleSituation\" v-if=\"!isHide('handleSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.handleSituation\" placeholder=\"请填写处理情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('handleSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"其他情况\" prop=\"otherSituation\" ref=\"otherSituation\" v-if=\"!isHide('otherSituation')\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"currentDeptData.otherSituation\" placeholder=\"请填写其他情况\" show-word-limit maxlength=\"2000\" :disabled=\"!isWriteOrNull('otherSituation')\" @blur=\"refreshWord\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"附件\" prop=\"feedbackFileUrl\" ref=\"feedbackFileUrl\" v-if=\"!isHide('feedbackFileUrl')\">\n              <file-upload v-model=\"currentDeptData.feedbackFileUrl\"\n                           :disUpload=\"!isWrite('feedbackForm') || !isWriteOrNull('feedbackFileUrl')\"\n                           :limit=\"5\"\n                           :file-type=\"['doc','docx', 'xls', 'xlsx','ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']\"\n                           @change=\"refreshWord\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n    <!-- 反馈单 结束 -->\n\n    <!-- 反馈单审核签名签章 开始 -->\n    <!--    <div class=\"ext_content\" v-if=\"isRead('feedbackFormSign')\">\n          <div class=\"title\">\n            签字签章\n          </div>\n        </div>-->\n    <!-- 反馈单审核签名签章 结束 -->\n\n    <!--  业务系统-资产选择  -->\n    <el-dialog v-if=\"applicationDialog\" title=选择资产 :visible.sync=\"applicationDialog\" class=\"application_dialog\" width=\"80%\" append-to-body>\n      <application-select v-if=\"applicationDialog\" ref=\"applicationSelect\" :value=\"currentApplicationSelect\" :isMultipleSelect=\"true\" :application-list=\"applicationList\" @cancel=\"applicationDialog = false\" @applicationSelected=\"applicationSelected\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitApplicationSelect\">确 定</el-button>\n        <el-button @click=\"applicationDialog = false\">取 消</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog v-if=\"openEventDetailDialog\" title=\"查看事件详情\" :visible.sync=\"openEventDetailDialog\" width=\"80%\" append-to-body>\n      <alarm-detail v-if=\"openEventDetailDialog && currentEventType == 3\" @openDetail=\"openDetail\" :asset-data=\"assetData\"/>\n      <new-addloophole v-if=\"openEventDetailDialog && currentEventType == 1\" :loophole-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n      <new-addwebvuln v-if=\"openEventDetailDialog && currentEventType == 2\" :webvuln-data=\"assetData\" :editable=\"false\" @cancel=\"openEventDetailDialog=false\" />\n    </el-dialog>\n\n    <!--  事件选择弹窗-开始  -->\n    <select-event v-if=\"openEventSelectDialog\" :selected-event-ids=\"ruleForm.eventIds\" :dest-ips=\"ruleForm.associatedIps\" :open.sync=\"openEventSelectDialog\" :setting=\"setting\" :threaten-dict=\"threatenDict\" :current-event-type=\"parseInt(currentEventType)\" :severity-options=\"severityOptions\" @cancel=\"openEventSelectDialog=false\" @selected=\"handleEventSelected\"/>\n    <!--  事件选择弹窗-结束  -->\n  </div>\n</template>\n\n<script>\nimport { getAllUserListByDept,getAllUserList } from \"../../../api/system/user\"\nimport { addOrder } from \"../../../api/tool/work\";\nimport DeptSelect from \"../../components/select/deptSelect\";\nimport RelevancyGapInfo from \"./relevancyGapInfo\";\nimport RelevancyAlarmInfo from \"./relevancyAlarmInfo\";\nimport relevancyWebvulnInfo from \"./relevancyWebvulnInfo\";\nimport {listWebVuln} from \"@/api/monitor2/webvuln\"; //web漏洞\nimport {getVulnDealList} from \"@/api/monitor2/assetFrailty\"; //IP漏洞\nimport {listAlarm} from \"@/api/threaten/threatenWarn\"; //威胁事件\nimport comMixin from '@/views/zeroCode/workFlow/workFlowForm/mixin'\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getApplicationListByCondition,getBusinessInfo,getApplicationDetails} from \"@/api/safe/application\";\nimport { assetSelectList,getInfoByids,listByApplicationId } from \"@/api/assetSelect/assetSelect.js\";\nimport ApplicationSelect from \"./applicationSelect.vue\";\nimport SelectedEventList from \"./selected_event_list.vue\";\nimport SelectEvent from \"./select_event.vue\";\nimport AlarmDetail from \"@/views/basis/securityWarn/alarmDetail.vue\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport newAddwebvuln from \"@/views/frailty/webvuln/newAddwebvuln\";\nimport reportTarget from \"@/views/todoItem/todo/report_target.vue\";\nimport {listDept} from \"@/api/system/dept\";\n\nexport default {\n  name: \"createWork\",\n  mixins: [comMixin],\n  dicts: [\n    'loophole_category',\n    'threaten_alarm_type',\n    'work_order_report_type',\n    'work_order_urgency',\n    'work_order_public',\n    'work_order_severity_level',\n  ],\n  components: {AlarmDetail, ApplicationSelect, RelevancyAlarmInfo, RelevancyGapInfo, DeptSelect,relevancyWebvulnInfo,\n    newAddloophole,newAddwebvuln,SelectedEventList,SelectEvent,reportTarget},\n  props: {\n    workType: {\n      type: String,\n      require: true\n    },\n    mId: {\n      type: Number,\n      require: true\n    }\n  },\n  data() {\n    return {\n      componentName: 'createWork',\n      scrollCache: null,\n      openEventSelectDialog: false,\n      assetData: {},\n      openEventDetailDialog: false,\n      currentEventType: 0,\n      queryEventParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      eventListLoading: false,\n      eventList: [],\n      total: 0,\n      ruleForm: {\n        workName: null,\n        complateTime: null,\n        handleDept: null,\n        handleUser: null,\n        workType: null,\n        eventType: null,\n        associatedIps: [],\n      },\n      dataForm: {\n      },\n      dataRule:{},\n      selectedEvent: [],\n      tempSelectedEvent: [],\n      severityOptions: {\n        '1': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '2': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},\n        ],\n        '3': [\n          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},\n        ],\n      },\n      threatenDict: [],\n      rules: {\n        workName: [\n          { required: true, message: '请输入通报名称', trigger: 'blur' },\n          { min: 3, max: 80, message: '长度在 3 到 80 个字符', trigger: 'blur' }\n        ],\n        applicationId: [\n          { required: false, message: '请选择业务系统', trigger: 'change' }\n        ],\n        eventCreateTime: [\n          { required: true, message: '请选择事件发生时间', trigger: 'change' }\n        ],\n        expectCompleteTime: [\n          { required: true, message: '计划完成时间', trigger: 'change' }\n        ],\n        handleDept: [\n          { required: true, message: '请选择处置单位', trigger: 'change' }\n        ],\n        handleUser: [\n          { required: true, message: '请选择处置人', trigger: 'change' }\n        ],\n        handleUserPhone: [\n          { required: false, message: '请输入处置人电话', trigger: 'blur' }\n        ],\n        remark6: [\n          { required: true, message: '请选择通报类型', trigger: 'change' }\n        ],\n      },\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      tableData: [],\n      alertTableData: [],\n      handleOption: [],\n      manageOption: [],\n      handleOptionCopy: [],\n      manageOptionCopy: [],\n      informForm: {},\n      informRules: {\n        eventSource: [\n          {required: true, message: '请输入事件来源', trigger: 'blur'}\n        ],\n        handleOpinion: [\n          {required: false, message: '请输入处理建议', trigger: 'blur'}\n        ],\n        eventNotification: [\n          {required: false, message: '请输入事件通报内容', trigger: 'blur'}\n        ],\n        describeFileUrl: [\n          {required: false, message: '请上传描述附件', trigger: 'change'}\n        ],\n        /*remark8: [\n          {required: true, message: '请输入', trigger: 'blur'}\n        ],*/\n      },\n      feedbackForm: {},\n      feedbackRules: {\n        eventDescription: [\n          {required: false, message: '请输入处理结果', trigger: 'blur'}\n        ],\n        handleSituation: [\n          {required: false, message: '请输入处理情况', trigger: 'blur'}\n        ]\n      },\n      applicationList: [],\n      applicationDialog: false,\n      macAipList: [],\n      assetIds: [],\n      currentApplicationSelect: null,\n      eventTypeOption: [\n        {\n          label: '漏洞',\n          value: 1\n        },\n        {\n          label: '后门',\n          value: 2\n        },\n        {\n          label: '外链',\n          value: 3\n        }\n      ],\n      eventLevelOption: [\n        {\n          label: 'Ⅰ级',\n          value: '1'\n        },\n        {\n          label: 'Ⅱ级',\n          value: '2'\n        },\n        {\n          label: 'Ⅲ级',\n          value: '3'\n        },\n        {\n          label: 'Ⅳ级',\n          value: '4'\n        },\n      ],\n      applicationInfo: {},\n      isChangeEventType: false,\n      eventTypeBtnKey: 0,\n      isFromEvent: false,\n      isChangeForm: false,\n      formOperatesRecord: [],\n      reportTargetForm: [],\n      reportTargetActive: '0',\n    }\n  },\n  watch: {\n    informForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    ruleForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    feedbackForm: {\n      handler(newVal,oldVal){\n        this.isChangeForm = true;\n      },\n      deep: true\n    },\n    'ruleForm.handleDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getUserList();\n        }\n      }\n    },\n    'ruleForm.manageDept': {\n      deep: false,\n      immediate: true,\n      handler(val) {\n        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n          this.getUserList()\n        }*/\n        if(val){\n          this.getManageUserList();\n        }\n      }\n    },\n    'ruleForm.applicationId': {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        if(newVal && newVal != oldVal){\n          if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){\n            this.getApplicationDetails();\n          }\n        }\n      }\n    },\n    'setting.row': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length>0){\n          if(val.eventType && val.eventType != 0){\n            this.currentEventType = val.eventType;\n          }\n          if(val.createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val.createTime);\n          }\n          /*if(val.id){\n            this.$set(this.ruleForm, 'eventIds', [val.id]);\n          }*/\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          if(val.deptId || (val.eventType === 3 && val.deptIdStr)){\n            deptId = val.deptId?val.deptId:val.deptIdStr.split(',')[0];\n            deptName = val.deptName.split(',')[0];\n          }\n          this.reportTargetForm = [\n            {\n              deptName: deptName,\n              deptId: deptId,\n              handleTitle: null,\n              eventDescription: null,\n              handleSituation: null,\n              otherSituation: null,\n              feedbackFileUrl: null,\n              formData: [{\n                handleDept: deptId,\n                handleUser: '',\n                applicationId: val.businessApplications?val.businessApplications[0].assetId:null,\n                assetName: val.businessApplications?val.businessApplications[0].assetName:null,\n                loginUrl: val.businessApplications?val.businessApplications[0].url:null,\n                manager: val.businessApplications?parseInt(val.businessApplications[0].manager):null,\n                phone: val.businessApplications?val.businessApplications[0].managerPhone:null,\n                eventData: {\n                  'type1': val.eventType === 1?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type2': val.eventType === 2?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type3': val.eventType === 3?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[],\n                  'type4': val.eventType === 4?[\n                    {type: val.eventType, eventId: val.id}\n                  ]:[]\n                }\n              }]\n            }\n          ];\n\n          //默认查业务系统\n          /*this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: this.setting && this.setting.row && this.setting.row.businessApplications && this.setting.row.businessApplications.length>0?this.setting.row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.rows': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && val.length>0){\n          if(val[0].eventType && val[0].eventType != 0){\n            this.currentEventType = val[0].eventType;\n          }\n          if(val[0].createTime){\n            this.$set(this.ruleForm, 'eventCreateTime', val[0].createTime);\n          }\n          let eventIds = val.map(item => item.id);\n          this.$set(this.ruleForm, 'eventIds', eventIds);\n          let row = val[0];\n          this.reportTargetForm = [];\n          let deptSet = new Set();\n          let deptId = sessionStorage.getItem('deptId');\n          let deptName = sessionStorage.getItem('deptName');\n          val.forEach(item => {\n            if(!item.deptId){\n              item.deptId = deptId;\n              item.deptName = deptName;\n            }\n            if(item.deptIdStr){\n              item.deptId = item.deptIdStr.split(',')[0];\n            }\n            deptSet.add({\n              deptName: item.deptName.split(',')[0],\n              deptId: item.deptId,\n            });\n          })\n          deptSet.forEach(item => {\n            let matchArr = val.filter(valItem => valItem.deptId === item.deptId);\n            let first = matchArr[0];\n            this.reportTargetForm.push(\n              {\n                deptName: item.deptName,\n                deptId: item.deptId,\n                handleTitle: null,\n                eventDescription: null,\n                handleSituation: null,\n                otherSituation: null,\n                feedbackFileUrl: null,\n                formData: [\n                  {\n                    handleDept: item.deptId,\n                    applicationId: first.businessApplications?first.businessApplications[0].assetId:null,\n                    assetName: first.businessApplications?first.businessApplications[0].assetName:null,\n                    loginUrl: first.businessApplications?first.businessApplications[0].url:null,\n                    manager: first.businessApplications?parseInt(first.businessApplications[0].manager):null,\n                    phone: first.businessApplications?first.businessApplications[0].managerPhone:null,\n                    eventData: {\n                      'type1': this.currentEventType===1?matchArr.map(item => ({type: 1, eventId: item.id})):[],\n                      'type2': this.currentEventType===2?matchArr.map(item => ({type: 2, eventId: item.id})):[],\n                      'type3': this.currentEventType===3?matchArr.map(item => ({type: 3, eventId: item.id})):[],\n                      'type4': this.currentEventType===4?matchArr.map(item => ({type: 4, eventId: item.id})):[]\n                    }\n                  }\n                ]\n              }\n            )\n          })\n          /*//默认查业务系统\n          this.$nextTick(()=>{\n            let ipv4 = this.getIpv4();\n            if(ipv4){\n              this.isFromEvent = true;\n              getApplicationListByCondition({\n                ipv4: ipv4,\n                eventType: this.currentEventType,\n                applicationId: row.businessApplications && row.businessApplications.length>0?row.businessApplications[0].assetId:null\n              }).then(res => {\n                if(res.data && res.data.length>0){\n                  let firstData = res.data[0];\n                  listByApplicationId({\n                    applicationId: firstData.assetId,\n                    ip: ipv4,\n                    eventType: this.currentEventType,\n                    pageNum: 1,\n                    pageSize: 1\n                  }).then(assetRes => {\n                    let assetData = [];\n                    if(assetRes.rows && assetRes.rows.length>0){\n                      assetData.push(assetRes.rows[0]);\n                    }\n                    let data = {\n                      application: firstData,\n                      selected: assetData\n                    };\n                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n                    this.$set(this.ruleForm,'applicationId',data.application.assetId);\n                    this.ruleForm.applicationName = data.application.assetName;\n                    this.currentApplicationSelect = data;\n                  })\n                }\n              })\n            }\n          })*/\n        }\n      }\n    },\n    'setting.formData': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(val && Object.keys(val).length !== 0){\n          this.initForm();\n        }\n      }\n    },\n    'setting.flowTaskOperatorRecordList': {\n      deep: true,\n      immediate: true,\n      handler(val){\n        if(!this.setting.flowTemplateJson){\n          return;\n        }\n        let allMatchNodes = [];\n        this.loopGetFlowNode(this.setting.flowTemplateJson,'end',allMatchNodes);\n        let allFormOperates = {};\n        allMatchNodes.map(item => item.properties.formOperates).forEach(item => {\n          item.forEach(arrItem => {\n            if(!allFormOperates[arrItem.id]){\n              allFormOperates[arrItem.id] = {\n                read: arrItem.read,\n                write: arrItem.write\n              }\n            }else {\n              if(!allFormOperates[arrItem.id].read){\n                allFormOperates[arrItem.id].read = arrItem.read;\n              }\n              if(!allFormOperates[arrItem.id].write){\n                allFormOperates[arrItem.id].write = arrItem.write;\n              }\n            }\n          })\n        });\n        this.formOperatesRecord = Object.entries(allFormOperates).map(([id, value]) => ({\n          id: id,\n          read: value.read,\n          write: value.write\n        }));\n      }\n    },\n    currentEventType: {\n      handler(newVal,oldVal){\n        this.$set(this.ruleForm,'workType',newVal);\n        /* if(newVal){\n          this.$nextTick(() => {\n            this.resetQuery();\n          })\n        } */\n      }\n    },\n    'ruleForm.handleUser': {\n      handler(newVal,oldVal){\n\n      }\n    },\n    reportTargetForm: {\n      deep: true,\n      immediate: true,\n      handler(newVal,oldVal){\n        this.$emit('reportDataChange',newVal);\n      }\n    },\n  },\n  created() {\n    getMulTypeDict({\n      dictType:'threaten_alarm_type'\n    }).then(res=>{\n      this.threatenDict=res.data;\n      // TODO  暂定为获取两层  当前方法有问题 其它版本也有修改需要等待后续修改\n      /* if (this.threatenDict.length > 0 && this.threatenDict[0].children.length > 0) {\n        this.ruleForm.eventType = this.threatenDict[0].dictValue + '/' + this.threatenDict[0].children[0].dictValue\n      } */\n      this.threatenDict.forEach(item=>{\n        item.value=item.dictValue;\n        item.label=item.dictLabel;\n        item.children.forEach(cItem=>{\n          cItem.value=cItem.dictValue;\n          cItem.label=cItem.dictLabel;\n        })\n      })\n    });\n  },\n  mounted() {\n    this.$nextTick(() => {\n      //默认值\n      this.setFormDefault();\n    })\n  },\n  computed: {\n    categoryDict(){\n      if(this.currentEventType == 1 || this.currentEventType == 2){\n        return this.dict.type.loophole_category;\n      }else if(this.currentEventType == 3){\n        return this.threatenDict;\n      }\n    },\n    reportOptions(){\n      return this.dict.type.work_order_report_type;\n    },\n    urgencyOptions(){\n      return this.dict.type.work_order_urgency;\n    },\n    isPublicOptions(){\n      return this.dict.type.work_order_public;\n    },\n    severityLevelOptions(){\n      return this.dict.type.work_order_severity_level;\n    },\n    // 获取当前部门数据（安全访问）\n    currentDeptData() {\n      return this.reportTargetForm && this.reportTargetForm.length>0 ? this.reportTargetForm[parseInt(this.reportTargetActive)] : null;\n    }\n  },\n  methods: {\n    initForm(){\n      this.setFormData(this.ruleForm, 'applicationId');\n      this.currentApplicationSelect = {\n        application: {\n          assetId: this.setting.formData.applicationId,\n        },\n        selected: this.setting.formData.remark1?JSON.parse(this.setting.formData.remark1).map(se => {return {assetId: se}}):[],\n      }\n      this.setFormData(this.ruleForm, 'associatedIps');\n      this.setFormData(this.ruleForm, 'eventIds');\n      this.$set(this.queryEventParams,\"ids\",this.setting.formData.eventIds);\n      this.setFormData(this.ruleForm, 'workType');\n      this.currentEventType = this.setting.formData.workType;\n      this.setFormData(this.ruleForm, 'workName');\n      this.setFormData(this.ruleForm, 'loginUrl');\n      this.setFormData(this.ruleForm, 'handleDept');\n      this.setFormData(this.ruleForm, 'manageDept');\n      this.setFormData(this.ruleForm, 'handleDeptName');\n      this.setFormData(this.ruleForm, 'manageDeptName');\n      this.setFormData(this.ruleForm, 'handleUser');\n      this.$set(this.ruleForm,\"manageUser\",this.setting.formData.manageUser?parseInt(this.setting.formData.manageUser) : null);\n      this.setFormData(this.ruleForm, 'handleUserPhone');\n      this.setFormData(this.ruleForm, 'handleUserName');\n      this.setFormData(this.ruleForm, 'manageUserName');\n      this.setFormData(this.ruleForm, 'remark6');\n      this.setFormData(this.ruleForm, 'issue');\n      this.setFormData(this.ruleForm, 'expectCompleteTime');\n      this.setFormData(this.ruleForm, 'eventCreateTime');\n      this.setFormData(this.ruleForm, 'applicationName');\n      this.$set(this.ruleForm,\"urgency\",this.setting.formData.urgency?this.setting.formData.urgency.toString() : '1');\n      this.setFormData(this.ruleForm, 'isPublic');\n      this.setFormData(this.ruleForm, 'severityLevel');\n      this.setFormData(this.ruleForm, 'reportDate');\n      this.setFormData(this.ruleForm, 'period');\n      this.setFormData(this.ruleForm, 'signed');\n      this.setFormData(this.ruleForm, 'proofread');\n      this.setFormData(this.ruleForm, 'editor');\n\n      this.setFormData(this.informForm, 'describeFileUrl');\n      this.setFormData(this.informForm, 'eventLevel');\n      this.setFormData(this.informForm, 'eventNotification');\n      this.setFormData(this.informForm, 'eventSource');\n      // this.setFormData(this.informForm, 'workType');\n      this.setFormData(this.informForm, 'handleOpinion');\n      this.setFormData(this.informForm, 'eventType');\n      this.setFormData(this.informForm, 'remark8');\n      this.setFormData(this.feedbackForm, 'eventDescription');\n      this.setFormData(this.feedbackForm, 'handleSituation');\n      this.setFormData(this.feedbackForm, 'otherSituation');\n      this.setFormData(this.feedbackForm, 'workNo');\n      this.setFormData(this.feedbackForm, 'feedbackDate');\n      this.setFormData(this.feedbackForm, 'feedbackFileUrl');\n      this.setFormData(this.feedbackForm, 'remark7');\n      //通报对象赋值\n      this.reportTargetForm = this.setting.formData.reportTargetForm;\n\n      this.refreshWord();\n    },\n    setFormData(form, column){\n      let data = this.setting.formData[column];\n      if('eventType' === column){\n        if(data && data.indexOf(\"/\")!==-1){\n          data = data.split(\"/\");\n        }\n      }\n      this.$set(form, column, data);\n    },\n    setFormDefault(){\n      if(!this.ruleForm.urgency){\n        this.$set(this.ruleForm,'urgency','1');\n      }\n      if(!this.ruleForm.reportDate){\n        this.$set(this.ruleForm,'reportDate',this.jnpf.toDate(new Date(), 'yyyy-MM-dd HH:mm:ss'));\n      }\n      if(!this.ruleForm.signed){\n        this.$set(this.ruleForm,'signed','叶琛');\n      }\n      if(!this.ruleForm.proofread){\n        this.$set(this.ruleForm,'proofread','王亮');\n      }\n      if(!this.ruleForm.editor){\n        this.$set(this.ruleForm,'editor','付扬');\n      }\n    },\n    submitForm(formName) {\n      const that = this\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          that.ruleForm.businessId = that.mId\n          that.ruleForm.workType = that.workType\n          addOrder(that.ruleForm).then(res => {\n            if (res.code === 200) {\n              this.$message({\n                message: '通报创建成功',\n                type: 'success'\n              })\n              that.resetForm()\n            } else {\n              this.$message.error('通报创建失败')\n            }\n          })\n        } else {\n          console.log('error submit!!');\n          return false;\n        }\n      })\n    },\n    resetForm() {\n      this.$emit('closeWork')\n    },\n    openFocus() {\n      if (!this.ruleForm.handleDept) {\n        this.$message.warning('请先选择处理部门！');\n      }\n    },\n    handleQuery(){\n      this.queryEventParams.pageNum = 1;\n      this.total = 0;\n      this.getEventDataList();\n    },\n    resetQuery(){\n      this.queryEventParams={\n        pageNum: 1,\n        pageSize: this.queryEventParams.pageSize\n      };\n      this.getEventDataList();\n    },\n    handleEventDetail(row){\n      this.assetData= {...row};\n      this.openDetail(true);\n    },\n    openDetail(val){\n      this.openEventDetailDialog = val;\n    },\n    eventBtnClick(type,evt){\n      if(this.currentEventType != type && this.currentEventType != 0){\n        this.$confirm('切换事件类型将清空之前选择的事件, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.selectedEvent = [];\n          this.ruleForm.eventIds = [];\n          this.currentEventType = type;\n          this.$refs.eventList && this.$refs.eventList.clearSelection();\n          this.isChangeEventType = true;\n        }).catch(() => {\n          this.eventTypeBtnKey++;\n        });\n      }else {\n        if(this.currentEventType != 0 && this.currentEventType == type){\n          if(this.isFromEvent){\n            //从事件发起，不允许取消\n            return false;\n          }\n          this.$confirm('取消选中事件类型将清空之前选择的事件, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.eventList = [];\n            this.total = 0;\n            this.selectedEvent = [];\n            this.ruleForm.eventIds = [];\n            this.currentEventType = 0;\n            this.$refs.eventList && this.$refs.eventList.clearSelection();\n            this.isChangeEventType = true;\n            const target = evt.target;\n            if(target){\n              if(target.nodeName === 'SPAN'){\n                if(target.parentNode){\n                  target.parentNode.blur();\n                }\n              }\n              target.blur();\n            }\n          }).catch(() => {\n\n          });\n        }else {\n          this.currentEventType = type;\n        }\n      }\n    },\n    flowStateFormatter(row, column, cellValue, index){\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value==cellValue);\n      if(match){\n        name = match.label;\n      }\n      return name;\n    },\n    getEventDataList(){\n      if(!this.currentEventType && (!this.dataForm.eventIds || this.dataForm.eventIds.length<1)){\n        return false;\n      }\n      if(this.currentEventType == 0){\n        return false;\n      }\n      this.eventListLoading = true;\n      this.requestList().then(response => {\n        let resData = this.convertEventData(response.rows);\n        this.eventList = resData;\n        this.total= response.total;\n        this.eventListLoading = false;\n      })\n    },\n    //转换\n    convertEventData(srcDataList){\n      if(!srcDataList){\n        return [];\n      }\n      if(this.currentEventType == 3){\n        return srcDataList.map(item => {\n          return {\n            id: item.id,\n            title: item.threatenName,\n            category: item.threatenType,\n            severity: item.alarmLevel,\n            webUrl: item.destIp,\n            handleStatus: item.handleState,\n            workState: item.orderState,\n            dataSource: item.dataSource,\n            scanNum: item.alarmNum,\n            updateTime: item.updateTime,\n            createTime: item.createTime,\n            hostPort: item.destPort,\n            flowState: item.flowState\n          }\n        })\n      }else if(this.currentEventType == 1){\n        return srcDataList.map(item => {\n          item.webUrl = item.hostIp;\n          return item;\n        });\n      }else {\n        return srcDataList;\n      }\n\n    },\n    requestList(){\n      let queryParams = this.convertQueryParams(this.queryEventParams);\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return getVulnDealList(queryParams);\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return listWebVuln(queryParams);\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return listAlarm(queryParams);\n      }\n    },\n    convertQueryParams(srcParams){\n      if(!this.isRead('list_select')){\n        srcParams.ids = this.dataForm.eventIds;\n      }\n      if(this.currentEventType == 3){\n        return {\n          pageNum: srcParams.pageNum,\n          pageSize: srcParams.pageSize,\n          threatenName: srcParams.title,\n          threatenType: srcParams.category?srcParams.category.join('/'):null,\n          alarmLevel: srcParams.severity,\n          destIp: srcParams.webUrl,\n          destPort: srcParams.hostPort,\n          handleState: srcParams.handleStatus,\n          orderState: srcParams.workState,\n          dataSource: srcParams.dataSource,\n          createTime: srcParams.createTime,\n          ids: srcParams.ids\n        }\n      }else if(this.currentEventType == 1){\n        srcParams.hostIp = srcParams.webUrl;\n        return srcParams;\n      }else {\n        return srcParams;\n      }\n    },\n    handleEventSelected(val, event){\n      if(!this.ruleForm.eventIds){\n        this.ruleForm.eventIds = [];\n      }\n      let tempArr = [...this.ruleForm.eventIds];\n      tempArr.push(...val);\n      if (event) {\n        this.ruleForm.eventType = event\n      }\n      this.$set(this.ruleForm,'eventIds',tempArr);\n      this.selectedEvent = this.ruleForm.eventIds;\n    },\n    handleSeverityTag(severity,key){\n      if(!severity){\n        return '未知';\n      }\n      if(this.severityOptions[this.currentEventType.toString()]){\n        let matchItem = this.severityOptions[this.currentEventType.toString()].find(item => item.value == severity);\n        if(!matchItem){\n          return '未知';\n        }\n        return matchItem[key];\n      }\n      return '';\n    },\n    openApplicationSelect(){\n      /*getApplicationListByCondition({\n        // ipv4: this.getIpv4()\n        ipv4: null\n      }).then(res=>{\n        this.applicationList=res.data;\n        this.applicationDialog = true;\n      });*/\n      this.applicationDialog = true;\n    },\n    getIpv4(){\n      if(!this.setting.row && (!this.setting.rows || this.setting.rows.length<1)){\n        return null;\n      }\n      let row = this.setting.row || this.setting.rows[0];\n      if(this.currentEventType == 1){\n        //IP漏洞事件\n        return row.hostIp;\n      }else if(this.currentEventType == 2){\n        //应用漏洞事件\n        return row.webUrl;\n      }else if(this.currentEventType == 3){\n        //威胁事件\n        return row.destIp;\n      }else if(this.currentEventType == 4){\n        //弱口令漏洞\n        return row.hostIp;\n      }\n    },\n    applicationSelected(data){\n      this.currentApplicationSelect = data;\n      this.ruleForm.associatedIps = data.selected.map(item => item.ip);\n      this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);\n      this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));\n      this.$set(this.ruleForm,'applicationId',data.application.assetId);\n      this.ruleForm.applicationName = data.application.assetName;\n      this.applicationDialog = false;\n      this.refreshWord();\n    },\n    submitApplicationSelect(){\n      this.$refs.applicationSelect.submit();\n    },\n    getApplicationDetails(){\n      let _that = this;\n      getApplicationDetails(this.ruleForm.applicationId).then(res => {\n        if(res.data.url){\n          this.$set(_that.ruleForm,'loginUrl',res.data.url);\n        }\n        if(res.data.dept_id){\n          this.$set(_that.ruleForm,'handleDept',res.data.dept_id);\n          this.$set(_that.ruleForm,'manageDept',res.data.dept_id);\n\n          this.$nextTick(() => {\n            this.getManageUserList();\n          })\n        }\n        this.applicationInfo = res.data;\n      })\n    },\n    eventTypeBtnDisable(){\n      if(\"-1\" == this.setting.opType){\n        return false;\n      }\n      if(this.setting.row && this.setting.row.eventType){\n        return true;\n      }\n      return false;\n    },\n    getManageUserList() {\n      // this.ruleForm.handleUser = ''\n      this.manageOption = []\n      if (this.ruleForm.manageDept) {\n        getAllUserList({\n          // deptId: this.ruleForm.manageDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.manageOption = res.rows;\n            this.manageOptionCopy = [...this.manageOption];\n            this.$nextTick(() => {\n              /*if(this.manageOption && !this.manageOption.find(manageUserItem => manageUserItem.userId == this.ruleForm.manageUser)){\n                this.ruleForm.manageUser = '';\n                this.ruleForm.handleUserPhone = '';\n              }*/\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  /*if(this.manageOption.find(manageUserItem => manageUserItem.userId == manager)){\n                    // this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                    this.$forceUpdate();\n                  }*/\n                  this.$set(this.ruleForm,'manageUser',parseInt(manager));\n                  this.$forceUpdate();\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    getUserList() {\n      // this.ruleForm.handleUser = ''\n      this.handleOption = []\n      if (this.ruleForm.handleDept) {\n        getAllUserListByDept({\n          deptId: this.ruleForm.handleDept\n        }).then(res => {\n          if (res.code === 200) {\n            this.handleOption = res.rows;\n            this.handleOptionCopy = [...this.handleOption];\n            this.$nextTick(() => {\n              if(this.handleOption && !this.handleOption.find(handleUserItem => handleUserItem.userId == this.ruleForm.handleUser)){\n                this.ruleForm.handleUser = '';\n                this.$set(this.ruleForm,'handleUser','');\n                // this.ruleForm.handleUserPhone = '';\n              }\n\n              if(this.applicationInfo){\n                if(this.applicationInfo.manager){\n                  let manager = this.applicationInfo.manager.split(',')[0];\n                  if(this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                    if(this.handleOption && this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){\n                      this.$set(this.ruleForm,'handleUser',parseInt(manager));\n                    }\n                  }\n                }\n                if(this.applicationInfo.phonenumber){\n                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);\n                }\n              }\n            })\n          }\n        })\n      }\n    },\n    manageUserFilter(val){\n      if(val){\n        this.manageOption = this.manageOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.manageOption = [...this.manageOptionCopy];\n      }\n    },\n    handleUserFilter(val){\n      if(val){\n        this.handleOption = this.handleOptionCopy.filter(option => {\n          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;\n        });\n      }else {\n        this.handleOption = [...this.handleOptionCopy];\n      }\n    },\n    manageUserVisibleChange(){\n      this.manageOption = [...this.manageOptionCopy];\n    },\n    handleUserVisibleChange(){\n      this.handleOption = [...this.handleOptionCopy];\n    },\n    manageUserChange(row){\n      if(row){\n        let matchUser = this.manageOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserChange(row){\n      if(row){\n        let matchUser = this.handleOption.find(item => item.userId==row);\n        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');\n      }\n    },\n    handleUserPhoneInput(){\n      this.$forceUpdate();\n    },\n    validateAllForm() {\n      /*if(this.isRead('list_select') && (this.currentEventType && this.currentEventType !== 0)){\n        if(!this.ruleForm.eventIds || this.ruleForm.eventIds.length < 1){\n          this.$modal.msgError('请选择关联事件');\n          this.$refs.event_list && this.$refs.event_list.scrollIntoView();\n          return new Promise((resolve, reject) => {\n            reject();\n          });\n        }\n      }*/\n\n      let validateForms = this.getValidateForm();\n      if(!validateForms || validateForms.length < 1){\n        return new Promise((resolve, reject) => {\n          resolve();\n        });\n      }\n      return Promise.all(validateForms);\n    },\n    validateForm(formName){\n      return new Promise((resolve, reject) => {\n        if(!this.$refs[formName]){\n          reject();\n        }\n        this.$refs[formName].validate((valid) => {\n          if (valid) {\n            resolve()\n          } else {\n            reject()\n          }\n        })\n      })\n    },\n    getValidateForm(){\n      let res = [];\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            res.push(this.validateForm(formName));\n          }\n        })\n      }\n      let refReportTarget = this.$refs.reportTarget;\n      if(refReportTarget){\n        res.push(...refReportTarget.validate());\n      }\n      return res;\n    },\n    getActiveForm(){\n      let res = [];\n      let flowVariable = this.setting.flowVariable;\n      if(flowVariable && flowVariable.length > 0){\n        let matchItem = flowVariable.find(item => item.key == 'formNames');\n        if(matchItem && matchItem.value){\n          let names = matchItem.value.split(',');\n          names.forEach(name => {\n            res.push(name);\n          })\n        }\n      }\n      return res;\n    },\n    handleColumn(column){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==column);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isRead(name){\n      let formOperates = [];\n      if(!this.setting.readonly){\n        formOperates = this.setting.formOperates;\n      }else {\n        formOperates = this.formOperatesRecord;\n      }\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return true;\n      }\n    },\n    isReadOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.read;\n      }else {\n        return false;\n      }\n    },\n    isWriteOrNull(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return false;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return false;\n      }\n    },\n    isWrite(name){\n      if(this.setting.readonly){\n        return false;\n      }\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.write;\n      }else {\n        return true;\n      }\n    },\n    isHideOrNull(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return true;\n      }\n    },\n    isHide(name){\n      let formOperates = this.setting.formOperates;\n      if(!formOperates || formOperates.length < 1){\n        return true;\n      }\n      let matchOperate = formOperates.find(item => item.id==name);\n      if(matchOperate){\n        return matchOperate.hide===true;\n      }else {\n        return false;\n      }\n    },\n    beforeSubmit(){\n      //动态表单\n      this.dataForm = {};\n      // let activeForms = this.getActiveForm();\n      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];\n      if(activeForms && activeForms.length>0){\n        activeForms.forEach(formName => {\n          if(this.$refs[formName]){\n            this.dataForm = {...this.dataForm, ...this[formName]};\n          }\n        })\n      }\n      if(this.setting.flowVariable){\n        let match = this.setting.flowVariable.find(item => item.key == 'workNoPrefix');\n        if(match){\n          this.dataForm.workNoPrefix = match.value;\n        }\n      }\n      /*      if (this.dataForm.eventType) {\n              this.dataForm.eventType = this.dataForm.eventType.join('/');\n            }*/\n      if(this.dataForm && this.dataForm.eventType){\n        if(Array.isArray(this.dataForm.eventType)){\n          this.dataForm.eventType = this.dataForm.eventType.join('/');\n        }\n      }else {\n        if(this.currentEventType === 4){\n          //弱口令\n          this.dataForm.eventType = '弱口令';\n        }\n      }\n\n      //通报对象\n      let reportTargetRef = this.$refs.reportTarget;\n      if(reportTargetRef){\n        this.dataForm.reportTargetForm = reportTargetRef.submitForm();\n      }\n      return this.dataForm;\n    },\n    isSelectAble(row,index){\n      if(this.setting && (!this.setting.originType || (this.setting.originType && this.setting.originType != 'event')) && (this.setting.row && this.setting.row.eventIds && this.setting.row.eventIds.find(item => item == row.id))){\n        return true;\n      }\n      return (!row.flowState || row.flowState == '99');\n    },\n    selectEventClick(){\n      if(!this.currentEventType){\n        this.$message.error('请先选择事件类型');\n        return false;\n      }\n      this.openEventSelectDialog = true;\n    },\n    isRequired(prop){\n      if(!this.formOperates){\n        return false;\n      }\n      let match = this.formOperates.find(item => item.id === prop);\n      if(match && match.required){\n        return true;\n      }\n      return false;\n    },\n    refreshWord(){\n      /* if(this.isChangeForm){\n        this.isChangeForm = false;\n        //暂存\n        // this.$eventBus.$emit('sendWorkForm', this.beforeSubmit());\n        this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n      } */\n      this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();\n    },\n    sendDataForm(){\n      //暂存\n      return this.beforeSubmit();\n    },\n    loopGetFlowNode(treeData,nodeId,arr){\n      if(!treeData){\n        return;\n      }\n      arr.push({\n        nodeId: treeData.nodeId,\n        properties: treeData.properties,\n        state: treeData.state,\n        type: treeData.type\n      })\n      if(treeData.nodeId === nodeId){\n        return;\n      }else {\n        return this.loopGetFlowNode(treeData.childNode,nodeId,arr);\n      }\n    },\n    addDept(){\n      this.$refs.reportTarget && this.$refs.reportTarget.addDept();\n    },\n    reportTargetActiveChange(val){\n      if(!val){\n        this.reportTargetActive = '0';\n      }else {\n        this.reportTargetActive = val;\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n@import \"../../../styles/track.css\";\n</style>\n<style lang=\"scss\" scoped>\n.main{\n  background-color: #F2F4F8;\n\n  > div{\n    background-color: #ffffff;\n    padding: 15px;\n  }\n  > div:not(:first-child){\n    margin-top: 0.8vh;\n  }\n\n  .base_content{\n    .ips{\n      display: flex;\n      > div{\n        background-color: #E7F2FF;\n        color: #0778FF;\n        border-width: 1px;\n        border-style: solid;\n        border-color: #0778FF;\n        border-radius: 3px;\n        height: 32px;\n        text-align: center;\n        overflow: hidden;\n        font-size: 0.5vw;\n      }\n      > div:not(:first-child){\n        margin-left: 1%;\n      }\n      .ips_item{\n        width: 31%;\n      }\n      .ips_item_overflow{\n        width: 7%;\n      }\n    }\n  }\n\n  .event_type_body{\n    > div:not(:first-child){\n      margin-top: 1.5vh;\n    }\n\n    .event_type_select{\n      display: flex;\n      .label{\n        align-content: center;\n      }\n      .event_type_btn{\n        display: flex;\n        margin-left: 20px;\n        > .event_type_btn_item:not(:first-child){\n          margin-left: 10px;\n        }\n\n        .btn_active{\n          background: #1890ff;\n          border-color: #1890ff;\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n\n  .title-right{\n    float: right;\n    font-size: 14px;\n    color: #6c6c6c;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+RA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,mBAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,qBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,aAAA,GAAAT,OAAA;AACA,IAAAU,MAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,YAAA,GAAAZ,OAAA;AACA,IAAAa,YAAA,GAAAb,OAAA;AACA,IAAAc,kBAAA,GAAAX,sBAAA,CAAAH,OAAA;AACA,IAAAe,oBAAA,GAAAZ,sBAAA,CAAAH,OAAA;AACA,IAAAgB,aAAA,GAAAb,sBAAA,CAAAH,OAAA;AACA,IAAAiB,YAAA,GAAAd,sBAAA,CAAAH,OAAA;AACA,IAAAkB,eAAA,GAAAf,sBAAA,CAAAH,OAAA;AACA,IAAAmB,cAAA,GAAAhB,sBAAA,CAAAH,OAAA;AACA,IAAAoB,cAAA,GAAAjB,sBAAA,CAAAH,OAAA;AACA,IAAAqB,KAAA,GAAArB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdA;AACA;AACA;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAcA;EACAC,IAAA;EACAC,MAAA,GAAAC,cAAA;EACAC,KAAA,GACA,qBACA,uBACA,0BACA,sBACA,qBACA,4BACA;EACAC,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,iBAAA,EAAAA,0BAAA;IAAAC,kBAAA,EAAAA,2BAAA;IAAAC,gBAAA,EAAAA,yBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,oBAAA,EAAAA,6BAAA;IACAC,cAAA,EAAAA,uBAAA;IAAAC,aAAA,EAAAA,sBAAA;IAAAC,iBAAA,EAAAA,4BAAA;IAAAC,WAAA,EAAAA,qBAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACA5C,OAAA;IACA;IACA6C,GAAA;MACAF,IAAA,EAAAG,MAAA;MACA9C,OAAA;IACA;EACA;EACA+C,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,qBAAA;MACAC,SAAA;MACAC,qBAAA;MACAC,gBAAA;MACAC,gBAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,gBAAA;MACAC,SAAA;MACAC,KAAA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,UAAA;QACAC,UAAA;QACAtB,QAAA;QACAuB,SAAA;QACAC,aAAA;MACA;MACAC,QAAA,GACA;MACAC,QAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,eAAA;QACA,MACA;UAAA5B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA,EACA;QACA,MACA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA,EACA;QACA,MACA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;UAAA9B,IAAA;UAAA6B,KAAA;UAAAC,KAAA;QAAA;MAEA;MACAC,YAAA;MACAC,KAAA;QACAd,QAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,aAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,eAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,kBAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,UAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,UAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,eAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,OAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAQ,gBAAA,GACA;QACAd,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAc,SAAA;MACAC,cAAA;MACAC,YAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;QACAC,WAAA,GACA;UAAAnB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAkB,aAAA,GACA;UAAApB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAmB,iBAAA,GACA;UAAArB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAoB,eAAA,GACA;UAAAtB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;AACA;AACA;MACA;MACAqB,YAAA;MACAC,aAAA;QACAC,gBAAA,GACA;UAAAzB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAwB,eAAA,GACA;UAAA1B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAyB,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,QAAA;MACAC,wBAAA;MACAC,eAAA,GACA;QACApC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAoC,gBAAA,GACA;QACArC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAqC,eAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,gBAAA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACAzB,UAAA;MACA0B,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAP,YAAA;MACA;MACAQ,IAAA;IACA;IACA9D,QAAA;MACA2D,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAP,YAAA;MACA;MACAQ,IAAA;IACA;IACAvB,YAAA;MACAoB,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAP,YAAA;MACA;MACAQ,IAAA;IACA;IACA;MACAA,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QACA;AACA;AACA;QACA,IAAAA,GAAA;UACA,KAAAC,WAAA;QACA;MACA;IACA;IACA;MACAH,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QACA;AACA;AACA;QACA,IAAAA,GAAA;UACA,KAAAE,iBAAA;QACA;MACA;IACA;IACA;MACAJ,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA,IAAAA,MAAA,IAAAC,MAAA;UACA,UAAAM,OAAA,CAAAC,QAAA,IAAAC,MAAA,CAAAC,IAAA,MAAAH,OAAA,CAAAC,QAAA,EAAAG,MAAA;YACA,KAAAC,qBAAA;UACA;QACA;MACA;IACA;IACA;MACAV,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QACA,IAAAA,GAAA,IAAAK,MAAA,CAAAC,IAAA,CAAAN,GAAA,EAAAO,MAAA;UACA,IAAAP,GAAA,CAAA3D,SAAA,IAAA2D,GAAA,CAAA3D,SAAA;YACA,KAAAZ,gBAAA,GAAAuE,GAAA,CAAA3D,SAAA;UACA;UACA,IAAA2D,GAAA,CAAAS,UAAA;YACA,KAAAC,IAAA,MAAA1E,QAAA,qBAAAgE,GAAA,CAAAS,UAAA;UACA;UACA;AACA;AACA;UACA,IAAAE,MAAA,GAAAC,cAAA,CAAAC,OAAA;UACA,IAAAC,QAAA,GAAAF,cAAA,CAAAC,OAAA;UACA,IAAAb,GAAA,CAAAW,MAAA,IAAAX,GAAA,CAAA3D,SAAA,UAAA2D,GAAA,CAAAe,SAAA;YACAJ,MAAA,GAAAX,GAAA,CAAAW,MAAA,GAAAX,GAAA,CAAAW,MAAA,GAAAX,GAAA,CAAAe,SAAA,CAAAC,KAAA;YACAF,QAAA,GAAAd,GAAA,CAAAc,QAAA,CAAAE,KAAA;UACA;UACA,KAAAxB,gBAAA,IACA;YACAsB,QAAA,EAAAA,QAAA;YACAH,MAAA,EAAAA,MAAA;YACAM,WAAA;YACAxC,gBAAA;YACAC,eAAA;YACAwC,cAAA;YACAC,eAAA;YACAf,QAAA;cACAjE,UAAA,EAAAwE,MAAA;cACAvE,UAAA;cACAiB,aAAA,EAAA2C,GAAA,CAAAoB,oBAAA,GAAApB,GAAA,CAAAoB,oBAAA,IAAAC,OAAA;cACAC,SAAA,EAAAtB,GAAA,CAAAoB,oBAAA,GAAApB,GAAA,CAAAoB,oBAAA,IAAAE,SAAA;cACAC,QAAA,EAAAvB,GAAA,CAAAoB,oBAAA,GAAApB,GAAA,CAAAoB,oBAAA,IAAAI,GAAA;cACAC,OAAA,EAAAzB,GAAA,CAAAoB,oBAAA,GAAAM,QAAA,CAAA1B,GAAA,CAAAoB,oBAAA,IAAAK,OAAA;cACAE,KAAA,EAAA3B,GAAA,CAAAoB,oBAAA,GAAApB,GAAA,CAAAoB,oBAAA,IAAAQ,YAAA;cACAC,SAAA;gBACA,SAAA7B,GAAA,CAAA3D,SAAA,UACA;kBAAAtB,IAAA,EAAAiF,GAAA,CAAA3D,SAAA;kBAAAyF,OAAA,EAAA9B,GAAA,CAAA+B;gBAAA,EACA;gBACA,SAAA/B,GAAA,CAAA3D,SAAA,UACA;kBAAAtB,IAAA,EAAAiF,GAAA,CAAA3D,SAAA;kBAAAyF,OAAA,EAAA9B,GAAA,CAAA+B;gBAAA,EACA;gBACA,SAAA/B,GAAA,CAAA3D,SAAA,UACA;kBAAAtB,IAAA,EAAAiF,GAAA,CAAA3D,SAAA;kBAAAyF,OAAA,EAAA9B,GAAA,CAAA+B;gBAAA,EACA;gBACA,SAAA/B,GAAA,CAAA3D,SAAA,UACA;kBAAAtB,IAAA,EAAAiF,GAAA,CAAA3D,SAAA;kBAAAyF,OAAA,EAAA9B,GAAA,CAAA+B;gBAAA,EACA;cACA;YACA;UACA,EACA;;UAEA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;MACA;IACA;IACA;MACAjC,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QAAA,IAAAgC,KAAA;QACA,IAAAhC,GAAA,IAAAA,GAAA,CAAAO,MAAA;UACA,IAAAP,GAAA,IAAA3D,SAAA,IAAA2D,GAAA,IAAA3D,SAAA;YACA,KAAAZ,gBAAA,GAAAuE,GAAA,IAAA3D,SAAA;UACA;UACA,IAAA2D,GAAA,IAAAS,UAAA;YACA,KAAAC,IAAA,MAAA1E,QAAA,qBAAAgE,GAAA,IAAAS,UAAA;UACA;UACA,IAAAwB,QAAA,GAAAjC,GAAA,CAAAkC,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAJ,EAAA;UAAA;UACA,KAAArB,IAAA,MAAA1E,QAAA,cAAAiG,QAAA;UACA,IAAAG,GAAA,GAAApC,GAAA;UACA,KAAAR,gBAAA;UACA,IAAA6C,OAAA,OAAAC,GAAA;UACA,IAAA3B,MAAA,GAAAC,cAAA,CAAAC,OAAA;UACA,IAAAC,QAAA,GAAAF,cAAA,CAAAC,OAAA;UACAb,GAAA,CAAAuC,OAAA,WAAAJ,IAAA;YACA,KAAAA,IAAA,CAAAxB,MAAA;cACAwB,IAAA,CAAAxB,MAAA,GAAAA,MAAA;cACAwB,IAAA,CAAArB,QAAA,GAAAA,QAAA;YACA;YACA,IAAAqB,IAAA,CAAApB,SAAA;cACAoB,IAAA,CAAAxB,MAAA,GAAAwB,IAAA,CAAApB,SAAA,CAAAC,KAAA;YACA;YACAqB,OAAA,CAAAG,GAAA;cACA1B,QAAA,EAAAqB,IAAA,CAAArB,QAAA,CAAAE,KAAA;cACAL,MAAA,EAAAwB,IAAA,CAAAxB;YACA;UACA;UACA0B,OAAA,CAAAE,OAAA,WAAAJ,IAAA;YACA,IAAAM,QAAA,GAAAzC,GAAA,CAAA0C,MAAA,WAAAC,OAAA;cAAA,OAAAA,OAAA,CAAAhC,MAAA,KAAAwB,IAAA,CAAAxB,MAAA;YAAA;YACA,IAAAiC,KAAA,GAAAH,QAAA;YACAT,KAAA,CAAAxC,gBAAA,CAAAqD,IAAA,CACA;cACA/B,QAAA,EAAAqB,IAAA,CAAArB,QAAA;cACAH,MAAA,EAAAwB,IAAA,CAAAxB,MAAA;cACAM,WAAA;cACAxC,gBAAA;cACAC,eAAA;cACAwC,cAAA;cACAC,eAAA;cACAf,QAAA,GACA;gBACAjE,UAAA,EAAAgG,IAAA,CAAAxB,MAAA;gBACAtD,aAAA,EAAAuF,KAAA,CAAAxB,oBAAA,GAAAwB,KAAA,CAAAxB,oBAAA,IAAAC,OAAA;gBACAC,SAAA,EAAAsB,KAAA,CAAAxB,oBAAA,GAAAwB,KAAA,CAAAxB,oBAAA,IAAAE,SAAA;gBACAC,QAAA,EAAAqB,KAAA,CAAAxB,oBAAA,GAAAwB,KAAA,CAAAxB,oBAAA,IAAAI,GAAA;gBACAC,OAAA,EAAAmB,KAAA,CAAAxB,oBAAA,GAAAM,QAAA,CAAAkB,KAAA,CAAAxB,oBAAA,IAAAK,OAAA;gBACAE,KAAA,EAAAiB,KAAA,CAAAxB,oBAAA,GAAAwB,KAAA,CAAAxB,oBAAA,IAAAQ,YAAA;gBACAC,SAAA;kBACA,SAAAG,KAAA,CAAAvG,gBAAA,SAAAgH,QAAA,CAAAP,GAAA,WAAAC,IAAA;oBAAA;sBAAApH,IAAA;sBAAA+G,OAAA,EAAAK,IAAA,CAAAJ;oBAAA;kBAAA;kBACA,SAAAC,KAAA,CAAAvG,gBAAA,SAAAgH,QAAA,CAAAP,GAAA,WAAAC,IAAA;oBAAA;sBAAApH,IAAA;sBAAA+G,OAAA,EAAAK,IAAA,CAAAJ;oBAAA;kBAAA;kBACA,SAAAC,KAAA,CAAAvG,gBAAA,SAAAgH,QAAA,CAAAP,GAAA,WAAAC,IAAA;oBAAA;sBAAApH,IAAA;sBAAA+G,OAAA,EAAAK,IAAA,CAAAJ;oBAAA;kBAAA;kBACA,SAAAC,KAAA,CAAAvG,gBAAA,SAAAgH,QAAA,CAAAP,GAAA,WAAAC,IAAA;oBAAA;sBAAApH,IAAA;sBAAA+G,OAAA,EAAAK,IAAA,CAAAJ;oBAAA;kBAAA;gBACA;cACA;YAEA,CACA;UACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;MACA;IACA;IACA;MACAjC,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QACA,IAAAA,GAAA,IAAAK,MAAA,CAAAC,IAAA,CAAAN,GAAA,EAAAO,MAAA;UACA,KAAAuC,QAAA;QACA;MACA;IACA;IACA;MACAhD,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAK,GAAA;QACA,UAAAG,OAAA,CAAA4C,gBAAA;UACA;QACA;QACA,IAAAC,aAAA;QACA,KAAAC,eAAA,MAAA9C,OAAA,CAAA4C,gBAAA,SAAAC,aAAA;QACA,IAAAE,eAAA;QACAF,aAAA,CAAAd,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAgB,UAAA,CAAAC,YAAA;QAAA,GAAAb,OAAA,WAAAJ,IAAA;UACAA,IAAA,CAAAI,OAAA,WAAAc,OAAA;YACA,KAAAH,eAAA,CAAAG,OAAA,CAAAtB,EAAA;cACAmB,eAAA,CAAAG,OAAA,CAAAtB,EAAA;gBACAuB,IAAA,EAAAD,OAAA,CAAAC,IAAA;gBACAC,KAAA,EAAAF,OAAA,CAAAE;cACA;YACA;cACA,KAAAL,eAAA,CAAAG,OAAA,CAAAtB,EAAA,EAAAuB,IAAA;gBACAJ,eAAA,CAAAG,OAAA,CAAAtB,EAAA,EAAAuB,IAAA,GAAAD,OAAA,CAAAC,IAAA;cACA;cACA,KAAAJ,eAAA,CAAAG,OAAA,CAAAtB,EAAA,EAAAwB,KAAA;gBACAL,eAAA,CAAAG,OAAA,CAAAtB,EAAA,EAAAwB,KAAA,GAAAF,OAAA,CAAAE,KAAA;cACA;YACA;UACA;QACA;QACA,KAAAhE,kBAAA,GAAAc,MAAA,CAAAmD,OAAA,CAAAN,eAAA,EAAAhB,GAAA,WAAAuB,IAAA;UAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA/J,OAAA,EAAA6J,IAAA;YAAA1B,EAAA,GAAA2B,KAAA;YAAA7G,KAAA,GAAA6G,KAAA;UAAA;YACA3B,EAAA,EAAAA,EAAA;YACAuB,IAAA,EAAAzG,KAAA,CAAAyG,IAAA;YACAC,KAAA,EAAA1G,KAAA,CAAA0G;UACA;QAAA;MACA;IACA;IACA9H,gBAAA;MACAkE,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAa,IAAA,MAAA1E,QAAA,cAAA4D,MAAA;QACA;AACA;AACA;AACA;AACA;MACA;IACA;IACA;MACAD,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA,GAEA;IACA;IACAL,gBAAA;MACAM,IAAA;MACAC,SAAA;MACAJ,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAA+D,KAAA,qBAAAhE,MAAA;MACA;IACA;EACA;EACAiE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,oBAAA;MACAC,QAAA;IACA,GAAAC,IAAA,WAAAC,GAAA;MACAJ,MAAA,CAAAhH,YAAA,GAAAoH,GAAA,CAAA/I,IAAA;MACA;MACA;AACA;AACA;MACA2I,MAAA,CAAAhH,YAAA,CAAAyF,OAAA,WAAAJ,IAAA;QACAA,IAAA,CAAAtF,KAAA,GAAAsF,IAAA,CAAAgC,SAAA;QACAhC,IAAA,CAAAvF,KAAA,GAAAuF,IAAA,CAAAiC,SAAA;QACAjC,IAAA,CAAAkC,QAAA,CAAA9B,OAAA,WAAA+B,KAAA;UACAA,KAAA,CAAAzH,KAAA,GAAAyH,KAAA,CAAAH,SAAA;UACAG,KAAA,CAAA1H,KAAA,GAAA0H,KAAA,CAAAF,SAAA;QACA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,SAAA;MACA;MACAD,MAAA,CAAAE,cAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,SAAAnJ,gBAAA,cAAAA,gBAAA;QACA,YAAAoJ,IAAA,CAAA9J,IAAA,CAAA+J,iBAAA;MACA,gBAAArJ,gBAAA;QACA,YAAAqB,YAAA;MACA;IACA;IACAiI,aAAA,WAAAA,cAAA;MACA,YAAAF,IAAA,CAAA9J,IAAA,CAAAiK,sBAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAAJ,IAAA,CAAA9J,IAAA,CAAAmK,kBAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,YAAAN,IAAA,CAAA9J,IAAA,CAAAqK,iBAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAR,IAAA,CAAA9J,IAAA,CAAAuK,yBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,YAAA/F,gBAAA,SAAAA,gBAAA,CAAAe,MAAA,YAAAf,gBAAA,CAAAkC,QAAA,MAAAjC,kBAAA;IACA;EACA;EACA+F,OAAA;IACA1C,QAAA,WAAAA,SAAA;MACA,KAAA2C,WAAA,MAAAzJ,QAAA;MACA,KAAA+C,wBAAA;QACA2G,WAAA;UACArE,OAAA,OAAAlB,OAAA,CAAAC,QAAA,CAAA/C;QACA;QACAsI,QAAA,OAAAxF,OAAA,CAAAC,QAAA,CAAAwF,OAAA,GAAAC,IAAA,CAAAC,KAAA,MAAA3F,OAAA,CAAAC,QAAA,CAAAwF,OAAA,EAAA1D,GAAA,WAAA6D,EAAA;UAAA;YAAA1E,OAAA,EAAA0E;UAAA;QAAA;MACA;MACA,KAAAN,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAA0E,IAAA,MAAAhF,gBAAA,cAAAyE,OAAA,CAAAC,QAAA,CAAA6B,QAAA;MACA,KAAAwD,WAAA,MAAAzJ,QAAA;MACA,KAAAP,gBAAA,QAAA0E,OAAA,CAAAC,QAAA,CAAAtF,QAAA;MACA,KAAA2K,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAA0E,IAAA,MAAA1E,QAAA,qBAAAmE,OAAA,CAAAC,QAAA,CAAA4F,UAAA,GAAAtE,QAAA,MAAAvB,OAAA,CAAAC,QAAA,CAAA4F,UAAA;MACA,KAAAP,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAA0E,IAAA,MAAA1E,QAAA,kBAAAmE,OAAA,CAAAC,QAAA,CAAA6F,OAAA,QAAA9F,OAAA,CAAAC,QAAA,CAAA6F,OAAA,CAAAC,QAAA;MACA,KAAAT,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MACA,KAAAyJ,WAAA,MAAAzJ,QAAA;MAEA,KAAAyJ,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAxH,UAAA;MACA,KAAAwH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA,KAAAkH,WAAA,MAAAlH,YAAA;MACA;MACA,KAAAiB,gBAAA,QAAAW,OAAA,CAAAC,QAAA,CAAAZ,gBAAA;MAEA,KAAA2G,WAAA;IACA;IACAV,WAAA,WAAAA,YAAAW,IAAA,EAAAC,MAAA;MACA,IAAAlL,IAAA,QAAAgF,OAAA,CAAAC,QAAA,CAAAiG,MAAA;MACA,oBAAAA,MAAA;QACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAmL,OAAA;UACAnL,IAAA,GAAAA,IAAA,CAAA6F,KAAA;QACA;MACA;MACA,KAAAN,IAAA,CAAA0F,IAAA,EAAAC,MAAA,EAAAlL,IAAA;IACA;IACAuJ,cAAA,WAAAA,eAAA;MACA,UAAA1I,QAAA,CAAAiK,OAAA;QACA,KAAAvF,IAAA,MAAA1E,QAAA;MACA;MACA,UAAAA,QAAA,CAAAuK,UAAA;QACA,KAAA7F,IAAA,MAAA1E,QAAA,qBAAAwK,IAAA,CAAAC,MAAA,KAAAC,IAAA;MACA;MACA,UAAA1K,QAAA,CAAA2K,MAAA;QACA,KAAAjG,IAAA,MAAA1E,QAAA;MACA;MACA,UAAAA,QAAA,CAAA4K,SAAA;QACA,KAAAlG,IAAA,MAAA1E,QAAA;MACA;MACA,UAAAA,QAAA,CAAA6K,MAAA;QACA,KAAAnG,IAAA,MAAA1E,QAAA;MACA;IACA;IACA8K,UAAA,WAAAA,WAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA;MACA,KAAAC,KAAA,CAAAH,QAAA,EAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,IAAA,CAAAjL,QAAA,CAAAqL,UAAA,GAAAJ,IAAA,CAAAhM,GAAA;UACAgM,IAAA,CAAAjL,QAAA,CAAAlB,QAAA,GAAAmM,IAAA,CAAAnM,QAAA;UACA,IAAAwM,cAAA,EAAAL,IAAA,CAAAjL,QAAA,EAAAiI,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAqD,IAAA;cACAP,MAAA,CAAAQ,QAAA;gBACAvK,OAAA;gBACAlC,IAAA;cACA;cACAkM,IAAA,CAAAQ,SAAA;YACA;cACAT,MAAA,CAAAQ,QAAA,CAAAE,KAAA;YACA;UACA;QACA;UACAC,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAA7D,KAAA;IACA;IACAiE,SAAA,WAAAA,UAAA;MACA,UAAA7L,QAAA,CAAAG,UAAA;QACA,KAAAqL,QAAA,CAAAM,OAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAArM,gBAAA,CAAAC,OAAA;MACA,KAAAI,KAAA;MACA,KAAAiM,gBAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAvM,gBAAA;QACAC,OAAA;QACAC,QAAA,OAAAF,gBAAA,CAAAE;MACA;MACA,KAAAoM,gBAAA;IACA;IACAE,iBAAA,WAAAA,kBAAA9F,GAAA;MACA,KAAA7G,SAAA,OAAA4M,cAAA,CAAAvO,OAAA,MAAAwI,GAAA;MACA,KAAAgG,UAAA;IACA;IACAA,UAAA,WAAAA,WAAApI,GAAA;MACA,KAAAxE,qBAAA,GAAAwE,GAAA;IACA;IACAqI,aAAA,WAAAA,cAAAtN,IAAA,EAAAuN,GAAA;MAAA,IAAAC,MAAA;MACA,SAAA9M,gBAAA,IAAAV,IAAA,SAAAU,gBAAA;QACA,KAAA+M,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA3N,IAAA;QACA,GAAAkJ,IAAA;UACAsE,MAAA,CAAA9L,aAAA;UACA8L,MAAA,CAAAvM,QAAA,CAAAiG,QAAA;UACAsG,MAAA,CAAA9M,gBAAA,GAAAV,IAAA;UACAwN,MAAA,CAAArB,KAAA,CAAApL,SAAA,IAAAyM,MAAA,CAAArB,KAAA,CAAApL,SAAA,CAAA6M,cAAA;UACAJ,MAAA,CAAApJ,iBAAA;QACA,GAAAyJ,KAAA;UACAL,MAAA,CAAAnJ,eAAA;QACA;MACA;QACA,SAAA3D,gBAAA,cAAAA,gBAAA,IAAAV,IAAA;UACA,SAAAsE,WAAA;YACA;YACA;UACA;UACA,KAAAmJ,QAAA;YACAC,iBAAA;YACAC,gBAAA;YACA3N,IAAA;UACA,GAAAkJ,IAAA;YACAsE,MAAA,CAAAzM,SAAA;YACAyM,MAAA,CAAAxM,KAAA;YACAwM,MAAA,CAAA9L,aAAA;YACA8L,MAAA,CAAAvM,QAAA,CAAAiG,QAAA;YACAsG,MAAA,CAAA9M,gBAAA;YACA8M,MAAA,CAAArB,KAAA,CAAApL,SAAA,IAAAyM,MAAA,CAAArB,KAAA,CAAApL,SAAA,CAAA6M,cAAA;YACAJ,MAAA,CAAApJ,iBAAA;YACA,IAAA0J,MAAA,GAAAP,GAAA,CAAAO,MAAA;YACA,IAAAA,MAAA;cACA,IAAAA,MAAA,CAAAC,QAAA;gBACA,IAAAD,MAAA,CAAAE,UAAA;kBACAF,MAAA,CAAAE,UAAA,CAAAC,IAAA;gBACA;cACA;cACAH,MAAA,CAAAG,IAAA;YACA;UACA,GAAAJ,KAAA,cAEA;QACA;UACA,KAAAnN,gBAAA,GAAAV,IAAA;QACA;MACA;IACA;IACAkO,kBAAA,WAAAA,mBAAA7G,GAAA,EAAAiE,MAAA,EAAA6C,SAAA,EAAAC,KAAA;MACA,IAAAtP,IAAA;MACA,IAAAuP,KAAA,QAAA1L,gBAAA,CAAA2L,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAtF,KAAA,IAAAqM,SAAA;MAAA;MACA,IAAAE,KAAA;QACAvP,IAAA,GAAAuP,KAAA,CAAAxM,KAAA;MACA;MACA,OAAA/C,IAAA;IACA;IACAmO,gBAAA,WAAAA,iBAAA;MAAA,IAAAsB,MAAA;MACA,UAAA7N,gBAAA,WAAAc,QAAA,CAAA0F,QAAA,SAAA1F,QAAA,CAAA0F,QAAA,CAAA1B,MAAA;QACA;MACA;MACA,SAAA9E,gBAAA;QACA;MACA;MACA,KAAAI,gBAAA;MACA,KAAA0N,WAAA,GAAAtF,IAAA,WAAAuF,QAAA;QACA,IAAAC,OAAA,GAAAH,MAAA,CAAAI,gBAAA,CAAAF,QAAA,CAAAG,IAAA;QACAL,MAAA,CAAAxN,SAAA,GAAA2N,OAAA;QACAH,MAAA,CAAAvN,KAAA,GAAAyN,QAAA,CAAAzN,KAAA;QACAuN,MAAA,CAAAzN,gBAAA;MACA;IACA;IACA;IACA6N,gBAAA,WAAAA,iBAAAE,WAAA;MACA,KAAAA,WAAA;QACA;MACA;MACA,SAAAnO,gBAAA;QACA,OAAAmO,WAAA,CAAA1H,GAAA,WAAAC,IAAA;UACA;YACAJ,EAAA,EAAAI,IAAA,CAAAJ,EAAA;YACA8H,KAAA,EAAA1H,IAAA,CAAA2H,YAAA;YACAC,QAAA,EAAA5H,IAAA,CAAA6H,YAAA;YACAC,QAAA,EAAA9H,IAAA,CAAA+H,UAAA;YACAC,MAAA,EAAAhI,IAAA,CAAAiI,MAAA;YACAC,YAAA,EAAAlI,IAAA,CAAAmI,WAAA;YACAC,SAAA,EAAApI,IAAA,CAAAqI,UAAA;YACAC,UAAA,EAAAtI,IAAA,CAAAsI,UAAA;YACAC,OAAA,EAAAvI,IAAA,CAAAwI,QAAA;YACAC,UAAA,EAAAzI,IAAA,CAAAyI,UAAA;YACAnK,UAAA,EAAA0B,IAAA,CAAA1B,UAAA;YACAoK,QAAA,EAAA1I,IAAA,CAAA2I,QAAA;YACAC,SAAA,EAAA5I,IAAA,CAAA4I;UACA;QACA;MACA,gBAAAtP,gBAAA;QACA,OAAAmO,WAAA,CAAA1H,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAAgI,MAAA,GAAAhI,IAAA,CAAA6I,MAAA;UACA,OAAA7I,IAAA;QACA;MACA;QACA,OAAAyH,WAAA;MACA;IAEA;IACAL,WAAA,WAAAA,YAAA;MACA,IAAA0B,WAAA,QAAAC,kBAAA,MAAAxP,gBAAA;MACA,SAAAD,gBAAA;QACA;QACA,WAAA0P,6BAAA,EAAAF,WAAA;MACA,gBAAAxP,gBAAA;QACA;QACA,WAAA2P,oBAAA,EAAAH,WAAA;MACA,gBAAAxP,gBAAA;QACA;QACA,WAAA4P,uBAAA,EAAAJ,WAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAI,SAAA;MACA,UAAAC,MAAA;QACAD,SAAA,CAAAE,GAAA,QAAAjP,QAAA,CAAA0F,QAAA;MACA;MACA,SAAAxG,gBAAA;QACA;UACAE,OAAA,EAAA2P,SAAA,CAAA3P,OAAA;UACAC,QAAA,EAAA0P,SAAA,CAAA1P,QAAA;UACAkO,YAAA,EAAAwB,SAAA,CAAAzB,KAAA;UACAG,YAAA,EAAAsB,SAAA,CAAAvB,QAAA,GAAAuB,SAAA,CAAAvB,QAAA,CAAA0B,IAAA;UACAvB,UAAA,EAAAoB,SAAA,CAAArB,QAAA;UACAG,MAAA,EAAAkB,SAAA,CAAAnB,MAAA;UACAW,QAAA,EAAAQ,SAAA,CAAAT,QAAA;UACAP,WAAA,EAAAgB,SAAA,CAAAjB,YAAA;UACAG,UAAA,EAAAc,SAAA,CAAAf,SAAA;UACAE,UAAA,EAAAa,SAAA,CAAAb,UAAA;UACAhK,UAAA,EAAA6K,SAAA,CAAA7K,UAAA;UACA+K,GAAA,EAAAF,SAAA,CAAAE;QACA;MACA,gBAAA/P,gBAAA;QACA6P,SAAA,CAAAN,MAAA,GAAAM,SAAA,CAAAnB,MAAA;QACA,OAAAmB,SAAA;MACA;QACA,OAAAA,SAAA;MACA;IACA;IACAI,mBAAA,WAAAA,oBAAA1L,GAAA,EAAA2L,KAAA;MACA,UAAA3P,QAAA,CAAAiG,QAAA;QACA,KAAAjG,QAAA,CAAAiG,QAAA;MACA;MACA,IAAA2J,OAAA,OAAAC,mBAAA,CAAAjS,OAAA,OAAAoC,QAAA,CAAAiG,QAAA;MACA2J,OAAA,CAAA/I,IAAA,CAAAiJ,KAAA,CAAAF,OAAA,MAAAC,mBAAA,CAAAjS,OAAA,EAAAoG,GAAA;MACA,IAAA2L,KAAA;QACA,KAAA3P,QAAA,CAAAK,SAAA,GAAAsP,KAAA;MACA;MACA,KAAAjL,IAAA,MAAA1E,QAAA,cAAA4P,OAAA;MACA,KAAAnP,aAAA,QAAAT,QAAA,CAAAiG,QAAA;IACA;IACA8J,iBAAA,WAAAA,kBAAA9B,QAAA,EAAA+B,GAAA;MACA,KAAA/B,QAAA;QACA;MACA;MACA,SAAAtN,eAAA,MAAAlB,gBAAA,CAAAyK,QAAA;QACA,IAAA+F,SAAA,QAAAtP,eAAA,MAAAlB,gBAAA,CAAAyK,QAAA,IAAAmD,IAAA,WAAAlH,IAAA;UAAA,OAAAA,IAAA,CAAAtF,KAAA,IAAAoN,QAAA;QAAA;QACA,KAAAgC,SAAA;UACA;QACA;QACA,OAAAA,SAAA,CAAAD,GAAA;MACA;MACA;IACA;IACAE,qBAAA,WAAAA,sBAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA,KAAAtN,iBAAA;IACA;IACAuN,OAAA,WAAAA,QAAA;MACA,UAAAhM,OAAA,CAAAiC,GAAA,WAAAjC,OAAA,CAAAwJ,IAAA,SAAAxJ,OAAA,CAAAwJ,IAAA,CAAApJ,MAAA;QACA;MACA;MACA,IAAA6B,GAAA,QAAAjC,OAAA,CAAAiC,GAAA,SAAAjC,OAAA,CAAAwJ,IAAA;MACA,SAAAlO,gBAAA;QACA;QACA,OAAA2G,GAAA,CAAA4I,MAAA;MACA,gBAAAvP,gBAAA;QACA;QACA,OAAA2G,GAAA,CAAA+H,MAAA;MACA,gBAAA1O,gBAAA;QACA;QACA,OAAA2G,GAAA,CAAAgI,MAAA;MACA,gBAAA3O,gBAAA;QACA;QACA,OAAA2G,GAAA,CAAA4I,MAAA;MACA;IACA;IACAoB,mBAAA,WAAAA,oBAAAjR,IAAA;MACA,KAAA4D,wBAAA,GAAA5D,IAAA;MACA,KAAAa,QAAA,CAAAM,aAAA,GAAAnB,IAAA,CAAAwK,QAAA,CAAAzD,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAkK,EAAA;MAAA;MACA,KAAArQ,QAAA,CAAAM,aAAA,QAAAN,QAAA,CAAAM,aAAA,CAAAoG,MAAA,WAAAP,IAAA,EAAAgH,KAAA,EAAAmD,IAAA;QAAA,OAAAA,IAAA,CAAAhG,OAAA,CAAAnE,IAAA,MAAAgH,KAAA;MAAA;MACA,KAAAnN,QAAA,CAAA4J,OAAA,GAAAC,IAAA,CAAA0G,SAAA,CAAApR,IAAA,CAAAwK,QAAA,CAAAzD,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAd,OAAA;MAAA;MACA,KAAAX,IAAA,MAAA1E,QAAA,mBAAAb,IAAA,CAAAuK,WAAA,CAAArE,OAAA;MACA,KAAArF,QAAA,CAAAwQ,eAAA,GAAArR,IAAA,CAAAuK,WAAA,CAAApE,SAAA;MACA,KAAA1C,iBAAA;MACA,KAAAuH,WAAA;IACA;IACAsG,uBAAA,WAAAA,wBAAA;MACA,KAAAvF,KAAA,CAAAwF,iBAAA,CAAAC,MAAA;IACA;IACAnM,qBAAA,WAAAA,sBAAA;MAAA,IAAAoM,MAAA;MACA,IAAAC,KAAA;MACA,IAAArM,kCAAA,OAAAxE,QAAA,CAAAqB,aAAA,EAAA4G,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA/I,IAAA,CAAAqG,GAAA;UACAoL,MAAA,CAAAlM,IAAA,CAAAmM,KAAA,CAAA7Q,QAAA,cAAAkI,GAAA,CAAA/I,IAAA,CAAAqG,GAAA;QACA;QACA,IAAA0C,GAAA,CAAA/I,IAAA,CAAA2R,OAAA;UACAF,MAAA,CAAAlM,IAAA,CAAAmM,KAAA,CAAA7Q,QAAA,gBAAAkI,GAAA,CAAA/I,IAAA,CAAA2R,OAAA;UACAF,MAAA,CAAAlM,IAAA,CAAAmM,KAAA,CAAA7Q,QAAA,gBAAAkI,GAAA,CAAA/I,IAAA,CAAA2R,OAAA;UAEAF,MAAA,CAAAnI,SAAA;YACAmI,MAAA,CAAA1M,iBAAA;UACA;QACA;QACA0M,MAAA,CAAA1N,eAAA,GAAAgF,GAAA,CAAA/I,IAAA;MACA;IACA;IACA4R,mBAAA,WAAAA,oBAAA;MACA,iBAAA5M,OAAA,CAAA6M,MAAA;QACA;MACA;MACA,SAAA7M,OAAA,CAAAiC,GAAA,SAAAjC,OAAA,CAAAiC,GAAA,CAAA/F,SAAA;QACA;MACA;MACA;IACA;IACA6D,iBAAA,WAAAA,kBAAA;MAAA,IAAA+M,MAAA;MACA;MACA,KAAAnP,YAAA;MACA,SAAA9B,QAAA,CAAAkR,UAAA;QACA,IAAAC,oBAAA;UACA;QAAA,CACA,EAAAlJ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAqD,IAAA;YACA0F,MAAA,CAAAnP,YAAA,GAAAoG,GAAA,CAAAyF,IAAA;YACAsD,MAAA,CAAAjP,gBAAA,OAAA6N,mBAAA,CAAAjS,OAAA,EAAAqT,MAAA,CAAAnP,YAAA;YACAmP,MAAA,CAAAxI,SAAA;cACA;AACA;AACA;AACA;;cAEA,IAAAwI,MAAA,CAAA/N,eAAA;gBACA,IAAA+N,MAAA,CAAA/N,eAAA,CAAAuC,OAAA;kBACA,IAAAA,OAAA,GAAAwL,MAAA,CAAA/N,eAAA,CAAAuC,OAAA,CAAAT,KAAA;kBACA;AACA;AACA;AACA;AACA;kBACAiM,MAAA,CAAAvM,IAAA,CAAAuM,MAAA,CAAAjR,QAAA,gBAAA0F,QAAA,CAAAD,OAAA;kBACAwL,MAAA,CAAAG,YAAA;gBACA;gBACA,IAAAH,MAAA,CAAA/N,eAAA,CAAAmO,WAAA;kBACAJ,MAAA,CAAAvM,IAAA,CAAAuM,MAAA,CAAAjR,QAAA,qBAAAiR,MAAA,CAAA/N,eAAA,CAAAmO,WAAA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACApN,WAAA,WAAAA,YAAA;MAAA,IAAAqN,MAAA;MACA;MACA,KAAAzP,YAAA;MACA,SAAA7B,QAAA,CAAAG,UAAA;QACA,IAAAoR,0BAAA;UACA5M,MAAA,OAAA3E,QAAA,CAAAG;QACA,GAAA8H,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAqD,IAAA;YACA+F,MAAA,CAAAzP,YAAA,GAAAqG,GAAA,CAAAyF,IAAA;YACA2D,MAAA,CAAAvP,gBAAA,OAAA8N,mBAAA,CAAAjS,OAAA,EAAA0T,MAAA,CAAAzP,YAAA;YACAyP,MAAA,CAAA7I,SAAA;cACA,IAAA6I,MAAA,CAAAzP,YAAA,KAAAyP,MAAA,CAAAzP,YAAA,CAAAwL,IAAA,WAAAmE,cAAA;gBAAA,OAAAA,cAAA,CAAAC,MAAA,IAAAH,MAAA,CAAAtR,QAAA,CAAAI,UAAA;cAAA;gBACAkR,MAAA,CAAAtR,QAAA,CAAAI,UAAA;gBACAkR,MAAA,CAAA5M,IAAA,CAAA4M,MAAA,CAAAtR,QAAA;gBACA;cACA;cAEA,IAAAsR,MAAA,CAAApO,eAAA;gBACA,IAAAoO,MAAA,CAAApO,eAAA,CAAAuC,OAAA;kBACA,IAAAA,OAAA,GAAA6L,MAAA,CAAApO,eAAA,CAAAuC,OAAA,CAAAT,KAAA;kBACA,IAAAsM,MAAA,CAAAzP,YAAA,CAAAwL,IAAA,WAAAmE,cAAA;oBAAA,OAAAA,cAAA,CAAAC,MAAA,IAAAhM,OAAA;kBAAA;oBACA,IAAA6L,MAAA,CAAAzP,YAAA,IAAAyP,MAAA,CAAAzP,YAAA,CAAAwL,IAAA,WAAAmE,cAAA;sBAAA,OAAAA,cAAA,CAAAC,MAAA,IAAAhM,OAAA;oBAAA;sBACA6L,MAAA,CAAA5M,IAAA,CAAA4M,MAAA,CAAAtR,QAAA,gBAAA0F,QAAA,CAAAD,OAAA;oBACA;kBACA;gBACA;gBACA,IAAA6L,MAAA,CAAApO,eAAA,CAAAmO,WAAA;kBACAC,MAAA,CAAA5M,IAAA,CAAA4M,MAAA,CAAAtR,QAAA,qBAAAsR,MAAA,CAAApO,eAAA,CAAAmO,WAAA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAK,gBAAA,WAAAA,iBAAA1N,GAAA;MACA,IAAAA,GAAA;QACA,KAAAlC,YAAA,QAAAE,gBAAA,CAAA0E,MAAA,WAAAiL,MAAA;UACA,OAAAA,MAAA,CAAAC,QAAA,CAAAtH,OAAA,CAAAtG,GAAA,WAAA2N,MAAA,CAAAE,QAAA,CAAAvH,OAAA,CAAAtG,GAAA;QACA;MACA;QACA,KAAAlC,YAAA,OAAA+N,mBAAA,CAAAjS,OAAA,OAAAoE,gBAAA;MACA;IACA;IACA8P,gBAAA,WAAAA,iBAAA9N,GAAA;MACA,IAAAA,GAAA;QACA,KAAAnC,YAAA,QAAAE,gBAAA,CAAA2E,MAAA,WAAAiL,MAAA;UACA,OAAAA,MAAA,CAAAC,QAAA,CAAAtH,OAAA,CAAAtG,GAAA,WAAA2N,MAAA,CAAAE,QAAA,CAAAvH,OAAA,CAAAtG,GAAA;QACA;MACA;QACA,KAAAnC,YAAA,OAAAgO,mBAAA,CAAAjS,OAAA,OAAAmE,gBAAA;MACA;IACA;IACAgQ,uBAAA,WAAAA,wBAAA;MACA,KAAAjQ,YAAA,OAAA+N,mBAAA,CAAAjS,OAAA,OAAAoE,gBAAA;IACA;IACAgQ,uBAAA,WAAAA,wBAAA;MACA,KAAAnQ,YAAA,OAAAgO,mBAAA,CAAAjS,OAAA,OAAAmE,gBAAA;IACA;IACAkQ,gBAAA,WAAAA,iBAAA7L,GAAA;MACA,IAAAA,GAAA;QACA,IAAA8L,SAAA,QAAApQ,YAAA,CAAAuL,IAAA,WAAAlH,IAAA;UAAA,OAAAA,IAAA,CAAAsL,MAAA,IAAArL,GAAA;QAAA;QACA,KAAA1B,IAAA,MAAA1E,QAAA,qBAAAkS,SAAA,GAAAA,SAAA,CAAAb,WAAA;MACA;IACA;IACAc,gBAAA,WAAAA,iBAAA/L,GAAA;MACA,IAAAA,GAAA;QACA,IAAA8L,SAAA,QAAArQ,YAAA,CAAAwL,IAAA,WAAAlH,IAAA;UAAA,OAAAA,IAAA,CAAAsL,MAAA,IAAArL,GAAA;QAAA;QACA,KAAA1B,IAAA,MAAA1E,QAAA,qBAAAkS,SAAA,GAAAA,SAAA,CAAAb,WAAA;MACA;IACA;IACAe,oBAAA,WAAAA,qBAAA;MACA,KAAAhB,YAAA;IACA;IACAiB,eAAA,WAAAA,gBAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA,IAAAC,aAAA,QAAAC,eAAA;MACA,KAAAD,aAAA,IAAAA,aAAA,CAAA/N,MAAA;QACA,WAAAiO,OAAA,WAAAC,OAAA,EAAAC,MAAA;UACAD,OAAA;QACA;MACA;MACA,OAAAD,OAAA,CAAAG,GAAA,CAAAL,aAAA;IACA;IACAM,YAAA,WAAAA,aAAA7H,QAAA;MAAA,IAAA8H,OAAA;MACA,WAAAL,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,KAAAG,OAAA,CAAA3H,KAAA,CAAAH,QAAA;UACA2H,MAAA;QACA;QACAG,OAAA,CAAA3H,KAAA,CAAAH,QAAA,EAAAI,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAqH,OAAA;UACA;YACAC,MAAA;UACA;QACA;MACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAO,OAAA;MACA,IAAA5K,GAAA;MACA;MACA,IAAA6K,WAAA;MACA,IAAAA,WAAA,IAAAA,WAAA,CAAAxO,MAAA;QACAwO,WAAA,CAAAxM,OAAA,WAAAwE,QAAA;UACA,IAAA+H,OAAA,CAAA5H,KAAA,CAAAH,QAAA;YACA7C,GAAA,CAAArB,IAAA,CAAAiM,OAAA,CAAAF,YAAA,CAAA7H,QAAA;UACA;QACA;MACA;MACA,IAAAiI,eAAA,QAAA9H,KAAA,CAAAtM,YAAA;MACA,IAAAoU,eAAA;QACA9K,GAAA,CAAArB,IAAA,CAAAiJ,KAAA,CAAA5H,GAAA,MAAA2H,mBAAA,CAAAjS,OAAA,EAAAoV,eAAA,CAAA7H,QAAA;MACA;MACA,OAAAjD,GAAA;IACA;IACA+K,aAAA,WAAAA,cAAA;MACA,IAAA/K,GAAA;MACA,IAAAgL,YAAA,QAAA/O,OAAA,CAAA+O,YAAA;MACA,IAAAA,YAAA,IAAAA,YAAA,CAAA3O,MAAA;QACA,IAAA0L,SAAA,GAAAiD,YAAA,CAAA7F,IAAA,WAAAlH,IAAA;UAAA,OAAAA,IAAA,CAAA6J,GAAA;QAAA;QACA,IAAAC,SAAA,IAAAA,SAAA,CAAApP,KAAA;UACA,IAAAsS,KAAA,GAAAlD,SAAA,CAAApP,KAAA,CAAAmE,KAAA;UACAmO,KAAA,CAAA5M,OAAA,WAAA1I,IAAA;YACAqK,GAAA,CAAArB,IAAA,CAAAhJ,IAAA;UACA;QACA;MACA;MACA,OAAAqK,GAAA;IACA;IACAkL,YAAA,WAAAA,aAAA/I,MAAA;MACA,IAAAjD,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAsE,MAAA;MAAA;MACA,IAAAgJ,YAAA;QACA,OAAAA,YAAA,CAAA/L,IAAA;MACA;QACA;MACA;IACA;IACAiI,MAAA,WAAAA,OAAA1R,IAAA;MACA,IAAAuJ,YAAA;MACA,UAAAjD,OAAA,CAAAmP,QAAA;QACAlM,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA;QACAA,YAAA,QAAA7D,kBAAA;MACA;MACA,KAAA6D,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAA/L,IAAA;MACA;QACA;MACA;IACA;IACAiM,YAAA,WAAAA,aAAA1V,IAAA;MACA,IAAAuJ,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAA/L,IAAA;MACA;QACA;MACA;IACA;IACAkM,aAAA,WAAAA,cAAA3V,IAAA;MACA,SAAAsG,OAAA,CAAAmP,QAAA;QACA;MACA;MACA,IAAAlM,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAA9L,KAAA;MACA;QACA;MACA;IACA;IACAkM,OAAA,WAAAA,QAAA5V,IAAA;MACA,SAAAsG,OAAA,CAAAmP,QAAA;QACA;MACA;MACA,IAAAlM,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAA9L,KAAA;MACA;QACA;MACA;IACA;IACAmM,YAAA,WAAAA,aAAA7V,IAAA;MACA,IAAAuJ,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAAM,IAAA;MACA;QACA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA/V,IAAA;MACA,IAAAuJ,YAAA,QAAAjD,OAAA,CAAAiD,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA7C,MAAA;QACA;MACA;MACA,IAAA8O,YAAA,GAAAjM,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,IAAAlI,IAAA;MAAA;MACA,IAAAwV,YAAA;QACA,OAAAA,YAAA,CAAAM,IAAA;MACA;QACA;MACA;IACA;IACAE,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAAvT,QAAA;MACA;MACA,IAAAwS,WAAA;MACA,IAAAA,WAAA,IAAAA,WAAA,CAAAxO,MAAA;QACAwO,WAAA,CAAAxM,OAAA,WAAAwE,QAAA;UACA,IAAA+I,OAAA,CAAA5I,KAAA,CAAAH,QAAA;YACA+I,OAAA,CAAAvT,QAAA,OAAA4L,cAAA,CAAAvO,OAAA,MAAAuO,cAAA,CAAAvO,OAAA,MAAAkW,OAAA,CAAAvT,QAAA,GAAAuT,OAAA,CAAA/I,QAAA;UACA;QACA;MACA;MACA,SAAA5G,OAAA,CAAA+O,YAAA;QACA,IAAA9F,KAAA,QAAAjJ,OAAA,CAAA+O,YAAA,CAAA7F,IAAA,WAAAlH,IAAA;UAAA,OAAAA,IAAA,CAAA6J,GAAA;QAAA;QACA,IAAA5C,KAAA;UACA,KAAA7M,QAAA,CAAAwT,YAAA,GAAA3G,KAAA,CAAAvM,KAAA;QACA;MACA;MACA;AACA;AACA;MACA,SAAAN,QAAA,SAAAA,QAAA,CAAAF,SAAA;QACA,IAAA2T,KAAA,CAAAC,OAAA,MAAA1T,QAAA,CAAAF,SAAA;UACA,KAAAE,QAAA,CAAAF,SAAA,QAAAE,QAAA,CAAAF,SAAA,CAAAoP,IAAA;QACA;MACA;QACA,SAAAhQ,gBAAA;UACA;UACA,KAAAc,QAAA,CAAAF,SAAA;QACA;MACA;;MAEA;MACA,IAAA6T,eAAA,QAAAhJ,KAAA,CAAAtM,YAAA;MACA,IAAAsV,eAAA;QACA,KAAA3T,QAAA,CAAAiD,gBAAA,GAAA0Q,eAAA,CAAApJ,UAAA;MACA;MACA,YAAAvK,QAAA;IACA;IACA4T,YAAA,WAAAA,aAAA/N,GAAA,EAAA+G,KAAA;MACA,SAAAhJ,OAAA,WAAAA,OAAA,CAAAiQ,UAAA,SAAAjQ,OAAA,CAAAiQ,UAAA,SAAAjQ,OAAA,CAAAiQ,UAAA,qBAAAjQ,OAAA,CAAAiC,GAAA,SAAAjC,OAAA,CAAAiC,GAAA,CAAAH,QAAA,SAAA9B,OAAA,CAAAiC,GAAA,CAAAH,QAAA,CAAAoH,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,IAAAC,GAAA,CAAAL,EAAA;MAAA;QACA;MACA;MACA,QAAAK,GAAA,CAAA2I,SAAA,IAAA3I,GAAA,CAAA2I,SAAA;IACA;IACAsF,gBAAA,WAAAA,iBAAA;MACA,UAAA5U,gBAAA;QACA,KAAA+L,QAAA,CAAAE,KAAA;QACA;MACA;MACA,KAAApM,qBAAA;IACA;IACAgV,UAAA,WAAAA,WAAAC,IAAA;MACA,UAAAnN,YAAA;QACA;MACA;MACA,IAAAgG,KAAA,QAAAhG,YAAA,CAAAiG,IAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA,KAAAwO,IAAA;MAAA;MACA,IAAAnH,KAAA,IAAAA,KAAA,CAAApM,QAAA;QACA;MACA;MACA;IACA;IACAmJ,WAAA,WAAAA,YAAA;MACA;AACA;AACA;AACA;AACA;AACA;MACA,KAAAqK,OAAA,CAAAA,OAAA,CAAAA,OAAA,SAAAA,OAAA,CAAAA,OAAA,CAAAA,OAAA,CAAAC,cAAA,SAAAD,OAAA,CAAAA,OAAA,CAAAA,OAAA,CAAAC,cAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,YAAAb,YAAA;IACA;IACA5M,eAAA,WAAAA,gBAAA0N,QAAA,EAAAC,MAAA,EAAAC,GAAA;MACA,KAAAF,QAAA;QACA;MACA;MACAE,GAAA,CAAAhO,IAAA;QACA+N,MAAA,EAAAD,QAAA,CAAAC,MAAA;QACAzN,UAAA,EAAAwN,QAAA,CAAAxN,UAAA;QACA2N,KAAA,EAAAH,QAAA,CAAAG,KAAA;QACA/V,IAAA,EAAA4V,QAAA,CAAA5V;MACA;MACA,IAAA4V,QAAA,CAAAC,MAAA,KAAAA,MAAA;QACA;MACA;QACA,YAAA3N,eAAA,CAAA0N,QAAA,CAAAI,SAAA,EAAAH,MAAA,EAAAC,GAAA;MACA;IACA;IACAG,OAAA,WAAAA,QAAA;MACA,KAAA9J,KAAA,CAAAtM,YAAA,SAAAsM,KAAA,CAAAtM,YAAA,CAAAoW,OAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAjR,GAAA;MACA,KAAAA,GAAA;QACA,KAAAP,kBAAA;MACA;QACA,KAAAA,kBAAA,GAAAO,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}