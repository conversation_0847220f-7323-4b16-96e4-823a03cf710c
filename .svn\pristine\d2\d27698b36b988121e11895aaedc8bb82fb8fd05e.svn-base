use aqsoc;

CREATE TABLE ffsafe_host_events (
                                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
                                    host_ip VARCHAR(45) COMMENT '主机IP',
                                    monitor_item VARCHAR(255) COMMENT '检测事项',
                                    overall_risk VARCHAR(50) COMMENT '综合风险',
                                    category VARCHAR(100) COMMENT '类别',
                                    handling_status VARCHAR(50) COMMENT '处置状态',
                                    alarm_time DATETIME COMMENT '告警时间',
                                    host_name VARCHAR(255) COMMENT '主机名称',
                                    operating_system VARCHAR(255) COMMENT '操作系统',
                                    md5_hash VARCHAR(32) COMMENT 'MD5哈希值',
                                    suspicious_file VARCHAR(500) COMMENT '可疑文件',
                                    sha256_hash VARCHAR(64) COMMENT 'SHA256哈希值',
                                    file_create_time DATETIME COMMENT '文件创建时间',
                                    file_modify_time DATETIME COMMENT '文件修改时间',
                                    inspection_engine VARCHAR(100) COMMENT '检查引擎',
                                    anomaly_type VARCHAR(100) COMMENT '异常类型',
                                    detection_info TEXT COMMENT '检出信息',
                                    ai_model VARCHAR(500) COMMENT 'AI模型名称',
                                    device_config_id BIGINT COMMENT '所属探针',
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间'
) COMMENT='主机事件';
-- =====================================================
-- 1. 扩展设备配置表，新增主机入侵攻击最后更新时间字段
-- =====================================================
ALTER TABLE tbl_device_config
    ADD COLUMN host_intrusion_last_time datetime DEFAULT NULL COMMENT '主机入侵攻击最后更新时间';

-- =====================================================
-- 2. 创建主机入侵攻击主表
-- =====================================================
CREATE TABLE `ffsafe_host_intrusion_attack` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ff_id` int NOT NULL COMMENT '非凡返回的ID',
  `sip` varchar(30) NOT NULL COMMENT '攻击源IP',
  `dip` varchar(30) NOT NULL COMMENT '目标IP',
  `dip_name` varchar(100) DEFAULT NULL COMMENT '目标IP主机名',
  `alert_name` varchar(200) NOT NULL COMMENT '告警名称',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `update_time` datetime DEFAULT NULL COMMENT '最近告警时间',
  `handle_state` tinyint DEFAULT '0' COMMENT '处置状态: 0=未处置,1=已处置,2=忽略',
  `handle_desc` varchar(255) DEFAULT NULL COMMENT '处置描述',
  `disposer` varchar(255) DEFAULT NULL COMMENT '处置人',
  `device_config_id` bigint DEFAULT NULL COMMENT '设备配置ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ff_id_device` (`ff_id`, `device_config_id`),
  KEY `idx_sip` (`sip`),
  KEY `idx_dip` (`dip`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_handle_state` (`handle_state`),
  KEY `idx_device_config_id` (`device_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='主机入侵攻击事件表';

-- =====================================================
-- 3. 创建主机入侵攻击详情表
-- =====================================================
CREATE TABLE `ffsafe_host_intrusion_attack_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `attack_id` bigint NOT NULL COMMENT '关联主表ID',
  `detail_type` varchar(20) NOT NULL COMMENT '详情类型: brute_force=暴力破解,web_attack=Web攻击,vuln_scan=漏洞扫描',
  `detail_data` json NOT NULL COMMENT '详情数据JSON格式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_attack_id` (`attack_id`),
  KEY `idx_detail_type` (`detail_type`),
  CONSTRAINT `fk_attack_detail_attack_id` FOREIGN KEY (`attack_id`) REFERENCES `ffsafe_host_intrusion_attack` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='主机入侵攻击详情表';

ALTER TABLE tbl_attack_alarm
ADD COLUMN handle_state BIGINT DEFAULT '0' COMMENT '处置状态',
ADD COLUMN handle_time DATETIME COMMENT '处置时间',
ADD COLUMN handle_desc VARCHAR(255) COMMENT '处置描述或备注',
ADD COLUMN handle_user BIGINT COMMENT '处置人';

-- 添加唯一索引
ALTER TABLE ffsafe_host_intrusion_attack_detail 
ADD UNIQUE KEY uk_attack_detail_type (attack_id, detail_type);

-- 1. 添加攻击方向字段到威胁告警表
ALTER TABLE tbl_threaten_alarm ADD COLUMN attack_direction VARCHAR(10) DEFAULT '4' COMMENT '攻击方向：1-内对内，2-内对外，3-外对内，4-未知';

ALTER TABLE tbl_operate_work_record
    ADD COLUMN update_time datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
