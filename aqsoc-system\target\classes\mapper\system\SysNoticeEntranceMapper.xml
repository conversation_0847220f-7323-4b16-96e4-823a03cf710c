<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysNoticeEntranceMapper">

    <resultMap type="SysNoticeEntrance" id="SysNoticeEntranceResult">
        <result property="id"             column="id"             />
        <result property="noticeId"       column="notice_id"      />
        <result property="entranceType"   column="entrance_type"  />
        <result property="entranceId"     column="entrance_id"    />
        <result property="entranceName"   column="entrance_name"  />
        <result property="entranceUrl"    column="entrance_url"   />
        <result property="sortOrder"      column="sort_order"     />
        <result property="createTime"     column="create_time"    />
        <result property="updateTime"     column="update_time"    />
    </resultMap>

    <sql id="selectSysNoticeEntranceVo">
        select id, notice_id, entrance_type, entrance_id, entrance_name, entrance_url, sort_order, create_time, update_time
        from sys_notice_entrance
    </sql>

    <select id="selectSysNoticeEntranceList" parameterType="SysNoticeEntrance" resultMap="SysNoticeEntranceResult">
        <include refid="selectSysNoticeEntranceVo"/>
        <where>
            <if test="noticeId != null">
                AND notice_id = #{noticeId}
            </if>
            <if test="entranceType != null and entranceType != ''">
                AND entrance_type = #{entranceType}
            </if>
            <if test="entranceName != null and entranceName != ''">
                AND entrance_name like concat('%', #{entranceName}, '%')
            </if>
        </where>
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <select id="selectSysNoticeEntranceById" parameterType="Long" resultMap="SysNoticeEntranceResult">
        <include refid="selectSysNoticeEntranceVo"/>
        where id = #{id}
    </select>

    <select id="selectSysNoticeEntranceByNoticeId" parameterType="Long" resultMap="SysNoticeEntranceResult">
        <include refid="selectSysNoticeEntranceVo"/>
        where notice_id = #{noticeId}
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <insert id="insertSysNoticeEntrance" parameterType="SysNoticeEntrance" useGeneratedKeys="true" keyProperty="id">
        insert into sys_notice_entrance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            notice_id,
            <if test="entranceType != null and entranceType != ''">entrance_type,</if>
            <if test="entranceId != null">entrance_id,</if>
            <if test="entranceName != null and entranceName != ''">entrance_name,</if>
            <if test="entranceUrl != null">entrance_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{noticeId},
            <if test="entranceType != null and entranceType != ''">#{entranceType},</if>
            <if test="entranceId != null">#{entranceId},</if>
            <if test="entranceName != null and entranceName != ''">#{entranceName},</if>
            <if test="entranceUrl != null">#{entranceUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateSysNoticeEntrance" parameterType="SysNoticeEntrance">
        update sys_notice_entrance
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="entranceType != null and entranceType != ''">entrance_type = #{entranceType},</if>
            <if test="entranceId != null">entrance_id = #{entranceId},</if>
            <if test="entranceName != null and entranceName != ''">entrance_name = #{entranceName},</if>
            <if test="entranceUrl != null">entrance_url = #{entranceUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysNoticeEntranceById" parameterType="Long">
        delete from sys_notice_entrance where id = #{id}
    </delete>

    <delete id="deleteSysNoticeEntranceByIds" parameterType="String">
        delete from sys_notice_entrance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSysNoticeEntranceByNoticeId" parameterType="Long">
        delete from sys_notice_entrance where notice_id = #{noticeId}
    </delete>

</mapper>
