package cn.anmte.aqsoc.work.service.impl;

import cn.anmte.aqsoc.common.SystemUtilService;
import cn.anmte.aqsoc.work.domain.WorkHwResultLink;
import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.entity.WorkHw;
import cn.anmte.aqsoc.work.entity.WorkHwDay;
import cn.anmte.aqsoc.work.mapper.WorkHwMapper;
import cn.anmte.aqsoc.work.model.StagesData;
import cn.anmte.aqsoc.work.service.IWorkHwDayService;
import cn.anmte.aqsoc.work.service.IWorkHwResultLinkService;
import cn.anmte.aqsoc.work.service.IWorkHwService;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.hash.Hash;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.service.ITblOperateWorkRecordService;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.stereotype.Service;
import cn.anmte.aqsoc.work.model.WorkHwForm;
import cn.anmte.aqsoc.work.model.WorkHwListVO;
import cn.anmte.aqsoc.work.model.WorkHwPagination;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.beans.BeanUtils;
import com.fumixuan.gencode.common.util.DataException;

import javax.annotation.Resource;

/**
 * <p>
 * 护网事务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
public class WorkHwServiceImpl extends ServiceImpl<WorkHwMapper, WorkHw> implements IWorkHwService {

    public static final String DOING = "1";
    public static final String INIT = "0";
    public static final String DONE = "2";
    @Resource
    private IWorkHwDayService workHwDayService;
    @Resource
    private SystemUtilService systemUtilService;
    @Resource
    private IWorkHwTaskService workHwTaskService;
    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private ITblOperateWorkRecordService operateWorkRecordService;
    @Resource
    private IWorkHwResultLinkService workHwResultLinkService;

    @Override
    public IPage<WorkHwListVO> queryPage(IPage<WorkHwListVO> page, WorkHwPagination pagination) {
        IPage<WorkHwListVO> workHwListVOIPage = baseMapper.queryPage(page, pagination);
        if(workHwListVOIPage != null && CollUtil.isNotEmpty(workHwListVOIPage.getRecords())){
            workHwListVOIPage.getRecords().forEach(this::handleStages);
        }
        return workHwListVOIPage;
    }

    @Override
    public WorkHwListVO queryById(String id) {
        WorkHwListVO workHwListVO = baseMapper.queryById(id);
        handleStages(workHwListVO);
        return workHwListVO;
    }

    private void handleStages(WorkHwListVO workHwListVO){
        if(workHwListVO != null){
            //查询计划任务
            WorkHwTask queryHwTask = new WorkHwTask();
            queryHwTask.setWorkId(workHwListVO.getId());
            List<WorkHwTask> taskList = workHwTaskService.selectWorkHwTaskList(queryHwTask);
            //查询任务关联的事务记录
            List<TblOperateWorkRecord> operateWorkRecordList = new ArrayList<>();
            if(CollUtil.isNotEmpty(taskList)){
                List<String> flowTaskIds = taskList.stream().filter(task -> StrUtil.isNotBlank(task.getFlowTaskId())).collect(Collectors.toList()).stream().map(WorkHwTask::getFlowTaskId)
                        .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(flowTaskIds)){
                    TblOperateWorkRecord queryOperateWorkRecord = new TblOperateWorkRecord();
                    queryOperateWorkRecord.setFlowTaskIdList(flowTaskIds);
                    queryOperateWorkRecord.setQueryAll(true);
                    queryOperateWorkRecord.setQueryAllData(true);
                    operateWorkRecordList = operateWorkRecordService.selectTblOperateWorkRecordListByFlowIds(queryOperateWorkRecord);
                }
            }
            //查询关联的hw结果链接列表
            List<WorkHwResultLink> resultLinkList = new ArrayList<>();
            if(CollUtil.isNotEmpty(operateWorkRecordList)){
                List<Long> recordIds = operateWorkRecordList.stream().map(TblOperateWorkRecord::getId).collect(Collectors.toList());
                resultLinkList = workHwResultLinkService.list(new LambdaQueryWrapper<WorkHwResultLink>().in(WorkHwResultLink::getWorkRecordId, recordIds));
            }
            //查询阶段
            SysDictData queryDictData = new SysDictData();
            queryDictData.setDictType("hw_stage_class");
            List<SysDictData> dictDataList = dictDataService.selectDictDataList(queryDictData);
            List<TblOperateWorkRecord> finalOperateWorkRecordList = operateWorkRecordList;
            List<WorkHwResultLink> finalResultLinkList = resultLinkList;
            List<StagesData.Stage> stageList = dictDataList.stream().map(dictData -> {
                StagesData.Stage stage = new StagesData.Stage(dictData.getDictSort().intValue(), 0, "0", dictData.getDictLabel());
                List<WorkHwTask> matchTasks = taskList.stream().filter(task -> task.getStageClass().equals(dictData.getDictValue())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(matchTasks)){
                    int index = 1;
                    List<StagesData.Step> stepList = matchTasks.stream().map(task -> {
                        StagesData.Step step = new StagesData.Step(task.getTaskName(), index, "0",
                                DateUtil.formatDateTime(task.getStartTime()), DateUtil.formatDateTime(task.getEndTime()), DateUtil.formatDateTime(task.getCompleteTime()),
                                "", task.getContent(), StrUtil.toStringOrNull(task.getManageUser()), new ArrayList<>(1));
                        step.setUserNames(task.getManageUserName());
                        if(task.getCompleteTime() == null && StrUtil.isNotBlank(task.getFlowTaskId())){
                            step.setState(WorkHwServiceImpl.DOING);
                        }
                        if(StrUtil.isNotBlank(step.getFinishTime())){
                            step.setState(WorkHwServiceImpl.DONE);
                        }
                        //匹配当前任务的事务记录
                        List<TblOperateWorkRecord> matchWorkRecordList = finalOperateWorkRecordList.stream().filter(record -> record.getFFlowtaskid().equals(task.getFlowTaskId())).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(matchWorkRecordList)){
                            String files = matchWorkRecordList.get(0).getFiles();
                            if(StrUtil.isNotBlank(files)){
                                List<JSONObject> srcFileList = JSONUtil.toList(files, JSONObject.class);
                                List<StagesData.Step.File> fileList = srcFileList.stream().map(item -> new StagesData.Step.File(item.getString("name"), item.getString("url"))).collect(Collectors.toList());
                                step.setFiles(fileList);
                                step.setNeedFile(1);
                            }
                            //匹配当前的结果链接
                            List<StagesData.Step.Link> linkList = new ArrayList<>();
                            finalResultLinkList.stream().filter(link -> link.getWorkRecordId().equals(matchWorkRecordList.get(0).getId()))
                                    .forEach(resultLink -> {
                                        linkList.add(new StagesData.Step.Link(null,resultLink.getLabel(), resultLink.getLinkUrl()));
                                    });
                            step.setLinks(linkList);
                        }
                        return step;
                    }).collect(Collectors.toList());
                    stage.setSteps(stepList);
                }
                if(CollUtil.isEmpty(stage.getSteps())){
                    //没有步骤，不显示
                    return null;
                }
                int completeNum = 0;
                if(CollUtil.isNotEmpty(stage.getSteps())){
                    for (StagesData.Step step : stage.getSteps()) {
                        if(StrUtil.isNotBlank(step.getState()) && step.getState().equals(WorkHwServiceImpl.DONE)){
                            completeNum++;
                        }
                    }
                }
                if(!CollUtil.isEmpty(stage.getSteps())){
                    double div = NumberUtil.div(completeNum, stage.getSteps().size(), 2);
                    stage.setPercent(Double.valueOf(div*100).intValue());
                }
                return stage;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            StagesData stagesData = new StagesData();
            stagesData.setStages(stageList);
            workHwListVO.setDataJson(JSON.toJSONString(stagesData));
        }
    }


    @Transactional
    @Override
    public WorkHw updateByForm(WorkHwForm form) {
        Assert.notNull(form, "from is null");
        if (form.getId() == null || form.getId() == 0L) {
            //新增
            WorkHw save = new WorkHw();
            BeanUtils.copyProperties(form, save);
            StagesData stagesData = new StagesData();
            save.setDataJson(JSONUtil.toJsonStr(stagesData));
            this.save(save);
            // 创建日表
            saveHwDay(form, save.getId());
            return save;
        } else {
            WorkHw byId = this.getById(form.getId());
            if (byId == null) {
                throw new DataException("数据不存在");
            }
            BeanUtils.copyProperties(form, byId);
            this.updateById(byId);
            //删除天表
            workHwDayService.remove(Wrappers.<WorkHwDay>lambdaQuery().eq(WorkHwDay::getHwId, form.getId()));
            saveHwDay(form, form.getId());
            return byId;
        }
    }

    @Override
    public WorkHw updateStages(WorkHwForm form) {
        WorkHw byId = this.getById(form.getId());
        if (byId == null) {
            throw new DataException("数据不存在");
        }
        String dataJson = form.getDataJson();
        //1.需要更新技术支撑单位和人员
        //2.需要更新阶段的状态和百分比
        Set<String> userIdsSet = new HashSet<>();
        StagesData bean = JSONUtil.toBean(dataJson, StagesData.class);
        boolean allowStageDoing = true;
        boolean setNextStart = false;
        for (StagesData.Stage stage : bean.getStages()) {
            int finish = 0;
            boolean stageDoing = false;
            for (StagesData.Step step : stage.getSteps()) {
                // 1责任人
                if (StrUtil.isNotEmpty(step.getUserIds())) {
                    String[] split = step.getUserIds().split(",");
                    userIdsSet.addAll(Arrays.asList(split));
                }
                // 2 百分比
                if (DONE.equals(step.getState())) {
                    finish++;
                }
                // 3状态，需要本阶段在进行中
                if (StrUtil.isNotEmpty(step.getStartTime()) && StrUtil.isNotEmpty(step.getEndTime())) {
                    DateTime now = DateUtil.date();
                    DateTime stepStartTime = DateUtil.parseDateTime(step.getStartTime());
                    DateTime stepEndTime = DateUtil.parseDateTime(step.getEndTime());
                    if (stepStartTime.before(now) && stepEndTime.after(now) && !DONE.equals(step.getState())) {
                        step.setState(DOING);
                        stageDoing = true;
                    } else if (stepEndTime.before(now) && !DONE.equals(step.getState())) {
                        stageDoing = true;
                        step.setState("3");
                    }else if (stepStartTime.after(now) ) {
                        step.setState("0");
                    }
                }
            }
            int percent = finish * 100 / stage.getSteps().size();
            stage.setPercent(percent);
            if (stageDoing && allowStageDoing && INIT.equals(stage.getState())) {
                stage.setState(DOING);
                allowStageDoing = false;
            } else if (DOING.equals(stage.getState())) {
                allowStageDoing = false;
            }
            if (percent == 100) {
                stage.setState(DONE);
                //下一阶段设置为开始
                setNextStart=true;
            } else if (percent > 0) {
                stage.setState(DOING);
            } else if(setNextStart){
                stage.setState(DOING);
                setNextStart=false;
            }
        }
        // 1责任人&支撑单位
        byId.setSupportUsers(String.join(",", userIdsSet));
        systemUtilService.refreshUserCache();
        byId.setSupportOrgs(systemUtilService.getDeptNames(byId.getSupportUsers()));

        byId.setDataJson(JSONUtil.toJsonStr(bean));
        this.updateById(byId);
        return byId;
    }

    private void saveHwDay(WorkHwForm form, Integer id) {
        Duration between = LocalDateTimeUtil.between(form.getHwStart(), form.getHwEnd());
        long days = between.toDays();

        LocalDateTime next = form.getHwStart();
        String day = LocalDateTimeUtil.formatNormal(form.getHwStart().toLocalDate());
        DayOfWeek dayOfWeek = form.getHwStart().getDayOfWeek();

        for (long i = 0; i <= days; i++) {
            //
            WorkHwDay workHwDay = new WorkHwDay();
            workHwDay.setDate(day);
            workHwDay.setDayWeek(SystemUtilService.getDayWeek(dayOfWeek));
            workHwDay.setIsSafe(INIT);
            workHwDay.setHwId(id);
            workHwDayService.save(workHwDay);

            next = next.plusDays(1);
            day = LocalDateTimeUtil.formatNormal(next.toLocalDate());
            dayOfWeek = next.getDayOfWeek();
        }
    }
}
