<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysNoticeMapper">

    <resultMap type="SysNotice" id="SysNoticeResult">
        <result property="noticeId"       column="notice_id"       />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"     column="notice_type"     />
        <result property="noticeContent"  column="notice_content"  />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />

        <result property="businessId"     column="business_id"     />
        <result property="isTop"          column="is_top"          />
        <result property="isPopup"        column="is_popup"        />
        <result property="deadline"       column="deadline"        />
        <result property="popupContent"   column="popup_content"   />
        <result property="acceptType"     column="accept_type"     />
        <result property="acceptIds"      column="accept_ids"      />
        <result property="createUserId"   column="user_id"         />
        <result property="createDeptId"   column="dept_id"         />
    </resultMap>

    <resultMap type="com.ruoyi.system.domain.vo.SysNoticeHomeVO" id="SysNoticeHomeResult">
        <result property="noticeId"       column="notice_id"       />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"     column="notice_type"     />
        <result property="noticeContent"  column="notice_content"  />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />

        <result property="businessId"     column="business_id"     />
        <result property="isTop"          column="is_top"          />
        <result property="isPopup"        column="is_popup"        />
        <result property="deadline"       column="deadline"        />
        <result property="popupContent"   column="popup_content"   />
        <result property="acceptType"     column="accept_type"     />
        <result property="acceptIds"      column="accept_ids"      />
        <result property="readStatus"     column="read_status"     />
    </resultMap>

    <sql id="selectNoticeVo">
        select notice_id, notice_title, notice_type, cast(notice_content as char) as notice_content, status, business_id,
               is_top, is_popup, deadline, popup_content, accept_type, accept_ids,
               create_by, create_time, update_by, update_time, remark, user_id, dept_id
        from sys_notice
    </sql>

    <select id="selectNoticeById" parameterType="Long" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where notice_id = #{noticeId}
    </select>

    <select id="selectNoticeList" parameterType="SysNotice" resultMap="SysNoticeResult">
        select n.notice_id, n.notice_title, n.notice_type, cast(n.notice_content as char) as notice_content, n.status, n.business_id,
               n.is_top, n.is_popup, n.deadline, n.popup_content, n.accept_type, n.accept_ids,
               n.create_by, n.create_time, n.update_by, n.update_time, n.remark, n.user_id, n.dept_id
        from sys_notice n
        <where>
            <if test="noticeTitle != null and noticeTitle != ''">
                AND n.notice_title like concat('%', #{noticeTitle}, '%')
            </if>
            <if test="noticeType != null and noticeType != ''">
                AND n.notice_type = #{noticeType}
            </if>
            <if test="createBy != null and createBy != ''">
                AND n.create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="startDate != null and endDate != null ">
                AND n.create_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="businessId != null and businessId != ''">
                AND n.business_id  = #{businessId}
            </if>
            <!-- 数据权限过滤条件将由DataScopeAspect自动注入 -->
            ${params.dataScope}
		</where>
        ORDER BY n.create_time DESC
    </select>

    <select id="selectNoticeByBusinessAndAcceptId" resultType="com.ruoyi.system.domain.SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where business_id = #{businessId}
          AND (
            accept_type = 'all'
            OR (accept_type = 'user' AND JSON_CONTAINS(accept_ids, CAST(#{userId} AS JSON)))
            OR (accept_type = 'role' AND EXISTS (
                SELECT 1 FROM sys_user_role ur
                WHERE ur.user_id = #{userId}
                AND JSON_CONTAINS(accept_ids, CAST(ur.role_id AS JSON))
            ))
            OR (accept_type = 'dept' AND EXISTS (
                SELECT 1 FROM sys_user u
                WHERE u.user_id = #{userId}
                AND JSON_CONTAINS(accept_ids, CAST(u.dept_id AS JSON))
            ))
          )
    </select>

    <insert id="insertNotice" parameterType="SysNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into sys_notice (
        <if test="noticeTitle != null and noticeTitle != '' ">notice_title, </if>
        <if test="noticeType != null and noticeType != '' ">notice_type, </if>
        <if test="noticeContent != null and noticeContent != '' ">notice_content, </if>
        <if test="status != null and status != '' ">status, </if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>

        <if test="businessId != null"> business_id,</if>
        <if test="isTop != null and isTop != ''">is_top,</if>
        <if test="isPopup != null and isPopup != ''">is_popup,</if>
        <if test="deadline != null">deadline,</if>
        <if test="popupContent != null and popupContent != ''">popup_content,</if>
        <if test="acceptType != null and acceptType != ''">accept_type,</if>
        <if test="acceptIds != null and acceptIds != ''">accept_ids,</if>
        <if test="createUserId != null">user_id,</if>
        <if test="createDeptId != null">dept_id,</if>
        create_time
        )values(
        <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle}, </if>
        <if test="noticeType != null and noticeType != ''">#{noticeType}, </if>
        <if test="noticeContent != null and noticeContent != ''">#{noticeContent}, </if>
        <if test="status != null and status != ''">#{status}, </if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>

        <if test="businessId != null">#{businessId},</if>
        <if test="isTop != null and isTop != ''">#{isTop},</if>
        <if test="isPopup != null and isPopup != ''">#{isPopup},</if>
        <if test="deadline != null">#{deadline},</if>
        <if test="popupContent != null and popupContent != ''">#{popupContent},</if>
        <if test="acceptType != null and acceptType != ''">#{acceptType},</if>
        <if test="acceptIds != null and acceptIds != ''">#{acceptIds},</if>
        <if test="createUserId != null">#{createUserId},</if>
        <if test="createDeptId != null">#{createDeptId},</if>
        sysdate()
        )
    </insert>

    <update id="updateNotice" parameterType="SysNotice">
        update sys_notice
        <set>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle}, </if>
            <if test="noticeType != null and noticeType != ''">notice_type = #{noticeType}, </if>
            <if test="noticeContent != null">notice_content = #{noticeContent}, </if>
            <if test="status != null and status != ''">status = #{status}, </if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>

            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="isPopup != null">is_popup = #{isPopup},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="popupContent != null">popup_content = #{popupContent},</if>
            <if test="acceptType != null">accept_type = #{acceptType},</if>
            <if test="acceptIds != null">accept_ids = #{acceptIds},</if>
            <if test="createUserId != null">user_id = #{createUserId},</if>
            <if test="createDeptId != null">dept_id = #{createDeptId},</if>
            update_time = sysdate()
        </set>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteNoticeById" parameterType="Long">
        delete from sys_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteNoticeByIds" parameterType="Long">
        delete from sys_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <select id="selectPopupNoticeList" parameterType="SysNotice" resultMap="SysNoticeResult">
        SELECT
            n.notice_id, n.notice_title, n.notice_type,
            cast(n.notice_content as char) as notice_content,
            n.status, n.business_id,
            n.is_top, n.is_popup, n.deadline, n.popup_content,
            n.accept_type, n.accept_ids,
            n.create_by, n.create_time, n.update_by, n.update_time, n.remark
        FROM sys_notice n
        LEFT JOIN sys_notice_read_status r ON n.notice_id = r.notice_id AND r.user_id = #{userId}
        WHERE n.status = '0'
          AND n.is_popup = '1'
          AND (n.deadline IS NULL OR n.deadline > NOW())
          AND (
            n.accept_type = 'all'
            OR (n.accept_type = 'user' AND JSON_CONTAINS(n.accept_ids, CAST(#{userId} AS JSON)))
            OR (n.accept_type = 'role' AND EXISTS (
                SELECT 1 FROM sys_user_role ur
                WHERE ur.user_id = #{userId}
                AND JSON_CONTAINS(n.accept_ids, CAST(ur.role_id AS JSON))
            ))
            OR (n.accept_type = 'dept' AND EXISTS (
                SELECT 1 FROM sys_user u
                WHERE u.user_id = #{userId}
                AND JSON_CONTAINS(n.accept_ids, CAST(u.dept_id AS JSON))
            ))
          )
        ORDER BY n.is_top DESC, n.create_time DESC
    </select>

    <select id="selectHomeNoticeList" parameterType="SysNotice" resultMap="SysNoticeHomeResult">
        SELECT
            n.notice_id, n.notice_title, n.notice_type,
            cast(n.notice_content as char) as notice_content,
            n.status, n.business_id,
            n.is_top, n.is_popup, n.deadline, n.popup_content,
            n.accept_type, n.accept_ids,
            n.create_by, n.create_time, n.update_by, n.update_time, n.remark,
            r.read_status as read_status
        FROM sys_notice n
        LEFT JOIN sys_notice_read_status r ON n.notice_id = r.notice_id AND r.user_id = #{userId}
        WHERE n.status = '0'
          AND (n.deadline IS NULL OR n.deadline > NOW())
          <!-- 根据includeRead参数控制是否包含已读通知 -->
          <if test="includeRead == null or includeRead == false">
              AND (r.read_status = '0' OR r.read_status IS NULL)
          </if>
          <!-- 管理页面模式下的readStatus筛选 -->
          <if test="includeRead == true and readStatus != null and readStatus != ''">
              <if test='readStatus == "0"'>
                  AND (r.read_status = '0' OR r.read_status IS NULL)
              </if>
              <if test='readStatus == "1"'>
                  AND r.read_status = '1'
              </if>
          </if>
          <!-- 搜索条件 -->
          <if test="noticeTitle != null and noticeTitle != ''">
              AND n.notice_title like concat('%', #{noticeTitle}, '%')
          </if>
          <if test="noticeType != null and noticeType != ''">
              AND n.notice_type = #{noticeType}
          </if>
          <!-- 用户权限判断 -->
          AND (
            n.accept_type = 'all'
            OR (n.accept_type = 'user' AND JSON_CONTAINS(n.accept_ids, CAST(#{userId} AS JSON)))
            OR (n.accept_type = 'role' AND EXISTS (
                SELECT 1 FROM sys_user_role ur
                WHERE ur.user_id = #{userId}
                AND JSON_CONTAINS(n.accept_ids, CAST(ur.role_id AS JSON))
            ))
            OR (n.accept_type = 'dept' AND EXISTS (
                SELECT 1 FROM sys_user u
                WHERE u.user_id = #{userId}
                AND JSON_CONTAINS(n.accept_ids, CAST(u.dept_id AS JSON))
            ))
          )
        ORDER BY n.is_top DESC, n.create_time DESC
    </select>

</mapper>
