{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\vue-simple-uploader\\fileUploader.vue?vue&type=style&index=0&id=2f8b7038&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\vue-simple-uploader\\fileUploader.vue", "mtime": 1756794279925}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKI2NvbW1vbi1maWxlLXVwbG9hZGVyIHsKICBtYXJnaW46IDA7CiAgcGFkZGluZzogMDsKICBmb250LXNpemU6IDA7CiAgJi5oYXNEZWZhdWx0IHsKICAgIC5lbC11cGxvYWQtbGlzdF9faXRlbTpmaXJzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi10b3A6IDVweDsKICAgIH0KICB9CiAgLmVsLXVwbG9hZC1saXN0IHsKICAgIDo6di1kZWVwIC51cGxvYWRlci1maWxlIHsKICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICAgICAgaGVpZ2h0OiAyNXB4ICFpbXBvcnRhbnQ7CiAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4OwogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogICAgICB9CiAgICB9CiAgfQogIDo6di1kZWVwIC51cGxvYWRlci1maWxlLWljb24gewogICAgJjpiZWZvcmUgewogICAgICBjb250ZW50OiAiIiAhaW1wb3J0YW50OwogICAgfQogIH0KICA6OnYtZGVlcCAudXBsb2FkZXItZmlsZS1hY3Rpb25zID4gc3BhbiB7CiAgICBtYXJnaW4tcmlnaHQ6IDZweDsKICB9Cn0KLyog6ZqQ6JeP5LiK5Lyg5oyJ6ZKuICovCiNmaWxlLXVwbG9hZGVyLWJ0biB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGNsaXA6IHJlY3QoMCwgMCwgMCwgMCk7Cn0K"}, {"version": 3, "sources": ["fileUploader.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fileUploader.vue", "sourceRoot": "src/components/Generator/components/Upload/vue-simple-uploader", "sourcesContent": ["<template>\n  <div id=\"common-file-uploader\" :class=\"{ hasDefault: value && !!value.length }\">\n    <uploader\n      class=\"uploader-app\"\n      ref=\"uploader\"\n      :options=\"options\"\n      :autoStart=\"false\"\n      :file-status-text=\"statusText\"\n      @file-added=\"onFileAdded\"\n      @file-success=\"onFileSuccess\"\n      @file-progress=\"onFileProgress\"\n      @file-error=\"onFileError\"\n      @complete=\"onComplete\"\n    >\n      <uploader-unsupport></uploader-unsupport>\n      <uploader-btn id=\"file-uploader-btn\" ref=\"uploadBtn\" :attrs=\"attrs\"\n        >选择文件</uploader-btn\n      >\n      <uploader-list>\n        <template slot-scope=\"{ fileList }\">\n          <ul class=\"el-upload-list el-upload-list el-upload-list--text\">\n            <li\n              class=\"el-upload-list__item\"\n              v-for=\"file in fileList\"\n              :key=\"file.id\"\n            >\n              <uploader-file\n                :class=\"'file_' + file.id\"\n                ref=\"files\"\n                :file=\"file\"\n                :list=\"true\"\n              >\n                <template slot-scope=\"props\">\n                  <FileItem :file=\"props.file\" :list=\"props.list\" />\n                </template>\n              </uploader-file>\n            </li>\n          </ul>\n        </template>\n      </uploader-list>\n    </uploader>\n  </div>\n</template>\n\n<script>\nimport { chunkMerge } from \"@/api/lowCode/common\";\nimport uploadMixin from \"@/components/Generator/components/Upload/vue-simple-uploader/mixin\";\n\nconst units = {\n  KB: 1024,\n  MB: 1024 * 1024,\n  GB: 1024 * 1024 * 1024,\n};\n\nexport default {\n  props: {\n    value: {\n      type: Array,\n      default: () => [],\n    },\n    type: {\n      type: String,\n      default: \"annex\",\n    },\n    limit: {\n      type: Number,\n      default: 0,\n    },\n    accept: {\n      type: String,\n      default: \"*\",\n    },\n    sizeUnit: {\n      type: String,\n      default: \"MB\",\n    },\n    pathType: {\n      type: String,\n      default: \"defaultPath\",\n    },\n    isAccount: {\n      type: Number,\n      default: \"0\",\n    },\n    folder: {\n      type: String,\n      default: \"\",\n    },\n    fileSize: {\n      default: 5,\n    },\n  },\n  mixins: [uploadMixin],\n  data() {\n    return {};\n  },\n  computed: {\n    acceptText() {\n      let txt = \"\";\n      if (this.accept.includes(\"image/*\")) txt += \"、图片\";\n      if (this.accept.includes(\"video/*\")) txt += \"、视频\";\n      if (this.accept.includes(\"audio/*\")) txt += \"、音频\";\n      if (this.accept.includes(\".xls,.xlsx\")) txt += \"、excel\";\n      if (this.accept.includes(\".doc,.docx\")) txt += \"、word\";\n      if (this.accept.includes(\".pdf\")) txt += \"、pdf\";\n      if (this.accept.includes(\".txt\")) txt += \"、txt\";\n      return txt.slice(1);\n    },\n  },\n  methods: {\n    beforeUpload(file) {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      const unitNum = units[this.sizeUnit];\n      let isRightSize = this.fileSize\n        ? file.size / unitNum < this.fileSize\n        : true;\n      if (!isRightSize) {\n        this.$message.error(`文件大小超过${this.fileSize}${this.sizeUnit}`);\n        return isRightSize;\n      }\n      const isAccept = this.checkAccept(file);\n      if (!isAccept) {\n        this.$message.error(`请选择${this.acceptText}类型的文件`);\n        return isAccept;\n      }\n      return isRightSize && isAccept;\n    },\n    // 校验格式\n    checkAccept(file) {\n      if (!this.accept || this.accept === \"*\") return true;\n      const extension = file.getExtension();\n      const fileType = file.fileType;\n      if (this.accept.indexOf(extension) > -1) return true;\n      if (\n        this.accept.includes(\"image/*\") &&\n        new RegExp(\"image/*\").test(fileType)\n      )\n        return true;\n      if (\n        this.accept.includes(\"video/*\") &&\n        new RegExp(\"video/*\").test(fileType)\n      )\n        return true;\n      if (\n        this.accept.includes(\"audio/*\") &&\n        new RegExp(\"audio/*\").test(fileType)\n      )\n        return true;\n      return false;\n    },\n    handelSuccess(file) {\n      const form = new FormData();\n      form.append(\"identifier\", file.uniqueIdentifier);\n      form.append(\"fileName\", file.name.replaceAll(\"#\", \"\"));\n      form.append(\"fileSize\", file.size);\n      form.append(\"fileType\", file.getType());\n      form.append(\"extension\", file.getExtension());\n      form.append(\"type\", this.type);\n      form.append(\"pathType\", this.pathType);\n      form.append(\"isAccount\", this.isAccount);\n      form.append(\"folder\", this.folder);\n      chunkMerge(form).then((res) => {\n        // 自定义完成状态\n        this.$set(file, \"customCompleted\", true);\n        let data = {\n          name: file.name.replaceAll(\"#\", \"\"),\n          fileId: res.data.name,\n          fileSize: res.data.fileSize,\n          fileExtension: res.data.fileExtension,\n          fileVersionId: res.data.fileVersionId,\n          url: res.data.url,\n          fileType: file.fileType\n        };\n        this.$emit(\"fileSuccess\", data);\n        file.cancel();\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n#common-file-uploader {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  &.hasDefault {\n    .el-upload-list__item:first-child {\n      margin-top: 5px;\n    }\n  }\n  .el-upload-list {\n    ::v-deep .uploader-file {\n      border-bottom: none;\n      height: 25px !important;\n      line-height: 25px;\n      &:hover {\n        background-color: #f5f7fa;\n      }\n    }\n  }\n  ::v-deep .uploader-file-icon {\n    &:before {\n      content: \"\" !important;\n    }\n  }\n  ::v-deep .uploader-file-actions > span {\n    margin-right: 6px;\n  }\n}\n/* 隐藏上传按钮 */\n#file-uploader-btn {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n}\n</style>\n"]}]}