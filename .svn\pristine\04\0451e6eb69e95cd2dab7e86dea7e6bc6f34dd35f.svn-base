package com.ruoyi.safe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.safe.domain.FfsafeHostEvents;
import com.ruoyi.safe.service.IFfsafeHostEventsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 主机事件Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
@RequestMapping("/safe/ffsafeHostEvents")
public class FfsafeHostEventsController extends BaseController
{
    @Autowired
    private IFfsafeHostEventsService ffsafeHostEventsService;

    /**
     * 查询主机事件列表
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:list')")
    @GetMapping("/list")
    public TableDataInfo list(FfsafeHostEvents ffsafeHostEvents)
    {
        startPage();
        List<FfsafeHostEvents> list = ffsafeHostEventsService.selectFfsafeHostEventsList(ffsafeHostEvents);
        return getDataTable(list);
    }

    /**
     * 导出主机事件列表
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:export')")
    @Log(title = "主机事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FfsafeHostEvents ffsafeHostEvents)
    {
        List<FfsafeHostEvents> list = ffsafeHostEventsService.selectFfsafeHostEventsList(ffsafeHostEvents);
        ExcelUtil<FfsafeHostEvents> util = new ExcelUtil<FfsafeHostEvents>(FfsafeHostEvents.class);
        util.exportExcel(response, list, "主机事件数据");
    }

    /**
     * 获取主机事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(ffsafeHostEventsService.selectFfsafeHostEventsById(id));
    }

    /**
     * 新增主机事件
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:add')")
    @Log(title = "主机事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FfsafeHostEvents ffsafeHostEvents)
    {
        return toAjax(ffsafeHostEventsService.insertFfsafeHostEvents(ffsafeHostEvents));
    }

    /**
     * 修改主机事件
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:edit')")
    @Log(title = "主机事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FfsafeHostEvents ffsafeHostEvents)
    {
        return toAjax(ffsafeHostEventsService.updateFfsafeHostEvents(ffsafeHostEvents));
    }

    /**
     * 删除主机事件
     */
    @PreAuthorize("@ss.hasPermi('safe:ffsafeHostEvents:remove')")
    @Log(title = "主机事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ffsafeHostEventsService.deleteFfsafeHostEventsByIds(ids));
    }
}
