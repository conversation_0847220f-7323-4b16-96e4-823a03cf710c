<template>
  <div v-if="localVisible" class="JNPF-common-layout bigscreen" ref="bigscreen">
    <div class="JNPF-common-layout-center">
      <el-row :gutter="10" style="height: 160px" ref="headRow">
        <el-col :span="11">
          <el-row>
            <el-card class="box-card" style="height: 160px">
              <div slot="header" class="clearfix" shadow="always">
                <el-row>
                  <el-col :span="21">
                    <div style="display: flex;justify-content: start;"><svg-icon icon-class="xdzl" style="width: 31px;height: 31px" /><span
                      style="font-size: 24px; font-weight: bold; margin-left: 5px">{{ hwWork.year }}年度HW行动</span></div>
                  </el-col>
                  <el-col :span="3">
                    <div style="margin-right: auto">
                      <img title="全屏" style="cursor: pointer; width: 24px;height: 24px;" src="@/assets/icons/qp.png" @click="handleFullScreen()">&nbsp;&nbsp;&nbsp;&nbsp;
                      <img title="编辑" style="cursor: pointer; width: 24px;height: 24px;" src="@/assets/icons/bj1.png" @click="updateFrom(hwWork.id)">&nbsp;&nbsp;&nbsp;&nbsp;
                      <img title="返回" style="cursor: pointer; width: 24px;height: 24px;" src="@/assets/icons/fh.png"
                           @click="goBack()">
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="text_item" style="height: 83px; overflow: auto">
                <el-row>
                  <el-col :span="10">
                    <div style="font-size: 14px; height: 30px"><span class="text_item_label">HW开始时间：</span>{{ hwWork.hwStart }}</div>
                    <div style="font-size: 14px; height: 30px"><span class="text_item_label">HW结束时间：</span>{{ hwWork.hwEnd }}</div>
                    <div style="font-size: 14px; height: 30px"><span class="text_item_label">联络人：</span>{{ hwWork.userNames }}</div>
                  </el-col>
                  <el-col :span="12">
                    <div style="font-size: 14px; height: 30px"><span class="text_item_label">创建时间：</span>{{ hwWork.createTime }}</div>
                    <el-row>
                      <el-col :span="6" style="width: 120px">
                        <span class="text_item_label" style="font-weight: bold">技术支撑单位：</span>
                      </el-col>
                      <el-col :span="16" style="margin-bottom: 20px;">
                        <el-row v-for="(dept,index) in usersGroupByDept" :key="index" >
                          <el-col :span="12" >
                            <div style="border: 1px dashed #a0a0a0;font-size: 14px; font-weight: bold;margin-right: 5px;padding: 0 8px;">{{ dept.deptName }}</div>
                          </el-col>
                          <el-col :span="12">
                            <span  v-for="(user,userIndex) in dept.users" :key="userIndex" >
                              <el-button v-if="userIndex + user.nickName !== buttonType" size="mini" style="margin: 0 3px 3px 0;height: 23px;" @click="checkStepUser(user, userIndex)">{{ user.nickName }}</el-button>
                              <el-button v-if="userIndex + user.nickName  === buttonType" size="mini" type="success" style="margin: 0 3px 3px 0;height: 23px;" @click="checkStepUser1(user, userIndex)">{{ user.nickName }}</el-button>
                            </span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-row>
        </el-col>
        <el-col :span="3">
          <el-card class="box-card box-card2" shadow="always" style="height: 160px; display: flex; justify-content: center;">
            <div style="display: flex; flex-direction: column;">
              <div style="font-size: 18px;margin: 10px; padding-top: 15%; color: #7f7f7f;">HW{{ remindTitle }}倒计时</div>
              <div style="display: flex; align-items: flex-end; margin-top: 15px">
                <span style="font-weight: bold;font-size: 36px;margin-left: 20px;color:dodgerblue;font-family: electronicFont;">{{day}}</span><span style="margin: 0 10px; font-size: 16px; color: #4382FD">天</span><span style="font-size: 20px;color: #D9001B;font-weight: bold; font-family: electronicFont;">{{hour}}:{{min}}:{{second}}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card class="box-card box-card1" shadow="always" style="height: 160px">
            <div class="box-card1-one" style="display: flex;justify-content: start; height: 100%">
              <div class="box-card1-one-left">
                <div style="font-size: 18px;font-weight: bold; padding: 0 20px 0 0; color: #333333">报平安</div>
              </div>
              <div class="box-card1-one-right" style="height: 100%; padding: 16px 0;width: 99%;">
                <div style="margin-left: 20px;height: 100%; overflow-y: auto; overflow-x: hidden">
                <el-row style="margin-right: 0.5px">
                  <el-col :span="8" v-for="(item, index) in dayList" :key="index">
                    <div style="border: 0.5px solid #a0a0a0; line-height: 40px; text-align: center;font-size:14px;margin-right: -0.5px; margin-bottom: -1px;cursor: pointer;" @click="daySafe(item)">
                        <span style="color: #7f7f7f;font-size: 14px;">{{ item.date }}</span><span style="color: #333;font-size: 14px;">【{{ item.dayWeek }}】</span>
                        <el-popover
                      placement="bottom"
                      width="400"
                      @show="daySafePopoverShow(item)"
                      :append-to-body="false"
                      :disabled="!fullscreen"
                      trigger="hover">
                      <div>
                        <el-form ref="hoverForm" size="small" label-width="100px" label-position="right">
                          <template>
                            <el-col :span="24">
                              <jnpf-form-tip-item label="情况说明" prop="remark">
                                <el-input autosize v-model="currentDayInfo.remark" placeholder="" :disable="true" readonly type="textarea" clearable :style='{"width":"100%"}'>
                                </el-input>
                              </jnpf-form-tip-item>
                            </el-col>
                            <el-col :span="24">
                              <jnpf-form-tip-item label="附件" prop="hwEnd">
                                <file-upload ref="fileUpload" v-model="currentDayInfo.files"
                                             :disUpload="true" @href-click="fullscreen=false"
                                />
                              </jnpf-form-tip-item>
                            </el-col>
                          </template>
                        </el-form>
                      </div>
                      <span slot="reference">
                        <el-button v-if="item.isSafe==='1'" type="primary" plain>平安</el-button>
                        <el-button v-if="item.isSafe==='2'" type="danger" plain>异常</el-button>
                        <el-button v-if="item.isSafe==='0'" disabled>待报</el-button>
                      </span>
                    </el-popover>
                      </div>
                  </el-col>
                </el-row>
              </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin: 10px 0 0 0; flex: 1">
        <el-card class="box-card" style="height: 100%;">
          <div slot="header" class="clearfix" shadow="always" style="padding: 0px 15px 0 0px" ref="boxHead">
            <el-row>
              <el-col :span="4">
                <div style="display: flex;justify-content: start; align-items: center">
                  <svg-icon icon-class="sw" style="width: 23px;height: 23px; margin-right: 5px" /><span style="font-size: 16px; font-weight: bold;">HW事务进展</span>
                </div>
              </el-col>
              <el-col :span="10" id="myDIV" style="overflow: hidden;">
                <div class="itemText">
                  <svg-icon icon-class="redzb" class="redicon"></svg-icon>
                  <div style="font-size: 14px;font-weight: unset" @click="openNewTab({url:'/service-ledger/theratManage'})" >当前威胁告警 <span
                    style="font-size: 24px;font-weight: 700;font-family: electronicFont;">{{ alarmNum }}</span></div>
                  <div style="font-size: 14px;font-weight: unset" @click="openNewTab({url:'/service-ledger/theratManage?tabs=five'})" >已成功阻断 <span
                    style="font-size: 24px;font-weight: 700;font-family: electronicFont;">{{ stopedNum }}</span></div>
                  <div style="font-size: 14px;font-weight: unset" @click="openNewTab({url:'/service-ledger/theratManage?handle=1'})" >已处置威胁 <span
                    style="font-size: 24px;font-weight: 700;font-family: electronicFont;">{{ handledAlarmNum }}</span></div>
                  <div style="font-size: 14px;font-weight: unset" @click="openNewTab({url:'/service-ledger/theratManage?datasource=7'})">蜜罐告警 <span
                    style="font-size: 24px;font-weight: 700;font-family: electronicFont;">{{ hpAlarmNum }}</span></div>
                </div>
                <svg-icon icon-class="redyb" class="redicon1"></svg-icon>
                <div class="itemBgm" style="top: 0"></div>
              </el-col>
              <el-col :span="10">
                <div class="itemState">
                  <span><svg-icon icon-class="huise"/> 未开始    </span>
                  <span><svg-icon icon-class="blueDian"/> 进行中    </span>
                  <span><svg-icon icon-class="redDian"/> 延期    </span>
                  <span><svg-icon icon-class="greenDian"/> 已完成</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div ref="box" class="item-list" :style="{overflow: 'hidden auto',height: cardHeight}">
            <el-row :gutter="20">
              <el-col v-for="(item, index) in stagesTemp" :key="index" :span="6" :class="item.state!=='1'?'line':'line jinxing'">
                <el-row>
                  <el-col :span="22" style="font-size: 14px; height: 25px; line-height: 25px; color: #333333; font-weight: bold; text-indent: 5px">{{ item.title }}</el-col>
                  <el-col :span="2" style="font-size: 12px; height: 25px; line-height: 25px; color: #7f7f7f; text-indent: 10px">{{ item.percent }}%</el-col>
                  <el-col :span="24">
                    <el-progress class="custom-progress" :show-text="false" :percentage="item.percent"></el-progress>
                  </el-col>
                  <el-col v-for="(step, stepIndex) in item.steps" :key="stepIndex" :span="24">
                    <div :class="stepClass(step.state)">
                      <div class="right-item" @click="stepUpdate(step)">
                        <div style="font-size: 16px; color: #7f7f7f">{{ step.title }}
                          <el-tooltip placement="top" effect="light">
                            <div slot="content" v-html="step.tips"></div>
                            <i class="el-icon-info"></i>
                          </el-tooltip>
                        </div>
                        <div class="text_item1">
                          <div><span class="text_item_label">计划开始时间：</span>{{ step.startTime }}</div>
                        </div>
                        <div class="text_item1">
                          <div><span class="text_item_label">计划完成时间：</span>{{ step.endTime }}</div>
                        </div>
                        <div class="text_item1">
                          <div><span class="text_item_label">负责人：</span>{{ step.userNames }}</div>
                        </div>
                        <div class="text_item1">
                          <div><span class="text_item_label">实际完成时间：</span>{{ step.finishTime }}</div>
                        </div>
                      </div>
                      <div class="left-item">
                        <div style="margin: 10px 10px 0 10px">
                          <div class="row-item">
                            <svg-icon icon-class="cg" style="width: 20px;height: 20px;" />
                            <div style="margin: 0 10px 0 5px; font-size: 16px; color: #666666">成果</div>
<!--                            <svg-icon icon-class="xg" style="width: 22px;height: 22px;cursor: pointer;" @click="checkStep(step, item)" />-->
                          </div>
                          <div v-if="step.needFile" class="text_item1 ellipsis"  v-for="file in step.files" :title="file.name">
                            <span class="linkPoint">
                            <el-link :href="encodeURI(file.url)" :underline="false" target="_blank" :download="file.name">
                              <span class="el-icon-document" style="color: #4382FD;">{{ file.name}}</span>
                            </el-link>
                            </span>
                        </div>
                          <div v-if="step.links" class="text_item1 ellipsis"  v-for="link in step.links" :title="link.label">
                            <span class="linkPoint" @click="openNewTab(link)" style="color: #4382FD;">{{ link.label }}
                            </span>
                          </div>
                      </div>
                    </div>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-row>
    </div>
    <Form :visible.sync="formVisible" ref="addForm" @refresh="refresh"></Form>
    <day-form :visible.sync="dayFormVisible" ref="dayForm" @refresh="refresh"></day-form>
    <step-form :visible.sync="stepFormVisible" ref="stepForm" @close="submitStages"></step-form>
    <step-check-form :visible.sync="stepCheckFormVisible" ref="checkForm" @save="submitStages"></step-check-form>
  </div>
</template>
<script>
import {getNum,updateStages,getInfo,getCountNum} from '@/api/aqsoc/work-hw/crud'
import {getInfo as getDayInfo,getList as getDayList} from '@/api/aqsoc/work-hw-day/crud'
import {mapGetters} from 'vuex'
import Form from './form'
import DayForm from './day-form'
import StepForm from './step-form'
import StepCheckForm from "./step-check-form";

export default {
  components: {StepCheckForm, StepForm, DayForm, Form},
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      remindTitle:'开始',
      formVisible: false,
      dayFormVisible: false,
      stepFormVisible: false,
      stepCheckFormVisible: false,
      fullscreen: false,
      curStartTime: '2024-12-31 23:59:59',
      day: '0',
      hour: '00',
      min: '00',
      second: '00',
      timer: null,
      hwWork: {},
      usersGroupByDept:{},
      stages: [],
      stagesTemp: [],
      dayList: [],
      alarmNum: 0,
      stopedNum: 0,
      handledAlarmNum: 0,
      hpAlarmNum: 0,
      dataForm: {},
      buttonType: null,
      buttonFlag: null,
      cardHeight: '600px',
      currentDayInfo: {},
    }
  },
  filters: {
    toNumber(value) {
      return Number(value);
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    localVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    remainDays() {
      if (this.hwWork.hwEnd) {
        return this.daysBetween(new Date(), new Date(this.hwWork.hwEnd))
      }
      return 0
    },
  },
  watch: {},
  created() {
    this.cardHeight = window.innerHeight - 400 + 'px'

  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
    window.removeEventListener('resize', this.handleResize)
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  methods: {
    getCountNum(link){
      if(link.countId){
        getCountNum(link.countId).then(res=>{
          link.num = res.data
        })
      }
    },
    openNewTab(link){
      const baseUrl = window.location.origin;
      /*if (link.label.includes('漏洞')) return window.open(baseUrl +'/service-ledger/frailty', '_blank')
      if (link.label.includes('蜜罐')) return window.open(baseUrl +'/service-ledger/theratManage?type=2', '_blank')
      if (link.label.includes('阻断')) return window.open(baseUrl +'/service-ledger/theratManage?type=4', '_blank')*/
      const newUrl = `${baseUrl}${link.url}`;
      window.open(newUrl, '_blank');
    },
    countTime () {
      // 获取当前时间
      let date = new Date()
      let now = date.getTime()

      // 设置截止时间
      let endDate = new Date(this.curStartTime) // this.curStartTime需要倒计时的日期
      let end = endDate.getTime()

      // 时间差
      let leftTime = end - now

      // 定义变量 day,h,m,s保存倒计时的时间
      if (leftTime >= 0) {
        // 天
        this.day = Math.floor(leftTime / 1000 / 60 / 60 / 24)
        // 时
        let h = Math.floor(leftTime / 1000 / 60 / 60 % 24)
        this.hour = h < 10 ? '0' + h : h
        // 分
        let m = Math.floor(leftTime / 1000 / 60 % 60)
        this.min = m < 10 ? '0' + m : m
        // 秒
        let s = Math.floor(leftTime / 1000 % 60)
        this.second = s < 10 ? '0' + s : s
      } else {
        this.day = 0
        this.hour = '00'
        this.min = '00'
        this.second = '00'
      }
      // 等于0的时候不调用
      if (Number(this.hour) === 0 && Number(this.day) === 0 && Number(this.min) === 0 && Number(this.second) === 0) {
        return
      } else {
        // 递归每秒调用countTime方法，显示动态时间效果,
        this.timer = setTimeout(this.countTime, 1000)
      }
    },
    checkStepUser(user, key) {
      this.stagesTemp = []
      this.buttonType = key + user.nickName
      this.buttonFlag = key + user.nickName
      // 筛选 下面事务
      const temp =  JSON.parse(JSON.stringify(this.stages));
      temp.forEach((e, key) => {
        let stagesTempSteps = []
        e.steps.forEach(f => {
          if (f.userNames) {
            if (f.userNames.indexOf(user.nickName) !== -1 && f.userIds.indexOf(user.userId) !== -1) {
              stagesTempSteps.push(f)
            }
          }
        })
        this.stagesTemp.push(e)
        this.stagesTemp[key].steps = stagesTempSteps
      })
    },
    checkStepUser1(user, key) {
      this.stagesTemp = []
      this.buttonType = key + user.nickName
      if (this.buttonFlag === this.buttonType) {
        this.buttonType = null
        // 还原 下面事务
        this.stagesTemp = this.stages
      } else {
        // 筛选 下面事务
        const temp =  JSON.parse(JSON.stringify(this.stages));
        temp.forEach((e, key) => {
          let stagesTempSteps = []
          e.steps.forEach(f => {
            if (f.userNames) {
              if (f.userNames.indexOf(user.nickName) !== -1 && f.userIds.indexOf(user.userId) !== -1) {
                stagesTempSteps.push(f)
              }
            }
          })
          this.stagesTemp.push(e)
          this.stagesTemp[key].steps = stagesTempSteps
        })
      }
    },
    handleResize() {
      let h = this.$refs.bigscreen.offsetHeight;
      let headHeight = this.$refs.headRow.$el.offsetHeight || 160;
      let boxHeadHeight = this.$refs.boxHead.offsetHeight || 45;
      //window.innerHeight
      // this.cardHeight = window.innerHeight - headHeight - boxHeadHeight + 'px'
      this.cardHeight = h-160-boxHeadHeight-10-15-15-7 + 'px'
      console.log(h-160-boxHeadHeight)
      console.log(this.cardHeight,555)
    },
    stepClass(stepState) {
      switch (stepState) {
        case '1':
          return 'blueCard'
        case '2':
          return 'greenCard'
        case '3':
          return 'warnCard'
        default:
          return 'huiCard'
      }
    },
    daysBetween(date1, date2) {
      if (date1 >= date2) {
        return 0
      }
      const oneDay = 24 * 60 * 60 * 1000;
      const firstDate = date1.getTime();
      const secondDate = date2.getTime();
      const difference = Math.abs(firstDate - secondDate);
      return Math.ceil(difference / oneDay);
    },
    init(row, isDetail, isAudit) {
      console.log('护网工作台init', row)
      this.hwWork = row
      // 倒计时
      const now = new Date();
      if(now< new Date(this.hwWork.hwStart)){
        this.curStartTime = this.hwWork.hwStart
      }else if(now< new Date(this.hwWork.hwEnd)){
        this.remindTitle = '结束'
        this.curStartTime = this.hwWork.hwEnd
      }
      this.countTime()
      // 统计
      getNum("getAlarmNum", this.hwWork.id).then(res => {
        this.alarmNum = res.data
      })
      getNum("getStopedNum", this.hwWork.id).then(res => {
        this.stopedNum = res.data
      })
      getNum("getHandledAlarmNum", this.hwWork.id).then(res => {
        this.handledAlarmNum = res.data
      })
      getNum("getHpAlarmNum", this.hwWork.id).then(res => {
        this.hpAlarmNum = res.data
      })
      getDayList({hwId: this.hwWork.id, pageSize: -1}).then(res => {
        this.dayList = res.data.list
      })
      getInfo(this.hwWork.id).then(res=>{
        this.hwWork=res.data
        this.usersGroupByDept = res.data.usersGroupByDept
        // 阶段json转对象
        this.stages = JSON.parse(this.hwWork.dataJson).stages
        // 遍历links
        for(const stage of this.stages){
          if(stage.steps && stage.steps.length>0){
            for(const step of stage.steps){
              if(step.links){
                for(const link of step.links){
                  this.getCountNum(link)
                }
              }
            }
          }
        }
        this.stagesTemp = this.stages
      })

    },
    updateFrom(id, isDetail, isAudit) {
      this.formVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.init(id, isDetail, isAudit)
      })
    },
    daySafe(item, isDetail, isAudit){
      this.dayFormVisible = true
      this.$nextTick(() => {
        this.$refs.dayForm.init(item.id, true, isAudit)
      })
    },
    stepUpdate(step) {
      return;
      if(this.buttonType!=null){
        this.$message.warning("筛选视图禁止编辑")
        return
      }
      if(step.state=='2'){
        this.$message.warning("已完成，不可修改计划")
        return
      }
      this.stepFormVisible = true
      this.$nextTick(() => {
        this.$refs.stepForm.init(step)
      })
    },
    checkStep(step, stage) {
      if(this.buttonType!=null){
        this.$message.warning("筛选视图禁止编辑")
        return
      }
      if(step.startTime==''||step.endTime==''||step.userIds==''){
        this.$message.warning("请先填写计划开始时间、计划结束时间、责任人")
        return
      }
      if(stage.state!=='1'){
        this.$message.warning("本阶段不在进行中，不能提交成果")
        return
      }
      this.stepCheckFormVisible = true
      this.$nextTick(() => {
        this.$refs.checkForm.init(step)
      })
    },
    refresh(isrRefresh) {
      this.formVisible = false
      this.dayFormVisible = false
      this.stepFormVisible = false
      this.stepCheckFormVisible = false
      if (isrRefresh) this.init(this.hwWork)
    },
    submitStages(){
      // 阶段对象转json
      this.hwWork.dataJson = JSON.stringify({stages:this.stages})
      updateStages(this.hwWork).then(res=>{
        //后台可能会更改阶段的状态，所以需要重新init
        //this.hwWork=res.data
        this.refresh(true)
      });
    },
    // 全屏事件
    handleFullScreen() {
      let element = document.getElementsByClassName("bigscreen")[0];
      // let element = document.getElementsByTagName("body")[0];
      // 判断是否已经是全屏
      // 如果是全屏，退出
      this.fullscreen = document.fullscreen;
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        // 否则，进入全屏
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }

      }
      // 改变当前全屏状态
      this.fullscreen = !this.fullscreen;
    },
    goBack() {
      this.localVisible = false
      this.$emit('refresh', true);
    },
    daySafePopoverShow(item){
      this.currentDayInfo = {};
      getDayInfo(item.id).then(res => {
        this.currentDayInfo = res.data;
      })
    },
  },
}

</script>
<style lang="scss" scoped>
@font-face {
  font-family: electronicFont;
  src: url(../../../assets/fonts/DS-DIGI.ttf);
}
.flex-center {
  display: flex;
  justify-content: center;
}

::v-deep .el-card {
  box-shadow: none;
  border: none;
  border-radius: 0;
}

::v-deep .el-card__body {
  padding: 15px 20px;
}

.nav-div {
  margin: 0 20px 10px 20px;
  border-bottom: 1px #E9E9E9 solid;
  font-weight: bold;
  color: #29b5ee;
  line-height: 40px;
}

.nav-title {
  margin-right: 5px;
}

.text_item1 {
  margin-top: 8px;
  font-size: 14px;
}

.text_item_label {
  font-size: 14px;
  color: #7f7f7f;
}

.line {
  //border-left: 1px dashed #cc3232;
  border-left: 1px dotted #cc3232;
  border-image-source: url('../../../assets/images/biankuangxian.svg');
  border-image-slice: 1;
  border-image-repeat: repeat;
}

.line:last-child {
  border-right: none;
}

.line:first-child {
  border-left: none;
}

.linkPoint {
  color: #29b5ee;
  cursor: pointer;
}

.greenCard {
  height: 153px;
  margin-top: 6px;
  background-color: rgba(112, 182, 3, 0.09803921568627451);
  border-left: 4px solid rgba(112, 182, 3, 1);
  display: flex;
  justify-content: start;
}

.warnCard {
  height: 153px;
  margin-top: 6px;
  background-color: rgba(236, 128, 141, 0.09803921568627451);
  border-left: 4px solid rgba(236, 128, 141, 1);
  display: flex;
  justify-content: start;
}

.blueCard {
  height: 153px;
  margin-top: 6px;
  background-color: rgba(67, 130, 253, 0.09803921568627451);
  border-left: 4px solid rgba(67, 130, 253, 1);
  display: flex;
  justify-content: start;
}

.huiCard {
  height: 153px;
  margin-top: 6px;
  background-color: rgba(215, 215, 215, 0.09803921568627451);
  border-left: 4px solid rgba(215, 215, 215, 1);
  display: flex;
  justify-content: start;
}

.jinxing {
  background: inherit;
  background-color: rgba(129, 211, 248, 0.09803921568627451)
}

.right-item {
  margin: 10px 10px 0 10px;
  width: 70%;
  //cursor: pointer;
}

.left-item {
  border-left: 1px dotted  #a0a0a0;
  width: 30%;
  overflow-x: hidden;
  overflow-y: auto;
}

.row-item {
  display: flex;
  justify-content: flex-start;
}
.bigscreen {
  height: 100%;
  background-color: #F2F4F8;
}

.ellipsis {
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
}

#myDIV {
  /*background: url("../../../assets/icons/gfgtest.gif") round;*/
  position: relative;
  z-index: 99;
  height: 31px;
  overflow-y: auto;
  cursor: pointer;
}
#myDIV::-webkit-scrollbar {
  display:none
}
.itemText {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 31px;
  line-height: 31px;
}
.itemBgm {
  position: absolute;
  left: 0;
  z-index: 8;
  display: flex;
  justify-content: space-around;
  height: 31px;
  width: 100%;
  background: linear-gradient(to bottom, rgba(236, 128, 141, 0.298), rgba(236, 128, 141, 0.298));
  animation: moveTop 1.5s infinite; /* 1.5秒内无限循环，来回交替 */
}
.redicon {
  position: absolute;
  left: -5px;
  height: 31px !important;
}
.redicon1 {
  position: absolute;
  right: -5px;
  height: 31px !important;
}
@keyframes moveTop {
  0% {
    top: -31px;
  }
  100% {
    top: 31px;
  }
}

.box-card1 {
  .box-card1-one{
    .box-card1-one-left{
      width: 40px;
      display: flex; align-items: center;
      //border-right: 1px dotted #cc3232;
      //border-image-source: url('../../../assets/images/biankuangxian.svg');
      //border-image-slice: 2;
      //border-image-width: 3px;
      //border-image-outset: 1;
      //border-image-repeat: repeat;
      //border-left-width: 1px;
    }
    .box-card1-one-right{
      /*border-image-source: url('../../../assets/images/biankuangxian2.svg');
      border-image-slice: 1;
      border-image-width: 2px;
      border-image-repeat: repeat;*/
      border-left: 1px dotted #a0a0a0;
    }
  }
  ::v-deep .el-card__body {
    padding: 0 20px;
    height: 100%;
  }
}
.box-card2 {
  background-image: url('../../../assets/images/u7689.svg');
  background-size: cover;
  background-position: top;
  background-repeat: round;
  ::v-deep .el-card__body {
    padding: 0;
    height: 100%;
  }
}

.itemState {
  color: #666666;
  float: right;
  font-size: 12px;
  line-height: 31px;
  span {
    margin: 0 5px;
    svg {
      width: 10px;
      height: 10px;
    }
  }
}

.custom-progress {
  ::v-deep .el-progress-bar__inner {
    background: linear-gradient(90deg, rgba(169, 255, 224, 1) 3%, rgba(26, 241, 148, 1) 98%);
  }
}

/* Chrome, Safari, Opera */
@-webkit-keyframes mymove {
  30% {background: #ffbac1 bottom right/50px 50px;}
}

/* Standard syntax */
@keyframes mymove {
  30% {background: #ffccc2 bottom right/50px 50px;}
}

/*.item-list{
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - 400px);
}*/
</style>
