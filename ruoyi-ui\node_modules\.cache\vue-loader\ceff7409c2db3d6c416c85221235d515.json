{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\UploadFz.vue?vue&type=style&index=0&id=04d653f6&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\UploadFz.vue", "mtime": 1756794279924}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouVXBsb2FkRmlsZS1jb250YWluZXIgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAuZWwtdXBsb2FkX190aXAgewogICAgbGluZS1oZWlnaHQ6IDEuMjsKICAgIGNvbG9yOiAjYTVhNWE1OwogICAgbWFyZ2luLXRvcDogNXB4OwogICAgd29yZC1icmVhazogYnJlYWstYWxsOwogIH0KfQouZWwtdXBsb2FkLWxpc3RfX2l0ZW0gewogICYuZWwtdXBsb2FkLWxpc3RfX2l0ZW1fZGV0YWlsOmZpcnN0LWNoaWxkIHsKICAgIG1hcmdpbi10b3A6IDVweCAhaW1wb3J0YW50OwogIH0KICAuZWwtdXBsb2FkLWxpc3RfX2l0ZW0tbmFtZSB7CiAgICBtYXJnaW4tcmlnaHQ6IDcwcHg7CiAgfQogICY6aG92ZXIgewogICAgLmVsLXVwbG9hZC1saXN0X19pdGVtLXN0YXR1cy1sYWJlbC5kaXNhYmxlZCB7CiAgICAgIGRpc3BsYXk6IGJsb2NrICFpbXBvcnRhbnQ7CiAgICB9CiAgfQogIC5lbC1pY29uLWRvd25sb2FkIHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogNXB4OwogICAgcmlnaHQ6IDI1cHg7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICBvcGFjaXR5OiAwLjc1OwogICAgY29sb3I6ICM2MDYyNjY7CiAgfQogIC5lbC1pY29uLXZpZXcgewogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiA1cHg7CiAgICByaWdodDogNDVweDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIG9wYWNpdHk6IDAuNzU7CiAgICBjb2xvcjogIzYwNjI2NjsKICB9Cn0K"}, {"version": 3, "sources": ["UploadFz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UploadFz.vue", "sourceRoot": "src/components/Generator/components/Upload", "sourcesContent": ["<template>\n  <div class=\"UploadFile-container\">\n    <template v-if=\"!detailed\">\n      <el-button\n        size=\"small\"\n        icon=\"el-icon-upload\"\n        @click=\"uploadFile\"\n        :disabled=\"disabled\"\n      >\n        {{ buttonText }}\n      </el-button>\n      <a\n        type=\"text\"\n        @click=\"downloadAll\"\n        style=\"float: right\"\n        v-if=\"fileList.length\"\n        class=\"el-button el-button--text el-button--small\"\n        ><i class=\"el-icon-download\"></i>全部下载</a\n      >\n    </template>\n    <template v-if=\"fileList.length\">\n      <ul class=\"el-upload-list el-upload-list el-upload-list--text\">\n        <li\n          class=\"el-upload-list__item is-success\"\n          v-for=\"(file, index) in fileList\"\n          :key=\"file.fileId\"\n          :class=\"{ 'el-upload-list__item_detail': detailed }\"\n        >\n          <a\n            class=\"el-upload-list__item-name\"\n            :style=\"{ color: !showIcon ? '#409eff' : '' }\"\n            :title=\"\n              file.name +\n              (file.fileSize ? `（${jnpf.toFileSize(file.fileSize)}）` : '')\n            \"\n            @click=\"handleClick(file,true)\"\n          >\n            <i class=\"el-icon-paperclip\" v-if=\"showIcon\"></i>\n            {{ file.name\n            }}{{ file.fileSize ? `（${jnpf.toFileSize(file.fileSize)}）` : \"\" }}\n          </a>\n          <!-- <i class=\"el-icon-view\" title=\"查看\" @click=\"handlePreview(file)\"></i> -->\n          <i\n            class=\"el-icon-download\"\n            title=\"下载\"\n            @click=\"handleClick(file)\"\n          ></i>\n          <label\n            class=\"el-upload-list__item-status-label\"\n            :class=\"{ disabled: disabled }\"\n          >\n            <i class=\"el-icon-upload-success el-icon-circle-check\"></i>\n          </label>\n          <i\n            class=\"el-icon-close\"\n            title=\"删除\"\n            v-if=\"!disabled\"\n            @click=\"handleRemove(index)\"\n          ></i>\n        </li>\n      </ul>\n    </template>\n    <template>\n      <div class=\"el-upload__tip\" v-if=\"tipText\">\n        {{ tipText }}\n      </div>\n    </template>\n    <fileUploader\n      ref=\"fileUploader\"\n      v-bind=\"$props\"\n      @fileSuccess=\"fileSuccess\"\n    />\n    <Preview :visible.sync=\"previewVisible\" :file=\"activeFile\" />\n  </div>\n</template>\n\n<script>\nimport { getDownloadUrl, getPackDownloadUrl } from \"@/api/lowCode/common\";\nimport Preview from \"./Preview\";\nimport FileUploader from \"./vue-simple-uploader/fileUploader\";\nimport emitter from \"element-ui/src/mixins/emitter\";\nlet {\n  methods: { dispatch },\n} = emitter;\nexport default {\n  name: \"UploadFile\",\n  components: { Preview, FileUploader },\n  props: {\n    value: {\n      type: Array,\n      default: () => [],\n    },\n    type: {\n      type: String,\n      default: \"annex\",\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    showIcon: {\n      type: Boolean,\n      default: true,\n    },\n    showTip: {\n      type: Boolean,\n      default: false,\n    },\n    detailed: {\n      type: Boolean,\n      default: false,\n    },\n    limit: {\n      type: Number,\n      default: 0,\n    },\n    accept: {\n      type: String,\n      default: \"*\",\n    },\n    buttonText: {\n      type: String,\n      default: \"选择文件\",\n    },\n    sizeUnit: {\n      type: String,\n      default: \"MB\",\n    },\n    pathType: {\n      type: String,\n      default: \"defaultPath\",\n    },\n    isAccount: {\n      type: Number,\n      default: 0,\n    },\n    folder: {\n      type: String,\n      default: \"\",\n    },\n    fileSize: {\n      default: 10,\n    },\n    tipText: {\n      type: String,\n      default: \"\",\n    },\n  },\n  data() {\n    return {\n      fileList: [],\n      previewVisible: false,\n      activeFile: {},\n    };\n  },\n  computed: {\n    acceptText() {\n      let txt = \"\";\n      if (this.accept.includes(\"image/*\")) {\n        txt += \"、图片\";\n      }\n      if (this.accept.includes(\"video/*\")) {\n        txt += \"、视频\";\n      }\n      if (this.accept.includes(\"audio/*\")) {\n        txt += \"、音频\";\n      }\n      if (this.accept.includes(\".xls,.xlsx\")) {\n        txt += \"、excel\";\n      }\n      if (this.accept.includes(\".doc,.docx\")) {\n        txt += \"、word\";\n      }\n      if (this.accept.includes(\".pdf\")) {\n        txt += \"、pdf\";\n      }\n      if (this.accept.includes(\".txt\")) {\n        txt += \"、txt\";\n      }\n      return txt.slice(1);\n    },\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler(val) {\n        this.fileList = Array.isArray(val) ? val : [];\n      },\n    },\n  },\n  methods: {\n    handleRemove(index) {\n      this.fileList.splice(index, 1);\n      this.$emit(\"input\", this.fileList);\n      this.$emit(\"change\", this.fileList);\n      dispatch.call(this, \"ElFormItem\", \"el.form.change\", this.fileList);\n    },\n    handleClick(file,isPreview) {\n      // 点击下载文件\n      if (!file.fileId) return;\n      getDownloadUrl(this.type, file.fileId).then((res) => {\n        this.$download.export(\"/proxy\" + res.data.url, file.name,null,file,isPreview);\n      });\n    },\n    handlePreview(file) {\n      this.activeFile = file;\n      this.previewVisible = true;\n    },\n    uploadFile() {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      this.$refs.fileUploader && this.$refs.fileUploader.openUploader();\n    },\n    fileSuccess(data) {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      this.fileList.push(data);\n      this.$emit(\"input\", this.fileList);\n      this.$emit(\"change\", this.fileList);\n      dispatch.call(this, \"ElFormItem\", \"el.form.change\", this.fileList);\n    },\n    downloadAll() {\n      //下载全部（打包下载）\n      if (!this.fileList.length) return this.$message.error(\"未发现文件\");\n      let fileInfo = [];\n      for (let i = 0, len = this.fileList.length; i < len; i++) {\n        fileInfo.push({\n          fileId: this.fileList[i].fileId,\n          fileName: this.fileList[i].name,\n        });\n      }\n      getPackDownloadUrl(this.type, fileInfo).then((res) => {\n        this.$download.export(\n          \"/proxy\" + res.data.downloadVo.url,\n          res.data.downloadName\n        );\n      });\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.UploadFile-container {\n  position: relative;\n  .el-upload__tip {\n    line-height: 1.2;\n    color: #a5a5a5;\n    margin-top: 5px;\n    word-break: break-all;\n  }\n}\n.el-upload-list__item {\n  &.el-upload-list__item_detail:first-child {\n    margin-top: 5px !important;\n  }\n  .el-upload-list__item-name {\n    margin-right: 70px;\n  }\n  &:hover {\n    .el-upload-list__item-status-label.disabled {\n      display: block !important;\n    }\n  }\n  .el-icon-download {\n    display: inline-block;\n    position: absolute;\n    top: 5px;\n    right: 25px;\n    cursor: pointer;\n    opacity: 0.75;\n    color: #606266;\n  }\n  .el-icon-view {\n    display: inline-block;\n    position: absolute;\n    top: 5px;\n    right: 45px;\n    cursor: pointer;\n    opacity: 0.75;\n    color: #606266;\n  }\n}\n</style>\n"]}]}