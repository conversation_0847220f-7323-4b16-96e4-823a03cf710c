{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue?vue&type=style&index=0&id=dd0cb8d8&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\zeroCode\\workFlow\\components\\FlowBox.vue", "mtime": 1756710899653}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZmxvdy1mb3JtLW1haW4gewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogIGxlZnQ6IDA7CiAgdG9wOiAwOwogIHotaW5kZXg6IDEwMDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgLkpOUEYtZWxfdGFicyB7CiAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDYycHgpOwogICAgYmFja2dyb3VuZDogI2YyZjRmODsKICB9Cn0KCi5jb2xvci1ib3ggewogIHdpZHRoOiA3cHg7CiAgaGVpZ2h0OiA3cHg7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIGJvcmRlci1yYWRpdXM6IDUwJTsKfQouZmxvdy11cmdlbnQtdmFsdWUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBzcGFuOmZpcnN0LWNoaWxkIHsKICAgIG1hcmdpbjogMCAzcHggMCAxMHB4OwogIH0KfQoKLm9wdGlvbnMgewogIC5kcm9wZG93biB7CiAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgfQogIC5lbC1idXR0b24gewogICAgbWluLXdpZHRoOiA3MHB4OwogIH0KfQouZHJvcGRvd24taXRlbSB7CiAgbWluLXdpZHRoOiA3MHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKfQouc3ViRmxvd190YWJzIHsKICAvLyA6OnYtZGVlcCAuZWwtdGFic19faXRlbSB7CiAgLy8gICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgLy8gfQogIC8vIDo6di1kZWVwIC5lbC10YWJzX19jb250ZW50IHsKICAvLyAgIHBhZGRpbmc6IDBweCAwIDE1cHg7CiAgLy8gfQogIGhlaWdodDogMTAwJTsKICBvdmVyZmxvdzogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47CiAgLyogcGFkZGluZzogMCAxMHB4IDEwcHg7ICovCn0KLmNvbW1vbldvcmRzLWJ1dHRvbiB7CiAgbWFyZ2luLXRvcDogNTdweDsKfQouSk5QRi1wYWdlLWhlYWRlci1jb250ZW50IHsKICBtYXgtd2lkdGg6IDQwdnc7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwp9CgoudHlwZV9idG57CiAgZGlzcGxheTogZmxleDsKICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICBwYWRkaW5nLWxlZnQ6IDEwcHg7CiAgPi50eXBlX2J0bl9pdGVtOm5vdCg6Zmlyc3QtY2hpbGQpewogICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgfQogIC5idG5fYWN0aXZlewogICAgYmFja2dyb3VuZDogIzE4OTBmZjsKICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgIGNvbG9yOiAjRkZGRkZGOwogIH0KfQoKOjp2LWRlZXAgLmVsLWRpYWxvZ19fYm9keXsKICAuZmxvdy1jb250YWluZXJ7CiAgICAuc2NhbGUtc2xpZGVyewogICAgICBwb3NpdGlvbjogYWJzb2x1dGUgIWltcG9ydGFudDsKICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OwogICAgfQogIH0KfQoKLndvcmQtcHJldmlldy1idG5zewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBtYXJnaW4tdG9wOiAxMHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAuYnRuc3sKICAgIHdpZHRoOiA3MCU7CiAgfQogIC50YXJnZXQtc2VsZWN0ewogICAgZmxleDogMTsKICAgIHBhZGRpbmctcmlnaHQ6IDVweDsKICB9CiAgOjp2LWRlZXAgLmVsLXRhYnNfX25hdi1zY3JvbGx7CiAgICAuZWwtdGFic19fYWN0aXZlLWJhcnsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM4MzgzODsKICAgICAgaGVpZ2h0OiAwOwogICAgfQogICAgLmVsLXRhYnNfX2l0ZW17CiAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgICAgY29sb3I6ICNBMUExQTE7CiAgICB9CiAgICAuZWwtdGFic19faXRlbS5pcy1hY3RpdmV7CiAgICAgIGNvbG9yOiAjMzgzODM4OwogICAgICBib3JkZXItY29sb3I6ICMzODM4Mzg7CiAgICAgIGJvcmRlci1ib3R0b20td2lkdGg6IDNweDsKICAgIH0KICB9Cn0KCi50b29sLWJ0bnN7CiAgbWFyZ2luLXRvcDogNXB4Owp9Cgo6OnYtZGVlcCAuY2VudGVyX3RhYnN7CiAgLmVsLXRhYnNfX2NvbnRlbnR7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgbWFyZ2luLXRvcDogOHB4OwogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgLmVsLXRhYnNfX2hlYWRlcnsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgfQogIH0KICAuY2VudGVyX3RhYnNfcGFuZXsKICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgaGVpZ2h0OiAxMDAlOwogICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50OwoKICAgIC50YWJzLXBhbmUtdGl0bGV7CiAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgfQogIH0KICA+IC5lbC10YWJzX19oZWFkZXJ7CiAgICAvKnBhZGRpbmctYm90dG9tOiAxMHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNkY2RmZTY7Ki8KICB9CiAgLyouY2VudGVyX3RhYnNfcGFuZTo6YmVmb3JlewogICAgY29udGVudDogIiI7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMnB4OwogICAgYmFja2dyb3VuZDogI0RDREZFNjsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7CiAgfSovCn0KCi53b3JrX29yZGVyX2Zsb3d7CiAgLmVsLXRhYi1wYW5lewogICAgcGFkZGluZzogMCAwIDEwcHggIWltcG9ydGFudDsKICAgIG92ZXJmbG93OiBoaWRkZW4gIWltcG9ydGFudDsKICB9Cn0KCi5mb3JtLWNvbnRhaW5lciB7CiAgZmxleDogMTsKICBoZWlnaHQ6IDEwMCU7CiAgb3ZlcmZsb3cteTogYXV0bzsKICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7CiAgICBiYWNrZ3JvdW5kOiAjZjJmNGY4ICFpbXBvcnRhbnQ7IC8qIOi9qOmBk+minOiJsiAqLwogIH0KfQoKLndvcmQtcHJldmlldy1jb250YWluZXIgewogIHdpZHRoOiA0MCU7CiAgaGVpZ2h0OiAxMDAlOwogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgI2YyZjRmODsKfQo="}, {"version": 3, "sources": ["FlowBox.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgmEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "FlowBox.vue", "sourceRoot": "src/views/zeroCode/workFlow/components", "sourcesContent": ["<template>\n  <!-- <transition name=\"el-zoom-in-center\"> -->\n  <div class=\"flow-form-main\">\n    <div class=\"JNPF-common-page-header\">\n      <div v-if=\"setting.fromForm\">{{ title }}</div>\n      <el-page-header @back=\"goBack\" v-else>\n        <template slot=\"content\">\n          <div class=\"JNPF-page-header-content\">{{ title }}</div>\n        </template>\n      </el-page-header>\n      <template v-if=\"!loading || title\">\n        <el-dropdown\n          placement=\"bottom\"\n          @command=\"handleFlowUrgent\"\n          trigger=\"click\"\n          v-show=\"setting.opType == '-1'\"\n        >\n          <div class=\"flow-urgent-value\" style=\"cursor: pointer\">\n            <span\n              :style=\"{ 'background-color': flowUrgentList[selectState]?flowUrgentList[selectState].color:'' }\"\n              class=\"color-box\"\n            ></span>\n            <span :style=\"{ color: flowUrgentList[selectState]?flowUrgentList[selectState].color:'' }\">\n              {{ flowUrgentList[selectState].name }}</span\n            >\n          </div>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item\n              v-for=\"(item, index) in flowUrgentList\"\n              :key=\"'flowUrgent' + index\"\n              :command=\"item.state\"\n            >\n              <span\n                :style=\"{ 'background-color': item.color }\"\n                class=\"color-box\"\n              >\n              </span>\n              {{ item.name }}\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n        <!--        <div class=\"flow-urgent-value\" v-show=\"setting.opType !== '-1'\">\n                  <span\n                    :style=\"{ 'background-color': flowUrgentList[selectState].color }\"\n                    class=\"color-box\"\n                  ></span>\n                  <span :style=\"{ color: flowUrgentList[selectState].color }\">{{\n                    flowUrgentList[selectState].name\n                  }}</span>\n                </div>-->\n      </template>\n      <div class=\"options\" v-if=\"!subFlowVisible\">\n        <!--        <el-dropdown\n                  class=\"dropdown\"\n                  placement=\"bottom\"\n                  @command=\"handleMore\"\n                  v-if=\"moreBtnList.length\"\n                >\n                  <el-button style=\"width: 70px\" :disabled=\"allBtnDisabled\">\n                    更 多1<i class=\"el-icon-arrow-down el-icon&#45;&#45;right\"></i>\n                  </el-button>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item\n                      class=\"dropdown-item\"\n                      v-for=\"(item, index) in moreBtnList\"\n                      :key=\"'moreBtn'+index\"\n                      :command=\"item.key\"\n                      >{{ item.label }}</el-dropdown-item\n                    >\n                  </el-dropdown-menu>\n                </el-dropdown>-->\n        <el-button\n          v-if=\"properties.hasSaveBtn && !setting.readOnly\"\n          type=\"primary\"\n          @click=\"eventLauncher('saveAudit')\"\n          :loading=\"candidateLoading\"\n          :disabled=\"allBtnDisabled\"\n        >\n          {{ properties.saveBtnText || \"暂 存\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == '-1'\"\n          type=\"primary\"\n          @click=\"eventLauncher('submit')\"\n          :loading=\"candidateLoading\"\n          :disabled=\"allBtnDisabled\"\n        >\n          {{ properties.submitBtnText || \"提 交\" }}</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"eventLauncher('audit')\"\n          :loading=\"candidateLoading\"\n          v-if=\"setting.opType == 1 && properties.hasAuditBtn\"\n        >{{ properties.auditBtnText || \"通 过\" }}\n        </el-button>\n        <el-button\n          type=\"danger\"\n          @click=\"eventLauncher('reject')\"\n          :loading=\"candidateLoading\"\n          v-if=\"setting.opType == 1 && properties.hasRejectBtn\"\n        >{{ properties.rejectBtnText || \"退 回\" }}\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"press()\"\n          v-if=\"\n            setting.opType == 0 &&\n            setting.status == 1 &&\n            (properties.hasPressBtn || properties.hasPressBtn === undefined)\n          \"\n        >\n          {{ properties.pressBtnText || \"催 办\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == 2 && properties.hasRevokeBtn\"\n          @click=\"actionLauncher('recall')\"\n        >{{ properties.revokeBtnText || \"撤 回\" }}</el-button\n        >\n        <el-button\n          v-if=\"setting.opType == 4 && setting.status == 1\"\n          @click=\"actionLauncher('cancel')\"\n        >\n          终 止</el-button\n        >\n        <el-button\n          @click=\"goBack()\"\n          v-if=\"!setting.hideCancelBtn\"\n          :disabled=\"allBtnDisabled\"\n        >\n          取消\n        </el-button>\n      </div>\n    </div>\n    <div\n      class=\"approve-result\"\n      v-if=\"\n        (setting.opType == 0 || setting.opType == 4) &&\n        activeTab === '0' &&\n        !subFlowVisible\n      \"\n    >\n      <div\n        class=\"approve-result-img\"\n        :class=\"flowTaskInfo.status | flowStatus()\"\n      ></div>\n    </div>\n    <el-tabs class=\"JNPF-el_tabs center_tabs work_order_flow\" v-model=\"activeTab\" @tab-click=\"activeTabClick\">\n      <el-tab-pane\n        label=\"表单信息\"\n        v-loading=\"loading\"\n        v-if=\"!setting.readOnly && setting.opType != '4' && !subFlowVisible\"\n      >\n        <div class=\"center_tabs_pane\">\n          <div class=\"form-container\">\n            <component\n              :is=\"currentView\"\n              @close=\"goBack\"\n              ref=\"form\"\n              @eventReceiver=\"eventReceiver\"\n              @setLoad=\"setLoad\"\n              @setCandidateLoad=\"setCandidateLoad\"\n              @setPageLoad=\"setPageLoad\"\n              @reportDataChange=\"reportDataChange\"\n            />\n          </div>\n          <div class=\"word-preview-container\" v-if=\"exportBtnArr && exportBtnArr.length>0\">\n            <div class=\"word-preview-btns\">\n              <!--          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                          <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                        </el-radio-group>-->\n              <div class=\"btns\">\n                <el-tabs v-model=\"currentWordBtn\">\n                  <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                </el-tabs>\n              </div>\n              <div class=\"target-select\">\n                <el-select v-model=\"wordTargetDept\" placeholder=\"请选择\" @change=\"wordTargetDeptChange\">\n                  <el-option\n                    v-for=\"item in wordTargetDeptOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </div>\n            </div>\n            <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n              <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n              <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n            </div>\n          </div>\n        </div>\n        <!--        <el-tabs v-model=\"formActiveTabs\" @tab-click=\"formTabsClick\">\n                  <el-tab-pane label=\"表单\" name=\"1\">\n                    <div class=\"center_tabs_pane\">\n                      <component\n                        :is=\"currentView\"\n                        @close=\"goBack\"\n                        ref=\"form\"\n                        @eventReceiver=\"eventReceiver\"\n                        @setLoad=\"setLoad\"\n                        @setCandidateLoad=\"setCandidateLoad\"\n                        @setPageLoad=\"setPageLoad\"\n                      />\n                    </div>\n                  </el-tab-pane>\n                  <el-tab-pane label=\"预览1\" name=\"2\">\n                    <div class=\"center_tabs_pane\">\n                      <div class=\"word-preview-btns\">\n                        &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                    <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                  </el-radio-group>&ndash;&gt;\n                        <el-tabs v-model=\"currentWordBtn\">\n                          <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                        </el-tabs>\n                      </div>\n                      <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                        <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                        <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                      </div>\n                    </div>\n                  </el-tab-pane>\n                </el-tabs>-->\n      </el-tab-pane>\n      <el-tab-pane label=\"填报信息\" v-loading=\"loading\" v-if=\"setting.row && setting.readonly\">\n        <div class=\"center_tabs_pane\">\n          <div style=\"flex: 1;height: 100%;overflow-y: auto\">\n<!--            <form-record ref=\"form\" :current-setting=\"setting\" />-->\n            <work-flow ref=\"form\" :current-setting=\"setting\" @reportDataChange=\"reportDataChange\" />\n          </div>\n          <!--          <div style=\"width: 8px;flex: none;background-color: #F3F3F3\"></div>-->\n          <div style=\"width: 40%;margin-left: 8px;border-left: 8px solid #f3f3f3;height: 100%;\" v-if=\"exportBtnArr && exportBtnArr.length>0\">\n            <div class=\"word-preview-btns\">\n              <div class=\"btns\">\n                <el-tabs v-model=\"currentWordBtn\">\n                  <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                </el-tabs>\n              </div>\n              <div class=\"target-select\">\n                <el-select v-model=\"wordTargetDept\" placeholder=\"请选择\" @change=\"wordTargetDeptChange\">\n                  <el-option\n                    v-for=\"item in wordTargetDeptOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </div>\n            </div>\n            <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n              <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n              <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n            </div>\n          </div>\n        </div>\n        <!--        <el-tabs v-model=\"formActiveTabs\">\n                  <el-tab-pane label=\"表单\" name=\"1\">\n                    <div class=\"center_tabs_pane\">\n                      <div style=\"width: 50%\">\n                        <form-record :current-setting=\"setting\" />\n                      </div>\n                      <div style=\"width: 50%\">\n                        <div class=\"word-preview-btns\">\n                          &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                      <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                    </el-radio-group>&ndash;&gt;\n                          <el-tabs v-model=\"currentWordBtn\">\n                            <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                          </el-tabs>\n                        </div>\n                        <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                          <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                          <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                        </div>\n                      </div>\n                    </div>\n                  </el-tab-pane>\n        &lt;!&ndash;          <el-tab-pane label=\"预览2\" name=\"2\">\n                    <div class=\"center_tabs_pane\">\n                      <div class=\"word-preview-btns\">\n                        &lt;!&ndash;          <el-radio-group v-model=\"currentWordBtn\" size=\"medium\">\n                                    <el-radio-button v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\"></el-radio-button>\n                                  </el-radio-group>&ndash;&gt;\n                        <el-tabs v-model=\"currentWordBtn\">\n                          <el-tab-pane v-for=\"(wordBtn,index) in exportBtnArr\" :label=\"wordBtn\" :name=\"wordBtn\"></el-tab-pane>\n                        </el-tabs>\n                      </div>\n                      <div class=\"word-preview-content\" v-loading=\"wordPreviewLoading\" style=\"height: 100%\">\n                        <word-preview v-if=\"exportBtnArr && exportBtnArr.length>0\" style=\"margin-top: 10px;height: 100%\" ref=\"wordPreview\" />\n                        <el-empty v-if=\"!exportBtnArr || exportBtnArr.length<1\" :image-size=\"200\"></el-empty>\n                      </div>\n                    </div>\n                  </el-tab-pane>&ndash;&gt;\n                </el-tabs>-->\n      </el-tab-pane>\n      <el-tab-pane\n        label=\"审批记录\"\n        v-loading=\"loading\"\n      >\n        <div class=\"type_select\">\n          <div class=\"type_btn\">\n            <!--            <div class=\"type_btn_item\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 0\" :class=\"recordType === 0 ? 'btn_active' : ''\">流程记录</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-for=\"(item, index) in recordBtnArr\" :key=\"'type_btn_item' + item.id\">\n                          <el-button type=\"primary\" plain @click=\"recordType = index+1\" :class=\"recordType === index+1 ? 'btn_active' : ''\">{{item.nodeName?(item.nodeName=='开始'?'创建通报':item.nodeName):''}}</el-button>\n                        </div>-->\n            <!--            <div class=\"type_btn_item\" v-if=\"setting.id\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 1\" :class=\"recordType === 1 ? 'btn_active' : ''\">创建通报</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('审批告知单',1)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 2\" :class=\"recordType === 2 ? 'btn_active' : ''\">审核告知单</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('处置通报',2)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 3\" :class=\"recordType === 3 ? 'btn_active' : ''\">处置通报</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('审核反馈单',3)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 4\" :class=\"recordType === 4 ? 'btn_active' : ''\">审核反馈单</el-button>\n                        </div>\n                        <div class=\"type_btn_item\" v-if=\"showTypeBtn('验证',4)\">\n                          <el-button type=\"primary\" plain @click=\"recordType = 5\" :class=\"recordType === 5 ? 'btn_active' : ''\">提交验证</el-button>\n                        </div>-->\n          </div>\n        </div>\n        <recordList\n          v-if=\"recordType === 0\"\n          :list=\"flowTaskOperatorRecordList\"\n          :endTime=\"endTime\"\n          :flowId=\"setting.flowId\"\n          :opType=\"setting.opType?parseInt(setting.opType):-1\"\n        />\n        <informRecord v-if=\"recordType === 1\" :current-setting=\"setting\"/>\n        <informSignRecord v-if=\"recordType === 2\" :current-setting=\"setting\"/>\n        <feedbackRecord v-if=\"recordType === 3\" :current-setting=\"setting\"/>\n        <feedbackSignRecord v-if=\"recordType === 4\" :current-setting=\"setting\"/>\n        <checkRecord v-if=\"recordType === 5\" :current-setting=\"setting\"/>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"流程信息\" v-loading=\"loading\">\n        <template v-if=\"!subFlowVisible\">\n          <Process\n            :setting=\"setting\"\n            :conf=\"flowTemplateJson\"\n            v-if=\"flowTemplateJson.nodeId\"\n            @subFlow=\"subFlow\"\n          />\n        </template>\n        <template v-else>\n          <el-tabs v-model=\"subFlowTab\" @tab-click=\"activeClick\" type=\"card\">\n            <el-tab-pane\n              v-for=\"(item, index) in subFlowInfoList\"\n              :key=\"'subFlowTab' + index\"\n              :label=\"item.flowTaskInfo.fullName\"\n              :name=\"item.flowTaskInfo.id\"\n            >\n              <Process :conf=\"item.flowTemplateInfo.flowTemplateJson\" />\n            </el-tab-pane>\n          </el-tabs>\n        </template>\n      </el-tab-pane>\n\n      <el-tab-pane\n        label=\"审批汇总\"\n        v-if=\"setting.opType != '-1' && isSummary\"\n        v-loading=\"loading\"\n        name=\"recordSummary\"\n      >\n        <RecordSummary\n          :id=\"setting.id\"\n          :summaryType=\"summaryType\"\n          ref=\"recordSummary\"\n        />\n      </el-tab-pane>\n      <el-tab-pane\n        label=\"流程评论\"\n        v-if=\"setting.opType != '-1' && isComment\"\n        v-loading=\"loading\"\n        name=\"comment\"\n      >\n        <Comment :id=\"setting.id\" ref=\"comment\" />\n      </el-tab-pane>\n    </el-tabs>\n    <el-dialog\n      :title=\"eventType === 'audit' ? auditText : '审批退回'\"\n      :close-on-click-modal=\"false\"\n      :visible.sync=\"visible\"\n      class=\"JNPF-dialog JNPF-dialog_center\"\n      lock-scroll\n      append-to-body\n      :before-close=\"beforeClose\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"candidateForm\"\n        :model=\"candidateForm\"\n        :label-width=\"\n          candidateForm.candidateList.length || branchList.length\n            ? '130px'\n            : '80px'\n        \"\n      >\n        <template v-if=\"eventType === 'audit'\">\n          <el-form-item\n            label=\"分支选择\"\n            prop=\"branchList\"\n            v-if=\"branchList.length\"\n            :rules=\"[\n              { required: true, message: `分支不能为空`, trigger: 'change' },\n            ]\"\n          >\n            <el-select\n              v-model=\"candidateForm.branchList\"\n              multiple\n              placeholder=\"请选择审批分支\"\n              clearable\n              @change=\"onBranchChange\"\n            >\n              <el-option\n                v-for=\"item in branchList\"\n                :key=\"'branch' + item.nodeId\"\n                :label=\"item.nodeName\"\n                :value=\"item.nodeId\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            :label=\"item.nodeName + item.label\"\n            :prop=\"'candidateList.' + i + '.value'\"\n            v-for=\"(item, i) in candidateForm.candidateList\"\n            :key=\"'candidateList' + i\"\n            :rules=\"item.rules\"\n          >\n            <candidate-user-select\n              v-model=\"item.value\"\n              multiple\n              :placeholder=\"'请选择' + item.label\"\n              :taskId=\"setting.taskId\"\n              :formData=\"formData\"\n              :nodeId=\"item.nodeId\"\n              v-if=\"item.hasCandidates\"\n            />\n            <user-select\n              v-model=\"item.value\"\n              multiple\n              :placeholder=\"'请选择' + item.label\"\n              title=\"候选人员\"\n              :is-self-dept=\"item.isSelfDept\"\n              v-else\n            />\n          </el-form-item>\n        </template>\n        <template\n          v-if=\"properties.rejectType && eventType !== 'audit' && showReject\"\n        >\n          <el-form-item label=\"退回节点\" prop=\"rejectStep\" :rules=\"[\n              { required: true, message: `节点不能为空`, trigger: 'blur' },\n            ]\">\n            <el-select\n              v-model=\"candidateForm.rejectStep\"\n              placeholder=\"请选择退回节点\"\n              @change=\"rejectChange\"\n              multiple\n            >\n              <el-option\n                v-for=\"item in rejectList\"\n                :key=\"'rejectStep'+item.nodeCode\"\n                :label=\"item.nodeName\"\n                :value=\"item.nodeCode\"\n                :disabled=\"item.disabled\"\n              >\n              </el-option>\n            </el-select>\n          </el-form-item>\n          <template v-if=\"properties.rejectType == 3\">\n            <el-form-item prop=\"rejectRadio\">\n              <el-radio-group\n                v-model=\"candidateForm.rejectType\"\n                class=\"form-item-content\"\n              >\n                <el-radio :label=\"1\"\n                >重新审批\n                  <el-tooltip\n                    content=\"若流程为A->B->C,C退回至A，则C->A->B->C\"\n                    placement=\"top\"\n                  >\n                    <i class=\"el-icon-question tooltip-question\"></i>\n                  </el-tooltip>\n                </el-radio>\n                <el-radio :label=\"2\"\n                >直接提交给我\n                  <el-tooltip\n                    content=\"若流程为A->B->C,C退回至A，则C->A->C\"\n                    placement=\"top\"\n                  >\n                    <i class=\"el-icon-question tooltip-question\"></i>\n                  </el-tooltip>\n                </el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </template>\n        </template>\n        <template v-if=\"properties.hasOpinion\">\n          <el-form-item label=\"审批意见\" prop=\"handleOpinion\">\n            <el-input\n              v-model=\"candidateForm.handleOpinion\"\n              placeholder=\"请输入审批意见\"\n              type=\"textarea\"\n              :rows=\"4\"\n              maxlength=\"2000\"\n              show-word-limit\n            />\n            <!--            <CommonWordsDialog ref=\"commonWordsDialog\" @change=\"common\" />-->\n            <common-words v-if=\"visible && setting && setting.isWork\" :eventType=\"eventType\" @selected=\"onCommonWordsSelected\" />\n          </el-form-item>\n          <el-form-item label=\"审批附件\" prop=\"fileList\">\n            <JNPF-UploadFz v-model=\"candidateForm.fileList\" :limit=\"3\" />\n          </el-form-item>\n        </template>\n        <el-form-item label=\"手写签名\" required v-if=\"properties.hasSign\">\n          <div class=\"sign-main\">\n            <img :src=\"signImg\" alt=\"\" v-if=\"signImg\" class=\"sign-img\" />\n            <div @click=\"addSign\" class=\"sign-style\">\n              <i class=\"icon-ym icon-ym-signature add-sign\"></i>\n              <span class=\"sign-title\" v-if=\"!signImg\">手写签名</span>\n            </div>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"抄送人员\" v-if=\"properties.isCustomCopy && eventType === 'audit'\">\n          <user-select v-model=\"copyIds\" placeholder=\"请选择\" multiple />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"visible = false\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleApproval()\"\n          :loading=\"approvalBtnLoading\"\n        >\n          确定\n        </el-button>\n      </span>\n    </el-dialog>\n    <!-- 流程节点变更复活对话框 -->\n    <el-dialog\n      :title=\"flowTaskInfo.completion == 100 ? '复活' : '变更'\"\n      :close-on-click-modal=\"false\"\n      :visible.sync=\"resurgenceVisible\"\n      class=\"JNPF-dialog JNPF-dialog_center\"\n      lock-scroll\n      append-to-body\n      width=\"600px\"\n    >\n      <el-form\n        label-width=\"80px\"\n        :model=\"resurgenceForm\"\n        :rules=\"resurgenceRules\"\n        ref=\"resurgenceForm\"\n      >\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活节点' : '变更节点'\"\n          prop=\"taskNodeId\"\n        >\n          <el-select\n            v-model=\"resurgenceForm.taskNodeId\"\n            :placeholder=\"\n              flowTaskInfo.completion == 100\n                ? '请选择复活节点'\n                : '请选择变更节点'\n            \"\n          >\n            <el-option\n              v-for=\"item in resurgenceNodeList\"\n              :key=\"'taskNode' + item.id\"\n              :label=\"item.nodeName\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活意见' : '变更意见'\"\n          prop=\"handleOpinion\"\n        >\n          <el-row>\n            <el-col :span=\"24\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"resurgenceForm.handleOpinion\"\n                placeholder=\"请填写意见\"\n                :rows=\"4\"\n              />\n            </el-col>\n          </el-row>\n        </el-form-item>\n        <el-form-item\n          :label=\"flowTaskInfo.completion == 100 ? '复活附件' : '变更附件'\"\n          prop=\"fileList\"\n        >\n          <JNPF-UploadFz v-model=\"resurgenceForm.fileList\" :limit=\"3\" />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"resurgenceVisible = false\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleResurgence()\"\n          :loading=\"resurgenceBtnLoading\"\n        >\n          确定\n        </el-button>\n      </span>\n    </el-dialog>\n    <print-browse\n      :visible.sync=\"printBrowseVisible\"\n      :id=\"printTemplateId\"\n      :formId=\"setting.id\"\n      :fullName=\"setting.fullName\"\n    />\n    <candidate-form\n      :visible.sync=\"candidateVisible\"\n      :candidateList=\"candidateList\"\n      :branchList=\"branchList\"\n      :taskId=\"setting.taskId?setting.taskId:setting.id\"\n      :formData=\"formData\"\n      @submitCandidate=\"submitCandidate\"\n      :isCustomCopy=\"properties.isCustomCopy\"\n      ref=\"candidateForm\"\n    />\n    <error-form\n      :visible.sync=\"errorVisible\"\n      :nodeList=\"errorNodeList\"\n      @submit=\"handleError\"\n    />\n    <actionDialog\n      v-if=\"actionVisible\"\n      ref=\"actionDialog\"\n      :assignNodeList=\"assignNodeList\"\n      @submit=\"actionReceiver\"\n    />\n    <SuspendDialog\n      v-if=\"suspendVisible\"\n      ref=\"suspendDialog\"\n      @submit=\"suspendReceiver\"\n    />\n    <HasFreeApprover\n      :visible.sync=\"hasFreeApproverVisible\"\n      :taskId=\"setting.taskId\"\n      :formData=\"formData\"\n      :properties=\"properties\"\n      @close=\"approverDialog\"\n    />\n    <SignImgDialog\n      v-if=\"signVisible\"\n      ref=\"SignImg\"\n      :lineWidth=\"3\"\n      :userInfo=\"userInfo\"\n      :isDefault=\"1\"\n      @close=\"signDialog\"\n    />\n    <FlowBox\n      v-if=\"flowBoxVisible\"\n      ref=\"FlowBox\"\n      @close=\"flowBoxVisible = false\"\n    />\n    <PrintDialog\n      v-if=\"printDialogVisible\"\n      ref=\"printDialog\"\n      @change=\"printBrowseHandle\"\n    >\n    </PrintDialog>\n\n    <!-- 查看流程 开始 -->\n    <!--    <el-dialog title=\"查看流程\" :visible.sync=\"dialogFlowProcessVisible\" :modal-append-to-body=\"false\">\n          <template v-if=\"!subFlowVisible\">\n            <Process\n              :conf=\"flowTemplateJson\"\n              v-if=\"flowTemplateJson.nodeId\"\n              @subFlow=\"subFlow\"\n            />\n          </template>\n          <template v-else>\n            <el-tabs v-model=\"subFlowTab\" @tab-click=\"activeClick\" type=\"card\">\n              <el-tab-pane\n                v-for=\"(item, index) in subFlowInfoList\"\n                :key=\"'subFlowTab' + index\"\n                :label=\"item.flowTaskInfo.fullName\"\n                :name=\"item.flowTaskInfo.id\"\n              >\n                <Process :conf=\"item.flowTemplateInfo.flowTemplateJson\" />\n              </el-tab-pane>\n            </el-tabs>\n          </template>\n        </el-dialog>-->\n    <!-- 查看流程 结束 -->\n\n  </div>\n  <!-- </transition> -->\n</template>\n\n<script>\nimport WordPreview from \"@/components/WordPreview/index.vue\";\nimport PrintDialog from \"@/components/PrintDialog\";\nimport SignImgDialog from \"@/components/SignImgDialog\";\nimport informRecord from \"./FlowRecord/informRecord.vue\";\nimport informSignRecord from \"./FlowRecord/informSignRecord.vue\";\nimport feedbackRecord from \"./FlowRecord/feedbackRecord.vue\";\nimport feedbackSignRecord from \"./FlowRecord/feedbackSignRecord.vue\";\nimport checkRecord from \"./FlowRecord/checkRecord.vue\";\nimport formRecord from './FlowRecord/formRecord.vue';\nimport workFlow from '@/views/todoItem/todo/work_flow.vue';\nimport {\n  Assign,\n  Audit,\n  Cancel,\n  Candidates,\n  FlowBeforeInfo,\n  Recall,\n  Reject,\n  RejectList,\n  restore,\n  Resurgence,\n  ResurgenceList,\n  SaveAudit,\n  subFlowInfo,\n  suspend,\n  Transfer,\n} from \"@/api/lowCode/FlowBefore\";\nimport {Press, Revoke} from \"@/api/lowCode/FlowLaunch\";\nimport {Create, Update} from \"@/api/lowCode/workFlowForm\";\nimport recordList from \"./RecordList\";\nimport Comment from \"./Comment\";\nimport RecordSummary from \"./RecordSummary\";\nimport CandidateForm from \"./CandidateForm\";\nimport ErrorForm from \"./ErrorForm\";\nimport CandidateUserSelect from \"./CandidateUserSelect\";\nimport Process from \"@/components/Process/Preview\";\nimport PrintBrowse from \"@/components/PrintBrowse\";\nimport ActionDialog from \"@/views/workFlow/components/ActionDialog\";\nimport HasFreeApprover from \"./HasFreeApprover\";\nimport SuspendDialog from \"./SuspendDialog\";\nimport CommonWordsDialog from \"./CommonWordsDialog\";\nimport {mapGetters} from \"vuex\";\nimport {commonGetWord, commonGetWordByTemp,getOrder} from \"@/api/tool/work\";\nimport CommonWords from \"@/views/zeroCode/workFlow/components/commonWords.vue\";\n\nexport default {\n  name: \"FlowBox\",\n  components: {\n    CommonWords,\n    PrintDialog,\n    SignImgDialog,\n    HasFreeApprover,\n    recordList,\n    Process,\n    PrintBrowse,\n    Comment,\n    RecordSummary,\n    CandidateForm,\n    CandidateUserSelect,\n    ErrorForm,\n    ActionDialog,\n    SuspendDialog,\n    CommonWordsDialog,\n    informRecord,\n    informSignRecord,\n    feedbackRecord,\n    feedbackSignRecord,\n    checkRecord,\n    formRecord,\n    WordPreview,\n    workFlow\n  },\n  data() {\n    return {\n      dialogFlowProcessVisible: false,\n      printTemplateId: \"\",\n      printDialogVisible: false,\n      subFlowTab: \"\",\n      resurgenceVisible: false,\n      actionVisible: false,\n      resurgenceForm: {\n        taskNodeId: \"\",\n        handleOpinion: \"\",\n        fileList: [],\n      },\n      resurgenceRules: {\n        taskNodeId: [\n          {\n            required: true,\n            message: \"请选择节点\",\n            trigger: \"change\",\n          },\n        ],\n      },\n      previewVisible: false,\n      assignNodeList: [],\n      resurgenceNodeList: [],\n      currentView: \"\",\n      previewTitle: \"\",\n      formData: {},\n      setting: {},\n      monitorList: [\n        {\n          fullName: \"1\",\n          flowName: \"1\",\n          startTime: \"1\",\n          userName: \"1\",\n          thisStep: \"1\",\n        },\n        {\n          fullName: \"1\",\n          flowName: \"1\",\n          startTime: \"1\",\n          userName: \"1\",\n          thisStep: \"1\",\n        },\n      ],\n      flowFormInfo: {},\n      flowTemplateInfo: {},\n      flowTaskInfo: {},\n      flowTaskNodeList: [],\n      flowTemplateJson: {},\n      flowTaskOperatorRecordList: [],\n      properties: {},\n      endTime: 0,\n      suspendVisible: false,\n      visible: false,\n      handleId: \"\",\n      activeTab: \"0\",\n      isComment: false,\n      isSummary: false,\n      summaryType: 0,\n      loading: false,\n      btnLoading: false,\n      approvalBtnLoading: false,\n      resurgenceBtnLoading: false,\n      candidateLoading: false,\n      candidateVisible: false,\n      hasFreeApproverVisible: false,\n      signVisible: false,\n      candidateType: 1,\n      branchList: [],\n      candidateList: [],\n      candidateForm: {\n        branchList: [],\n        candidateList: [],\n        fileList: [],\n        handleOpinion: \"\",\n        rejectStep: \"\",\n        rejectType: 1,\n      },\n      printBrowseVisible: false,\n      rejectList: [],\n      showReject: false,\n      eventType: \"\",\n      signImg: \"\",\n      copyIds: [],\n      fullName: \"\",\n      thisStep: \"\",\n      allBtnDisabled: false,\n      flowUrgent: 1,\n      flowUrgentList: [\n        { name: \"普通\", color: \"#409EFF\", state: 1 },\n        { name: \"重要\", color: \"#E6A23C\", state: 2 },\n        { name: \"紧急\", color: \"#F56C6C\", state: 3 },\n      ],\n      errorVisible: false,\n      errorNodeList: [],\n      isValidate: false,\n      moreBtnList: [],\n      subFlowVisible: false,\n      flowBoxVisible: false,\n      subFlowInfoList: [],\n      commonWordsVisible: false,\n      recordType: 0,\n      lastRecord: {},\n      recordBtnArr: [],\n      exportBtnArr: [],\n      currentWordBtn: null,\n      wordPreviewLoading: false,\n      wordSrc: null,\n      isSelfDept: false,\n      formActiveTabs: '1',\n      currentWordForm: null,\n      wordTargetDept: null,\n      wordTargetDeptOptions: [],\n    };\n  },\n  computed: {\n    title() {\n      if ([2, 3, 4].includes(this.setting.opType)) return this.fullName;\n      return this.thisStep\n        ? this.fullName + \"/\" + this.thisStep\n        : this.fullName;\n    },\n    selectState() {\n      const index = this.flowUrgentList.findIndex(\n        (c) => this.flowUrgent === c.state\n      );\n      return index;\n    },\n    ...mapGetters([\"userInfo\"]),\n    auditText(){\n      let title = '审批通过';\n      if(this.properties && this.properties.submitBtnText && this.properties.submitBtnText.indexOf('提交') !== -1){\n        title = '提交';\n      }\n      return title;\n    },\n  },\n  created() {\n    this.$eventBus.$on('sendWorkForm', (val) => {\n      this.currentWordForm = val;\n      this.wordBtnSelected(this.currentWordBtn);\n    })\n    this.$eventBus.$on('setFormActiveTabs', (val) => {\n      this.formActiveTabs = val;\n    })\n  },\n  watch: {\n    activeTab(val) {\n      if (val === \"comment\") {\n        this.$refs.comment && this.$refs.comment.init();\n        this.moreBtnList.push({ label: \"评 论\", key: \"comment\" });\n      } else {\n        this.moreBtnList = this.moreBtnList.filter((o) => o.key != \"comment\");\n      }\n      if (val === \"recordSummary\") {\n        this.$refs.recordSummary && this.$refs.recordSummary.init();\n      }\n    },\n    flowTaskOperatorRecordList(val){\n      this.recordBtnArr = [];\n      if(val && val.length>0){\n        for (let i = val.length - 1; i >= 0; i--) {\n          let o = val[i];\n          let matchItem = this.recordBtnArr.find((i) => i.nodeCode == o.nodeCode);\n          if(!matchItem){\n            this.recordBtnArr.push(o);\n          }else {\n            //替换\n            this.recordBtnArr.splice(this.recordBtnArr.indexOf(matchItem),1,o);\n          }\n        }\n      }\n    },\n    flowUrgent(val){\n      let match = this.flowUrgentList.find((c) => c.state === val);\n      if(!match){\n        this.flowUrgent = 1;\n      }\n    },\n    currentWordBtn: {\n      immediate: true,\n      handler(val) {\n        if(val){\n          this.handleShowWord();\n        }\n      },\n    },\n    formActiveTabs(val){\n      this.currentWordBtn = null;\n      if(val === \"2\"){\n        this.loopWordBtns();\n      }\n    },\n  },\n  methods: {\n    common(val) {\n      this.commonWordsVisible = false;\n      if (val) {\n        if (this.resurgenceVisible) {\n          this.resurgenceForm.handleOpinion += val.commonWordsText;\n        } else {\n          this.candidateForm.handleOpinion += val.commonWordsText;\n        }\n      }\n    },\n    beforeClose() {\n      this.visible = false;\n      this.$refs.commonWordsDialog.close();\n    },\n    addSign() {\n      this.signVisible = true;\n      this.$nextTick(() => {\n        this.$refs.SignImg.init();\n      });\n    },\n    signDialog(val) {\n      this.signVisible = false;\n      if (val) {\n        this.signImg = val;\n      }\n    },\n    approverDialog(needClose) {\n      if (needClose) this.$emit(\"close\", true);\n    },\n    activeClick() {\n      let data =\n        this.subFlowInfoList.filter(\n          (o) => o.flowTaskInfo.id == this.subFlowTab\n        ) || [];\n      if (data.length) {\n        this.fullName = data[0].flowTaskInfo.fullName;\n        this.flowTaskOperatorRecordList =\n          data[0].flowTaskOperatorRecordList || [];\n        let templateJson = data[0].flowTaskInfo.flowTemplateJson\n          ? JSON.parse(data[0].flowTaskInfo.flowTemplateJson)\n          : null;\n        this.isComment = templateJson.properties.isComment;\n        this.isSummary = templateJson.properties.isSummary;\n        this.summaryType = templateJson.properties.summaryType;\n        this.flowUrgent = data[0].flowTaskInfo.flowUrgent || 1;\n        this.setting.id = data[0].flowTaskInfo.id;\n      }\n    },\n    subFlow(enCode) {\n      let flowTaskNodeList = this.flowTaskNodeList.filter(\n        (res) => res.nodeCode == enCode\n      );\n      if (!flowTaskNodeList.length) return;\n      if (\n        !flowTaskNodeList[0].type ||\n        flowTaskNodeList[0].nodeType != \"subFlow\"\n      )\n        return;\n      let item = {\n        subFlowVisible: true,\n        ...flowTaskNodeList,\n        ...this.setting,\n      };\n      this.flowBoxVisible = true;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(item);\n      });\n    },\n    handleResurgence(errorRuleUserList) {\n      this.$refs[\"resurgenceForm\"].validate((valid) => {\n        if (!valid) return;\n        let query = {\n          ...this.resurgenceForm,\n          taskId: this.setting.taskId,\n          resurgence: this.flowTaskInfo.completion == 100,\n        };\n        if (errorRuleUserList) query.errorRuleUserList = errorRuleUserList;\n        this.resurgenceBtnLoading = true;\n        Resurgence(query)\n          .then((res) => {\n            const errorData = res.data;\n            if (errorData && Array.isArray(errorData) && errorData.length) {\n              this.errorNodeList = errorData;\n              this.eventType = \"resurgence\";\n              this.errorVisible = true;\n              this.resurgenceBtnLoading = false;\n            } else {\n              this.$message({\n                type: \"success\",\n                message: res.msg,\n                duration: 1000,\n                onClose: () => {\n                  this.resurgenceBtnLoading = false;\n                  this.visible = false;\n                  this.errorVisible = false;\n                  this.$emit(\"close\", true);\n                },\n              });\n            }\n          })\n          .catch(() => {\n            this.resurgenceBtnLoading = false;\n          });\n      });\n    },\n    flowResurgence() {\n      this.resurgenceVisible = true;\n      ResurgenceList(this.setting.taskId).then((res) => {\n        this.resurgenceNodeList = res.data;\n      });\n    },\n    goBack(isRefresh) {\n      this.$emit(\"close\", isRefresh);\n    },\n    init(data) {\n      this.activeTab = \"0\";\n      if(data.activeTabs){\n        this.activeTab = data.activeTabs;\n      }\n      /* if(data.id && data.isWork){\n        this.getConfigKey(\"workOrderDefaultTab\").then(res =>{\n          if(res.msg){\n            this.$nextTick(() => {\n              this.formActiveTabs = res.msg;\n            })\n          }\n        })\n      } */\n      sessionStorage.removeItem('flowRowData');\n      this.loading = true;\n      this.setting = data;\n      if (data.subFlowVisible) {\n        this.subFlowInfo(data);\n      } else {\n        /**\n         * opType\n         * -1 - 我发起的新建/编辑\n         * 0 - 我发起的详情\n         * 1 - 待办事宜\n         * 2 - 已办事宜\n         * 3 - 抄送事宜\n         * 4 - 流程监控\n         */\n        this.getBeforeInfo(data);\n      }\n\n    },\n    getBeforeInfo(data) {\n      FlowBeforeInfo(data.id || 0, {\n        taskNodeId: data.taskNodeId,\n        taskOperatorId: data.taskId?data.taskId:data.taskId,\n        flowId: data.flowId,\n      })\n        .then((res) => {\n          this.flowFormInfo = res.data.flowFormInfo;\n          this.flowTaskInfo = res.data.flowTaskInfo || {};\n          this.flowTemplateInfo = res.data.flowTemplateInfo;\n          const fullName =\n            data.opType == \"-1\"\n              ? this.flowTemplateInfo.fullName\n              : this.flowTaskInfo.fullName;\n          data.fullName = fullName;\n          this.fullName = fullName;\n          this.thisStep = this.flowTaskInfo.thisStep;\n          this.flowUrgent = this.flowTaskInfo.flowUrgent || 1;\n          data.type = this.flowTemplateInfo.type;\n          data.draftData = res.data.draftData || null;\n          data.formData = res.data.formData || {};\n          data.formEnCode = this.flowFormInfo.enCode;\n          const formUrl =\n            this.flowFormInfo.formType == 2\n              ? \"workFlow/workFlowForm/dynamicForm\"\n              : this.flowFormInfo.urlAddress\n                ? this.flowFormInfo.urlAddress.replace(/\\s*/g, \"\")\n                : `workFlow/workFlowForm/${this.flowFormInfo.enCode}`;\n          this.currentView = (resolve) =>\n            require([`@/views/${formUrl}`], resolve);\n          this.flowTaskNodeList = res.data.flowTaskNodeList || [];\n          this.setting.flowTaskNodeList = this.flowTaskNodeList;\n          this.flowTemplateJson = this.flowTaskInfo && this.flowTaskInfo.flowTemplateJson ? JSON.parse(this.flowTaskInfo.flowTemplateJson) : this.flowTemplateInfo.flowTemplateJson?JSON.parse(this.flowTemplateInfo.flowTemplateJson):null;\n          /*this.flowTemplateJson = this.flowTemplateInfo.flowTemplateJson\n            ? JSON.parse(this.flowTemplateInfo.flowTemplateJson)\n            : null;*/\n          this.isComment = this.flowTemplateJson.properties.isComment;\n          this.isSummary = this.flowTemplateJson.properties.isSummary;\n          this.summaryType = this.flowTemplateJson.properties.summaryType;\n          this.flowTaskOperatorRecordList =\n            res.data.flowTaskOperatorRecordList || [];\n          this.flowTaskOperatorRecordList =\n            this.flowTaskOperatorRecordList.reverse();\n          this.properties = res.data.approversProperties || {};\n          if(this.properties && this.properties.flowVariable){\n            data.flowVariable = this.properties.flowVariable;\n          }\n          this.candidateForm.rejectType =\n            this.properties.rejectType == 3 ? 1 : this.properties.rejectType;\n          this.endTime =\n            this.flowTaskInfo.completion == 100 ? this.flowTaskInfo.endTime : 0;\n          data.formConf = this.flowFormInfo.propertyJson;\n          if (data.opType != 1 && data.opType != \"-1\") data.readonly = true;\n          data.formOperates = res.data.formOperates || [];\n          if (data.opType == 0) {\n            for (let i = 0; i < data.formOperates.length; i++) {\n              data.formOperates[i].write = false;\n            }\n          }\n          data.flowTemplateJson = this.flowTemplateJson;\n          if (this.flowTaskNodeList.length) {\n            let assignNodeList = [];\n            for (let i = 0; i < this.flowTaskNodeList.length; i++) {\n              const nodeItem = this.flowTaskNodeList[i];\n              data.opType == 4 &&\n              nodeItem.type == 1 &&\n              nodeItem.nodeType === \"approver\" &&\n              assignNodeList.push(nodeItem);\n              const loop = (data) => {\n                if (Array.isArray(data)) data.forEach((d) => loop(d));\n                if (data.nodeId === nodeItem.nodeCode) {\n                  if (nodeItem.type == 0) data.state = \"state-past\";\n                  if (nodeItem.type == 1) data.state = \"state-curr\";\n                  if (\n                    nodeItem.nodeType === \"approver\" ||\n                    nodeItem.nodeType === \"start\" ||\n                    nodeItem.nodeType === \"subFlow\"\n                  )\n                    data.content = nodeItem.userName;\n                  return;\n                }\n                if (data.conditionNodes && Array.isArray(data.conditionNodes))\n                  loop(data.conditionNodes);\n                if (data.childNode) loop(data.childNode);\n              };\n              loop(this.flowTemplateJson);\n            }\n            this.assignNodeList = assignNodeList;\n          } else {\n            this.flowTemplateJson.state = \"state-curr\";\n          }\n          data.flowTaskOperatorRecordList = this.flowTaskOperatorRecordList;\n          this.initBtnList();\n          setTimeout(() => {\n            this.$nextTick(() => {\n              this.$refs.form && this.$refs.form.init(data);\n              if (!this.$refs.form)\n                setTimeout(() => {\n                  this.$refs.form && this.$refs.form.init(data);\n                }, 500);\n            });\n          }, 500);\n        })\n        .catch(() => {\n          this.loading = false;\n        })\n        .finally(() => {\n          setTimeout(() => {\n            this.loopWordBtns();\n          },500);\n        });\n    },\n    initBtnList() {\n      const list = [];\n      const setting = this.setting;\n      const opType = this.setting.opType;\n      const properties = this.properties;\n      const flowTaskInfo = this.flowTaskInfo;\n      if (opType == \"-1\" && !setting.hideCancelBtn)\n        //list.push({ label: properties.saveBtnText || \"暂 存\", key: \"save\" });\n        if (\n          opType == 0 &&\n          setting.status == 1 &&\n          (properties.hasRevokeBtn || properties.hasRevokeBtn === undefined)\n        )\n          list.push({\n            label: properties.revokeBtnText || \"撤 回\",\n            key: \"revoke\",\n          });\n      if (\n        opType != 4 &&\n        setting.id &&\n        properties.hasPrintBtn &&\n        properties.printId\n      )\n        list.push({ label: properties.printBtnText || \"打 印\", key: \"print\" });\n      if (opType == 1) {\n        if (properties.hasTransferBtn)\n          list.push({\n            label: properties.transferBtnText || \"转 审\",\n            key: \"transfer\",\n          });\n        if (properties.hasSaveBtn)\n          list.push({\n            label: properties.saveBtnText || \"暂 存\",\n            key: \"saveAudit\",\n          });\n        if (properties.hasRejectBtn)\n          list.push({\n            label: properties.rejectBtnText || \"退 回\",\n            key: \"reject\",\n          });\n        if (properties.hasFreeApproverBtn)\n          list.push({\n            label: properties.hasFreeApproverBtnText || \"加 签\",\n            key: \"hasFreeApprover\",\n          });\n      }\n      if (opType == 4) {\n        if (flowTaskInfo.completion == 100)\n          list.push({ label: \"复 活\", key: \"resurgence\" });\n        if (\n          flowTaskInfo.completion > 0 &&\n          flowTaskInfo.completion < 100 &&\n          !flowTaskInfo.rejectDataId &&\n          (setting.status == 1 || setting.status == 3)\n        )\n          list.push({ label: \"变 更\", key: \"resurgence\" });\n        if (setting.status == 1 && this.assignNodeList.length)\n          list.push({ label: \"指 派\", key: \"assign\" });\n        if (flowTaskInfo.status == 1)\n          list.push({ label: \"挂 起\", key: \"suspend\" });\n        if (flowTaskInfo.status == 6 && !flowTaskInfo.suspend)\n          list.push({ label: \"恢 复\", key: \"recovery\" });\n      }\n      this.moreBtnList = list;\n    },\n    subFlowInfo(data) {\n      this.loading = false;\n      this.activeTab = \"0\";\n      this.subFlowVisible = true;\n      subFlowInfo(data[0].id)\n        .then((res) => {\n          this.subFlowInfoList = res.data || [];\n          this.subFlowTab = this.subFlowInfoList[0].flowTaskInfo.id;\n          this.flowUrgent =\n            this.subFlowInfoList[0].flowTaskInfo.flowUrgent || 1;\n          this.fullName = this.subFlowInfoList[0].flowTaskInfo.fullName;\n          this.flowTaskOperatorRecordList =\n            this.subFlowInfoList[0].flowTaskOperatorRecordList || [];\n          this.flowTaskOperatorRecordList =\n            this.flowTaskOperatorRecordList.reverse();\n          for (let index = 0; index < this.subFlowInfoList.length; index++) {\n            let element = this.subFlowInfoList[index];\n            element.flowTemplateInfo.flowTemplateJson = element.flowTemplateInfo\n              ? JSON.parse(element.flowTemplateInfo.flowTemplateJson)\n              : {};\n            if (element.flowTaskNodeList.length) {\n              let assignNodeList = [];\n              for (let i = 0; i < element.flowTaskNodeList.length; i++) {\n                const nodeItem = element.flowTaskNodeList[i];\n                data.opType == 4 &&\n                nodeItem.type == 1 &&\n                nodeItem.nodeType === \"approver\" &&\n                assignNodeList.push(nodeItem);\n                const loop = (data) => {\n                  if (Array.isArray(data)) data.forEach((d) => loop(d));\n                  if (data.nodeId === nodeItem.nodeCode) {\n                    if (nodeItem.type == 0) data.state = \"state-past\";\n                    if (nodeItem.type == 1) data.state = \"state-curr\";\n                    if (\n                      nodeItem.nodeType === \"approver\" ||\n                      nodeItem.nodeType === \"start\" ||\n                      nodeItem.nodeType === \"subFlow\"\n                    )\n                      data.content = nodeItem.userName;\n                    return;\n                  }\n                  if (data.conditionNodes && Array.isArray(data.conditionNodes))\n                    loop(data.conditionNodes);\n                  if (data.childNode) loop(data.childNode);\n                };\n                loop(element.flowTemplateInfo.flowTemplateJson);\n              }\n              element.assignNodeList = assignNodeList;\n            } else {\n              element.flowTemplateInfo.flowTemplateJson.state = \"state-curr\";\n            }\n            let templateJson = this.subFlowInfoList[0].flowTaskInfo\n              .flowTemplateJson\n              ? JSON.parse(\n                this.subFlowInfoList[0].flowTaskInfo.flowTemplateJson\n              )\n              : null;\n            this.isComment = templateJson.properties.isComment;\n            this.isSummary = templateJson.properties.isSummary;\n            this.summaryType = templateJson.properties.summaryType;\n            this.setting.id = this.subFlowInfoList[0].flowTaskInfo.id;\n          }\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    },\n    printBrowseHandle(id) {\n      this.printTemplateId = id;\n      this.printDialogVisible = false;\n      this.printBrowseVisible = true;\n    },\n    printDialog() {\n      this.printDialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.printDialog.init(this.properties.printId);\n      });\n    },\n    handleMore(e) {\n      if (e == \"revoke\") return this.actionLauncher(\"revoke\");\n      if (e == \"transfer\") return this.actionLauncher(\"transfer\");\n      if (e == \"saveAudit\") return this.eventLauncher(\"saveAudit\");\n      if (e == \"reject\") return this.eventReceiver({}, \"reject\");\n      if (e == \"resurgence\") return this.flowResurgence();\n      if (e == \"assign\") return this.actionLauncher(\"assign\");\n      if (e == \"comment\") return this.addComment();\n      if (e == \"print\") return this.printDialog();\n      if (e == \"suspend\") return this.suspend();\n      if (e == \"recovery\") return this.recovery();\n      this.eventLauncher(e);\n    },\n    suspend() {\n      this.suspendVisible = true;\n      this.$nextTick(() => {\n        this.$refs.suspendDialog.init(this.setting.id);\n      });\n    },\n    recovery() {\n      let data = {\n        handleOpinion: \"\",\n        fileList: [],\n      };\n      restore(this.setting.id, data)\n        .then((res) => {\n          this.$message({\n            message: res.msg,\n            type: \"success\",\n            duration: 1500,\n            onClose: () => {\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.$refs.suspendDialog.btnLoading = false;\n        });\n    },\n    suspendReceiver(dataForm) {\n      suspend(this.setting.id, dataForm).then((res) => {\n        this.$message({\n          message: res.msg,\n          type: \"success\",\n          duration: 1500,\n          onClose: () => {\n            this.$emit(\"close\", true);\n          },\n        });\n      });\n    },\n    eventLauncher(eventType) {\n      this.$refs.form &&\n      this.$refs.form.dataFormSubmit(eventType, this.flowUrgent);\n    },\n    eventReceiver(formData, eventType) {\n      this.formData = formData;\n      this.formData.flowId = this.setting.flowId;\n      this.formData.id = this.setting.id;\n      this.eventType = eventType;\n      if (eventType === \"save\" || eventType === \"submit\") {\n        return this.submitOrSave();\n      }\n      if (eventType === \"saveAudit\") {\n        return this.saveAudit();\n      }\n      if (eventType === \"hasFreeApprover\") {\n        return (this.hasFreeApproverVisible = true);\n      }\n      if (eventType === \"audit\" || eventType === \"reject\") {\n        this.handleId = \"\";\n        this.candidateForm.handleOpinion = \"\";\n        this.candidateForm.fileList = [];\n        this.copyIds = [];\n        this.isValidate = false;\n        // if (this.properties.hasSign) this.signImg = this.userInfo.signImg;\n        if (eventType === \"reject\") {\n          RejectList(this.setting.taskId?this.setting.taskId:this.setting.id)\n            .then((res) => {\n              this.showReject = res.data.isLastAppro;\n              this.rejectList = res.data.list || [];\n              if(this.properties.rejectStep !== '2'){\n                if(this.rejectList && this.rejectList.length === 1){\n                  this.candidateForm.rejectStep = [this.rejectList[0].nodeCode];\n                }else{\n                  this.candidateForm.rejectStep = [];\n                }\n              }\n              if (\n                !this.properties.hasSign &&\n                !this.properties.hasOpinion &&\n                !this.properties.isCustomCopy &&\n                !this.showReject\n              ) {\n                this.$confirm(\"此操作将退回该审批单，是否继续？\", \"提示\", {\n                  type: \"warning\",\n                })\n                  .then(() => {\n                    this.handleApproval();\n                  })\n                  .catch(() => {});\n                return;\n              }\n              this.isValidate = true;\n              this.visible = true;\n            })\n            .catch({});\n          return;\n        }\n        this.candidateLoading = true;\n        Candidates(this.setting.taskId?this.setting.taskId:this.flowTaskInfo.thisOperatorId, this.formData)\n          .then((res) => {\n            let data = res.data;\n            this.candidateType = data.type;\n            this.candidateLoading = false;\n            this.candidateForm.branchList = [];\n            this.branchList = [];\n            if (data.type == 1) {\n              this.branchList = res.data.list.filter((o) => o.isBranchFlow);\n              let list = res.data.list.filter(\n                (o) => !o.isBranchFlow && o.isCandidates\n              );\n              this.candidateForm.candidateList = list.map((o) => ({\n                ...o,\n                isDefault: true,\n                label: \"审批人\",\n                value: [],\n                rules: [\n                  {\n                    required: true,\n                    message: `审批人不能为空`,\n                    trigger: \"click\",\n                  },\n                ],\n                isSelfDept: o.isSelfDept\n              }));\n              this.$nextTick(() => {\n                this.$refs[\"candidateForm\"].resetFields();\n              });\n              this.isValidate = true;\n              this.visible = true;\n            } else if (data.type == 2) {\n              let list = res.data.list.filter((o) => o.isCandidates);\n              this.candidateForm.candidateList = list.map((o) => ({\n                ...o,\n                label: \"审批人\",\n                value: [],\n                rules: [\n                  {\n                    required: true,\n                    message: `审批人不能为空`,\n                    trigger: \"click\",\n                  },\n                ],\n              }));\n              this.$nextTick(() => {\n                this.$refs[\"candidateForm\"].resetFields();\n              });\n              this.isValidate = true;\n              this.visible = true;\n            } else {\n              this.candidateForm.candidateList = [];\n              if (\n                !this.properties.hasSign &&\n                !this.properties.hasOpinion &&\n                !this.properties.hasFreeApprover &&\n                !this.properties.isCustomCopy\n              ) {\n                let confirmMsg = \"此操作将通过该审批单，是否继续？\";\n                if(this.properties && this.properties.submitBtnText.indexOf('提交') !== -1){\n                  confirmMsg = \"此操作将提交该审批单，是否继续？\";\n                }\n                this.$confirm(confirmMsg, \"提示\", {\n                  type: \"warning\",\n                })\n                  .then(() => {\n                    this.handleApproval();\n                  })\n                  .catch(() => {});\n                return;\n              }\n              this.isValidate = true;\n              this.visible = true;\n            }\n          })\n          .catch(() => {\n            this.candidateLoading = false;\n          });\n      }\n    },\n    onBranchChange(val) {\n      const defaultList = this.candidateForm.candidateList.filter(\n        (o) => o.isDefault\n      );\n      if (!val.length) return (this.candidateForm.candidateList = defaultList);\n      let list = [];\n      for (let i = 0; i < val.length; i++) {\n        inner: for (let j = 0; j < this.branchList.length; j++) {\n          let o = this.branchList[j];\n          if (val[i] === o.nodeId && o.isCandidates) {\n            list.push({\n              ...o,\n              label: \"审批人\",\n              value: [],\n              rules: [\n                { required: true, message: `审批人不能为空`, trigger: \"click\" },\n              ],\n            });\n            break inner;\n          }\n        }\n      }\n      this.candidateForm.candidateList = [...defaultList, ...list];\n    },\n    saveAudit() {\n      this.allBtnDisabled = true;\n      this.btnLoading = true;\n      SaveAudit(this.setting.taskId || this.setting.id || 0, this.formData)\n        .then((res) => {\n          this.$message({\n            message: res.msg,\n            type: \"success\",\n            duration: 1500,\n            onClose: () => {\n              this.btnLoading = false;\n              this.allBtnDisabled = false;\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.allBtnDisabled = false;\n          this.btnLoading = false;\n        });\n    },\n    submitOrSave() {\n      this.formData.status = this.eventType === \"submit\" ? 0 : 1;\n      this.formData.flowUrgent = this.flowUrgent;\n      if (this.setting.delegateUserList) {\n        //受委托人不为空的时候走委托创建流程\n        this.formData.delegateUserList = this.setting.delegateUserList;\n      }\n\n      if (this.eventType === \"save\") return this.handleRequest();\n      this.candidateLoading = true;\n      Candidates(0, this.formData)\n        .then((res) => {\n          let data = res.data;\n          this.candidateLoading = false;\n          this.candidateType = data.type;\n          if (data.type == 1) {\n            this.branchList = res.data.list.filter((o) => o.isBranchFlow);\n            this.candidateList = res.data.list.filter(\n              (o) => !o.isBranchFlow && o.isCandidates\n            );\n            this.candidateVisible = true;\n          } else if (data.type == 2) {\n            this.branchList = [];\n            this.candidateList = res.data.list.filter((o) => o.isCandidates);\n            this.candidateVisible = true;\n          } else {\n            if (this.properties.isCustomCopy) {\n              this.branchList = [];\n              this.candidateList = [];\n              this.candidateVisible = true;\n              return;\n            }\n            this.$confirm(\"您确定要提交当前流程吗, 是否继续?\", \"提示\", {\n              type: \"warning\",\n            })\n              .then(() => {\n                this.handleRequest();\n              })\n              .catch(() => {});\n          }\n        })\n        .catch(() => {\n          this.candidateLoading = false;\n        });\n    },\n    handleRequest(candidateData) {\n      if (candidateData) this.formData = { ...this.formData, ...candidateData };\n      this.formData.candidateType = this.candidateType;\n      if (!this.formData.id) delete this.formData.id;\n      if (this.eventType === \"save\") this.btnLoading = true;\n      this.allBtnDisabled = true;\n      const formMethod = this.formData.id ? Update : Create;\n      formMethod(this.formData)\n        .then((res) => {\n          const errorData = res.data;\n          if (errorData && Array.isArray(errorData) && errorData.length) {\n            this.errorNodeList = errorData;\n            this.errorVisible = true;\n            this.allBtnDisabled = false;\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"success\",\n              duration: 1500,\n              onClose: () => {\n                if (this.eventType === \"save\") this.btnLoading = false;\n                this.candidateVisible = false;\n                this.allBtnDisabled = false;\n                this.errorVisible = false;\n                this.$emit(\"close\", true);\n              },\n            });\n          }\n        })\n        .catch(() => {\n          if (this.eventType === \"save\") this.btnLoading = false;\n          this.allBtnDisabled = false;\n          this.errorVisible = false;\n          let candidateFormRef = this.$refs.candidateForm;\n          if(candidateFormRef){\n            candidateFormRef.btnLoading = false;\n          }\n        });\n    },\n    submitCandidate(data) {\n      this.handleRequest(data);\n    },\n    actionLauncher(eventType) {\n      this.eventType = eventType;\n      if (\n        (eventType === \"revoke\" || eventType === \"recall\") &&\n        !this.properties.hasOpinion &&\n        !this.properties.hasSign\n      ) {\n        const title =\n          this.eventType == \"revoke\"\n            ? \"此操作将撤回该流程，是否继续？\"\n            : \"此操作将撤回该审批单，是否继续？\";\n        this.$confirm(title, \"提示\", {\n          type: \"warning\",\n        })\n          .then(() => {\n            this.actionReceiver();\n          })\n          .catch(() => {});\n        return;\n      }\n      this.showActionDialog();\n    },\n    showActionDialog() {\n      this.actionVisible = true;\n      this.$nextTick(() => {\n        this.$refs.actionDialog.init(this.properties, this.eventType);\n      });\n    },\n    actionReceiver(query) {\n      if (!query) {\n        query = {\n          handleOpinion: \"\",\n          signImg: \"\",\n          fileList: [],\n        };\n      }\n      const id =\n        this.eventType == \"revoke\" ? this.setting.id : this.setting.taskId;\n      const actionMethod = this.getActionMethod();\n      this.approvalBtnLoading = true;\n      actionMethod(id, query)\n        .then((res) => {\n          this.approvalBtnLoading = false;\n          this.$message({\n            type: \"success\",\n            message: res.msg,\n            duration: 1000,\n            onClose: () => {\n              this.$emit(\"close\", true);\n            },\n          });\n        })\n        .catch(() => {\n          this.$refs.actionDialog.btnLoading = false;\n          this.approvalBtnLoading = false;\n        });\n    },\n    getActionMethod() {\n      if (this.eventType === \"transfer\") return Transfer;\n      if (this.eventType === \"assign\") return Assign;\n      if (this.eventType === \"revoke\") return Revoke;\n      if (this.eventType === \"recall\") return Recall;\n      if (this.eventType === \"cancel\") return Cancel;\n    },\n    press() {\n      this.$confirm(\"此操作将提示该节点尽快处理，是否继续?\", \"提示\", {\n        type: \"warning\",\n      })\n        .then(() => {\n          Press(this.setting.id).then((res) => {\n            this.$message({\n              type: \"success\",\n              message: res.msg,\n              duration: 1000,\n            });\n          });\n        })\n        .catch(() => {});\n    },\n    handleError(data) {\n      if (this.eventType === \"submit\") {\n        this.formData.errorRuleUserList = data;\n        this.handleRequest();\n        return;\n      }\n      if (this.eventType === \"audit\" || this.eventType === \"reject\") {\n        this.handleApproval(data);\n        return;\n      }\n      if (this.eventType === \"resurgence\") {\n        this.handleResurgence(data);\n        return;\n      }\n    },\n    handleApproval(errorRuleUserList) {\n      const handleRequest = () => {\n        if (this.properties.hasSign && !this.signImg) {\n          this.$message({\n            message: \"请签名\",\n            type: \"error\",\n          });\n          return;\n        }\n        let query = {\n          handleOpinion: this.candidateForm.handleOpinion,\n          fileList: this.candidateForm.fileList,\n          ...this.formData,\n          enCode: this.setting.enCode,\n          signImg: this.signImg,\n          copyIds: this.copyIds.join(\",\"),\n          branchList: this.candidateForm.branchList,\n          candidateType: this.candidateType,\n          rejectType: this.candidateForm.rejectType,\n        };\n        if (this.eventType === \"reject\"){\n          if(this.candidateForm.rejectStep instanceof Array){\n            query.rejectStep = this.candidateForm.rejectStep.join(\",\");\n          }else {\n            query.rejectStep = this.candidateForm.rejectStep;\n          }\n        }\n        if (errorRuleUserList) query.errorRuleUserList = errorRuleUserList;\n        if (this.candidateForm.candidateList.length) {\n          let candidateList = {};\n          for (let i = 0; i < this.candidateForm.candidateList.length; i++) {\n            candidateList[this.candidateForm.candidateList[i].nodeId] =\n              this.candidateForm.candidateList[i].value;\n          }\n          query.candidateList = candidateList;\n        }\n        if (this.eventType === \"audit\" && this.properties.hasFreeApprover) {\n          query = { freeApproverUserId: this.handleId, ...query };\n        }\n        const approvalMethod = this.eventType === \"audit\" ? Audit : Reject;\n        this.approvalBtnLoading = true;\n        approvalMethod(this.setting.taskId?this.setting.taskId:this.flowTaskInfo.thisOperatorId, query)\n          .then((res) => {\n            const errorData = res.data;\n            if (errorData && Array.isArray(errorData) && errorData.length) {\n              this.errorNodeList = errorData;\n              this.errorVisible = true;\n              this.approvalBtnLoading = false;\n            } else {\n              this.$message({\n                type: \"success\",\n                message: res.msg,\n                duration: 1000,\n                onClose: () => {\n                  this.approvalBtnLoading = false;\n                  this.visible = false;\n                  this.errorVisible = false;\n                  this.$emit(\"close\", true);\n                },\n              });\n            }\n          })\n          .catch(() => {\n            this.approvalBtnLoading = false;\n          });\n      };\n      if (!this.isValidate) return handleRequest();\n      this.$refs[\"candidateForm\"].validate((valid) => {\n        if (valid) {\n          handleRequest();\n        }\n      });\n    },\n    addComment() {\n      this.$refs.comment && this.$refs.comment.showCommentDialog();\n    },\n    setPageLoad(val) {\n      this.loading = !!val;\n    },\n    setCandidateLoad(val) {\n      this.candidateLoading = !!val;\n      this.allBtnDisabled = !!val;\n    },\n    setLoad(val) {\n      this.btnLoading = !!val;\n    },\n    handleFlowUrgent(e) {\n      this.flowUrgent = e;\n    },\n    showTypeBtn(btnName,state){\n      if(this.setting && this.setting.row && this.setting.row.handleState && this.setting.row.handleState >= state){\n        return true;\n      }\n      let records = this.setting.flowTaskOperatorRecordList;\n      if(!records || records.length < 1){\n        return false;\n      }\n      return records[0].handleStatus == 0 && records[0].nodeName === btnName;\n    },\n    activeTabClick(e){\n      if(e.name === '99'){\n        this.loopWordBtns();\n      }\n    },\n    loopWordBtns(){\n      /*if(this.setting && this.setting.row && this.setting.row.nodeProperties){\n        let nodeProperties = JSON.parse(this.setting.row.nodeProperties);\n        if(nodeProperties.wordExport){\n          let arr = new Set();\n          for(let i = 0; i < nodeProperties.wordExport.length; i++){\n            if(nodeProperties.wordExport[i].ftlName){\n              arr.add(nodeProperties.wordExport[i].ftlName.replace('.ftl',''));\n            }\n          }\n          this.exportBtnArr = Array.from(arr);\n\n          let arr2 = new Set();\n          if(this.flowTaskInfo && this.flowTaskInfo.flowTemplateJson){\n            this.loopFtlName(JSON.parse(this.flowTaskInfo.flowTemplateJson),arr2);\n          }\n          this.exportBtnArr = Array.from(arr);\n          /!*this.exportBtnArr = nodeProperties.wordExport.filter(item => item.ftlName).map(item => {\n            if(item.ftlName){\n              return item.ftlName.replace('.ftl','');\n            }\n          })*!/\n\n          this.$nextTick(() => {\n            if(this.exportBtnArr && this.exportBtnArr.length>0){\n              this.currentWordBtn = this.exportBtnArr[0];\n            }\n          })\n        }\n      }*/\n      let arr = new Set();\n      let currentNodeInfo = {\n        currentNodeFtlName: null,\n      };\n      if(this.flowTaskInfo && this.flowTaskInfo.thisStepId){\n        currentNodeInfo.currentNode = this.flowTaskInfo.thisStepId.split(',')[0];\n      }\n      if(this.flowTemplateJson){\n        this.loopFtlName(this.flowTemplateJson,arr,currentNodeInfo);\n      }\n      this.exportBtnArr = Array.from(arr);\n      /*this.exportBtnArr = nodeProperties.wordExport.filter(item => item.ftlName).map(item => {\n        if(item.ftlName){\n          return item.ftlName.replace('.ftl','');\n        }\n      })*/\n\n      this.$nextTick(() => {\n        if(this.exportBtnArr && this.exportBtnArr.length>0){\n          this.currentWordBtn = this.exportBtnArr[0];\n          if(currentNodeInfo.currentNodeFtlName){\n            let match = this.exportBtnArr.find(item => item === currentNodeInfo.currentNodeFtlName);\n            if(match){\n              this.currentWordBtn = match;\n            }\n          }\n        }\n      })\n    },\n    loopFtlName(data,arr,currentNodeInfo){\n      if(data){\n        let properties = data.properties;\n        if(properties){\n          let nodeProperties = properties.nodeProperties;\n          if(nodeProperties){\n            let wordExportList = [];\n            if(typeof nodeProperties === 'string'){\n              wordExportList = JSON.parse(nodeProperties).wordExport;\n            }else {\n              wordExportList = nodeProperties.wordExport;\n            }\n            if(wordExportList && wordExportList.length>0){\n              wordExportList.forEach(wordExport => {\n                let ftlName = wordExport.ftlName;\n                if(ftlName){\n                  arr.add(ftlName.replace('.ftl',''));\n                  if(data.nodeId === currentNodeInfo.currentNode){\n                    currentNodeInfo.currentNodeFtlName = ftlName.replace('.ftl','');\n                  }\n                }\n              })\n            }\n          }\n        }\n\n        let conditionNodes = data.conditionNodes;\n        if(conditionNodes && conditionNodes.length > 0){\n          conditionNodes.forEach(conditionItem => this.loopFtlName(conditionItem,arr,currentNodeInfo));\n        }\n        let childNode = data.childNode;\n        if(childNode){\n          this.loopFtlName(childNode,arr,currentNodeInfo);\n        }\n      }\n    },\n    wordBtnSelected(label){\n      if(!label || '0' === label || 0 === label){\n        return;\n      }\n      if(this.setting && this.setting.row && this.setting.row.id){\n        getOrder(this.setting.row.id).then(baseRes => {\n          let data = {...baseRes.data};\n          this.currentWordForm = {...data,...this.currentWordForm}\n        }).finally(() => {\n          if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n            this.wordPreviewLoading = true;\n            let data = {...this.currentWordForm};\n            data.ftlName = label+'.ftl';\n            data.curTargetDept = this.wordTargetDept;\n            commonGetWordByTemp(data).then(res => {\n              this.$nextTick(() => {\n                setTimeout(() => {\n                  this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n                },300)\n              })\n            }).finally(()=>{\n              this.wordPreviewLoading = false;\n            })\n          }else {\n            if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n              return;\n            }\n            this.wordPreviewLoading = true;\n            commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id,curTargetDept: this.wordTargetDept}).then(res => {\n              this.$nextTick(() => {\n                setTimeout(() => {\n                  this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n                },300)\n              })\n            }).finally(()=>{\n              this.wordPreviewLoading = false;\n            })\n          }\n        })\n      }else {\n        if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n          this.wordPreviewLoading = true;\n          let data = {...this.currentWordForm};\n          data.ftlName = label+'.ftl';\n          commonGetWordByTemp(data).then(res => {\n            this.$nextTick(() => {\n              setTimeout(() => {\n                this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n              },300)\n            })\n          }).finally(()=>{\n            this.wordPreviewLoading = false;\n          })\n        }else {\n          if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n            return;\n          }\n          this.wordPreviewLoading = true;\n          commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id}).then(res => {\n            this.$nextTick(() => {\n              setTimeout(() => {\n                this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n              },300)\n            })\n          }).finally(()=>{\n            this.wordPreviewLoading = false;\n          })\n        }\n      }\n\n      /* if(this.currentWordForm && Object.keys(this.currentWordForm).length>0){\n        this.wordPreviewLoading = true;\n        let data = {...this.currentWordForm};\n        data.ftlName = label+'.ftl';\n        commonGetWordByTemp(data).then(res => {\n          this.$nextTick(() => {\n            setTimeout(() => {\n              this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n            },300)\n          })\n        }).finally(()=>{\n          this.wordPreviewLoading = false;\n        })\n      }else {\n        if(!this.setting || !this.setting.row || !this.setting.row.id || !this.setting.id){\n          return;\n        }\n        this.wordPreviewLoading = true;\n        commonGetWord({ftlName: label+'.ftl',id:this.setting.row.id}).then(res => {\n          this.$nextTick(() => {\n            setTimeout(() => {\n              this.$refs.wordPreview && this.$refs.wordPreview.init(new Blob([res], {type: \"application/pdf\"}))\n            },300)\n          })\n        }).finally(()=>{\n          this.wordPreviewLoading = false;\n        })\n      } */\n    },\n    onCommonWordsSelected(val){\n      this.candidateForm.handleOpinion = val;\n    },\n    formTabsClick(el){\n      if(\"2\" === el.name){\n        this.$eventBus.$emit('getWorkForm',1);\n        //this.loopWordBtns();\n      }\n    },\n    handleShowWord(){\n      this.currentWordForm = this.$refs.form && this.$refs.form.sendDataForm && this.$refs.form.sendDataForm();\n      this.wordBtnSelected(this.currentWordBtn);\n    },\n    reportDataChange(val){\n      if(val && val.length>0){\n        this.wordTargetDeptOptions = val.filter(item => item.formData && item.formData.length>0).map(item => {\n          return {\n            label: item.deptName,\n            value: item.deptId,\n            key: item.deptId\n          }\n        });\n        if(this.wordTargetDeptOptions && this.wordTargetDeptOptions.length>0){\n          if(!this.wordTargetDept){\n            this.wordTargetDept = this.wordTargetDeptOptions[0].value;\n          }else {\n            let match = this.wordTargetDeptOptions.find(item => item.value === this.wordTargetDept);\n            if(!match){\n              this.wordTargetDept = this.wordTargetDeptOptions[0].value;\n            }\n          }\n        }\n      }\n    },\n    wordTargetDeptChange(val){\n      this.handleShowWord();\n    },\n    rejectChange(val){\n      if(val && val.length>0){\n        let curNode = this.rejectList.find(item => item.nodeCode === val[0]);\n        if(!curNode){\n          return;\n        }\n        this.rejectList.forEach(item => {\n          if(item.sortCode === curNode.sortCode){\n            item.disabled = false;\n          }else {\n            item.disabled = true;\n          }\n        })\n      }else {\n        this.rejectList.forEach(item => {\n          item.disabled = false;\n        })\n      }\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.flow-form-main {\n  position: absolute;\n  background-color: #fff;\n  left: 0;\n  top: 0;\n  z-index: 100;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  .JNPF-el_tabs {\n    height: calc(100% - 62px);\n    background: #f2f4f8;\n  }\n}\n\n.color-box {\n  width: 7px;\n  height: 7px;\n  display: inline-block;\n  border-radius: 50%;\n}\n.flow-urgent-value {\n  display: flex;\n  align-items: center;\n  span:first-child {\n    margin: 0 3px 0 10px;\n  }\n}\n\n.options {\n  .dropdown {\n    margin-right: 10px;\n  }\n  .el-button {\n    min-width: 70px;\n  }\n}\n.dropdown-item {\n  min-width: 70px;\n  text-align: center;\n}\n.subFlow_tabs {\n  // ::v-deep .el-tabs__item {\n  //   text-align: center;\n  // }\n  // ::v-deep .el-tabs__content {\n  //   padding: 0px 0 15px;\n  // }\n  height: 100%;\n  overflow: auto;\n  overflow-x: hidden;\n  /* padding: 0 10px 10px; */\n}\n.commonWords-button {\n  margin-top: 57px;\n}\n.JNPF-page-header-content {\n  max-width: 40vw;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.type_btn{\n  display: flex;\n  padding-bottom: 10px;\n  padding-left: 10px;\n  >.type_btn_item:not(:first-child){\n    margin-left: 10px;\n  }\n  .btn_active{\n    background: #1890ff;\n    border-color: #1890ff;\n    color: #FFFFFF;\n  }\n}\n\n::v-deep .el-dialog__body{\n  .flow-container{\n    .scale-slider{\n      position: absolute !important;\n      display: none !important;\n    }\n  }\n}\n\n.word-preview-btns{\n  text-align: center;\n  margin-top: 10px;\n  display: flex;\n  align-items: center;\n  .btns{\n    width: 70%;\n  }\n  .target-select{\n    flex: 1;\n    padding-right: 5px;\n  }\n  ::v-deep .el-tabs__nav-scroll{\n    .el-tabs__active-bar{\n      background-color: #383838;\n      height: 0;\n    }\n    .el-tabs__item{\n      font-size: 18px;\n      font-weight: 700;\n      color: #A1A1A1;\n    }\n    .el-tabs__item.is-active{\n      color: #383838;\n      border-color: #383838;\n      border-bottom-width: 3px;\n    }\n  }\n}\n\n.tool-btns{\n  margin-top: 5px;\n}\n\n::v-deep .center_tabs{\n  .el-tabs__content{\n    background: #fff;\n    margin-top: 8px;\n    position: relative;\n    .el-tabs__header{\n      position: relative;\n    }\n  }\n  .center_tabs_pane{\n    margin-top: 20px;\n    display: flex;\n    height: 100%;\n    overflow: hidden !important;\n\n    .tabs-pane-title{\n      font-size: 18px;\n      text-align: center;\n    }\n  }\n  > .el-tabs__header{\n    /*padding-bottom: 10px;\n    border-bottom: 1px solid #dcdfe6;*/\n  }\n  /*.center_tabs_pane::before{\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 2px;\n    background: #DCDFE6;\n    transform: translateY(-10px);\n  }*/\n}\n\n.work_order_flow{\n  .el-tab-pane{\n    padding: 0 0 10px !important;\n    overflow: hidden !important;\n  }\n}\n\n.form-container {\n  flex: 1;\n  height: 100%;\n  overflow-y: auto;\n  &::-webkit-scrollbar-track {\n    background: #f2f4f8 !important; /* 轨道颜色 */\n  }\n}\n\n.word-preview-container {\n  width: 40%;\n  height: 100%;\n  border-left: 3px solid #f2f4f8;\n}\n</style>\n"]}]}