package jnpf.engine.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jnpf.base.UserInfo;
import jnpf.engine.entity.*;
import jnpf.engine.enums.FlowRecordEnum;
import jnpf.engine.enums.FlowStatusEnum;
import jnpf.engine.enums.FlowTaskOperatorEnum;
import jnpf.engine.model.flowbefore.FlowTemplateAllModel;
import jnpf.engine.model.flowengine.FlowModel;
import jnpf.engine.model.flowengine.shuntjson.childnode.ChildNode;
import jnpf.engine.service.*;
import jnpf.engine.util.FlowContextHolder;
import jnpf.engine.util.FlowTaskUtil;
import jnpf.entity.FlowFormEntity;
import jnpf.exception.WorkFlowException;
import jnpf.model.LoginUser;
import jnpf.model.SysUser;
import jnpf.model.visualJson.TableModel;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.RuoYiSysUserService;
import jnpf.service.FlowFormService;
import jnpf.service.TokenService;
import jnpf.util.*;
import jnpf.util.context.RequestContextHolderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线开发工作流
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/15 9:19
 */
@Slf4j
@Service
public class FlowDynamicServiceImpl implements FlowDynamicService {


    @Autowired
    public ServiceAllUtil serviceUtil;
    @Autowired
    private FlowTaskUtil flowTaskUtil;
    @Autowired
    private FlowTaskNewService flowTaskNewService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RuoYiSysUserService ruoYiSysUserService;
    @Autowired
    private FlowTaskService flowTaskService;
    @Autowired
    private FlowFormService flowFormService;
    @Autowired
    private FlowTaskOperatorRecordService flowTaskOperatorRecordService;
    @Autowired
    private FlowTaskOperatorService flowTaskOperatorService;
    @Autowired
    private FlowTaskNodeService flowTaskNodeService;
    @Autowired
    private FlowFormHttpReqUtils flowFormHttpReqUtils;

    @Override
    @DSTransactional
    public void flowTask(FlowModel flowModel, FlowStatusEnum flowStatus, String formId) throws WorkFlowException {
        //流程数据
        switch (flowStatus) {
            case save:
                FlowTaskEntity taskEntity = flowTaskNewService.save(flowModel);
                JSONObject resData = flowModel.getResData();
                resData.put("flowTask",taskEntity);
                flowModel.setResData(resData);
                break;
            case submit:
                //todo 获取表单最新数据
                flowModel.setFormData(flowTaskUtil.infoData(formId, flowModel.getProcessId()));
                flowTaskNewService.submitAll(flowModel);
                break;
            default:
                break;
        }
    }

    @Override
    @DSTransactional
    public void createOrUpdate(FlowModel flowModel) throws WorkFlowException {

        try {
            //txh 20240613添加
            if(StrUtil.isBlank(flowModel.getFlowId())){
                if(StrUtil.isNotBlank(flowModel.getId())){
                    FlowTaskEntity flowTask = flowTaskService.getById(flowModel.getId());
                    if(flowTask != null){
                        flowModel.setFlowId(flowTask.getFlowId());
                    }
                }
            }

            FlowTemplateAllModel model = flowTaskUtil.templateJson(flowModel.getFlowId());
            FlowTemplateJsonEntity templateJson = model.getTemplateJson();
            FlowTemplateEntity template = model.getTemplate();
            ChildNode childNode = JsonUtil.getJsonToBean(templateJson.getFlowTemplateJson(), ChildNode.class);
            String formId = childNode.getProperties().getFormId();
            String processId = flowModel.getProcessId();
            Map<String, Object> formData = flowModel.getFormData();
            formData.put("flowId", flowModel.getFlowId());
            JSONObject flowInfo = new JSONObject();
            ChildNode nodeChildNode = childNode.getChildNode();
            if(nodeChildNode != null && nodeChildNode.getProperties() != null){
                flowInfo.put("handleUser",nodeChildNode.getProperties().getApprovers());
                flowInfo.put("counterSign",nodeChildNode.getProperties().getCounterSign());
                flowInfo.put("nextNodeId",nodeChildNode.getNodeId());
            }
            flowInfo.put("thisStepId",childNode.getNodeId());
            flowInfo.put("nodeCode",childNode.getNodeId());
            if(CollUtil.isEmpty(flowInfo.getJSONArray("handleUser"))){
                Map<String, List<String>> candidateListMap = flowModel.getCandidateList();
                if(CollUtil.isNotEmpty(candidateListMap)){
                    Set<String> keys = candidateListMap.keySet();
                    List<String> users = new ArrayList<>();
                    for (String key : keys) {
                        users.addAll(candidateListMap.get(key));
                    }
                    flowInfo.put("handleUser",CollUtil.distinct(users));
                }
            }
            if(CollUtil.isEmpty(flowInfo.getJSONArray("handleUser"))){
                if(FlowTaskOperatorEnum.InitiatorMe.getCode().equals(childNode.getProperties().getAssigneeType())){
                    flowInfo.put("handleUser",CollUtil.toList(flowModel.getUserInfo() == null ? "" : flowModel.getUserInfo().getUserId()));
                }
            }
            if("-1".equals(flowModel.getStatus())){
                flowInfo.put("handleUser",CollUtil.toList(flowModel.getUserInfo() == null ? "" : flowModel.getUserInfo().getUserId()));
                List<JSONObject> flowVariable = flowInfo.getList("flowVariable", JSONObject.class);
                if(flowVariable == null){
                    flowVariable = new ArrayList<>();
                }
                JSONObject matchStat = flowVariable.stream().filter(variable -> "state".equals(variable.getString("key"))).findFirst().orElse(null);
                if(matchStat == null){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("key","state");
                    jsonObject.put("value","-1");
                    flowVariable.add(jsonObject);
                } else {
                    matchStat.put("value","-1");
                }
                flowInfo.put("flowVariable",flowVariable);
            }
            formData.put("flowInfo",flowInfo);
            FlowContextHolder.addData(formId, formData);
            //todo 调用表单保存数据
            if(CollUtil.isNotEmpty(flowInfo.getJSONArray("handleUser"))){
                formData.put(FlowFormConstant.HANDLE_USER,CollUtil.join(flowInfo.getJSONArray("handleUser"),","));
            }
            if(childNode.getProperties() != null && StrUtil.isNotBlank(childNode.getProperties().getTitle())){
                formData.put(FlowFormConstant.NODE_NAME,childNode.getProperties().getTitle());
            }

            FlowStatusEnum statusEnum = FlowStatusEnum.submit.getMessage().equals(flowModel.getStatus()) ? FlowStatusEnum.submit :
                    template.getType() == 0 ? FlowStatusEnum.save : FlowStatusEnum.none;
            if(statusEnum.equals(FlowStatusEnum.none) && "-1".equals(flowModel.getStatus())){
                statusEnum = FlowStatusEnum.save;
            }
            if(statusEnum.equals(FlowStatusEnum.none) && StrUtil.isNotBlank(flowModel.getStatus())){
                statusEnum = FlowStatusEnum.getByCode(Integer.valueOf(flowModel.getStatus()));
            }
            if(Objects.equals(statusEnum, FlowStatusEnum.submit)){
                formData.put(FlowFormConstant.FLOW_STATE, FlowRecordEnum.submit.getCode());
            }
            formData.put("isTrue", "true");

            serviceUtil.createOrUpdateDelegateUser(formId, processId, formData, serviceUtil.getUserInfo(flowModel.getUserId()));
            //获取最新表单
            FlowFormEntity flowFormEntity = flowFormService.getById(formId);
            if(flowFormEntity==null){
                throw new WorkFlowException("表单信息不存在");
            }
            Map<String, Object> allDataMap = new HashMap();
            //判断是否为系统表单
            boolean b = flowFormEntity.getFormType() == 1;
            if (b){
                allDataMap = flowFormHttpReqUtils.info(flowFormEntity,flowModel.getProcessId(),UserProvider.getToken());
                BeanUtil.copyProperties(allDataMap,formData);
            }
            this.flowTask(flowModel, statusEnum, formId);
        } finally {
            FlowContextHolder.clearAll();
        }
    }

    @Override
    @DSTransactional
    public void batchCreateOrUpdate(FlowModel flowModel) throws WorkFlowException {
        UserInfo userInfo = flowModel.getUserInfo();
        List<String> batchUserId = flowModel.getDelegateUserList();
        boolean isBatchUser = batchUserId.size() == 0;
        if (isBatchUser) {
            batchUserId.add(userInfo.getUserId());
        }
        for (String id : batchUserId) {
            FlowModel model = JsonUtil.getJsonToBean(flowModel, FlowModel.class);
            model.setDelegateUser(isBatchUser ? model.getDelegateUser() : userInfo.getUserId());
            model.setProcessId(StringUtil.isNotEmpty(model.getId()) ? model.getId() : RandomUtil.uuId());
            if (!isBatchUser) {
                UserEntity userEntity = serviceUtil.getUserInfo(id);
                if (userEntity != null) {
                    UserInfo info = new UserInfo();
                    info.setUserName(userEntity.getRealName());
                    info.setUserId(userEntity.getId());
                    model.setUserInfo(info);
                }
            }
            model.setUserId(id);
            this.createOrUpdate(model);
            flowModel.setResData(model.getResData());
            flowModel.setProcessId(model.getProcessId());
            flowModel.setTaskId(model.getTaskId());
            flowModel.setTaskOperatorId(model.getTaskOperatorId());
        }
    }

    @Override
    public List<String> getFormTableInfo(FlowModel srcModel) throws WorkFlowException {
        FlowModel flowModel = JsonUtil.getJsonToBean(srcModel, FlowModel.class);
        FlowTemplateAllModel model = flowTaskUtil.templateJson(flowModel.getFlowId());
        if(model == null){
            return null;
        }
        FlowTemplateJsonEntity templateJson = model.getTemplateJson();
        ChildNode childNode = JsonUtil.getJsonToBean(templateJson.getFlowTemplateJson(), ChildNode.class);
        String formId = childNode.getProperties().getFormId();
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        List<TableModel> tableModels = JsonUtil.getJsonToList(flowFormEntity.getTableJson(), TableModel.class);
        return tableModels.stream().map(TableModel::getTable).collect(Collectors.toList());
    }

    @Override
    public void delTask(String taskId) {
        //删除flow_taskoperatorrecord
        flowTaskOperatorRecordService.remove(new LambdaQueryWrapper<FlowTaskOperatorRecordEntity>().eq(FlowTaskOperatorRecordEntity::getTaskId, taskId));
        //删除flow_taskoperator
        flowTaskOperatorService.remove(new LambdaQueryWrapper<FlowTaskOperatorEntity>().eq(FlowTaskOperatorEntity::getTaskId, taskId));
        //删除task_node
        flowTaskNodeService.remove(new LambdaQueryWrapper<FlowTaskNodeEntity>().eq(FlowTaskNodeEntity::getTaskId, taskId));
        //删除flow_task
        flowTaskService.remove(new LambdaQueryWrapper<FlowTaskEntity>().eq(FlowTaskEntity::getId, taskId));
    }

    @Override
    public Map<String, Object> selectFormData(String taskId) throws WorkFlowException {
        //查询taskNode
        List<FlowTaskNodeEntity> taskNodeList = flowTaskNodeService.getList(taskId);
        if(CollUtil.isEmpty(taskNodeList)){
            return null;
        }
        //取最后
        List<FlowTaskNodeEntity> matchList = taskNodeList.stream().filter(node -> StrUtil.isNotBlank(node.getFormId())).collect(Collectors.toList());
        if(CollUtil.isEmpty(matchList)){
            return null;
        }
        return serviceUtil.infoData(matchList.get(matchList.size()-1).getFormId(), taskId);
    }
}
