package cn.anmte.aqsoc.work.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-25 16:27
 */
@Data
@TableName("work_hw_task")
@Schema(name = "WorkHwTask", description = "HW事务任务表")
public class WorkHwTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** HW事务ID */
    private Integer workId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 所属阶段 */
    @Excel(name = "所属阶段")
    private String stageClass;

    /** 任务内容 */
    @Excel(name = "任务内容")
    private String content;

    /** 计划开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 计划结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 实际完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 负责人 */
    @Excel(name = "负责人")
    private Long manageUser;

    @TableField(exist = false)
    private String manageUserName;

    /** 关联事务ID */
    @Excel(name = "关联事务ID")
    private Integer operateWorkId;

    @TableField(exist = false)
    private String operateWorkName;

    private String flowTaskId;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
