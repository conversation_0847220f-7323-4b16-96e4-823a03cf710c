package com.ruoyi.monitor2.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 事务列表对象 tbl_operate_work_record
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class TblOperateWorkRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 事务ID */
    private Long workId;

    /** 事务分类 */
    @Excel(name = "事务分类")
    private Long workClass;

    /** 事务名称 */
    @Excel(name = "事务名称")
    private String workName;

    /** 事务类型 */
    @Excel(name = "事务类型")
    private Long workType;

    /** 事务标题 */
    @Excel(name = "事务标题")
    private String workTitle;

    /** 流程任务主键 */
    private String fFlowtaskid;

    @TableField(exist = false)
    private List<String> flowTaskIdList;

    /** 流程id */
    private String fFlowid;

    private Integer fFlowstate;

    private List<String> fHandleUser;

    private String fNodeName;

    private String nodeProperties;

    private Long deptId;

    private String createByName;

    private String flowHandleUser;

    private Boolean queryAll = false;

    private List<Long> handleDeptIds;

    private Boolean onlySelf = false;

    private String ftlName;

    private Integer queryFlowState; //1=待办 2=已办

    private String type;

    @TableField(exist = false)
    private Date startTime;
    @TableField(exist = false)
    private Date endTime;

    private String files;
}
