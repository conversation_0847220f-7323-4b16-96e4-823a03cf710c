package cn.anmte.aqsoc.work.model;

import lombok.Data;

import java.util.List;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class StagesData {

    private List<Stage> stages;

    public StagesData() {
        /*// 初始化stages列表
        stages = new ArrayList<>();

        // 添加各个阶段对象
        stages.add(new Stage(1, 0, "0", "准备阶段"));
        stages.add(new Stage(2, 0, "0", "安全自查和整改阶段"));
        stages.add(new Stage(3, 0, "0", "正式HW阶段"));
        stages.add(new Stage(4, 0, "0", "总结阶段"));*/
    }

    @Data
    public static class Stage {
        private int order;
        private int percent;
        private String state;
        private String title;
        private List<Step> steps;

        public Stage(int order, int percent, String state, String title) {
            this.order = order;
            this.percent = percent;
            this.state = state;
            this.title = title;

            // 根据不同阶段初始化对应的步骤列表
            /*if ("准备阶段".equals(title)) {
                steps = initPrepareStageSteps();
            } else if ("安全自查和整改阶段".equals(title)) {
                steps = initSelfCheckAndRectifyStageSteps();
            } else if ("正式HW阶段".equals(title)) {
                steps = initFormalHWStageSteps();
            } else if ("总结阶段".equals(title)) {
                steps = initSummaryStageSteps();
            }*/
        }

        private List<Step> initPrepareStageSteps() {
            List<Step> stepList = new ArrayList<>();

            stepList.add(new Step("HW工作启动会", 1, "0", "", "", "", "",
                    "协助单位进行启动会仪式的流程，生成相应文档记录，确保HW工作顺利、有序、安全进行", "", null, 1));

            List<Step.Link> links = new ArrayList<>();
            links.add(new Step.Link("server", "主机", "/asset-ledger/monitor2/server"));
            links.add(new Step.Link("application", "业务系统", "/asset-ledger/monitor2/application"));
            stepList.add(new Step("资产梳理", 2, "0", "", "", "", "",
                    "1. 资产探测：探测存活主机、网站资产，发现未知资产，跟踪资产变更趋势<br/>" +
                            "2. 资产分类：业务系统、服务器、终端、网络设备等物联网设备<br/>" +
                            "3. 资产台账：形成可动态维护的资产台账，关联资产指纹、资产风险、资产组、责任人、部门等信息", "", links));

            List<Step.Link> links2 = new ArrayList<>();
            links2.add(new Step.Link("", "网络拓扑", "/asset-ledger/mode/topologyConfiguration"));
            stepList.add(new Step("网络架构梳理", 3, "0", "", "", "", "",
                    "明确网络结构，划分安全域，发现潜在的安全隐患，并为后续的安全加固和应急响应提供依据", "", links2));

            List<Step.Link> links3 = new ArrayList<>();
            links3.add(new Step.Link("safety", "安全设备", "/asset-ledger/monitor2/safety"));
            stepList.add(new Step("安全设备梳理", 4, "0", "", "", "", "",
                    "对现有的IT安全设备资产（如:防火墙、WAF等）进行梳理排查，其中包括状态检查、策略调整等，根据单位的情况，配合单位进行清查、加固，帮助单位减少当前存在的安全隐患", "", links3));

            List<Step.Link> links4 = new ArrayList<>();
            links4.add(new Step.Link("network", "网络设备", "/asset-ledger/monitor2/networkdevices"));
            stepList.add(new Step("监测设备梳理", 5, "0", "", "", "", "",
                    "配合进行监测设备梳理，通过人工检查，及时发现如镜像流量不全、特征库引用不全等设置使用问题", "", links4));

            return stepList;
        }

        private List<Step> initSelfCheckAndRectifyStageSteps() {
            List<Step> stepList = new ArrayList<>();

            List<Step.Link> links = new ArrayList<>();
            links.add(new Step.Link("domain", "域名", "/asset-ledger/external?tab=domain"));
            links.add(new Step.Link("app", "APP应用程序", "/asset-ledger/external?tab=app"));
            links.add(new Step.Link("program", "微信小程序", "/asset-ledger/external?tab=program"));
            links.add(new Step.Link("account", "微信公众号", "/asset-ledger/external?tab=account"));
            stepList.add(new Step("资产暴露面探测", 1, "0", "", "", "", "", "1.联动外网云端SaaS扫描系统，以攻击者视角探测用户互联网边界资产<br/>" +
                    "2.探测互联网上可能泄露的“邮箱账号信息、代码、文档”等敏感资产", "", links));

            List<Step.Link> links2 = new ArrayList<>();
            links2.add(new Step.Link("highRiskVulner", "高危漏洞", "/service-ledger/assetVulner/frailty?severity=3"));
            links2.add(new Step.Link("noHandleVulner", "未修复漏洞", "/service-ledger/assetVulner/frailty?handleStatus=0"));
            stepList.add(new Step("漏洞管理", 2, "0", "", "", "", "",
                    "将周期发现的Web漏洞和主机漏洞汇总成漏洞风险台账，持续跟踪漏洞风险，统计漏洞修复情况", "", links2));


            stepList.add(new Step("渗透测试", 3, "0", "", "", "", "", "模拟黑客的攻击方法，发现系统可能存在的安全隐患，比如从内网、外网等位置利用各种手段对某个特定网络进行测试，发现和挖掘系统中可能存在的安全漏洞", "", null, 1));

            List<Step.Link> links3 = new ArrayList<>();
            links3.add(new Step.Link("", "攻击者溯源", "/service-ledger/theratManage?tabs=second"));
            links3.add(new Step.Link("hotpot", "蜜罐告警", "/service-ledger/theratManage?datasource=7"));
            stepList.add(new Step("蜜罐诱捕", 4, "0", "", "", "", "", "1.旁路部署外网仿真蜜罐（保证无可利用漏洞），捕获针对用户的外网威胁，形成精准的威胁情报IP表；此外，可帮助用户仿真业务系统，以假乱真，消耗攻击者时间<br/>" +
                    "2.旁路部署内网高交互蜜罐，精准诱捕攻击蜜罐的失陷主机，深挖各种内网潜伏威胁：勒索病毒传播、内网主机被远控后横向攻击（如钓鱼远控、供应链威胁）", "", links3));

            List<Step.Link> links4 = new ArrayList<>();
            links4.add(new Step.Link("noHandleVulner", "未修复漏洞", "/service-ledger/assetVulner/frailty?handleState=0"));
            links4.add(new Step.Link("handledVulner", "已修复漏洞", "/service-ledger/assetVulner/frailty?handleState=1"));
            stepList.add(new Step("安全复测 ", 5, "0", "", "", "", "",
                    "对系统之前存在的漏洞风险进行复测，确保暴露的漏洞整改修复到位", "", links4));

            return stepList;
        }

        private List<Step> initFormalHWStageSteps() {
            List<Step> stepList = new ArrayList<>();
            List<Step.Link> links = new ArrayList<>();
            links.add(new Step.Link("ipFilter", "已阻断IP", "/service-ledger/theratManage?tabs=five"));
            stepList.add(new Step("攻击行为实时阻断", 1, "0", "", "", "", "",
                    "结合已有攻击特征库、第三方情报同时联动流量告警、外网蜜罐告警，自动阻断来自外网的恶意攻击、国外的可疑访问。", "", links));

            List<Step.Link> links2 = new ArrayList<>();
            links2.add(new Step.Link("alarm", "安全事件", "/service-ledger/theratManage"));
            links2.add(new Step.Link("application", "关联业务系统", "/asset-ledger/monitor2/application"));
            stepList.add(new Step("安全事件实时监测", 2, "0", "", "", "", "",
                    "开展攻击安全事件实时监测，对发现的攻击行为进行确认，详细记录攻击相关数据，为后续处置工作开展提供信息", "", links2));

            List<Step.Link> links3 = new ArrayList<>();
            links3.add(new Step.Link("handledAlarm", "已处置事件", "/service-ledger/theratManage?handle=1"));
            links3.add(new Step.Link("noHandledAlarm", "未处置事件", "/service-ledger/theratManage?handle=0"));
            stepList.add(new Step("事件分析与处置", 3, "0", "", "", "", "",
                    "发生安全事件第一时间安排人员进行应急处置，一般安全事件远程处理，重大安全事件现场应急排查，提供相应的应急响应报告", "", links3));

            return stepList;
        }

        private List<Step> initSummaryStageSteps() {
            List<Step> stepList = new ArrayList<>();

            stepList.add(new Step("防守工作汇总", 1, "0", "", "", "", "", "全面总结本次攻防演习各阶段的工作情况，包括组织队伍、攻击情况、防守情况、安全防护措施、监测手段、响应和协同处置等，形成总结报告并向防守单位汇报", "", null, 1));

            return stepList;
        }
    }

    @Data
    public static class Step {
        private int needFile;
        private String endTime;
        private List<File> files;
        private String finishTime;
        static final List<Link> emptyLinkList = new ArrayList<>();
        private List<Link> links = emptyLinkList;
        private int order;
        private String startTime;
        private String state;
        private String tips;
        private String title;
        private String userIds;
        private String userNames;

        public Step(String title, int order, String state, String startTime,
                    String endTime, String finishTime, String files, String tips,
                    String userIds, List<Link> links) {
            this(title, order, state, startTime, endTime, finishTime, files, tips, userIds, links, 0);
        }

        public Step(String title, int order, String state, String startTime,
                    String endTime, String finishTime, String files, String tips,
                    String userIds, List<Link> links, int needFile) {
            this.title = title;
            this.order = order;
            this.state = state;
            this.startTime = startTime;
            this.endTime = endTime;
            this.finishTime = finishTime;
            this.tips = tips;
            this.userIds = userIds;
            this.links = links;
            this.needFile = needFile;
        }

        @Data
        public static class Link {
            private String countId;
            private String label;
            private String url;

            public Link(String countId, String label, String url) {
                this.countId = countId;
                this.label = label;
                this.url = url;
            }
        }

        @Data
        public static class File {
            private String name;
            private String url;

            public File(String name, String url) {
                this.name = name;
                this.url = url;
            }
        }
    }

    public static void main(String[] args) {
        StagesData stagesData = new StagesData();
        System.out.println(stagesData);
    }
}
