package com.ruoyi.monitor2.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.safe.domain.TblBusinessApplication;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 漏洞处置对象 monitor_bss_vuln_deal
 *
 * <AUTHOR>
 * @date 2023-10-24
 */
@Data
public class MonitorBssVulnDeal extends BaseEntity {
    private static final long serialVersionUID = 1L;

    public static final int WAIT_DEAL = 1;
    public static final int REPAIRING = 2;
    public static final int WAIT_CHECK = 3;
    public static final int FALSE_ALARM = 4;
    public static final int IGNORE = 5;
    public static final int REPAIRED = 6;

    public static final int SCAN_DATA = 1;
    public static final int MANUAL_DATA = 2;
    /* 无工单状态 */
    public static final String NO_WORK="99";
    /* 已完成工单状态 */
    public static final String IS_FINISH_WORK="3";

    /** 自增id */
    private Long id;

    private List<Long> notIds;

    /** 漏洞标题 */
    @Excel(name = "漏洞标题")
    private String title;

    /** 漏洞类型 */
    @Excel(name = "漏洞类型")
    private String category;

    /** 危险程度(0: UNKNOWN  1: NONE, 2: LOW  3: MEDIUM,  4: HIGH) */
    @Excel(name = "危险程度",readConverterExp = "0=未知,1=低危,2=中危,3=高危,4=严重")
    private Integer severity;

    /** 影响ip */
    @Excel(name = "影响ip")
    private String hostIp;

    private List<String> destIps;

    /** 协议 */
    private String protocol;

    /** 端口 */
    @Excel(name = "端口")
    private Integer hostPort;

    /** 漏洞概述 */
    @Excel(name = "漏洞说明")
    private String summary;

    /** 修复方式 */
    @Excel(name = "加固建议")
    private String solution;

    /** 漏洞验证方式 */
    @Excel(name = "利用证明")
    private String poc;

    /** 附件url */
    private String fileUrl;

    /** 处理状态（1: 待分配  2: 修复中  3: 待验证  4: 误报  5: 忽略  default: 1） */
//    @Excel(name = "处理状态",readConverterExp = "1=待分配,2=修复中,3=待验证,4=误报,5=忽略,6=修复完成")
    private Integer dealStatus;

    /* 漏洞id */
    private Long vulnId;

    public String getSearchKey() {
        return title + "_" + hostIp + "_" + hostPort;
    }
    //处理类型
    private String handleType;
    //处置状态
//    @Excel(name = "处置状态",readConverterExp = "0=完成修复,1=误报,2=忽略,99=未处置")
    private String handleStatus;
    @Excel(name = "处置状态",readConverterExp = "0=未处置,1=已处置,2=忽略,3=处置中")
    private String handleState;
    @Excel(name = "处置描述")
    private String handleDesc;
    //工单状态
    //@Excel(name = "工单状态",readConverterExp = "0=待审核,1=待处置,2=待审核,3=已完成,99=待分配")
    private String flowState;

    /** 数据来源 */
    @Excel(name = "数据来源",readConverterExp = "1=探测,2=手动")
    private Integer dataSource;

    /** 发现次数 */
    @Excel(name = "发现次数")
    private Integer scanNum;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最近漏洞时间",dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Date handleTime;

    /** 工单id */
    private Long workId;

    /** JNPF工单任务ID */
    private String prodId;

    /** 资产名称 */
    private String assetName;

    /** 资产类型 */
    private String assetType;

    /** 资产类型描述 */
    private String assetTypeDesc;

    /* 漏洞信息 */
    private MonitorBssVulnInfo bssVulnInfo;

    /** 是否查询 top10 */
    private boolean top10 = false;

    private Date startTime;
    private Date endTime;

    /** 需要展示的危险程度 */
    private List<Integer> showSeverities;

    private List<Long> ids;

    /** 资产id */
    private Long assetId;

    private List<TblBusinessApplication> businessApplications;

    private Long deptId;

    private Long applicationId;

    private String deptName;

    private String domainId;
    private Long workOrderId;

    private String cycleType;

    /**
     * 时间范围:
     *  1:一天
     * 	2:一周
     * 	3:一月
     * 	4:三月
     * 	5:半年
     */
    private String timeRange;

    private List<String> ipv4List;
    private List<Long> workOrderIdList;

    private String disposer;
    private String synchronizationStatus;
    private List<Long> disposers;
    private String dataId;
}
