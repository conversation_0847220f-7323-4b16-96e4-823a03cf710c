package com.ruoyi.external.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackApp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * APP应用程序Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface ExternalAttackAppMapper
{
    /**
     * 查询APP应用程序
     *
     * @param id APP应用程序主键
     * @return APP应用程序
     */
    public ExternalAttackApp selectExternalAttackAppById(Long id);

    /**
     * 批量查询APP应用程序
     *
     * @param ids APP应用程序主键集合
     * @return APP应用程序集合
     */
    public List<ExternalAttackApp> selectExternalAttackAppByIds(Long[] ids);

    /**
     * 查询APP应用程序列表
     *
     * @param externalAttackApp APP应用程序
     * @return APP应用程序集合
     */
    public List<ExternalAttackApp> selectExternalAttackAppList(ExternalAttackApp externalAttackApp);

    /**
     * 新增APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    public int insertExternalAttackApp(ExternalAttackApp externalAttackApp);

    /**
     * 批量插入APP应用程序
     *
     * @param entityList APP应用程序实体列表
     * @return 插入成功的记录数
     */
    int batchInsertExternalAttackApp(@Param("entityList") List<ExternalAttackApp> entityList);

    /**
     * 修改APP应用程序
     *
     * @param externalAttackApp APP应用程序
     * @return 结果
     */
    public int updateExternalAttackApp(ExternalAttackApp externalAttackApp);

    /**
     * 删除APP应用程序
     *
     * @param id APP应用程序主键
     * @return 结果
     */
    public int deleteExternalAttackAppById(Long id);

    /**
     * 批量删除APP应用程序
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExternalAttackAppByIds(Long[] ids);

    /**
     * 检查 APP应用程序 是否已存在（排除指定 id）
     * @param appId
     * @param appName
     * @param id
     */
    int checkAppExistence(@Param("appId") String appId, @Param("appName") String appName, @Param("id") Long id);

    /**
     * 根据 appIds 查询 APP应用程序
     * @param appIds
     * @return
     */
    List<ExternalAttackApp> selectByAppIds(@Param("appIds") List<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(@Param("uniqueKeys") Set<String> uniqueKeys, @Param("deviceConfigId") Long deviceConfigId);

    /**
     * 选择性更新APP应用程序：只更新第三方接口返回的原有字段，保护用户维护的新增字段
     *
     * @param externalAttackApp APP应用程序实体，包含要更新的数据
     * @return 更新的记录数
     */
    int selectiveUpdateByCondition(ExternalAttackApp externalAttackApp);

    /**
     * 根据唯一键查询现有记录，用于智能同步时的数据检测
     *
     * @param uniqueKey 唯一键，格式为 "appName-appId"
     * @return APP应用程序实体，如果不存在则返回null
     */
    ExternalAttackApp selectByUniqueKey(@Param("uniqueKey") String uniqueKey);

    /**
     * 批量查询现有记录，用于智能同步时的批量数据检测
     *
     * @param uniqueKeys 唯一键集合
     * @return APP应用程序实体列表
     */
    List<ExternalAttackApp> selectByUniqueKeys(@Param("uniqueKeys") Set<String> uniqueKeys);
}
