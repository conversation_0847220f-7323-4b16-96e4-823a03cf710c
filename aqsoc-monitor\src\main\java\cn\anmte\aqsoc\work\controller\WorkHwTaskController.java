package cn.anmte.aqsoc.work.controller;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.service.ITblOperateWorkRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description: HW事务任务
 * @date 2025-08-25 16:26
 */
@RestController
@RequestMapping("/workHwTask")
public class WorkHwTaskController extends BaseController {
    @Resource
    private IWorkHwTaskService workHwTaskService;
    @Resource
    private ITblOperateWorkRecordService tblOperateWorkRecordService;

    /**
     * 查询HW事务任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WorkHwTask workHwTask) {
        startPage();
        List<WorkHwTask> list = workHwTaskService.selectWorkHwTaskList(workHwTask);
        return getDataTable(list);
    }

    /**
     * 获取HW事务任务详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(workHwTaskService.selectWorkHwTaskById(id));
    }

    /**
     * 新增HW事务任务
     */
    @Log(title = "HW事务任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WorkHwTask workHwTask)
    {
        return toAjax(workHwTaskService.insertWorkHwTask(workHwTask));
    }

    /**
     * 修改HW事务任务
     */
    @Log(title = "HW事务任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WorkHwTask workHwTask)
    {
        return toAjax(workHwTaskService.updateWorkHwTask(workHwTask));
    }

    /**
     * 删除HW事务任务
     */
    @Log(title = "HW事务任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(workHwTaskService.deleteWorkHwTaskByIds(ids));
    }

    @GetMapping("/getStageTree")
    public AjaxResult getStageTree(WorkHwTask workHwTask){
        if(workHwTask.getWorkId() == null){
            return AjaxResult.error("参数异常");
        }
        return AjaxResult.success(workHwTaskService.getStageTree(workHwTask));
    }

    @PostMapping("/callback")
    public AjaxResult callback(@RequestBody WorkHwTask workHwTask){
        List<WorkHwTask> taskList = workHwTaskService.list(new LambdaQueryWrapper<WorkHwTask>().eq(WorkHwTask::getFlowTaskId, workHwTask.getFlowTaskId()));
        if(CollUtil.isNotEmpty(taskList)){
            TblOperateWorkRecord queryOperateWorkRecord = new TblOperateWorkRecord();
            queryOperateWorkRecord.setFlowTaskIdList(CollUtil.toList(workHwTask.getFlowTaskId()));
            queryOperateWorkRecord.setQueryAll(true);
            queryOperateWorkRecord.setQueryAllData(true);
            List<TblOperateWorkRecord> workRecordList = tblOperateWorkRecordService.selectTblOperateWorkRecordListByFlowIds(queryOperateWorkRecord);
            if(CollUtil.isNotEmpty(workRecordList)){
                TblOperateWorkRecord operateWorkRecord = workRecordList.get(0);
                if(operateWorkRecord.getFFlowstate() == 100){
                    taskList.forEach(task -> {
                        task.setCompleteTime(operateWorkRecord.getUpdateTime());
                    });
                    workHwTaskService.saveOrUpdateBatch(taskList);
                }
            }
        }
        return AjaxResult.success();
    }
}
