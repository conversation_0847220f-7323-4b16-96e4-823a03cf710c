package com.ruoyi.monitor2.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.domain.dto.TblOperateWorkRecordStatisticsDto;
import com.ruoyi.safe.domain.dto.OverviewParams;

import java.util.List;

/**
 * 事务列表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface ITblOperateWorkRecordService
{
    /**
     * 查询事务列表
     *
     * @param id 事务列表主键
     * @return 事务列表
     */
    public TblOperateWorkRecord selectTblOperateWorkRecordById(Long id);

    /**
     * 批量查询事务列表
     *
     * @param ids 事务列表主键集合
     * @return 事务列表集合
     */
    public List<TblOperateWorkRecord> selectTblOperateWorkRecordByIds(Long[] ids);

    /**
     * 查询事务列表列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 事务列表集合
     */
    public List<TblOperateWorkRecord> selectTblOperateWorkRecordList(TblOperateWorkRecord tblOperateWorkRecord);

    /**
     * 新增事务列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 结果
     */
    public int insertTblOperateWorkRecord(TblOperateWorkRecord tblOperateWorkRecord);

    /**
     * 修改事务列表
     *
     * @param tblOperateWorkRecord 事务列表
     * @return 结果
     */
    public int updateTblOperateWorkRecord(TblOperateWorkRecord tblOperateWorkRecord);

    /**
     * 删除事务列表信息
     *
     * @param id 事务列表主键
     * @return 结果
     */
    public int deleteTblOperateWorkRecordById(Long id);

    /**
     * 批量删除事务列表
     *
     * @param ids 需要删除的事务列表主键集合
     * @return 结果
     */
    public int deleteTblOperateWorkRecordByIds(Long[] ids);

    int updateByFlowTaskId(TblOperateWorkRecord saveRecord);

    TblOperateWorkRecord getDetailsByTaskId(TblOperateWorkRecord tblOperateWorkRecord);

    int countOperationalMattersNum(OverviewParams params);

    int countOperateWorkRecordByStatus(TblOperateWorkRecord tblOperateWorkRecord);

    public void handlePrem(TblOperateWorkRecord workRecord);

    List<TblOperateWorkRecord> selectTblOperateWorkRecordListByFlowIds(TblOperateWorkRecord operateWorkRecord);
}
