{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1756706031904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_application", "require", "_applicationLink", "_interopRequireDefault", "_applicationSite", "_userSelect", "_deptSelect", "_networkSelect", "_DynamicTag", "_vendorSelect", "_dictSelect", "_utils", "_ruoyi", "_vendor", "_serverEV", "_dateEV", "_networkEV", "_safeEV", "_overViewSelect", "_overview", "_editServer", "_user", "_domain", "_dayjs", "name", "components", "EditServer", "overViewSelect", "safeEV", "networkEV", "dateEV", "serverEV", "ApplicationLink", "ApplicationSite", "UserSelect", "DeptSelect", "NetworkSelect", "DictSelect", "DynamicTag", "VendorSelect2", "dicts", "inject", "$editable", "default", "value", "props", "assetId", "type", "String", "Number", "required", "changeId", "Function", "readonly", "Boolean", "disabled", "assetList", "Array", "data", "loading", "collapseNames", "vendorsdata", "userdata", "functionStateList", "form", "businessForm", "delList", "deptOptions", "gv", "getValFromObject", "deployLocation", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "managePlaceholder", "<PERSON><PERSON><PERSON>", "networkDomainOptions", "vendorsData", "refs", "collapse", "showAddServer", "serverOptions", "currentAssociationServer", "afterInit", "uploadType", "selectType", "mounted", "_this", "getAllServerList", "getDeptTree", "getManagerList", "getNetworkDomainTree", "getVendorsData", "$nextTick", "reset", "init", "computed", "filterAssetList", "filter", "item", "isShow", "map", "_item$fieldsItems", "_objectSpread2", "fieldsItems", "field", "getServerName", "_this2", "id", "_this2$serverOptions$", "find", "s", "assetName", "getServerIp", "_this3", "_this3$serverOptions$", "ip", "processedManagerList", "_this4", "ids", "_toConsumableArray2", "Set", "manager", "split", "user", "u", "userId", "nick<PERSON><PERSON>", "phone", "phonenumber", "processedVendorsList", "_this5", "vendors", "vendorName", "processedServiceGroups", "_this6", "serviceGroup", "val", "_this6$dict$type$serv", "dict", "d", "label", "activated", "_this7", "watch", "handler", "newVal", "oldVal", "length", "for<PERSON>ach", "index", "Object", "keys", "tempId", "generateSecureUUID", "systemType", "toString", "methods", "getFieldValue", "_this8", "filterArr", "includes", "<PERSON><PERSON><PERSON>", "_this$dict$type$hw_is", "hw_is_true_shut_down", "dayjs", "format", "getFieldSpan", "fullSpanFields", "shouldShowField", "isadapt", "iscipher", "getDictOptions", "dictMap", "construct", "loginType", "technical", "deploy", "state", "protectGrade", "evaluationResults", "evaluationStatus", "hwIsTrueShutDown", "coverArea", "_this9", "listAllOverview", "then", "res", "_this10", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getApplication", "response", "applicationVO", "waitForValue", "$refs", "site", "getList", "tblBusinessApplication", "userNums", "everydayVisitNums", "everydayActiveNums", "tblMapperList", "i", "push", "finally", "stop", "undefined", "assetCode", "softwareVersion", "degreeImportance", "domainUrl", "assetType", "assetTypeDesc", "assetClass", "assetClassDesc", "netType", "appType", "frequency", "usageCount", "userScale", "userObject", "url", "ipd", "storage", "netenv", "iskey", "datanum", "isbase", "islink", "ishare", "islog", "isplan", "adaptDate", "cipherDate", "function", "remark", "deptId", "orgnId", "upTime", "dwid", "contactor", "domainId", "netScale", "netTopo", "netMemo", "tags", "links", "eids", "sysBusinessState", "resetForm", "serverSelect", "$set", "serverId", "_this11", "getAllDeptTree", "flattenTreeToArray", "deptTreeSelect", "_this12", "listUser", "isAsc", "orderByColumn", "isAllData", "userName", "rows", "_this13", "listDomain", "_this14", "listVendorByApplication", "applicationId", "applicationCode", "pageNum", "pageSize", "vendorCode", "vendorManageName"], "sources": ["src/views/hhlCode/component/OperationSystemDetails.vue"], "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in filterAssetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {flattenTreeData, flattenTreeToArray, getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus', 'coverArea'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    filterAssetList() {\n      return this.assetList\n        .filter(item => item.isShow)\n        .map(item => {\n          return {\n            ...item,\n            fieldsItems: item.fieldsItems?.filter(field => field.isShow) || []\n          };\n        });\n    },\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      if(field.fieldKey === 'uodTime'){\n        if(!this.form[field.fieldKey]){\n          return ''\n        }else {\n          return dayjs(this.form[field.fieldKey]).format('YYYY-MM-DD HH:mm:ss')\n        }\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down',\n        coverArea: 'cover_area'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsRA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,WAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,SAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,OAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,UAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,OAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,eAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,SAAA,GAAAlB,OAAA;AACA,IAAAmB,WAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,KAAA,GAAApB,OAAA;AACA,IAAAqB,OAAA,GAAArB,OAAA;AACA,IAAAsB,MAAA,GAAApB,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAuB,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA,GACA,eACA,cACA,cACA,iBACA,kBACA,eACA,oBACA,eACA,kBACA,iBACA,cACA,eACA,sBACA,qBACA,mBACA,uBACA;EACAC,MAAA;IACAC,SAAA;MACAC,OAAA;QAAAC,KAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;MACAP,OAAA;IACA;IACAQ,QAAA,EAAAC,QAAA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAY,QAAA;MACAR,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAa,SAAA;MACAT,IAAA,EAAAU,KAAA;MACAd,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAe,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,iBAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;QACAC,OAAA;MACA;MACAC,WAAA;MACAC,EAAA,EAAAC,uBAAA;MACAC,cAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,IAAA;QACA;QACA;QACA;QACA;MACA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,WAAA;IACA,KAAAC,cAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,cAAA;IACA,KAAAC,SAAA;MACA,IAAAN,KAAA,CAAAjB,cAAA;QACAiB,KAAA,CAAAd,YAAA;QACAc,KAAA,CAAAb,iBAAA;MACA;MACAa,KAAA,CAAAO,KAAA;MACAP,KAAA,CAAAQ,IAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,YAAAzC,SAAA,CACA0C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA,GACAC,GAAA,WAAAF,IAAA;QAAA,IAAAG,iBAAA;QACA,WAAAC,cAAA,CAAA5D,OAAA,MAAA4D,cAAA,CAAA5D,OAAA,MACAwD,IAAA;UACAK,WAAA,IAAAF,iBAAA,GAAAH,IAAA,CAAAK,WAAA,cAAAF,iBAAA,uBAAAA,iBAAA,CAAAJ,MAAA,WAAAO,KAAA;YAAA,OAAAA,KAAA,CAAAL,MAAA;UAAA;QAAA;MAEA;IACA;IACA;IACAM,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,iBAAAC,EAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAF,MAAA,CAAA1B,aAAA,CAAA6B,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAjE,OAAA,KAAA8D,EAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,SAAA;MAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,iBAAAN,EAAA;QAAA,IAAAO,qBAAA;QAAA,SAAAA,qBAAA,GAAAD,MAAA,CAAAjC,aAAA,CAAA6B,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAjE,OAAA,KAAA8D,EAAA;QAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAC,EAAA;MAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,GAAA,OAAAC,mBAAA,CAAA7E,OAAA,MAAA8E,GAAA,CACA,MAAAzD,IAAA,CAAA0D,OAAA,QACAC,KAAA,MACAzB,MAAA,CAAA5C,OAAA,CACA;MAEA,OAAAiE,GAAA,CAAAlB,GAAA,WAAAO,EAAA;QACA,IAAAgB,IAAA,GAAAN,MAAA,CAAA3C,WAAA,CAAAmC,IAAA,WAAAe,CAAA;UAAA,OACA5E,MAAA,CAAA4E,CAAA,CAAAC,MAAA,MAAA7E,MAAA,CAAA2D,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACApF,IAAA,GAAAoG,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAG,QAAA;UACAC,KAAA,GAAAJ,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAK,WAAA;QACA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAAZ,GAAA,OAAAC,mBAAA,CAAA7E,OAAA,MAAA8E,GAAA,CACA,MAAAzD,IAAA,CAAAoE,OAAA,QACAT,KAAA,MACAzB,MAAA,CAAA5C,OAAA,CACA;MAEA,OAAAiE,GAAA,CAAAlB,GAAA,WAAAO,EAAA;QACA,IAAAgB,IAAA,GAAAO,MAAA,CAAAtD,WAAA,CAAAiC,IAAA,WAAAe,CAAA;UAAA,OACA5E,MAAA,CAAA4E,CAAA,CAAAjB,EAAA,MAAA3D,MAAA,CAAA2D,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACApF,IAAA,GAAAoG,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAS,UAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,UAAAtE,YAAA,CAAAuE,YAAA;MACA,YAAAvE,YAAA,CAAAuE,YAAA,CAAAb,KAAA,MACAtB,GAAA,WAAAoC,GAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAH,MAAA,CAAAI,IAAA,CAAA5F,IAAA,gBAAA+D,IAAA,WAAA8B,CAAA;UAAA,OAAAA,CAAA,CAAAhG,KAAA,KAAA6F,GAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,KAAA,KAAAJ,GAAA;MAAA;IACA;EACA;EACAK,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACA,KAAAlD,SAAA;MACAkD,MAAA,CAAAjD,KAAA;MACAiD,MAAA,CAAAhD,IAAA;IACA;EACA;EACAiD,KAAA;IACAjF,iBAAA;MACAkF,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,MAAA;UACAF,MAAA,CAAAG,OAAA,WAAAlD,IAAA,EAAAmD,KAAA;YACA,IAAAC,MAAA,CAAAC,IAAA,CAAArD,IAAA,EAAAiD,MAAA;cACAjD,IAAA,CAAAsD,MAAA,OAAAC,yBAAA;YACA;UACA;QACA;MACA;IACA;IACA;MACAT,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA;UACA,KAAAlF,IAAA,CAAA2F,UAAA,GAAAT,MAAA,CAAAU,QAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAArD,KAAA;MAAA,IAAAsD,MAAA;MACA;MACA,IAAAC,SAAA;MACA,IAAAA,SAAA,CAAAC,QAAA,CAAAxD,KAAA,CAAAyD,QAAA;QACA,YAAAlG,IAAA,CAAAyC,KAAA,CAAAyD,QAAA;MACA;MACA,IAAAzD,KAAA,CAAAyD,QAAA;QAAA,IAAAC,qBAAA;QACA,SAAAA,qBAAA,QAAAxB,IAAA,CAAA5F,IAAA,CAAAqH,oBAAA,CAAAtD,IAAA,WAAA8B,CAAA;UAAA,OAAAA,CAAA,CAAAhG,KAAA,KAAAmH,MAAA,CAAA/F,IAAA,CAAAyC,KAAA,CAAAyD,QAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAtB,KAAA,UAAA7E,IAAA,CAAAyC,KAAA,CAAAyD,QAAA;MACA;MACA,IAAAzD,KAAA,CAAAyD,QAAA;QACA,UAAAlG,IAAA,CAAAyC,KAAA,CAAAyD,QAAA;UACA;QACA;UACA,WAAAG,cAAA,OAAArG,IAAA,CAAAyC,KAAA,CAAAyD,QAAA,GAAAI,MAAA;QACA;MACA;MACA,YAAAtG,IAAA,CAAAyC,KAAA,CAAAyD,QAAA;IACA;IAEAK,YAAA,WAAAA,aAAA9D,KAAA;MACA,IAAA+D,cAAA;MACA,IAAAA,cAAA,CAAAP,QAAA,CAAAxD,KAAA,CAAAyD,QAAA;MACA;MACA;IACA;IACA;IACAO,eAAA,WAAAA,gBAAAhE,KAAA;MACA,IAAAA,KAAA,CAAAyD,QAAA;QACA,YAAAlG,IAAA,CAAA2F,UAAA;MACA;MACA,IAAAlD,KAAA,CAAAyD,QAAA;QACA,YAAAlG,IAAA,CAAA0G,OAAA;MACA;MACA,IAAAjE,KAAA,CAAAyD,QAAA;QACA,YAAAlG,IAAA,CAAA2G,QAAA;MACA;MACA,IAAAlE,KAAA,CAAAyD,QAAA;QACA,YAAA5F,cAAA;MACA;MACA;IACA;IAEAsG,cAAA,WAAAA,eAAAV,QAAA;MACA,IAAAW,OAAA;QACAlB,UAAA;QACAmB,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,KAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,SAAA;MACA;MACA,YAAA5C,IAAA,CAAA5F,IAAA,CAAA8H,OAAA,CAAAX,QAAA;IACA;IAEA1E,gBAAA,WAAAA,iBAAA;MAAA,IAAAgG,MAAA;MACA,IAAAC,yBAAA;QAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAvG,aAAA,GAAA0G,GAAA,CAAAjI,IAAA;MACA;IACA;IACA,UACAqC,IAAA,WAAAA,KAAA;MAAA,IAAA6F,OAAA;MAAA,WAAAC,kBAAA,CAAAlJ,OAAA,mBAAAmJ,oBAAA,CAAAnJ,OAAA,IAAAoJ,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAnJ,OAAA,IAAAsJ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KAEAT,OAAA,CAAA9I,OAAA;gBAAAqJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,2BAAA,EAAAV,OAAA,CAAA9I,OAAA,EAAA4I,IAAA,WAAAa,QAAA;gBACA;gBACAX,OAAA,CAAA5H,IAAA,CAAAlB,OAAA,GAAA8I,OAAA,CAAA9I,OAAA;gBACA8I,OAAA,CAAA5H,IAAA,GAAAuI,QAAA,CAAA7I,IAAA,CAAA8I,aAAA;gBACA,IAAAC,mBAAA;kBAAA,WAAApI,uBAAA,UAAAuH,OAAA,CAAAc,KAAA;gBAAA,GAAAhB,IAAA,WAAAiB,IAAA;kBACA,KAAAA,IAAA;oBACA;kBACA;kBACA,IAAAA,IAAA,YAAAlJ,KAAA;oBACAkJ,IAAA,CAAAtD,OAAA,WAAAlD,IAAA;sBAAA,OAAAA,IAAA,CAAAyG,OAAA;oBAAA;kBACA;oBACAD,IAAA,CAAAC,OAAA;kBACA;gBACA;gBACA;gBACAhB,OAAA,CAAA3H,YAAA,CAAAnB,OAAA,GAAA8I,OAAA,CAAA9I,OAAA;gBACA8I,OAAA,CAAA3H,YAAA,GAAAsI,QAAA,CAAA7I,IAAA,CAAAmJ,sBAAA;gBACAjB,OAAA,CAAA3H,YAAA,CAAA6I,QAAA,GAAAlB,OAAA,CAAA3H,YAAA,CAAA6I,QAAA,YAAAlB,OAAA,CAAA3H,YAAA,CAAA6I,QAAA;gBACAlB,OAAA,CAAA3H,YAAA,CAAA8I,iBAAA,GAAAnB,OAAA,CAAA3H,YAAA,CAAA8I,iBAAA,YAAAnB,OAAA,CAAA3H,YAAA,CAAA8I,iBAAA;gBACAnB,OAAA,CAAA3H,YAAA,CAAA+I,kBAAA,GAAApB,OAAA,CAAA3H,YAAA,CAAA+I,kBAAA,YAAApB,OAAA,CAAA3H,YAAA,CAAA+I,kBAAA;gBACApB,OAAA,CAAA7H,iBAAA,GAAAwI,QAAA,CAAA7I,IAAA,CAAAmJ,sBAAA,CAAAI,aAAA;gBACA,IAAArB,OAAA,CAAA7H,iBAAA,CAAAqF,MAAA;kBACA,IAAA8D,CAAA;kBACA,OAAAA,CAAA,OAAAtB,OAAA,CAAA7H,iBAAA,CAAAqF,MAAA;oBACAwC,OAAA,CAAA7H,iBAAA,CAAAoJ,IAAA;kBACA;gBACA;cACA,GAAAC,OAAA;gBACAxB,OAAA,CAAAzG,SAAA;cACA;YAAA;cAAAgH,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAT,OAAA,CAAAzG,SAAA;YAAA;YAAA;cAAA,OAAAgH,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAArB,OAAA;MAAA;IAEA;IAGA,WACAlG,KAAA,WAAAA,MAAA;MACA,KAAA9B,IAAA;QACAlB,OAAA,EAAAwK,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAtG,SAAA,EAAAsG,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,gBAAA,EAAAH,SAAA;QACA5F,OAAA,EAAA4F,SAAA;QACAI,SAAA,EAAAJ,SAAA;QACA3D,UAAA,EAAA2D,SAAA;QACAtF,KAAA,EAAAsF,SAAA;QACAK,SAAA,EAAAL,SAAA;QACAM,aAAA,EAAAN,SAAA;QACAO,UAAA,EAAAP,SAAA;QACAQ,cAAA,EAAAR,SAAA;QACAxC,SAAA,EAAAwC,SAAA;QACAS,OAAA,EAAAT,SAAA;QACAU,OAAA,EAAAV,SAAA;QACA9E,YAAA,EAAA8E,SAAA;QACAW,SAAA,EAAAX,SAAA;QACAY,UAAA,EAAAZ,SAAA;QACAa,SAAA,EAAAb,SAAA;QACAc,UAAA,EAAAd,SAAA;QACAe,GAAA,EAAAf,SAAA;QACAgB,GAAA,EAAAhB,SAAA;QACAtC,SAAA,EAAAsC,SAAA;QACArC,MAAA,EAAAqC,SAAA;QACAiB,OAAA,EAAAjB,SAAA;QACAkB,MAAA,EAAAlB,SAAA;QACAmB,KAAA,EAAAnB,SAAA;QACAoB,OAAA,EAAApB,SAAA;QACAqB,MAAA;QACAC,MAAA,EAAAtB,SAAA;QACAuB,MAAA,EAAAvB,SAAA;QACAwB,KAAA,EAAAxB,SAAA;QACAyB,MAAA,EAAAzB,SAAA;QACA5C,OAAA,EAAA4C,SAAA;QACA3C,QAAA,EAAA2C,SAAA;QACA0B,SAAA,EAAA1B,SAAA;QACA2B,UAAA,EAAA3B,SAAA;QACA4B,QAAA,EAAA5B,SAAA;QACA6B,MAAA,EAAA7B,SAAA;QACAxF,MAAA,EAAAwF,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACA+B,MAAA,EAAA/B,SAAA;QACAlF,OAAA,EAAAkF,SAAA;QACAgC,MAAA,EAAAhC,SAAA;QACAiC,IAAA,EAAAjC,SAAA;QACAkC,SAAA,EAAAlC,SAAA;QACAmC,QAAA,EAAAnC,SAAA;QACAoC,QAAA,EAAApC,SAAA;QACAqC,OAAA,EAAArC,SAAA;QACAsC,OAAA,EAAAtC,SAAA;QACAuC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACA,KAAA9L,YAAA;QACA+L,gBAAA,EAAA1C,SAAA;QACAR,QAAA,EAAAQ,SAAA;QACAP,iBAAA,EAAAO,SAAA;QACAN,kBAAA,EAAAM;MACA;MACA,KAAA2C,SAAA;MACA,KAAAA,SAAA;IACA;IAEAC,YAAA,WAAAA,aAAAxM,IAAA;MACA,IAAAA,IAAA;QACA,KAAAyM,IAAA,MAAAnM,IAAA,uBAAAN,IAAA,CAAA2C,GAAA,WAAAF,IAAA;UAAA,OAAAA,IAAA,CAAAiK,QAAA;QAAA;MACA;IACA;IAEA,aACA3K,WAAA,WAAAA,YAAA;MAAA,IAAA4K,OAAA;MACA,SAAA3N,SAAA,CAAAE,KAAA;QACA,IAAA0N,oBAAA,IAAA5E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAAlM,WAAA,OAAAoM,yBAAA,EAAAhE,QAAA,CAAA7I,IAAA;QACA;MACA;QACA,IAAA8M,oBAAA,IAAA9E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAAlM,WAAA,OAAAoM,yBAAA,EAAAhE,QAAA,CAAA7I,IAAA;QACA;MACA;IACA;IAEA;IACAgC,cAAA,WAAAA,eAAA;MAAA,IAAA+K,OAAA;MACA,IAAAC,cAAA;QACAC,KAAA;QACAC,aAAA;QACAC,SAAA;QACAC,QAAA;QACA/I,QAAA;QACAE,WAAA;MACA,GAAAyD,IAAA,WAAAa,QAAA;QACAkE,OAAA,CAAA9L,WAAA,GAAA4H,QAAA,CAAAwE,IAAA;MACA;IACA;IAEA,cACApL,oBAAA,WAAAA,qBAAA;MAAA,IAAAqL,OAAA;MACA,IAAAC,kBAAA,IAAAvF,IAAA,WAAAa,QAAA;QACAyE,OAAA,CAAApM,oBAAA,GAAA2H,QAAA,CAAA7I,IAAA;MACA;IACA;IAEA,cACAkC,cAAA,WAAAA,eAAA;MAAA,IAAAsL,OAAA;MACA,IAAAC,+BAAA;QACAC,aAAA,OAAAtO,OAAA;QACAuO,eAAA,OAAArN,IAAA,CAAAoE,OAAA;QACAuI,KAAA;QACAC,aAAA;QACAU,OAAA;QACAC,QAAA;QACAC,UAAA;QACAnJ,UAAA;QACAoJ,gBAAA;MACA,GAAA/F,IAAA,WAAAa,QAAA;QACA2E,OAAA,CAAArM,WAAA,GAAA0H,QAAA,CAAAwE,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}