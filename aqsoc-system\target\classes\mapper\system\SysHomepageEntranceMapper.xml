<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysHomepageEntranceMapper">
    
    <resultMap type="SysHomepageEntrance" id="SysHomepageEntranceResult">
        <result property="id"    column="id"    />
        <result property="entranceId"    column="entrance_id"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="SysHomepageEntrance" id="SysHomepageEntranceDetailResult">
        <result property="id"    column="id"    />
        <result property="entranceId"    column="entrance_id"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <association property="shortcutEntrance" javaType="SysShortcutEntrance">
            <result property="entranceId"    column="se_entrance_id"    />
            <result property="entranceCategory"    column="se_entrance_category"    />
            <result property="entranceName"    column="se_entrance_name"    />
            <result property="menuId"    column="se_menu_id"    />
            <result property="iconUrl"    column="se_icon_url"    />
            <result property="status"    column="se_status"    />
        </association>
    </resultMap>

    <sql id="selectSysHomepageEntranceVo">
        select id, entrance_id, sort_order, create_by, create_time, update_by, update_time from sys_homepage_entrance
    </sql>

    <sql id="selectSysHomepageEntranceDetailVo">
        select 
            he.id, he.entrance_id, he.sort_order, he.create_by, he.create_time, he.update_by, he.update_time,
            se.entrance_id as se_entrance_id, se.entrance_category as se_entrance_category, 
            se.entrance_name as se_entrance_name, se.menu_id as se_menu_id, 
            se.icon_url as se_icon_url, se.status as se_status
        from sys_homepage_entrance he
        left join sys_shortcut_entrance se on he.entrance_id = se.entrance_id
    </sql>

    <select id="selectSysHomepageEntranceList" parameterType="SysHomepageEntrance" resultMap="SysHomepageEntranceResult">
        <include refid="selectSysHomepageEntranceVo"/>
        <where>  
            <if test="entranceId != null "> and entrance_id = #{entranceId}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
        </where>
        order by sort_order asc, create_time asc
    </select>

    <select id="selectSysHomepageEntranceListWithDetail" parameterType="SysHomepageEntrance" resultMap="SysHomepageEntranceDetailResult">
        <include refid="selectSysHomepageEntranceDetailVo"/>
        <where>
            <if test="entranceId != null "> and he.entrance_id = #{entranceId}</if>
            <if test="sortOrder != null "> and he.sort_order = #{sortOrder}</if>
             <if test="menuIds != null and menuIds.size() > 0">
                and se.menu_id in
                <foreach item="menuId" collection="menuIds" open="(" separator="," close=")">
                    #{menuId}
                </foreach>
            </if>
            and se.status = '0'
        </where>
        order by he.sort_order asc, he.create_time asc
    </select>

    <select id="selectSysHomepageEntranceDetailList" parameterType="SysHomepageEntrance" resultMap="SysHomepageEntranceDetailResult">
        <include refid="selectSysHomepageEntranceDetailVo"/>
        <where>
            <if test="entranceId != null "> and he.entrance_id = #{entranceId}</if>
            <if test="sortOrder != null "> and he.sort_order = #{sortOrder}</if>
            and se.status = '0'
        </where>
        order by he.sort_order asc, he.create_time asc
    </select>
    
    <select id="selectSysHomepageEntranceById" parameterType="Long" resultMap="SysHomepageEntranceResult">
        <include refid="selectSysHomepageEntranceVo"/>
        where id = #{id}
    </select>

    <select id="selectSysHomepageEntranceByEntranceId" parameterType="Long" resultMap="SysHomepageEntranceResult">
        <include refid="selectSysHomepageEntranceVo"/>
        where entrance_id = #{entranceId}
    </select>

    <select id="selectMaxSortOrder" resultType="Integer">
        select IFNULL(MAX(sort_order), 0) from sys_homepage_entrance
    </select>
        
    <insert id="insertSysHomepageEntrance" parameterType="SysHomepageEntrance" useGeneratedKeys="true" keyProperty="id">
        insert into sys_homepage_entrance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entranceId != null">entrance_id,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entranceId != null">#{entranceId},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysHomepageEntrance" parameterType="SysHomepageEntrance">
        update sys_homepage_entrance
        <trim prefix="SET" suffixOverrides=",">
            <if test="entranceId != null">entrance_id = #{entranceId},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysHomepageEntranceById" parameterType="Long">
        delete from sys_homepage_entrance where id = #{id}
    </delete>

    <delete id="deleteSysHomepageEntranceByIds" parameterType="String">
        delete from sys_homepage_entrance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSysHomepageEntranceByEntranceId" parameterType="Long">
        delete from sys_homepage_entrance where entrance_id = #{entranceId}
    </delete>

    <delete id="deleteSysHomepageEntranceByEntranceIds" parameterType="String">
        delete from sys_homepage_entrance where entrance_id in
        <foreach item="entranceId" collection="array" open="(" separator="," close=")">
            #{entranceId}
        </foreach>
    </delete>

    <!-- 批量增加排序值（向前移动时使用） -->
    <update id="batchIncrementSortOrder">
        update sys_homepage_entrance
        set sort_order = sort_order + 1
        where sort_order >= #{minSortOrder}
        <if test="maxSortOrder != null">
            and sort_order &lt; #{maxSortOrder}
        </if>
        and id != #{excludeId}
    </update>

    <!-- 批量减少排序值（向后移动时使用） -->
    <update id="batchDecrementSortOrder">
        update sys_homepage_entrance
        set sort_order = sort_order - 1
        where sort_order > #{minSortOrder}
        and sort_order &lt;= #{maxSortOrder}
        and id != #{excludeId}
    </update>

    <!-- 重新编号所有记录的排序值为连续数字 -->
    <update id="resequenceAllSortOrder">
        UPDATE sys_homepage_entrance t1
        JOIN (
            SELECT id, ROW_NUMBER() OVER (ORDER BY sort_order ASC, create_time ASC) as new_sort_order
            FROM sys_homepage_entrance
        ) t2 ON t1.id = t2.id
        SET t1.sort_order = t2.new_sort_order
    </update>

</mapper>
