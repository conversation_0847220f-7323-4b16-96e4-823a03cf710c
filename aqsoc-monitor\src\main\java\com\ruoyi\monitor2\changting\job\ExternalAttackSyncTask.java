package com.ruoyi.monitor2.changting.job;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.external.enums.SyncType;
import com.ruoyi.external.service.ISyncExternalAttackService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 定时同步非凡外部攻击面数据任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component("externalAttackSyncTask")
public class ExternalAttackSyncTask {

    @Resource
    private ISyncExternalAttackService syncExternalAttackService;

    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 非凡外部攻击面数据同步
     */
    public void syncExternalAttack() {
        // 同步开关判断逻辑已迁移到SyncExternalAttackServiceImpl内部处理
        log.debug("-------- 非凡外部攻击面数据同步开始 --------");
        syncExternalAttackService.syncExternalAttackData(SyncType.ALL);
        log.debug("-------- 非凡外部攻击面数据同步结束 --------");
    }



}