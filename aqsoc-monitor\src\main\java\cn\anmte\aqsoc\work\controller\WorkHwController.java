package cn.anmte.aqsoc.work.controller;

import cn.anmte.aqsoc.common.SystemUtilService;
import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.entity.WorkHwDay;
import cn.anmte.aqsoc.work.service.IWorkHwDayService;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.external.service.IExternalAttackAppService;
import com.ruoyi.external.service.IExternalAttackDomainService;
import com.ruoyi.external.service.IExternalAttackMiniProgramService;
import com.ruoyi.external.service.IExternalAttackOfficialAccountService;
import com.ruoyi.ffsafe.api.service.IFfsafeIpfilterLogService;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.service.IMonitorBssVulnDealService;
import com.ruoyi.safe.service.*;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.service.ITblThreatenAlarmService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.fumixuan.gencode.common.constants.MsgCode;
import com.fumixuan.gencode.common.dict.DictUtil;
import com.fumixuan.gencode.common.util.PageUtil;
import com.fumixuan.gencode.common.valid.CreateGroup;
import com.fumixuan.gencode.common.valid.UpdateGroup;
import com.fumixuan.gencode.common.web.ActionResult;
import com.fumixuan.gencode.common.web.PageListVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.anmte.aqsoc.work.model.WorkHwForm;
import cn.anmte.aqsoc.work.model.WorkHwListVO;
import cn.anmte.aqsoc.work.model.WorkHwPagination;
import cn.anmte.aqsoc.work.entity.WorkHw;
import cn.anmte.aqsoc.work.service.IWorkHwService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 护网事务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Tag(name = "护网事务表", description = "护网事务表")
@RestController
@RequestMapping("/api/work-hw")
public class WorkHwController {
    @Resource
    private IWorkHwService baseService;
    @Resource
    private IWorkHwDayService workHwDayService;
    @Resource
    private SystemUtilService systemUtilService;
    @Resource
    private IFfsafeIpfilterLogService ffsafeIpfilterLogService;
    @Resource
    private ITblThreatenAlarmService tblThreatenAlarmService;
    @Resource
    private ITblServerService tblServerService;
    @Resource
    private ITblBusinessApplicationService businessApplicationService;
    @Resource
    private ITblSafetyService tblSafetyService;
    @Resource
    private ITblNetworkDevicesService tblNetworkDevicesService;
    @Resource
    private ITblFirewallNatService tblFirewallNatService;
    @Resource
    private IExternalAttackAppService externalAttackAppService;
    @Resource
    private IExternalAttackDomainService externalAttackDomainService;
    @Resource
    private IExternalAttackMiniProgramService externalAttackMiniProgramService;
    @Resource
    private IExternalAttackOfficialAccountService externalAttackOfficialAccountService;
    @Resource
    private IMonitorBssVulnDealService monitorBssVulnDealService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private IWorkHwTaskService workHwTaskService;

    @Operation(summary = "获取列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<WorkHwListVO>> list(@RequestBody WorkHwPagination pagination) {
        // 分页查询
        IPage<WorkHwListVO> page = PageUtil.toIPage(pagination);

        IPage<WorkHwListVO> result = baseService.queryPage(page, pagination);
        DictUtil.tranDict(result.getRecords());
        systemUtilService.refreshUserCache();
        DateTime nowDate = DateUtil.date();
        result.getRecords().forEach(r -> {
            LocalDateTime hwStart = r.getHwStart();
            if(hwStart.isBefore(nowDate.toLocalDateTime())){
                r.setIsStart(true);
            }
            r.setUserNames(systemUtilService.tranUserNames(r.getUserId()));
        });
        return ActionResult.success(PageUtil.toJnpfWebPage(result.getRecords(), result));
    }

    @Operation(summary = "获取数据")
    @GetMapping("/{id}")
    public ActionResult<WorkHwListVO> info(@PathVariable String id) {
        // 查询
        WorkHwListVO vo = baseService.queryById(id);
        DictUtil.tranDict(vo);
        vo.setUserNames(systemUtilService.tranUserNames(vo.getUserId()));
        List<SystemUtilService.DeptUsers> usersGroupByDept = systemUtilService.getUsersGroupByDept(vo.getSupportUsers());
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<LoginUser> onlineUsers = new ArrayList<>();
        keys.forEach(key -> {
            LoginUser user = redisCache.getCacheObject(key);
            if(user != null){
                onlineUsers.add(user);
            }
        });
        if(CollUtil.isNotEmpty(usersGroupByDept) && CollUtil.isNotEmpty(onlineUsers)){
            usersGroupByDept.forEach(item -> {
                Set<SysUser> users = item.getUsers();
                if(CollUtil.isNotEmpty(users)){
                    users.forEach(user -> {
                        //设置是否在线
                        user.setOnline(onlineUsers.stream().anyMatch(loginUser -> loginUser.getUserId().equals(user.getUserId())));
                    });
                }
            });
        }
        vo.setUsersGroupByDept(usersGroupByDept);
        return ActionResult.success(vo);
    }

    @Operation(summary = "获取用户名称")
    @GetMapping("/getUserNames")
    public ActionResult<String> getUserNames(@RequestParam String userIds) {
        // 查询
        systemUtilService.refreshUserCache();
        return ActionResult.success(systemUtilService.tranUserNames(userIds));
    }


    @Operation(summary = "保存数据")
    @PostMapping
    public ActionResult<WorkHw> save(@RequestBody @Validated(CreateGroup.class) WorkHwForm form) {
        // 保存
        WorkHw entity = baseService.updateByForm(form);

        return ActionResult.success(MsgCode.SU005.get(), entity);
    }

    @Operation(summary = "修改阶段")
    @PostMapping("/updateStages")
    public ActionResult<WorkHw> updateStages(@RequestBody WorkHwForm form) {
        // 保存
        WorkHw entity = baseService.updateStages(form);
//        WorkHwListVO vo = baseService.queryById(String.valueOf(form.getId()));
//        vo.setUserNames(systemUtilService.tranUserNames(vo.getUserId()));
//        vo.setUsersGroupByDept(systemUtilService.getUsersGroupByDept(vo.getSupportUsers()));
        return ActionResult.success(MsgCode.SU005.get(), entity);
    }

    @Operation(summary = "修改数据")
    @PutMapping
    public ActionResult<WorkHw> update(@RequestBody @Validated(UpdateGroup.class) WorkHwForm form) {
        // 更新
        WorkHw entity = baseService.updateByForm(form);

        return ActionResult.success(MsgCode.SU005.get(), entity);
    }

    @Operation(summary = "删除数据")
    @DeleteMapping("/{id}")
    public ActionResult<Void> delete(@PathVariable String id) {
        // 删除
        baseService.removeById(id);
        workHwDayService.remove(Wrappers.<WorkHwDay>lambdaQuery().eq(WorkHwDay::getHwId, id));
        return ActionResult.success(MsgCode.SU005.get());
    }

    @Operation(summary = "当前威胁告警 ")
    @GetMapping("/getAlarmNum")
    public ActionResult<Integer> getAlarmNum(@RequestParam String id) {
        // 查询
        TblThreatenAlarm query = new TblThreatenAlarm();
        query.setDataSource(8);
        int threatenAlarmNum = tblThreatenAlarmService.getThreatenAlarmNum(query);
        return ActionResult.success(threatenAlarmNum);
    }

    @Operation(summary = "已成功阻断 ")
    @GetMapping("/getStopedNum")
    public ActionResult<Integer> getStopedNum(@RequestParam String id) {
        // 查询
        return ActionResult.success(ffsafeIpfilterLogService.countIpNum());
    }

    @Operation(summary = "已处置威胁")
    @GetMapping("/getHandledAlarmNum")
    public ActionResult<Integer> getHandledAlarmNum(@RequestParam String id) {
        // 查询
        TblThreatenAlarm query = new TblThreatenAlarm();
        query.setHandleState("1");
        int threatenAlarmNum = tblThreatenAlarmService.getThreatenAlarmNum(query);
        return ActionResult.success(threatenAlarmNum);
    }

    @Operation(summary = "蜜罐告警  ")
    @GetMapping("/getHpAlarmNum")
    public ActionResult<Integer> getHpAlarmNum(@RequestParam String id) {
        // 查询
        TblThreatenAlarm query = new TblThreatenAlarm();
        query.setDataSource(7);
        int threatenAlarmNum = tblThreatenAlarmService.getThreatenAlarmNum(query);
        return ActionResult.success(threatenAlarmNum);
    }

    @Operation(summary = "获取统计数量  ")
    @GetMapping("/getCountNum")
    public ActionResult<Integer> getCountNum(@RequestParam String countId) {
        int num;
        // 查询
        TblThreatenAlarm query = new TblThreatenAlarm();
        MonitorBssVulnDeal vulnQuery = new MonitorBssVulnDeal();
        switch (countId) {
            case "server":
                num = tblServerService.countNum();
                break;
            case "application":
                num = businessApplicationService.countNum();
                break;
            case "safety":
                num = tblSafetyService.countNum();
                break;
            case "network":
                num = tblNetworkDevicesService.countNum();
                break;
            case "nat":
                num = tblFirewallNatService.countNum();
                break;
            case "app":
                num = externalAttackAppService.countNum();
                break;
            case "domain":
                num = externalAttackDomainService.countNum();
                break;
            case "program":
                num = externalAttackMiniProgramService.countNum();
                break;
            case "account":
                num = externalAttackOfficialAccountService.countNum();
                break;
            case "highRiskVulner":
                vulnQuery.setSeverity(3);
                num = monitorBssVulnDealService.countNum(vulnQuery);
                break;
            case "noHandleVulner":
                vulnQuery.setHandleState("0");
                num = monitorBssVulnDealService.countNum(vulnQuery);
                break;
            case "handledVulner":
                vulnQuery.setHandleState("1");
                num = monitorBssVulnDealService.countNum(vulnQuery);
                break;
            case "ipFilter":
                num = ffsafeIpfilterLogService.countIpNum();
                break;
            case "alarm":
                num = tblThreatenAlarmService.getThreatenAlarmNum(query);
                break;
            case "handledAlarm":
                query = new TblThreatenAlarm();
                query.setHandleState("1");
                num = tblThreatenAlarmService.getThreatenAlarmNum(query);
                break;
            case "noHandledAlarm":
                query = new TblThreatenAlarm();
                query.setHandleState("0");
                num = tblThreatenAlarmService.getThreatenAlarmNum(query);
                break;
            case "hotpot":
                query = new TblThreatenAlarm();
                query.setDataSource(7);
                num = tblThreatenAlarmService.getThreatenAlarmNum(query);
                break;
            default:
                num = 0;
        }
        return ActionResult.success(num);
    }

    @Operation(summary = "复制任务")
    @PostMapping("/copyWork")
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<WorkHw> copyWork(@RequestBody WorkHwForm form){
        //查询任务
        WorkHwTask queryHwTask = new WorkHwTask();
        queryHwTask.setWorkId(form.getId());
        List<WorkHwTask> taskList = workHwTaskService.selectWorkHwTaskList(queryHwTask);
        form.setId(null);
        WorkHw entity = baseService.updateByForm(form);
        //新增任务
        DateTime nowDate = DateUtil.date();
        taskList.forEach(task -> {
            task.setId(null);
            task.setWorkId(entity.getId());
            task.setCreateTime(nowDate);
            task.setCreateBy(SecurityUtils.getUsername());
            task.setUpdateTime(nowDate);
            task.setUpdateBy(SecurityUtils.getUsername());
            task.setStartTime(null);
            task.setEndTime(null);
            task.setManageUser(null);
            task.setCompleteTime(null);
            task.setFlowTaskId(null);
        });
        workHwTaskService.saveBatch(taskList);
        return ActionResult.success(MsgCode.SU005.get(), entity);
    }
}
