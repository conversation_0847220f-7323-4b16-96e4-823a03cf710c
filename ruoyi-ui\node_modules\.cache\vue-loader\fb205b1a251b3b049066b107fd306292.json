{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue?vue&type=template&id=95131482&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\todoItem\\todo\\work_flow.vue", "mtime": 1756710899571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}