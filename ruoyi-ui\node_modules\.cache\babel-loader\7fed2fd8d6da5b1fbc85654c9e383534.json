{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue", "mtime": 1756706031905}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_application", "require", "_applicationLink", "_interopRequireDefault", "_applicationSite", "_userSelect", "_deptSelect", "_networkSelect", "_DynamicTag", "_vendorSelect", "_dictSelect", "_utils", "_ruoyi", "_vendor", "_serverEV", "_dateEV", "_networkEV", "_safeEV", "_overViewSelect", "_overview", "_editServer", "name", "components", "EditServer", "overViewSelect", "safeEV", "networkEV", "dateEV", "serverEV", "ApplicationLink", "ApplicationSite", "UserSelect", "DeptSelect", "NetworkSelect", "DictSelect", "DynamicTag", "VendorSelect2", "dicts", "inject", "$editable", "default", "value", "props", "assetId", "type", "String", "Number", "required", "changeId", "Function", "readonly", "Boolean", "disabled", "assetList", "Array", "data", "loading", "collapseNames", "vendorsdata", "userdata", "functionStateList", "form", "businessForm", "delList", "rules", "assetName", "message", "trigger", "min", "max", "assetClass", "domainId", "domainUrl", "pattern", "manager", "userId", "deptId", "phone", "url", "ipd", "netMemo", "businessRules", "sysBusinessState", "userNums", "everydayVisitNums", "everydayActiveNums", "moduleRule", "moduleName", "moduleDesc", "gv", "getValFromObject", "deployLocation", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "managePlaceholder", "refs", "collapse", "showAddServer", "serverOptions", "currentAssociationServer", "afterInit", "selectType", "dateType", "textareaType", "radioGroupType", "mounted", "_this", "getAllServerList", "$nextTick", "reset", "init", "activated", "_this2", "watch", "handler", "newVal", "oldVal", "length", "for<PERSON>ach", "item", "index", "Object", "keys", "tempId", "generateSecureUUID", "computed", "businessAssetFields", "slice", "map", "group", "_objectSpread2", "fieldsItems", "filter", "isShow", "basicInfoAssetFields", "assetFields", "runTimeAssetFields", "dynamicRules", "ruleSets", "visibleAssetFields", "concat", "_toConsumableArray2", "<PERSON><PERSON><PERSON>", "_rules$fieldKey", "filteredRules", "rule", "push", "apply", "hasRequiredRule", "some", "fieldName", "methods", "getFieldRules", "field", "getFieldSpan", "fullSpanFields", "includes", "shouldShowField", "systemType", "isadapt", "iscipher", "getDictOptions", "dictMap", "construct", "loginType", "technical", "deploy", "state", "protectGrade", "evaluationResults", "evaluationStatus", "hwIsTrueShutDown", "dict", "getDateType", "showDapt", "adaptDate", "showCipher", "cipherDate", "selectVendor", "params", "vendors", "listVendorByApplication", "applicationId", "applicationCode", "_this3", "listAllOverview", "then", "res", "_this4", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getApplication", "response", "applicationVO", "toString", "waitForValue", "$refs", "site", "getList", "tblBusinessApplication", "tblMapperList", "i", "finally", "stop", "validateForm", "flag", "validate", "valid", "handleSave", "_this5", "Promise", "resolve", "reject", "pass1", "pass2", "link", "filterNull", "links", "l", "linkIp", "linkPort", "fun", "moduleFormKey", "moduleForm", "console", "log", "hardwareComponents", "_i", "_hardwareComponents", "ref", "component", "error", "compInstance", "isArray", "submit", "$message", "list", "serialVersionUID", "hardWareEV", "updateApplicationInfo", "$modal", "msgSuccess", "catch", "err", "addApplicationInfo", "inputToString", "val", "handleAdd", "handleDel", "moduleId", "splice", "undefined", "assetCode", "softwareVersion", "degreeImportance", "assetType", "assetTypeDesc", "assetClassDesc", "netType", "appType", "serviceGroup", "frequency", "usageCount", "userScale", "userObject", "storage", "netenv", "iskey", "datanum", "isbase", "islink", "ishare", "islog", "isplan", "function", "remark", "orgnId", "upTime", "dwid", "contactor", "netScale", "netTopo", "tags", "eids", "resetForm", "handleUserSelect", "addServerSuccess", "row", "addServerCancel", "addAssetHandle", "serverSelect", "$set", "serverId", "serverChange", "_this6", "find", "server"], "sources": ["src/views/hhlCode/component/systemDetails.vue"], "sourcesContent": ["<template>\n  <div class=\"box-container\" style=\"overflow-x: hidden;\" v-loading=\"loading\">\n    <!--系统基本信息-->\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" >\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染字段 -->\n          <template v-for=\"group in basicInfoAssetFields\">\n            <el-row\n              :gutter=\"20\"\n              type=\"flex\"\n              :key=\"group.formName\"\n              v-if=\"group.isShow\"\n              style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\" v-if=\"group.isShow\">\n                  <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <template v-if=\"group.formName === '外部连接信息'\">\n                <el-col :span=\"24\">\n                  <ApplicationLink\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    v-model=\"form.links\"/>\n                </el-col>\n              </template>\n\n              <template v-else-if=\"group.formName === '运营维护情况'\">\n                <el-col :span=\"24\">\n                  <ApplicationSite\n                    ref=\"site\"\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    :value.sync=\"form.eids\"\n                    :asset-id=\"form.assetId\"\n                    multiple/>\n                </el-col>\n              </template>\n\n              <template v-else>\n                <el-col\n                  v-for=\"field in group.fieldsItems\"\n                  :key=\"field.fieldKey\"\n                  :style=\"radioGroupType.includes(field.fieldKey) ? { display: 'flex' } : ''\"\n                  :span=\"getFieldSpan(field)\"\n                  v-if=\"shouldShowField(field)\"\n                >\n                  <!-- 其他系统备注 -->\n                  <template v-if=\"field.fieldKey === 'otherSystemNotes'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.systemType == 12\"\n                    >\n                      <el-input\n                        v-model=\"form.otherSystemNotes\"\n                        placeholder=\"请输入其它系统备注\"\n                      />\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：信创适配时间 -->\n                  <template v-else-if=\"field.fieldKey === 'adaptDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.isadapt === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.adaptDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：密码应用建设时间 -->\n                  <template v-else-if=\"field.fieldKey === 'cipherDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.iscipher === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.cipherDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：警综对接 -->\n                  <template v-else-if=\"field.fieldKey === 'islink'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"deployLocation === 'fair'\"\n                    >\n                      <el-radio-group v-model=\"form.islink\">\n                        <el-radio\n                          v-for=\"dict in dict.type.sys_yes_no\"\n                          :key=\"dict.value\"\n                          :label=\"dict.value\"\n                        >{{ dict.label }}\n                        </el-radio>\n                      </el-radio-group>\n                    </el-form-item>\n                  </template>\n                  <!-- 关联服务器字段处理 -->\n                  <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      class=\"associationServer\"\n                    >\n                      <el-select\n                        v-model=\"form.associationServer\"\n                        placeholder=\"请选择服务器\"\n                        multiple\n                        filterable\n                        clearable\n                        @change=\"serverChange\"\n                      >\n                        <el-option\n                          v-for=\"server in serverOptions\"\n                          :key=\"server.assetId\"\n                          :label=\"server.assetName\"\n                          :value=\"server.assetId\"\n                        >\n                          <span style=\"float: left\">{{ server.assetName }}</span>\n                          <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ server.ip }}</span>\n                        </el-option>\n                      </el-select>\n                      <el-button\n                        size=\"mini\"\n                        plain\n                        type=\"primary\"\n                        style=\"margin-left: 10px;\"\n                        @click=\"addAssetHandle\"\n                        :disabled=\"!$editable.value\"\n                      >\n                        新增服务器\n                      </el-button>\n                    </el-form-item>\n                  </template>\n\n                  <el-form-item\n                    v-else\n                    :label=\"field.fieldName\"\n                    :prop=\"field.fieldKey\"\n                    :rules=\"getFieldRules(field)\"\n                  >\n                    <template v-if=\"field.fieldKey === 'deptId'\">\n                      <dept-select\n                        v-model=\"form.deptId\"\n                        is-current\n                        :isAllData=\"!$editable.value\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'manager'\">\n                      <user-select\n                        v-model=\"form.manager\"\n                        :placeholder=\"managePlaceholder\"\n                        :userdata=\"userdata\"\n                        multiple\n                        @setPhone=\"handleUserSelect\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'domainId'\">\n                      <NetworkSelect v-model=\"form.domainId\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'tags'\">\n                      <Dynamic-Tag v-model=\"form.tags\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'vendor'\">\n                      <VendorSelect2\n                        v-model=\"form.vendors\"\n                        multiple\n                        placeholder=\"请选择开发合作企业\"\n                        :vendorsdata=\"vendorsdata\"\n                        :selectVendor=\"selectVendor\"\n                        :isDisabled=\"!$editable.value\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'waitingInsuranceFilingScan' || field.fieldKey === 'evaluationReport' || field.fieldKey === 'netTopo'\">\n                      <file-upload\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </template>\n\n                    <!-- 默认字段渲染 -->\n                    <template v-else>\n                      <!-- 下拉选择框 -->\n                      <el-select\n                        v-if=\"selectType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        :popper-append-to-body=\"false\"\n                        clearable\n                        filterable\n                        v-bind=\"field.props\"\n                      >\n                        <el-option\n                          v-for=\"item in getDictOptions(field.fieldKey)\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\"\n                        />\n                      </el-select>\n\n                      <!-- 日期选择器 -->\n                      <el-date-picker\n                        v-else-if=\"dateType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :type=\"getDateType(field)\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 单选按钮组 -->\n                      <el-radio-group\n                        v-else-if=\"radioGroupType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        v-bind=\"field.props\"\n                      >\n                        <el-radio\n                          v-for=\"item in dict.type.sys_yes_no\"\n                          :key=\"item.value\"\n                          :label=\"item.value\"\n                          @change=\"field.fieldKey === 'isadapt' ? showDapt : field.fieldKey === 'iscipher' ? showCipher : null \"\n                        >{{ item.label }}</el-radio>\n                      </el-radio-group>\n\n                      <!-- 多行文本输入 -->\n                      <el-input\n                        v-else-if=\"textareaType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        type=\"textarea\"\n                        :rows=\"3\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 文件上传 -->\n                      <file-upload\n                        v-else-if=\"field.type === 'file'\"\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 默认文本输入 -->\n                      <el-input\n                        v-else\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n                    </template>\n                  </el-form-item>\n                </el-col>\n              </template>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--业务基本信息-->\n    <el-form ref=\"businessForm\" :model=\"businessForm\" :rules=\"businessRules\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染业务信息字段 -->\n          <template v-for=\"group in businessAssetFields\">\n            <el-row :key=\"group.formName\" :gutter=\"20\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\" v-if=\"group.isShow\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '用户规模' && group.isShow\" src=\"@/assets/images/application/yhgm.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '业务描述' && group.isShow\" src=\"@/assets/images/application/ywms.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '功能模块' && group.isShow\" src=\"@/assets/images/application/gnmk.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <div class=\"my-form\" v-if=\"group.isShow\">\n                <!-- 用户规模特殊处理 -->\n                <template v-if=\"group.formName === '用户规模'\">\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"覆盖地域\" prop=\"coverArea\">\n                      <el-select v-model=\"businessForm.coverArea\" placeholder=\"请选择覆盖地域\">\n                        <el-option\n                          v-for=\"dict in dict.type.cover_area\"\n                          :key=\"dict.value\"\n                          :label=\"dict.label\"\n                          :value=\"dict.value\"\n                        ></el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"使用对象\" prop=\"serviceGroup\">\n                      <dict-select v-model=\"businessForm.serviceGroup\" dict-name=\"serve_group\" placeholder=\"请选择使用对象\"\n                                   multiple\n                                   clearable/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"授权用户数\" prop=\"userNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.userNums\" @input=\"val=>inputToString(val,'userNums')\"\n                        placeholder=\"输入用户数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统日均访客量\" prop=\"everydayVisitNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayVisitNums\"\n                        @input=\"val=>inputToString(val,'everydayVisitNums')\"\n                        placeholder=\"输入系统日均访客量\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统月均活跃人数\" prop=\"everydayActiveNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayActiveNums\"\n                        @input=\"val=>inputToString(val,'everydayActiveNums')\"\n                        placeholder=\"输入系统月均活跃人数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 业务描述特殊处理 -->\n                <template v-else-if=\"group.formName === '业务描述'\">\n                  <el-col :span=\"24\">\n                    <el-form-item :label=\"'总体系统业务说明'\" prop=\"sysBusinessState\">\n                      <el-input :rows=\"6\" :maxlength=\"800\" v-model=\"businessForm.sysBusinessState\" type=\"textarea\"\n                                placeholder=\"请输入系统业务说明..\"/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"上传操作手册\" prop=\"operateHandbook\">\n                      <file-upload :disUpload=\"!$editable.value\"\n                                   v-model=\"businessForm.operateHandbook\"\n                                   :limit=\"5\"\n                                   :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 功能模块特殊处理 -->\n                <template v-else-if=\"group.formName === '功能模块'\">\n                  <el-col :span=\"24\" class=\"my-form\">\n                    <div v-for=\"(item, index) in functionStateList\" :key=\"index\">\n                      <el-form ref=\"moduleForm\" :model=\"item\" :rules=\"moduleRule\" :disabled=\"!$editable.value\">\n                        <el-row :gutter=\"20\" type=\"flex\" align=\"middle\" style=\"flex-wrap: wrap;\">\n                          <el-col :span=\"4\">\n                            <el-form-item prop=\"moduleName\">\n                              <el-input v-model=\"item.moduleName\" placeholder=\"功能模块名称\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"18\">\n                            <el-form-item prop=\"moduleDesc\">\n                              <el-input :rows=\"3\" v-model=\"item.moduleDesc\" type=\"textarea\"\n                                        placeholder=\"请将该功能模块实现的功能、用途做全面的说明，并保证内容应正确、完整、一致和可验证。\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"2\">\n                            <el-button\n                              size=\"mini\"\n                              type=\"text\"\n                              icon=\"el-icon-remove\"\n                              @click=\"handleDel(item.moduleId,index)\"\n                            >删除\n                            </el-button>\n                          </el-col>\n                        </el-row>\n                      </el-form>\n                    </div>\n                    <el-row type=\"flex\" justify=\"end\" style=\"flex-wrap: wrap;\">\n                      <el-col :span=\"2\">\n                        <el-button\n                          type=\"primary\"\n                          plain\n                          icon=\"el-icon-plus\"\n                          size=\"mini\"\n                          @click=\"handleAdd\"\n                        >新增\n                        </el-button>\n                      </el-col>\n                    </el-row>\n                  </el-col>\n                </template>\n              </div>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--系统软硬件环境-->\n    <el-form ref=\"hardWareForm\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染软硬件环境字段 -->\n          <template v-for=\"group in runTimeAssetFields\">\n            <el-row :key=\"group.formName\" v-if=\"group.isShow\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <el-col :span=\"24\">\n                <!-- 根据分组名称渲染不同子组件 -->\n                <template v-if=\"group.formName === '所安装服务器环境'\">\n                  <serverEV\n                    ref=\"serverEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :data-list=\"currentAssociationServer\"\n                    @selected=\"serverSelect\"\n                    v-if=\"afterInit\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '所安装数据库环境'\">\n                  <dateEV\n                    ref=\"dateEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联网络设备'\">\n                  <networkEV\n                    ref=\"networkEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联安全设备'\">\n                  <safeEV\n                    ref=\"safeEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"/>\n                </template>\n              </el-col>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <edit-server\n      ref=\"adds\"\n      title=\"添加服务器\"\n      :edit-flag-visible.sync=\"showAddServer\"\n      @cancel=\"addServerCancel\"\n      @confirm=\"addServerSuccess\"/>\n\n  </div>\n</template>\n\n<script>\nimport {addApplicationInfo, updateApplicationInfo, getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\n\nexport default {\n  name: \"systemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"应用名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetClass: [\n          {required: true, message: \"资产分类不能为空\", trigger: \"blur\"},\n        ],\n        domainId : [\n          {required: true, message: \"主部署网络不能为空\", trigger: \"blur\"},\n        ],\n        domainUrl: [\n          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},\n          {\n            pattern: /^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*$/,\n            message: \"请输入正确的域名\",\n            trigger: ['blur', 'change']\n          },\n        ],\n        manager: [\n          {required: true, message: \"负责人不能为空\", trigger: \"blur\"},\n        ],\n        userId: [\n          // { required: true, message: \"用户ID不能为空\", trigger: \"blur\" }\n        ],\n        deptId: [\n          {required: true, message: \"单位不能为空\", trigger: \"blur\"},\n        ],\n        phone: [\n          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},\n          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的联系电话\", trigger: ['blur', 'change']},\n        ],\n        url: [\n          {required: true, message: \"登录地址不能为空\", trigger: \"blur\"},\n          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},\n          {\n            // 正则表达式用于验证 URL 格式\n            // 支持 http/https 协议，允许 IP 地址或域名，支持端口号和路径\n            //pattern: /^(https?:\\/\\/)?(([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[\\w.-]*)*$/,\n            //pattern: /^(?:#\\/?[^\\s#]+|(https?|ftp):\\/\\/([\\w.-]+|\\[[\\da-fA-F:]+\\])(:\\d+)?(\\/[^?\\s#]*)?(\\?[^\\s#]*)?(#.*)?)$/,\n            pattern: /^(https?:\\/\\/)?((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[^\\s?#]*)?(\\?[^\\s#]*)?(#.*)?$/,\n            message: \"请输入正确的登录地址\",\n            trigger: ['blur', 'change']\n          }\n        ],\n        ipd: [\n          {required: true, message: \"Ip地址段不能为空\", trigger: \"blur\"},\n          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},\n          {\n            pattern: /^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(,((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})*$/,\n            message: \"请输入正确的IP地址段，多个IP地址用逗号隔开\",\n            trigger: ['blur', 'change']\n          },\n        ],\n\n        netMemo: [\n          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},\n        ],\n      },\n      businessRules: {\n        sysBusinessState: [\n          {required: false, min: 0, max: 800, message: '拓扑图说明不能超过 800 个字符', trigger: 'blur'},\n        ],\n        userNums: [\n          {required: false, max: 12, message: '用户数量不能超过 12 个字符', trigger: 'blur'},\n          {required: false, pattern: /^[0-9]*$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayVisitNums: [\n          {min: 0, max: 12, message: '日均访问数量不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayActiveNums: [\n          {min: 0, max: 12, message: '月均活跃人数不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n      },\n      moduleRule: {\n        moduleName: [\n          {min: 0, max: 64, message: '功能模块名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        moduleDesc: [\n          {min: 0, max: 2000, message: '功能模块说明不能超过 2000 个字符', trigger: 'blur'},\n        ],\n      },\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      selectType: ['systemType','construct','loginType','technical','deploy','state','protectGrade', 'evaluationResults', 'evaluationStatus', 'hwIsTrueShutDown'],\n      dateType: ['uodTime', 'waitingInsuranceFilingTime', 'evaluationYear'],\n      textareaType: ['netMemo', 'remark'],\n      radioGroupType: ['iskey', 'isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'isOpenNetwork'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if(Object.keys(item).length > 0){\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    }\n  },\n  computed: {\n    // 业务信息动态字段\n    businessAssetFields() {\n      return (this.assetList.slice(7, 10) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    // 基本信息动态字段\n    basicInfoAssetFields() {\n      let assetFields = this.assetList.slice(0, 7);\n      return assetFields.map(group => {\n        return {\n          ...group,\n          fieldsItems: group.fieldsItems.filter(item => item.isShow)\n        };\n      })\n    },\n    // 软硬件环境动态字段\n    runTimeAssetFields() {\n      return (this.assetList.slice(10, 14) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    dynamicRules() {\n      const rules = {};\n      const ruleSets = {...this.rules, ...this.businessRules};\n      let visibleAssetFields = [...this.basicInfoAssetFields, ...this.businessAssetFields]\n      visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (ruleSets[fieldKey]) {\n            const filteredRules = ruleSets[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required)\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: ['blur', 'change']\n              });\n            }\n          }\n        })\n      })\n      return rules;\n    }\n  },\n  methods: {\n    // 字段校验规则\n    getFieldRules(field) {\n      return this.dynamicRules[field.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getFieldSpan(field) {\n      // 特殊字段占24列\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan', 'remark'];\n      if (fullSpanFields.includes(field.fieldKey)) return 24;\n      // 其他字段默认占8列\n      return 8;\n    },\n\n    shouldShowField(field) {\n      // 其他系统备注 - 只在 systemType 为 12 时显示\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType == 12;\n      }\n\n      // 信创适配时间 - 只在 isadapt 为 'Y' 时显示\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n\n      // 密码应用建设时间 - 只在 iscipher 为 'Y' 时显示\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n\n      // 警综对接 - 只在特定部署位置显示\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n\n      // 其他字段默认显示\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down'\n      };\n\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    // 获取字段的日期类型\n    getDateType(field) {\n      switch (field.fieldKey) {\n        case 'uodTime':\n          return 'date';\n          case 'evaluationYear':\n            return 'year';\n        default:\n          return 'date';\n      }\n    },\n\n    //信创适配时间显示\n    showDapt() {\n      if (this.form.isadapt === 'N') {\n        this.form.adaptDate = null;\n      }\n    },\n    //密屏应用建设时间显示\n    showCipher() {\n      if (this.form.iscipher === 'N') {\n        this.form.cipherDate = null;\n      }\n    },\n\n    selectVendor(params) {\n      if (this.form.vendors == null || this.form.vendors == '') {\n        this.vendorsdata = null;\n      } else {\n        this.vendorsdata = '1';\n      }\n      return listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        ...params\n      });\n    },\n    getAllServerList(){\n      listAllOverview({\"assetClass\":4}).then(res =>{\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          if(response.data && response.data.applicationVO && response.data.applicationVO.systemType){\n            this.form.systemType = response.data.applicationVO.systemType.toString();\n          }\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(()=>{\n          this.afterInit = true;\n        })\n      }else {\n        this.afterInit = true;\n      }\n    },\n\n    // 校验数据\n    validateForm() {\n      let flag = true;\n      this.$refs['form'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      this.$refs['businessForm'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      return flag;\n    },\n\n    /** 保存按钮操作 */\n    handleSave() {\n      return new Promise((resolve, reject) => {\n        let pass1, pass2 = true;\n        this.$refs[\"form\"].validate(valid => pass1 = valid); // 系统基本信息校验\n        this.$refs[\"businessForm\"].validate(valid => pass2 = valid); // 业务信息校验\n\n        // 处理系统基本信息\n        let link = this.filterNull(this.form.links)\n        link.forEach(l => {\n          if (!(l.linkIp && l.linkIp.length > 0)) {\n            l.linkIp = null;\n          }\n          if (!(l.linkPort && l.linkPort.length > 0)) {\n            l.linkPort = null;\n          }\n        })\n        this.form.links = link;\n\n        // 处理业务信息\n        this.businessForm.tblMapperList = this.functionStateList.filter(fun => fun.moduleName);\n        for (let moduleFormKey in this.$refs.moduleForm) {\n          if (!pass2) return reject();\n          this.$refs.moduleForm[moduleFormKey].validate(valid => pass2 = valid)\n        }\n\n        console.log(this.$refs.networkEV, this.$refs.safeEV, this.$refs.serverEV, this.$refs.dateEV, 'sssssss')\n\n        // 处理软硬件信息\n        let form = {};\n        const hardwareComponents = [\n          'serverEV',\n          'dateEV',\n          'networkEV',\n          'safeEV'\n        ];\n\n        for (let ref of hardwareComponents) {\n          const component = this.$refs[ref];\n          if (!component) {\n            console.error(`${ref} 组件未找到`);\n            continue;\n          }\n\n          const compInstance = Array.isArray(component) ? component[0] : component;\n\n          try {\n            const data = compInstance.submit();\n            if (typeof data === 'string') {\n              this.$message.error(data);\n              return;\n            }\n            form[ref] = { list: data.list, delList: data.delList };\n          } catch (error) {\n            console.error(`调用 ${ref}.submit() 失败:`, error);\n            this.$message.error(`${ref} 提交失败: ${error.message}`);\n          }\n        }\n\n\n        // 整合参数\n        let params = {\n          serialVersionUID: 0,\n          applicationVO: this.form,\n          tblBusinessApplication: this.businessForm,\n          hardWareEV: form,\n        }\n\n        if (this.assetId != null) {\n          updateApplicationInfo(params).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.init()\n            return resolve();\n          }).catch((err) => {\n            return reject(err);\n          });\n        } else {\n          addApplicationInfo(params).then(response => {\n            this.form.assetId = response.data;\n            this.changeId(response.data);\n            this.$modal.msgSuccess(\"新增成功\");\n            this.init()\n            return resolve();\n          }).catch(err => {\n            return reject(err);\n          });\n        }\n      })\n    },\n\n    inputToString(val, name) {\n      if (val)\n        this.form[name] = \"\" + val;\n    },\n\n    handleAdd() {\n      this.functionStateList.push({});\n      this.functionStateList.forEach((item, index) => {\n        item.tempId = generateSecureUUID();\n      })\n    },\n    handleDel(moduleId, index) {\n      console.log('del',this.functionStateList)\n      this.functionStateList.splice(index, 1);\n      if (!this.businessForm.delList) {\n        this.businessForm.delList = [];\n      }\n      this.businessForm.delList.push(moduleId);\n    },\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n    /** 用户选择 */\n    handleUserSelect(val) {\n      if (this.form.manager == null || this.form.manager == '') {\n        this.userdata = null;\n      } else {\n        this.userdata = '1';\n      }\n      this.form.phone = val;\n    },\n    /** 过滤空值 */\n    filterNull(value) {\n      // return value.filter((item) => JSON.stringify(item) !== '{}');\n      return value.filter((item) => Object.keys(item).length !== 0);\n    },\n    addServerSuccess(row){\n      this.getAllServerList();\n      this.showAddServer = false;\n    },\n    addServerCancel(){\n      this.showAddServer = false;\n    },\n    addAssetHandle(){\n      this.showAddServer = true;\n    },\n    serverSelect(data){\n      if(data){\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n    serverChange(val){\n      console.log(\"server:\",val)\n      if(!val || val.length<1){\n        this.currentAssociationServer = [];\n      }else {\n        this.currentAssociationServer = val.map(item => this.serverOptions.find(server => server.assetId === item));\n      }\n      return val;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.box-container {\n  padding-right: 20px;\n  .my-form {\n    width: 100%;\n    margin: 0 10px;\n  }\n}\n::v-deep .el-select-dropdown {\n  position: absolute !important;\n  left: 0 !important;\n  top: 30px!important;\n}\n::v-deep .el-date-editor {\n  width: 100%;\n}\n\n::v-deep .associationServer{\n  .el-form-item__content{\n    display: flex;\n    align-items: center;\n  }\n  .el-form-item__label{\n    display: contents;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwfA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,WAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,SAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,OAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,UAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,OAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,eAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,SAAA,GAAAlB,OAAA;AACA,IAAAmB,WAAA,GAAAjB,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAoB,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA,GACA,eACA,cACA,cACA,iBACA,kBACA,eACA,oBACA,eACA,kBACA,iBACA,cACA,eACA,sBACA,qBACA,mBACA,uBACA;EACAC,MAAA;IACAC,SAAA;MACAC,OAAA;QAAAC,KAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;MACAP,OAAA;IACA;IACAQ,QAAA,EAAAC,QAAA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAY,QAAA;MACAR,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAa,SAAA;MACAT,IAAA,EAAAU,KAAA;MACAd,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAe,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,iBAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;QACAC,OAAA;MACA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAlB,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,UAAA,GACA;UAAAvB,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,QAAA,GACA;UAAAxB,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,SAAA,GACA;UAAAJ,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UACAM,OAAA;UACAP,OAAA;UACAC,OAAA;QACA,EACA;QACAO,OAAA,GACA;UAAA3B,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,MAAA;UACA;QAAA,CACA;QACAC,MAAA,GACA;UAAA7B,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAU,KAAA,GACA;UAAAT,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,OAAA;UAAAP,OAAA;UAAAC,OAAA;QAAA,EACA;QACAW,GAAA,GACA;UAAA/B,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UACA;UACA;UACA;UACA;UACAM,OAAA;UACAP,OAAA;UACAC,OAAA;QACA,EACA;QACAY,GAAA,GACA;UAAAhC,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UACAM,OAAA;UACAP,OAAA;UACAC,OAAA;QACA,EACA;QAEAa,OAAA,GACA;UAAAZ,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAc,aAAA;QACAC,gBAAA,GACA;UAAAnC,QAAA;UAAAqB,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgB,QAAA,GACA;UAAApC,QAAA;UAAAsB,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAApB,QAAA;UAAA0B,OAAA;UAAAP,OAAA;UAAAC,OAAA;QAAA,EACA;QACAiB,iBAAA,GACA;UAAAhB,GAAA;UAAAC,GAAA;UAAAH,OAAA;QAAA,GACA;UAAAnB,QAAA;UAAA0B,OAAA;UAAAP,OAAA;UAAAC,OAAA;QAAA,EACA;QACAkB,kBAAA,GACA;UAAAjB,GAAA;UAAAC,GAAA;UAAAH,OAAA;QAAA,GACA;UAAAnB,QAAA;UAAA0B,OAAA;UAAAP,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAmB,UAAA;QACAC,UAAA,GACA;UAAAnB,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAqB,UAAA,GACA;UAAApB,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAsB,EAAA,EAAAC,uBAAA;MACAC,cAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,IAAA;QACA;QACA;QACA;QACA;MACA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,QAAA;MACAC,YAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,SAAA;MACA,IAAAF,KAAA,CAAAhB,cAAA;QACAgB,KAAA,CAAAb,YAAA;QACAa,KAAA,CAAAZ,iBAAA;MACA;MACAY,KAAA,CAAAG,KAAA;MACAH,KAAA,CAAAI,IAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACA,KAAAJ,SAAA;MACAI,MAAA,CAAAH,KAAA;MACAG,MAAA,CAAAF,IAAA;IACA;EACA;EACAG,KAAA;IACAtD,iBAAA;MACAuD,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,MAAA;UACAF,MAAA,CAAAG,OAAA,WAAAC,IAAA,EAAAC,KAAA;YACA,IAAAC,MAAA,CAAAC,IAAA,CAAAH,IAAA,EAAAF,MAAA;cACAE,IAAA,CAAAI,MAAA,OAAAC,yBAAA;YACA;UACA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,aAAA1E,SAAA,CAAA2E,KAAA,eAAAC,GAAA,WAAAC,KAAA;QAAA,WAAAC,cAAA,CAAA3F,OAAA,MAAA2F,cAAA,CAAA3F,OAAA,MACA0F,KAAA;UACAE,WAAA,EAAAF,KAAA,CAAAE,WAAA,CAAAC,MAAA,WAAAb,IAAA;YAAA,OAAAA,IAAA,CAAAc,MAAA;UAAA;QAAA;MAAA,CACA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,IAAAC,WAAA,QAAAnF,SAAA,CAAA2E,KAAA;MACA,OAAAQ,WAAA,CAAAP,GAAA,WAAAC,KAAA;QACA,WAAAC,cAAA,CAAA3F,OAAA,MAAA2F,cAAA,CAAA3F,OAAA,MACA0F,KAAA;UACAE,WAAA,EAAAF,KAAA,CAAAE,WAAA,CAAAC,MAAA,WAAAb,IAAA;YAAA,OAAAA,IAAA,CAAAc,MAAA;UAAA;QAAA;MAEA;IACA;IACA;IACAG,kBAAA,WAAAA,mBAAA;MACA,aAAApF,SAAA,CAAA2E,KAAA,gBAAAC,GAAA,WAAAC,KAAA;QAAA,WAAAC,cAAA,CAAA3F,OAAA,MAAA2F,cAAA,CAAA3F,OAAA,MACA0F,KAAA;UACAE,WAAA,EAAAF,KAAA,CAAAE,WAAA,CAAAC,MAAA,WAAAb,IAAA;YAAA,OAAAA,IAAA,CAAAc,MAAA;UAAA;QAAA;MAAA,CACA;IACA;IACAI,YAAA,WAAAA,aAAA;MACA,IAAA1E,KAAA;MACA,IAAA2E,QAAA,OAAAR,cAAA,CAAA3F,OAAA,MAAA2F,cAAA,CAAA3F,OAAA,WAAAwB,KAAA,QAAAiB,aAAA;MACA,IAAA2D,kBAAA,MAAAC,MAAA,KAAAC,mBAAA,CAAAtG,OAAA,OAAA+F,oBAAA,OAAAO,mBAAA,CAAAtG,OAAA,OAAAuF,mBAAA;MACAa,kBAAA,CAAArB,OAAA,WAAAW,KAAA;QACAA,KAAA,CAAAE,WAAA,CAAAb,OAAA,WAAAC,IAAA;UACA,IAAAuB,QAAA,GAAAvB,IAAA,CAAAuB,QAAA;UACA,KAAA/E,KAAA,CAAA+E,QAAA;YACA/E,KAAA,CAAA+E,QAAA;UACA;UAEA,IAAAJ,QAAA,CAAAI,QAAA;YAAA,IAAAC,eAAA;YACA,IAAAC,aAAA,GAAAN,QAAA,CAAAI,QAAA,EAAAV,MAAA,WAAAa,IAAA;cAAA,QAAAA,IAAA,CAAAnG,QAAA;YAAA;YACA,CAAAiG,eAAA,GAAAhF,KAAA,CAAA+E,QAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAJ,eAAA,MAAAF,mBAAA,CAAAtG,OAAA,EAAAyG,aAAA;UACA;UACA,IAAAzB,IAAA,CAAAzE,QAAA;YACA,IAAAsG,eAAA,GAAArF,KAAA,CAAA+E,QAAA,EAAAO,IAAA,WAAAJ,IAAA;cAAA,OAAAA,IAAA,CAAAnG,QAAA;YAAA;YACA,KAAAsG,eAAA;cACArF,KAAA,CAAA+E,QAAA,EAAAI,IAAA;gBACApG,QAAA;gBACAmB,OAAA,KAAA2E,MAAA,CAAArB,IAAA,CAAA+B,SAAA;gBACApF,OAAA;cACA;YACA;UACA;QACA;MACA;MACA,OAAAH,KAAA;IACA;EACA;EACAwF,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,YAAAhB,YAAA,CAAAgB,KAAA,CAAAX,QAAA;IACA;IAEA;IACAY,YAAA,WAAAA,aAAAD,KAAA;MACA;MACA,IAAAE,cAAA;MACA,IAAAA,cAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAX,QAAA;MACA;MACA;IACA;IAEAe,eAAA,WAAAA,gBAAAJ,KAAA;MACA;MACA,IAAAA,KAAA,CAAAX,QAAA;QACA,YAAAlF,IAAA,CAAAkG,UAAA;MACA;;MAEA;MACA,IAAAL,KAAA,CAAAX,QAAA;QACA,YAAAlF,IAAA,CAAAmG,OAAA;MACA;;MAEA;MACA,IAAAN,KAAA,CAAAX,QAAA;QACA,YAAAlF,IAAA,CAAAoG,QAAA;MACA;;MAEA;MACA,IAAAP,KAAA,CAAAX,QAAA;QACA,YAAApD,cAAA;MACA;;MAEA;MACA;IACA;IAEAuE,cAAA,WAAAA,eAAAnB,QAAA;MACA,IAAAoB,OAAA;QACAJ,UAAA;QACAK,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,KAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;MACA;MAEA,YAAAC,IAAA,CAAAjI,IAAA,CAAAuH,OAAA,CAAApB,QAAA;IACA;IAEA;IACA+B,WAAA,WAAAA,YAAApB,KAAA;MACA,QAAAA,KAAA,CAAAX,QAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA;IACAgC,QAAA,WAAAA,SAAA;MACA,SAAAlH,IAAA,CAAAmG,OAAA;QACA,KAAAnG,IAAA,CAAAmH,SAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAApH,IAAA,CAAAoG,QAAA;QACA,KAAApG,IAAA,CAAAqH,UAAA;MACA;IACA;IAEAC,YAAA,WAAAA,aAAAC,MAAA;MACA,SAAAvH,IAAA,CAAAwH,OAAA,iBAAAxH,IAAA,CAAAwH,OAAA;QACA,KAAA3H,WAAA;MACA;QACA,KAAAA,WAAA;MACA;MACA,WAAA4H,+BAAA,MAAAnD,cAAA,CAAA3F,OAAA;QACA+I,aAAA,OAAA5I,OAAA;QACA6I,eAAA,OAAA3H,IAAA,CAAAwH;MAAA,GACAD,MAAA,CACA;IACA;IACAxE,gBAAA,WAAAA,iBAAA;MAAA,IAAA6E,MAAA;MACA,IAAAC,yBAAA;QAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAtF,aAAA,GAAAyF,GAAA,CAAArI,IAAA;MACA;IACA;IACA,UACAwD,IAAA,WAAAA,KAAA;MAAA,IAAA8E,MAAA;MAAA,WAAAC,kBAAA,CAAAtJ,OAAA,mBAAAuJ,oBAAA,CAAAvJ,OAAA,IAAAwJ,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAvJ,OAAA,IAAA0J,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KAEAT,MAAA,CAAAlJ,OAAA;gBAAAyJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,2BAAA,EAAAV,MAAA,CAAAlJ,OAAA,EAAAgJ,IAAA,WAAAa,QAAA;gBACA;gBACAX,MAAA,CAAAhI,IAAA,CAAAlB,OAAA,GAAAkJ,MAAA,CAAAlJ,OAAA;gBACAkJ,MAAA,CAAAhI,IAAA,GAAA2I,QAAA,CAAAjJ,IAAA,CAAAkJ,aAAA;gBACA,IAAAD,QAAA,CAAAjJ,IAAA,IAAAiJ,QAAA,CAAAjJ,IAAA,CAAAkJ,aAAA,IAAAD,QAAA,CAAAjJ,IAAA,CAAAkJ,aAAA,CAAA1C,UAAA;kBACA8B,MAAA,CAAAhI,IAAA,CAAAkG,UAAA,GAAAyC,QAAA,CAAAjJ,IAAA,CAAAkJ,aAAA,CAAA1C,UAAA,CAAA2C,QAAA;gBACA;gBACA,IAAAC,mBAAA;kBAAA,WAAAjH,uBAAA,UAAAmG,MAAA,CAAAe,KAAA;gBAAA,GAAAjB,IAAA,WAAAkB,IAAA;kBACA,KAAAA,IAAA;oBACA;kBACA;kBACA,IAAAA,IAAA,YAAAvJ,KAAA;oBACAuJ,IAAA,CAAAtF,OAAA,WAAAC,IAAA;sBAAA,OAAAA,IAAA,CAAAsF,OAAA;oBAAA;kBACA;oBACAD,IAAA,CAAAC,OAAA;kBACA;gBACA;gBACA;gBACAjB,MAAA,CAAA/H,YAAA,CAAAnB,OAAA,GAAAkJ,MAAA,CAAAlJ,OAAA;gBACAkJ,MAAA,CAAA/H,YAAA,GAAA0I,QAAA,CAAAjJ,IAAA,CAAAwJ,sBAAA;gBACAlB,MAAA,CAAA/H,YAAA,CAAAqB,QAAA,GAAA0G,MAAA,CAAA/H,YAAA,CAAAqB,QAAA,YAAA0G,MAAA,CAAA/H,YAAA,CAAAqB,QAAA;gBACA0G,MAAA,CAAA/H,YAAA,CAAAsB,iBAAA,GAAAyG,MAAA,CAAA/H,YAAA,CAAAsB,iBAAA,YAAAyG,MAAA,CAAA/H,YAAA,CAAAsB,iBAAA;gBACAyG,MAAA,CAAA/H,YAAA,CAAAuB,kBAAA,GAAAwG,MAAA,CAAA/H,YAAA,CAAAuB,kBAAA,YAAAwG,MAAA,CAAA/H,YAAA,CAAAuB,kBAAA;gBACAwG,MAAA,CAAAjI,iBAAA,GAAA4I,QAAA,CAAAjJ,IAAA,CAAAwJ,sBAAA,CAAAC,aAAA;gBACA,IAAAnB,MAAA,CAAAjI,iBAAA,CAAA0D,MAAA;kBACA,IAAA2F,CAAA;kBACA,OAAAA,CAAA,OAAApB,MAAA,CAAAjI,iBAAA,CAAA0D,MAAA;oBACAuE,MAAA,CAAAjI,iBAAA,CAAAuF,IAAA;kBACA;gBACA;cACA,GAAA+D,OAAA;gBACArB,MAAA,CAAAxF,SAAA;cACA;YAAA;cAAA+F,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAT,MAAA,CAAAxF,SAAA;YAAA;YAAA;cAAA,OAAA+F,QAAA,CAAAe,IAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEA;IAEA;IACAmB,YAAA,WAAAA,aAAA;MACA,IAAAC,IAAA;MACA,KAAAT,KAAA,SAAAU,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;UACAF,IAAA;QACA;MACA;MACA,KAAAT,KAAA,iBAAAU,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;UACAF,IAAA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IAEA,aACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IAAAC,KAAA;UAAAC,KAAA;QACAL,MAAA,CAAAb,KAAA,SAAAU,QAAA,WAAAC,KAAA;UAAA,OAAAM,KAAA,GAAAN,KAAA;QAAA;QACAE,MAAA,CAAAb,KAAA,iBAAAU,QAAA,WAAAC,KAAA;UAAA,OAAAO,KAAA,GAAAP,KAAA;QAAA;;QAEA;QACA,IAAAQ,IAAA,GAAAN,MAAA,CAAAO,UAAA,CAAAP,MAAA,CAAA5J,IAAA,CAAAoK,KAAA;QACAF,IAAA,CAAAxG,OAAA,WAAA2G,CAAA;UACA,MAAAA,CAAA,CAAAC,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAA7G,MAAA;YACA4G,CAAA,CAAAC,MAAA;UACA;UACA,MAAAD,CAAA,CAAAE,QAAA,IAAAF,CAAA,CAAAE,QAAA,CAAA9G,MAAA;YACA4G,CAAA,CAAAE,QAAA;UACA;QACA;QACAX,MAAA,CAAA5J,IAAA,CAAAoK,KAAA,GAAAF,IAAA;;QAEA;QACAN,MAAA,CAAA3J,YAAA,CAAAkJ,aAAA,GAAAS,MAAA,CAAA7J,iBAAA,CAAAyE,MAAA,WAAAgG,GAAA;UAAA,OAAAA,GAAA,CAAA9I,UAAA;QAAA;QACA,SAAA+I,aAAA,IAAAb,MAAA,CAAAb,KAAA,CAAA2B,UAAA;UACA,KAAAT,KAAA,SAAAF,MAAA;UACAH,MAAA,CAAAb,KAAA,CAAA2B,UAAA,CAAAD,aAAA,EAAAhB,QAAA,WAAAC,KAAA;YAAA,OAAAO,KAAA,GAAAP,KAAA;UAAA;QACA;QAEAiB,OAAA,CAAAC,GAAA,CAAAhB,MAAA,CAAAb,KAAA,CAAAlL,SAAA,EAAA+L,MAAA,CAAAb,KAAA,CAAAnL,MAAA,EAAAgM,MAAA,CAAAb,KAAA,CAAAhL,QAAA,EAAA6L,MAAA,CAAAb,KAAA,CAAAjL,MAAA;;QAEA;QACA,IAAAkC,IAAA;QACA,IAAA6K,kBAAA,IACA,YACA,UACA,aACA,SACA;QAEA,SAAAC,EAAA,MAAAC,mBAAA,GAAAF,kBAAA,EAAAC,EAAA,GAAAC,mBAAA,CAAAtH,MAAA,EAAAqH,EAAA;UAAA,IAAAE,GAAA,GAAAD,mBAAA,CAAAD,EAAA;UACA,IAAAG,SAAA,GAAArB,MAAA,CAAAb,KAAA,CAAAiC,GAAA;UACA,KAAAC,SAAA;YACAN,OAAA,CAAAO,KAAA,IAAAlG,MAAA,CAAAgG,GAAA;YACA;UACA;UAEA,IAAAG,YAAA,GAAA1L,KAAA,CAAA2L,OAAA,CAAAH,SAAA,IAAAA,SAAA,MAAAA,SAAA;UAEA;YACA,IAAAvL,IAAA,GAAAyL,YAAA,CAAAE,MAAA;YACA,WAAA3L,IAAA;cACAkK,MAAA,CAAA0B,QAAA,CAAAJ,KAAA,CAAAxL,IAAA;cACA;YACA;YACAM,IAAA,CAAAgL,GAAA;cAAAO,IAAA,EAAA7L,IAAA,CAAA6L,IAAA;cAAArL,OAAA,EAAAR,IAAA,CAAAQ;YAAA;UACA,SAAAgL,KAAA;YACAP,OAAA,CAAAO,KAAA,iBAAAlG,MAAA,CAAAgG,GAAA,8BAAAE,KAAA;YACAtB,MAAA,CAAA0B,QAAA,CAAAJ,KAAA,IAAAlG,MAAA,CAAAgG,GAAA,iCAAAhG,MAAA,CAAAkG,KAAA,CAAA7K,OAAA;UACA;QACA;;QAGA;QACA,IAAAkH,MAAA;UACAiE,gBAAA;UACA5C,aAAA,EAAAgB,MAAA,CAAA5J,IAAA;UACAkJ,sBAAA,EAAAU,MAAA,CAAA3J,YAAA;UACAwL,UAAA,EAAAzL;QACA;QAEA,IAAA4J,MAAA,CAAA9K,OAAA;UACA,IAAA4M,kCAAA,EAAAnE,MAAA,EAAAO,IAAA,WAAAa,QAAA;YACAiB,MAAA,CAAA+B,MAAA,CAAAC,UAAA;YACAhC,MAAA,CAAA1G,IAAA;YACA,OAAA4G,OAAA;UACA,GAAA+B,KAAA,WAAAC,GAAA;YACA,OAAA/B,MAAA,CAAA+B,GAAA;UACA;QACA;UACA,IAAAC,+BAAA,EAAAxE,MAAA,EAAAO,IAAA,WAAAa,QAAA;YACAiB,MAAA,CAAA5J,IAAA,CAAAlB,OAAA,GAAA6J,QAAA,CAAAjJ,IAAA;YACAkK,MAAA,CAAAzK,QAAA,CAAAwJ,QAAA,CAAAjJ,IAAA;YACAkK,MAAA,CAAA+B,MAAA,CAAAC,UAAA;YACAhC,MAAA,CAAA1G,IAAA;YACA,OAAA4G,OAAA;UACA,GAAA+B,KAAA,WAAAC,GAAA;YACA,OAAA/B,MAAA,CAAA+B,GAAA;UACA;QACA;MACA;IACA;IAEAE,aAAA,WAAAA,cAAAC,GAAA,EAAAzO,IAAA;MACA,IAAAyO,GAAA,EACA,KAAAjM,IAAA,CAAAxC,IAAA,SAAAyO,GAAA;IACA;IAEAC,SAAA,WAAAA,UAAA;MACA,KAAAnM,iBAAA,CAAAuF,IAAA;MACA,KAAAvF,iBAAA,CAAA2D,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACAD,IAAA,CAAAI,MAAA,OAAAC,yBAAA;MACA;IACA;IACAmI,SAAA,WAAAA,UAAAC,QAAA,EAAAxI,KAAA;MACA+G,OAAA,CAAAC,GAAA,aAAA7K,iBAAA;MACA,KAAAA,iBAAA,CAAAsM,MAAA,CAAAzI,KAAA;MACA,UAAA3D,YAAA,CAAAC,OAAA;QACA,KAAAD,YAAA,CAAAC,OAAA;MACA;MACA,KAAAD,YAAA,CAAAC,OAAA,CAAAoF,IAAA,CAAA8G,QAAA;IACA;IAEA,WACAnJ,KAAA,WAAAA,MAAA;MACA,KAAAjD,IAAA;QACAlB,OAAA,EAAAwN,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAlM,SAAA,EAAAkM,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,gBAAA,EAAAH,SAAA;QACAzL,OAAA,EAAAyL,SAAA;QACA3L,SAAA,EAAA2L,SAAA;QACApG,UAAA,EAAAoG,SAAA;QACAtL,KAAA,EAAAsL,SAAA;QACAI,SAAA,EAAAJ,SAAA;QACAK,aAAA,EAAAL,SAAA;QACA7L,UAAA,EAAA6L,SAAA;QACAM,cAAA,EAAAN,SAAA;QACA/F,SAAA,EAAA+F,SAAA;QACAO,OAAA,EAAAP,SAAA;QACAQ,OAAA,EAAAR,SAAA;QACAS,YAAA,EAAAT,SAAA;QACAU,SAAA,EAAAV,SAAA;QACAW,UAAA,EAAAX,SAAA;QACAY,SAAA,EAAAZ,SAAA;QACAa,UAAA,EAAAb,SAAA;QACArL,GAAA,EAAAqL,SAAA;QACApL,GAAA,EAAAoL,SAAA;QACA7F,SAAA,EAAA6F,SAAA;QACA5F,MAAA,EAAA4F,SAAA;QACAc,OAAA,EAAAd,SAAA;QACAe,MAAA,EAAAf,SAAA;QACAgB,KAAA,EAAAhB,SAAA;QACAiB,OAAA,EAAAjB,SAAA;QACAkB,MAAA;QACAC,MAAA,EAAAnB,SAAA;QACAoB,MAAA,EAAApB,SAAA;QACAqB,KAAA,EAAArB,SAAA;QACAsB,MAAA,EAAAtB,SAAA;QACAnG,OAAA,EAAAmG,SAAA;QACAlG,QAAA,EAAAkG,SAAA;QACAnF,SAAA,EAAAmF,SAAA;QACAjF,UAAA,EAAAiF,SAAA;QACAuB,QAAA,EAAAvB,SAAA;QACAwB,MAAA,EAAAxB,SAAA;QACAxL,MAAA,EAAAwL,SAAA;QACAvL,MAAA,EAAAuL,SAAA;QACAyB,MAAA,EAAAzB,SAAA;QACA9E,OAAA,EAAA8E,SAAA;QACA0B,MAAA,EAAA1B,SAAA;QACA2B,IAAA,EAAA3B,SAAA;QACA4B,SAAA,EAAA5B,SAAA;QACA5L,QAAA,EAAA4L,SAAA;QACA6B,QAAA,EAAA7B,SAAA;QACA8B,OAAA,EAAA9B,SAAA;QACAnL,OAAA,EAAAmL,SAAA;QACA+B,IAAA;QACAjE,KAAA;QACAkE,IAAA;MACA;MACA,KAAArO,YAAA;QACAoB,gBAAA,EAAAiL,SAAA;QACAhL,QAAA,EAAAgL,SAAA;QACA/K,iBAAA,EAAA+K,SAAA;QACA9K,kBAAA,EAAA8K;MACA;MACA,KAAAiC,SAAA;MACA,KAAAA,SAAA;IACA;IACA,WACAC,gBAAA,WAAAA,iBAAAvC,GAAA;MACA,SAAAjM,IAAA,CAAAa,OAAA,iBAAAb,IAAA,CAAAa,OAAA;QACA,KAAAf,QAAA;MACA;QACA,KAAAA,QAAA;MACA;MACA,KAAAE,IAAA,CAAAgB,KAAA,GAAAiL,GAAA;IACA;IACA,WACA9B,UAAA,WAAAA,WAAAvL,KAAA;MACA;MACA,OAAAA,KAAA,CAAA4F,MAAA,WAAAb,IAAA;QAAA,OAAAE,MAAA,CAAAC,IAAA,CAAAH,IAAA,EAAAF,MAAA;MAAA;IACA;IACAgL,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAA3L,gBAAA;MACA,KAAAV,aAAA;IACA;IACAsM,eAAA,WAAAA,gBAAA;MACA,KAAAtM,aAAA;IACA;IACAuM,cAAA,WAAAA,eAAA;MACA,KAAAvM,aAAA;IACA;IACAwM,YAAA,WAAAA,aAAAnP,IAAA;MACA,IAAAA,IAAA;QACA,KAAAoP,IAAA,MAAA9O,IAAA,uBAAAN,IAAA,CAAA0E,GAAA,WAAAT,IAAA;UAAA,OAAAA,IAAA,CAAAoL,QAAA;QAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACAtE,OAAA,CAAAC,GAAA,YAAAqB,GAAA;MACA,KAAAA,GAAA,IAAAA,GAAA,CAAAxI,MAAA;QACA,KAAAlB,wBAAA;MACA;QACA,KAAAA,wBAAA,GAAA0J,GAAA,CAAA7H,GAAA,WAAAT,IAAA;UAAA,OAAAsL,MAAA,CAAA3M,aAAA,CAAA4M,IAAA,WAAAC,MAAA;YAAA,OAAAA,MAAA,CAAArQ,OAAA,KAAA6E,IAAA;UAAA;QAAA;MACA;MACA,OAAAsI,GAAA;IACA;EACA;AACA", "ignoreList": []}]}