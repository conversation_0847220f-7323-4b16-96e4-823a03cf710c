<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysLoginTipsMapper">

    <resultMap type="SysLoginTips" id="SysLoginTipsResult">
        <result property="id"    column="id"    />
        <result property="userIds"    column="user_ids"    />
        <result property="endTime"    column="end_time"    />
    </resultMap>

    <sql id="selectSysLoginTipsVo">
        select id, user_ids, end_time from sys_login_tips
    </sql>

    <select id="selectSysLoginTipsList" parameterType="SysLoginTips" resultMap="SysLoginTipsResult">
        <include refid="selectSysLoginTipsVo"/>
        <where>
            <if test="userIds != null and userIds != ''"> and find_in_set(#{userIds}, user_ids)</if>
            <if test="endTime != null"> and end_time >= #{endTime}</if>
        </where>
    </select>

    <select id="selectSysLoginTipsById" parameterType="Long" resultMap="SysLoginTipsResult">
        <include refid="selectSysLoginTipsVo"/>
        where id = #{id}
    </select>

    <select id="selectSysLoginTipsByIds" parameterType="Long" resultMap="SysLoginTipsResult">
        <include refid="selectSysLoginTipsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertSysLoginTips" parameterType="SysLoginTips">
        insert into sys_login_tips
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userIds != null">user_ids,</if>
            <if test="endTime != null">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userIds != null">#{userIds},</if>
            <if test="endTime != null">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateSysLoginTips" parameterType="SysLoginTips">
        update sys_login_tips
        <trim prefix="SET" suffixOverrides=",">
            <if test="userIds != null">user_ids = #{userIds},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysLoginTipsById" parameterType="Long">
        delete from sys_login_tips where id = #{id}
    </delete>

    <delete id="deleteSysLoginTipsByIds" parameterType="String">
        delete from sys_login_tips where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
