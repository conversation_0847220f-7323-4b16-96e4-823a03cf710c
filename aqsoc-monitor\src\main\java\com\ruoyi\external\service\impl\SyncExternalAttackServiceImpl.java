package com.ruoyi.external.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.external.domain.ExternalAttackApp;
import com.ruoyi.external.domain.ExternalAttackMiniProgram;
import com.ruoyi.external.domain.ExternalAttackOfficialAccount;
import com.ruoyi.external.enums.SyncType;
import com.ruoyi.external.mapper.ExternalAttackAppMapper;
import com.ruoyi.external.mapper.ExternalAttackMiniProgramMapper;
import com.ruoyi.external.mapper.ExternalAttackOfficialAccountMapper;
import com.ruoyi.external.model.ExternalAttackAppDTO;
import com.ruoyi.external.model.ExternalAttackMiniProgramDTO;
import com.ruoyi.external.model.ExternalAttackOfficialAccountDTO;
import com.ruoyi.external.service.IExternalAttackAppService;
import com.ruoyi.external.service.IExternalAttackMiniProgramService;
import com.ruoyi.external.service.IExternalAttackOfficialAccountService;
import com.ruoyi.external.service.ISyncExternalAttackService;
import com.ruoyi.external.util.UserDataDetector;
import com.ruoyi.ffsafe.api.domain.ExternalAttackConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.mapper.SysDeptMapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import org.apache.commons.lang3.StringUtils;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 外部攻击面同步服务实现类
 *
 * 提供外部攻击面数据的智能同步功能，支持：
 * 1. 智能同步策略：基于用户数据检测进行选择性更新，保护用户维护的新增字段
 * 2. 批量处理优化：使用固定批处理大小1000，分批处理提升性能
 * 3. 选择性更新：支持App、小程序、公众号三种实体类型的批量选择性更新
 * 4. 性能监控：提供详细的同步进度和性能统计日志
 *
 * 优化特性：
 * - 移除了复杂的配置依赖，使用固定批处理大小
 * - 实现了真正的批量选择性更新，使用MyBatis批量执行器，性能提升显著
 * - 采用SqlSession.ExecutorType.BATCH模式，一次提交多条SQL
 * - 保持事务一致性和完整的异常处理机制
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class SyncExternalAttackServiceImpl implements ISyncExternalAttackService {

    // 获取外部攻击面信息接口
    private static final String GET_ENT_DATAS = "/external/get_ent_datas";

    // 批处理大小：每批处理的实体数量
    private static final int BATCH_SIZE = 1000;

    @Autowired
    private IExternalAttackAppService externalAttackAppService;

    @Autowired
    private IExternalAttackMiniProgramService externalAttackMiniProgramService;

    @Autowired
    private IExternalAttackOfficialAccountService externalAttackOfficialAccountService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private UserDataDetector userDataDetector;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Autowired
    private ExternalAttackAppMapper externalAttackAppMapper;

    @Autowired
    private ExternalAttackMiniProgramMapper externalAttackMiniProgramMapper;

    @Autowired
    private ExternalAttackOfficialAccountMapper externalAttackOfficialAccountMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private ITblDeviceConfigService deviceConfigService;

    private Long defaultDeptId = 100L;

    @PostConstruct
    public void initDefaultDeptId() {
        SysDept defaultDept = deptMapper.selectDeptByParentId(0L);
        if (Objects.nonNull(defaultDept)) {
            this.defaultDeptId = Objects.nonNull(defaultDept.getDeptId()) ? defaultDept.getDeptId() : 100L;
        }
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncExternalAttackData(SyncType syncType) {
        log.info("开始同步外部攻击面数据，同步类型：{}", syncType);

        try {
            // 获取所有启用的设备配置
            TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
            queryDeviceConfig.setStatus(1);
            List<TblDeviceConfig> deviceConfigList = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);

            if (CollUtil.isEmpty(deviceConfigList)) {
                log.warn("未找到启用的设备配置");
                return false;
            }

            boolean allSuccess = true;

            // 循环处理每个设备配置
            for (TblDeviceConfig deviceConfig : deviceConfigList) {
                try {
                    // 获取外部攻击面配置
                    ExternalAttackConfig externalAttackConfig = deviceConfigService.getExternalAttackConfig(deviceConfig);

                    if (!externalAttackConfig.isEnable()) {
                        log.info("设备配置ID：{} 的外部攻击面同步功能已禁用", deviceConfig.getId());
                        continue;
                    }

                    String url = externalAttackConfig.getUrl();
                    String xApiKey = externalAttackConfig.getXApiKey();
                    String ids = externalAttackConfig.getIds();

                    if (StringUtils.isAnyBlank(url, xApiKey, ids)) {
                        log.warn("设备配置ID：{} 的外部攻击面配置信息不完整", deviceConfig.getId());
                        allSuccess = false;
                        continue;
                    }

                    // 获取外部攻击面数据
                    List<JSONObject> externalAttackDataByFeiFan = this.getExternalAttackDataByFeiFan(url, xApiKey, ids);
                    if (CollUtil.isEmpty(externalAttackDataByFeiFan)) {
                        log.warn("设备配置ID：{} 获取外部攻击面信息响应为空", deviceConfig.getId());
                        continue;
                    }

                    // 处理数据
                    externalAttackDataByFeiFan.forEach(item -> {
                        String itemLog = JSONUtil.escape(item.toJSONString());
                        log.info("设备配置ID：{} 获取外部攻击面信息--数据：{}", deviceConfig.getId(), itemLog);
                        JSONObject jsonObject = item.getJSONObject(ids);
                        try {
                            if (shouldProcess(syncType, SyncType.APP)) {
                                // 处理App应用程序
                                processExternalAttackData(jsonObject, "app_data", ExternalAttackAppDTO.class,
                                        ExternalAttackApp::new,
                                        (app) -> this.syncExternalAttackApp(app, deviceConfig.getId()),
                                        ExternalAttackAppDTO::getAppName,
                                        ExternalAttackAppDTO::getAppId);
                            }
                            if (shouldProcess(syncType, SyncType.MINI_PROGRAM)) {
                                // 处理微信小程序
                                processExternalAttackData(jsonObject, "applet", ExternalAttackMiniProgramDTO.class,
                                        ExternalAttackMiniProgram::new,
                                        (miniProgram) -> this.syncExternalAttackMiniProgram(miniProgram, deviceConfig.getId()),
                                        ExternalAttackMiniProgramDTO::getMiniProgramName,
                                        ExternalAttackMiniProgramDTO::getMiniProgramAppId);
                            }
                            if (shouldProcess(syncType, SyncType.OFFICIAL_ACCOUNT)) {
                                // 处理微信公众号
                                processExternalAttackData(jsonObject, "official_account", ExternalAttackOfficialAccountDTO.class,
                                        ExternalAttackOfficialAccount::new,
                                        (officialAccount) -> this.syncExternalAttackOfficialAccount(officialAccount, deviceConfig.getId()),
                                        ExternalAttackOfficialAccountDTO::getOfficialAccountName,
                                        ExternalAttackOfficialAccountDTO::getOfficialAccountAppId);
                            }
                        } catch (Exception e) {
                            log.error("设备配置ID：{} 处理外部攻击面信息失败，数据：{}，错误：{}", deviceConfig.getId(), itemLog, e.getMessage(), e);
                            throw new ServiceException("处理外部攻击面信息失败：" + e.getMessage());
                        }
                    });

                } catch (Exception e) {
                    log.error("处理设备配置ID：{} 时发生异常", deviceConfig.getId(), e);
                    allSuccess = false;
                }
            }

            return allSuccess;
        } catch (Exception e) {
            log.error("同步外部攻击面数据失败", e);
            return false;
        }
    }

    /**
     * 判断如何处理，是处理单个还是全部
     *
     * @param syncType
     * @param targetType
     * @return
     */
    private boolean shouldProcess(SyncType syncType, SyncType targetType) {
        return syncType == SyncType.ALL || syncType == targetType;
    }

    /**
     * 处理外部攻击面数据
     *
     * @param jsonObject
     * @param key
     * @param dtoClass
     * @param supplier
     * @param syncFunction
     * @param nameExtractor
     * @param idExtractor
     * @param <T>
     * @param <R>
     */
    private <T, R> void processExternalAttackData(JSONObject jsonObject, String key, Class<T> dtoClass,
                                                 Supplier<R> supplier,
                                                 SyncFunction<R> syncFunction,
                                                 Function<T, String> nameExtractor,
                                                 Function<T, String> idExtractor) {
        // 输入验证
        if (jsonObject == null || key == null || dtoClass == null || supplier == null || nameExtractor == null || idExtractor == null) {
            log.error("处理外部攻击面信息失败，参数不能为null");
            throw new ServiceException("处理外部攻击面信息失败，参数不能为null");
        }

        List<T> dtos = jsonObject.getList(key, dtoClass);
        if (CollUtil.isEmpty(dtos)) {
            return;
        }

        // 过滤和去重
        List<T> filteredDtos = filterAndDeduplicate(dtos, nameExtractor, idExtractor);

        if (CollUtil.isEmpty(filteredDtos)) {
            return;
        }

        // 转换为实体
        List<R> entities = filteredDtos.stream()
                .map(dto -> {
                    R entity = supplier.get();
                    try {
                        BeanUtils.copyProperties(dto, entity);
                    } catch (Exception e) {
                        log.error("处理外部攻击面信息失败,属性名不正确", e);
                        throw new ServiceException("处理外部攻击面信息失败,属性名不正确");
                    }
                    return entity;
                })
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(entities)) {
            syncFunction.sync(entities);
        }
    }

    /**
     * 过滤和去重逻辑
     * 1. 过滤掉名称和AppId都为空的数据
     * 2. 根据名称和AppId的组合进行去重，保留发现时间最新的数据
     * 3. 如果发现时间相同，则保留后面那条数据
     *
     * @param dtos
     * @param nameExtractor
     * @param idExtractor
     * @param <T>
     * @return
     */
    private <T> List<T> filterAndDeduplicate(List<T> dtos, Function<T, String> nameExtractor, Function<T, String> idExtractor) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .filter(dto -> StrUtil.isNotBlank(nameExtractor.apply(dto)) || StrUtil.isNotBlank(idExtractor.apply(dto)))
                .collect(Collectors.toMap(
                        dto -> StrUtil.format("{}-{}", nameExtractor.apply(dto), idExtractor.apply(dto)),
                        Function.identity(),
                        (existing, replacement) -> {
                            Date existingTime = getDiscoveryTime(existing);
                            Date replacementTime = getDiscoveryTime(replacement);
                            if (existingTime == null) {
                                return replacement;
                            } else if (replacementTime == null) {
                                return existing;
                            } else {
                                if (existingTime.equals(replacementTime)) {
                                    return replacement; // 保留后面那条数据
                                }
                                return existingTime.after(replacementTime) ? existing : replacement;
                            }
                        },
                        LinkedHashMap::new
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    /**
     * 同步App应用程序数据
     *
     * @param newEntities
     * @param deviceConfigId 设备配置ID
     * @param <T>
     */
    private <T> void syncExternalAttackApp(List<ExternalAttackApp> newEntities, Long deviceConfigId) {
        // 设置 entryType 为 1 接口同步，设置设备配置ID
        newEntities.forEach(entity -> {
            entity.setEntryType(1);
            entity.setDeviceConfigId(deviceConfigId);
        });
        syncExternalAttack(newEntities,
                externalAttackAppService::deleteByEntryTypeAndAppId,
                externalAttackAppService::insertExternalAttackApp);
    }

    /**
     * 同步微信小程序数据
     *
     * @param newEntities
     * @param deviceConfigId 设备配置ID
     * @param <T>
     */
    private <T> void syncExternalAttackMiniProgram(List<ExternalAttackMiniProgram> newEntities, Long deviceConfigId) {
        // 设置 entryType 为 1 接口同步，设置设备配置ID
        newEntities.forEach(entity -> {
            entity.setEntryType(1);
            entity.setDeviceConfigId(deviceConfigId);
        });
        syncExternalAttack(newEntities,
                externalAttackMiniProgramService::deleteByEntryTypeAndAppId,
                externalAttackMiniProgramService::insertExternalAttackMiniProgram);
    }

    /**
     * 同步微信公众号数据
     *
     * @param newEntities
     * @param deviceConfigId 设备配置ID
     * @param <T>
     */
    private <T> void syncExternalAttackOfficialAccount(List<ExternalAttackOfficialAccount> newEntities, Long deviceConfigId) {
        // 设置 entryType 为 1 接口同步，设置设备配置ID
        newEntities.forEach(entity -> {
            entity.setEntryType(1);
            entity.setDeviceConfigId(deviceConfigId);
        });
        syncExternalAttack(newEntities,
                externalAttackOfficialAccountService::deleteByEntryTypeAndAppId,
                externalAttackOfficialAccountService::insertExternalAttackOfficialAccount);
    }

    /**
     * 同步数据到数据库表中，使用智能同步策略
     * 智能同步策略：检测用户数据并进行选择性更新，保护用户维护的新增字段
     *
     * @param newEntities
     * @param deleteFunction
     * @param insertFunction
     * @param <T>
     */
    private <T> void syncExternalAttack(List<T> newEntities,
                                       DeleteFunction<T> deleteFunction,
                                       Function<T, Integer> insertFunction) {
        log.info("使用智能同步策略，实体数量: {}", newEntities.size());
        syncExternalAttackIntelligent(newEntities, deleteFunction, insertFunction);
    }

    /**
     * 传统同步策略：删除旧数据并插入新数据
     *
     * @param newEntities
     * @param deleteFunction
     * @param insertFunction
     * @param <T>
     */
    private <T> void syncExternalAttackTraditional(List<T> newEntities,
                                                   DeleteFunction<T> deleteFunction,
                                                   Function<T, Integer> insertFunction) {
        if (CollUtil.isEmpty(newEntities)) {
            return;
        }

        // 提取所有的名称和AppId组合
        Set<String> uniqueKeys = newEntities.stream()
                .map(entity -> StrUtil.format("{}-{}", getNameField(entity), getIdField(entity)))
                .collect(Collectors.toSet());

        // 获取deviceConfigId（所有实体的deviceConfigId应该相同）
        Long deviceConfigId = null;
        if (!newEntities.isEmpty()) {
            Object firstEntity = newEntities.get(0);
            if (firstEntity instanceof ExternalAttackApp) {
                deviceConfigId = ((ExternalAttackApp) firstEntity).getDeviceConfigId();
            } else if (firstEntity instanceof ExternalAttackMiniProgram) {
                deviceConfigId = ((ExternalAttackMiniProgram) firstEntity).getDeviceConfigId();
            } else if (firstEntity instanceof ExternalAttackOfficialAccount) {
                deviceConfigId = ((ExternalAttackOfficialAccount) firstEntity).getDeviceConfigId();
            }
        }

        // 删除符合条件的旧数据
        deleteFunction.delete(uniqueKeys, deviceConfigId);

        // 批量插入新数据 - 使用真正的批量插入优化性能
        if (!newEntities.isEmpty()) {
            Object firstEntity = newEntities.get(0);
            if (firstEntity instanceof ExternalAttackApp) {
                batchInsertExternalAttackApps((List<ExternalAttackApp>) newEntities);
            } else if (firstEntity instanceof ExternalAttackMiniProgram) {
                batchInsertExternalAttackMiniPrograms((List<ExternalAttackMiniProgram>) newEntities);
            } else if (firstEntity instanceof ExternalAttackOfficialAccount) {
                batchInsertExternalAttackOfficialAccounts((List<ExternalAttackOfficialAccount>) newEntities);
            } else {
                // 兼容性保障：如果类型不匹配，回退到原有实现
                log.warn("未知实体类型，回退到单条插入模式: {}", firstEntity.getClass().getSimpleName());
                newEntities.forEach(insertFunction::apply);
            }
        }
        log.info("批量插入完成，实体数量: {}", newEntities.size());
    }

    /**
     * 智能同步策略：基于用户数据检测的条件更新
     * 使用固定批处理大小1000进行分批处理，确保性能和稳定性
     *
     * @param newEntities
     * @param deleteFunction
     * @param insertFunction
     * @param <T>
     */
    private <T> void syncExternalAttackIntelligent(List<T> newEntities,
                                                   DeleteFunction<T> deleteFunction,
                                                   Function<T, Integer> insertFunction) {
        if (CollUtil.isEmpty(newEntities)) {
            return;
        }

        log.info("开始智能同步，实体数量: {}", newEntities.size());

        int batchSize = BATCH_SIZE;
        int totalEntities = newEntities.size();
        int processedEntities = 0;
        int selectiveUpdatedEntities = 0;  // 执行选择性更新的实体数量
        int fullReplacedEntities = 0;      // 执行完全替换的实体数量

        // 收集需要选择性更新的实体，按类型分组以便后续批量处理
        List<ExternalAttackApp> appUpdateList = new ArrayList<>();
        List<ExternalAttackMiniProgram> miniProgramUpdateList = new ArrayList<>();
        List<ExternalAttackOfficialAccount> officialAccountUpdateList = new ArrayList<>();

        // 收集无用户数据的实体，稍后批量执行传统同步策略
        List<T> noUserDataEntities = new ArrayList<>();

        // 分批处理，避免一次性处理过多数据
        for (int i = 0; i < totalEntities; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalEntities);
            List<T> batchEntities = newEntities.subList(i, endIndex);

            // 批量检测用户数据
            Map<Object, Boolean> userDataResults = userDataDetector.batchDetectUserData(
                new ArrayList<>(batchEntities));

            for (T entity : batchEntities) {
                try {
                    Boolean hasUserData = userDataResults.get(entity);
                    if (hasUserData != null && hasUserData) {
                        // 有用户数据，收集到对应列表中，稍后批量执行选择性更新
                        log.debug("实体包含用户数据，收集进行批量选择性更新: {}", getEntityIdentifier(entity));
                        if (entity instanceof ExternalAttackApp) {
                            appUpdateList.add((ExternalAttackApp) entity);
                        } else if (entity instanceof ExternalAttackMiniProgram) {
                            miniProgramUpdateList.add((ExternalAttackMiniProgram) entity);
                        } else if (entity instanceof ExternalAttackOfficialAccount) {
                            officialAccountUpdateList.add((ExternalAttackOfficialAccount) entity);
                        }
                        selectiveUpdatedEntities++;
                    } else {
                        // 无用户数据，收集到列表中，稍后批量执行传统同步策略
                        log.debug("无用户数据，收集进行批量传统同步: {}", getEntityIdentifier(entity));
                        noUserDataEntities.add(entity);
                        fullReplacedEntities++;
                    }
                    processedEntities++;
                } catch (ServiceException e) {
                    log.error("数据验证失败: {}, 错误: {}", getEntityIdentifier(entity), e.getMessage());
                    // ServiceException通常是数据验证问题，跳过该实体但不影响整体事务
                } catch (Exception e) {
                    log.error("插入操作失败: {}, 错误: {}", getEntityIdentifier(entity), e.getMessage(), e);
                    // 对于其他异常，记录详细信息用于问题排查
                }
            }
        }

        // 批量执行传统同步策略
        if (!noUserDataEntities.isEmpty()) {
            log.info("开始批量传统同步策略，实体数量: {}", noUserDataEntities.size());
            syncExternalAttackTraditional(noUserDataEntities, deleteFunction, insertFunction);
        }

        // 批量执行选择性更新，提升性能
        long batchUpdateStartTime = System.currentTimeMillis();
        performBatchSelectiveUpdate(appUpdateList, miniProgramUpdateList, officialAccountUpdateList);
        long batchUpdateEndTime = System.currentTimeMillis();

        log.info("智能同步完成 - 总数: {}, 已处理: {}, 选择性更新: {}, 完全替换: {}, 批量更新耗时: {}ms",
            totalEntities, processedEntities, selectiveUpdatedEntities, fullReplacedEntities,
            (batchUpdateEndTime - batchUpdateStartTime));
    }


    /**
     * 批量执行选择性更新，使用MyBatis批量执行器真正实现批量更新
     * 使用SqlSession的BATCH模式，一次提交多条SQL，显著提升性能
     *
     * @param appUpdateList 需要更新的App实体列表
     * @param miniProgramUpdateList 需要更新的小程序实体列表
     * @param officialAccountUpdateList 需要更新的公众号实体列表
     */
    private void performBatchSelectiveUpdate(List<ExternalAttackApp> appUpdateList,
                                           List<ExternalAttackMiniProgram> miniProgramUpdateList,
                                           List<ExternalAttackOfficialAccount> officialAccountUpdateList) {

        // 批量处理App实体
        if (!appUpdateList.isEmpty()) {
            log.info("开始真正批量选择性更新App实体，总数: {}", appUpdateList.size());
            int updateCount = executeBatchSelectiveUpdate(appUpdateList,
                "com.ruoyi.external.mapper.ExternalAttackAppMapper.selectiveUpdateByCondition");
            log.info("App实体批量选择性更新完成，成功更新: {} 条", updateCount);
        }

        // 批量处理小程序实体
        if (!miniProgramUpdateList.isEmpty()) {
            log.info("开始真正批量选择性更新小程序实体，总数: {}", miniProgramUpdateList.size());
            int updateCount = executeBatchSelectiveUpdate(miniProgramUpdateList,
                "com.ruoyi.external.mapper.ExternalAttackMiniProgramMapper.selectiveUpdateByCondition");
            log.info("小程序实体批量选择性更新完成，成功更新: {} 条", updateCount);
        }

        // 批量处理公众号实体
        if (!officialAccountUpdateList.isEmpty()) {
            log.info("开始真正批量选择性更新公众号实体，总数: {}", officialAccountUpdateList.size());
            int updateCount = executeBatchSelectiveUpdate(officialAccountUpdateList,
                "com.ruoyi.external.mapper.ExternalAttackOfficialAccountMapper.selectiveUpdateByCondition");
            log.info("公众号实体批量选择性更新完成，成功更新: {} 条", updateCount);
        }
    }

    /**
     * 使用MyBatis批量执行器执行真正的批量选择性更新
     *
     * @param entityList 实体列表
     * @param mapperMethod Mapper方法的完整路径
     * @param <T> 实体类型
     * @return 成功更新的记录数
     */
    private <T> int executeBatchSelectiveUpdate(List<T> entityList, String mapperMethod) {
        if (CollUtil.isEmpty(entityList)) {
            return 0;
        }

        SqlSession batchSqlSession = sqlSessionTemplate.getSqlSessionFactory()
            .openSession(ExecutorType.BATCH, false);

        int successCount = 0;
        try {
            // 设置更新时间
            for (T entity : entityList) {
                setUpdateTime(entity);
                batchSqlSession.update(mapperMethod, entity);
            }

            // 批量提交
            batchSqlSession.commit();
            successCount = entityList.size(); // 批量执行成功，认为所有记录都更新成功

            log.debug("批量选择性更新成功，处理记录数: {}", successCount);

        } catch (Exception e) {
            log.error("批量选择性更新失败，回滚事务: {}", e.getMessage(), e);
            batchSqlSession.rollback();
            throw new ServiceException("批量选择性更新失败: " + e.getMessage());
        } finally {
            batchSqlSession.close();
        }

        return successCount;
    }

    /**
     * 设置实体的更新时间
     *
     * @param entity 实体对象
     * @param <T> 实体类型
     */
    private <T> void setUpdateTime(T entity) {
        try {
            if (entity instanceof ExternalAttackApp) {
                ((ExternalAttackApp) entity).setUpdateTime(new Date());
            } else if (entity instanceof ExternalAttackMiniProgram) {
                ((ExternalAttackMiniProgram) entity).setUpdateTime(new Date());
            } else if (entity instanceof ExternalAttackOfficialAccount) {
                ((ExternalAttackOfficialAccount) entity).setUpdateTime(new Date());
            }
        } catch (Exception e) {
            log.warn("设置实体更新时间失败: {}", e.getMessage());
        }
    }

    /**
     * 获取实体的标识符，用于日志记录
     *
     * @param entity 实体对象
     * @return 实体标识符字符串
     */
    private <T> String getEntityIdentifier(T entity) {
        if (entity == null) {
            return "null";
        }
        return StrUtil.format("{}[name={}, id={}]",
            entity.getClass().getSimpleName(),
            getNameField(entity),
            getIdField(entity));
    }

    /**
     * 获取实体的标识唯一的ID字段值
     *
     * @param entity
     * @param <T>
     * @return
     */
    private <T> String getIdField(T entity) {
        if (entity instanceof ExternalAttackApp) {
            return ((ExternalAttackApp) entity).getAppId();
        } else if (entity instanceof ExternalAttackMiniProgram) {
            return ((ExternalAttackMiniProgram) entity).getMiniProgramAppId();
        } else if (entity instanceof ExternalAttackOfficialAccount) {
            return ((ExternalAttackOfficialAccount) entity).getOfficialAccountAppId();
        }
        return null;
    }

    /**
     * 获取实体的发现时间
     *
     * @param dto
     * @param <R>
     * @return
     */
    private <R> Date getDiscoveryTime(R dto) {
        if (dto instanceof ExternalAttackAppDTO) {
            return ((ExternalAttackAppDTO) dto).getDiscoveryTime();
        } else if (dto instanceof ExternalAttackMiniProgramDTO) {
            return ((ExternalAttackMiniProgramDTO) dto).getDiscoveryTime();
        } else if (dto instanceof ExternalAttackOfficialAccountDTO) {
            return ((ExternalAttackOfficialAccountDTO) dto).getDiscoveryTime();
        }
        return null;
    }

    /**
     * 获取实体的名称字段值
     *
     * @param entity
     * @return
     * @param <T>
     */
    private <T> String getNameField(T entity) {
        if (entity instanceof ExternalAttackApp) {
            return ((ExternalAttackApp) entity).getAppName();
        } else if (entity instanceof ExternalAttackMiniProgram) {
            return ((ExternalAttackMiniProgram) entity).getMiniProgramName();
        } else if (entity instanceof ExternalAttackOfficialAccount) {
            return ((ExternalAttackOfficialAccount) entity).getOfficialAccountName();
        }
        return null;
    }

    /**
     * 获取外部攻击面信息数据
     *
     * @param url API地址
     * @param xApiKey API密钥
     * @param ids 企业ID
     * @return
     */
    private List<JSONObject> getExternalAttackDataByFeiFan(String url, String xApiKey, String ids) {
        List<JSONObject> jsonObjectList = Collections.emptyList();
        String requestUrl = StrUtil.format("{}{}?ids={}", url, GET_ENT_DATAS, ids);
        try (HttpResponse response = HttpUtil.createGet(requestUrl).header("x-api-key", xApiKey).execute()) {
            log.info("非凡-----获取企业外部资产信息列表--响应：{}", response.body());
            JSONObject res = JSONObject.parseObject(response.body());
            if (res.getBoolean("status")) {
                jsonObjectList = res.getList("data", JSONObject.class);
                jsonObjectList.forEach(item -> {
                    log.info("非凡-----获取企业外部资产信息列表--数据：{}", item.toJSONString());
                });
            }
        } catch (Exception e) {
            log.error("非凡-----获取企业外部资产信息列表--异常：{}", e.getMessage());
        }
        return jsonObjectList;
    }

    /**
     * 批量插入 APP 应用程序实体
     *
     * @param entities 实体列表
     */
    private void batchInsertExternalAttackApps(List<ExternalAttackApp> entities) {
        if (CollUtil.isEmpty(entities)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalCount = entities.size();
        int successCount = 0;

        // 实体预处理：设置必要字段
        for (ExternalAttackApp entity : entities) {
            entity.setCreateTime(DateUtils.getNowDate());
            if (Objects.isNull(entity.getDeptId())) {
                entity.setDeptId(defaultDeptId);
            }
        }

        // 分批处理，每批1000条
        List<List<ExternalAttackApp>> batches = CollUtil.split(entities, BATCH_SIZE);
        log.info("开始批量插入 APP 实体，总数: {}, 分批数: {}", totalCount, batches.size());

        for (int i = 0; i < batches.size(); i++) {
            List<ExternalAttackApp> batch = batches.get(i);
            long batchStartTime = System.currentTimeMillis();

            try {
                int batchResult = externalAttackAppMapper.batchInsertExternalAttackApp(batch);
                successCount += batchResult;
                long batchEndTime = System.currentTimeMillis();
                log.debug("第 {} 批 APP 插入完成，本批数量: {}, 耗时: {}ms", i + 1, batch.size(), (batchEndTime - batchStartTime));
            } catch (Exception e) {
                log.error("第 {} 批 APP 插入失败，本批数量: {}, 错误: {}", i + 1, batch.size(), e.getMessage(), e);
                throw new ServiceException("批量插入 APP 失败：" + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("APP 批量插入完成，总耗时: {}ms, 成功插入: {} 条", (endTime - startTime), successCount);
    }

    /**
     * 批量插入微信小程序实体
     *
     * @param entities 实体列表
     */
    private void batchInsertExternalAttackMiniPrograms(List<ExternalAttackMiniProgram> entities) {
        if (CollUtil.isEmpty(entities)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalCount = entities.size();
        int successCount = 0;

        // 实体预处理：设置必要字段
        for (ExternalAttackMiniProgram entity : entities) {
            entity.setCreateTime(DateUtils.getNowDate());
            if (Objects.isNull(entity.getDeptId())) {
                entity.setDeptId(defaultDeptId);
            }
        }

        // 分批处理，每批1000条
        List<List<ExternalAttackMiniProgram>> batches = CollUtil.split(entities, BATCH_SIZE);
        log.info("开始批量插入微信小程序实体，总数: {}, 分批数: {}", totalCount, batches.size());

        for (int i = 0; i < batches.size(); i++) {
            List<ExternalAttackMiniProgram> batch = batches.get(i);
            long batchStartTime = System.currentTimeMillis();

            try {
                int batchResult = externalAttackMiniProgramMapper.batchInsertExternalAttackMiniProgram(batch);
                successCount += batchResult;
                long batchEndTime = System.currentTimeMillis();
                log.debug("第 {} 批微信小程序插入完成，本批数量: {}, 耗时: {}ms", i + 1, batch.size(), (batchEndTime - batchStartTime));
            } catch (Exception e) {
                log.error("第 {} 批微信小程序插入失败，本批数量: {}, 错误: {}", i + 1, batch.size(), e.getMessage(), e);
                throw new ServiceException("批量插入微信小程序失败：" + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("微信小程序批量插入完成，总耗时: {}ms, 成功插入: {} 条", (endTime - startTime), successCount);
    }

    /**
     * 批量插入微信公众号实体
     *
     * @param entities 实体列表
     */
    private void batchInsertExternalAttackOfficialAccounts(List<ExternalAttackOfficialAccount> entities) {
        if (CollUtil.isEmpty(entities)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalCount = entities.size();
        int successCount = 0;

        // 实体预处理：设置必要字段
        for (ExternalAttackOfficialAccount entity : entities) {
            entity.setCreateTime(DateUtils.getNowDate());
            if (Objects.isNull(entity.getDeptId())) {
                entity.setDeptId(defaultDeptId);
            }
        }

        // 分批处理，每批1000条
        List<List<ExternalAttackOfficialAccount>> batches = CollUtil.split(entities, BATCH_SIZE);
        log.info("开始批量插入微信公众号实体，总数: {}, 分批数: {}", totalCount, batches.size());

        for (int i = 0; i < batches.size(); i++) {
            List<ExternalAttackOfficialAccount> batch = batches.get(i);
            long batchStartTime = System.currentTimeMillis();

            try {
                int batchResult = externalAttackOfficialAccountMapper.batchInsertExternalAttackOfficialAccount(batch);
                successCount += batchResult;
                long batchEndTime = System.currentTimeMillis();
                log.debug("第 {} 批微信公众号插入完成，本批数量: {}, 耗时: {}ms", i + 1, batch.size(), (batchEndTime - batchStartTime));
            } catch (Exception e) {
                log.error("第 {} 批微信公众号插入失败，本批数量: {}, 错误: {}", i + 1, batch.size(), e.getMessage(), e);
                throw new ServiceException("批量插入微信公众号失败：" + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("微信公众号批量插入完成，总耗时: {}ms, 成功插入: {} 条", (endTime - startTime), successCount);
    }

    /**
     * 同步函数接口
     *
     * @param <T>
     */
    @FunctionalInterface
    private interface SyncFunction<T> {
        void sync(List<T> entities);
    }

    /**
     * 删除函数接口
     *
     * @param <T>
     */
    @FunctionalInterface
    private interface DeleteFunction<T> {
        void delete(Set<String> uniqueKeys, Long deviceConfigId);
    }
}
