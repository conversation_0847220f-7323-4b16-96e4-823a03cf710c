package com.ruoyi.external.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackMiniProgram;
import org.apache.ibatis.annotations.Param;

/**
 * 微信小程序Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface ExternalAttackMiniProgramMapper
{
    /**
     * 查询微信小程序
     *
     * @param id 微信小程序主键
     * @return 微信小程序
     */
    public ExternalAttackMiniProgram selectExternalAttackMiniProgramById(Long id);

    /**
     * 批量查询微信小程序
     *
     * @param ids 微信小程序主键集合
     * @return 微信小程序集合
     */
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramByIds(Long[] ids);

    /**
     * 查询微信小程序列表
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 微信小程序集合
     */
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramList(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 新增微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    public int insertExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 批量插入微信小程序
     *
     * @param entityList 微信小程序实体列表
     * @return 插入成功的记录数
     */
    int batchInsertExternalAttackMiniProgram(@Param("entityList") List<ExternalAttackMiniProgram> entityList);

    /**
     * 修改微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    public int updateExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 删除微信小程序
     *
     * @param id 微信小程序主键
     * @return 结果
     */
    public int deleteExternalAttackMiniProgramById(Long id);

    /**
     * 批量删除微信小程序
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExternalAttackMiniProgramByIds(Long[] ids);

    /**
     * 检查 微信小程序 是否已存在（排除指定 id）
     * @param miniProgramAppId
     * @param id
     */
    int checkMiniProgramExistence(@Param("miniProgramAppId") String miniProgramAppId, @Param("miniProgramName") String miniProgramName, @Param("id") Long id);

    List<ExternalAttackMiniProgram> selectByAppIds(@Param("appIds")List<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(@Param("uniqueKeys") Set<String> uniqueKeys, @Param("deviceConfigId") Long deviceConfigId);

    /**
     * 选择性更新微信小程序：只更新第三方接口返回的原有字段，保护用户维护的新增字段
     *
     * @param externalAttackMiniProgram 微信小程序实体，包含要更新的数据
     * @return 更新的记录数
     */
    int selectiveUpdateByCondition(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 根据唯一键查询现有记录，用于智能同步时的数据检测
     *
     * @param uniqueKey 唯一键，格式为 "miniProgramName-miniProgramAppId"
     * @return 微信小程序实体，如果不存在则返回null
     */
    ExternalAttackMiniProgram selectByUniqueKey(@Param("uniqueKey") String uniqueKey);

    /**
     * 批量查询现有记录，用于智能同步时的批量数据检测
     *
     * @param uniqueKeys 唯一键集合
     * @return 微信小程序实体列表
     */
    List<ExternalAttackMiniProgram> selectByUniqueKeys(@Param("uniqueKeys") Set<String> uniqueKeys);
}
