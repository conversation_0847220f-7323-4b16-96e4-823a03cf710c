<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysNoticeReadStatusMapper">

    <resultMap type="SysNoticeReadStatus" id="SysNoticeReadStatusResult">
        <result property="id"          column="id"          />
        <result property="noticeId"    column="notice_id"   />
        <result property="userId"      column="user_id"     />
        <result property="readStatus"  column="read_status" />
        <result property="readTime"    column="read_time"   />
        <result property="createTime"  column="create_time" />
        <result property="updateTime"  column="update_time" />
    </resultMap>

    <resultMap type="com.ruoyi.system.domain.dto.NoticeReadStatistics" id="NoticeReadStatisticsResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="totalCount"  column="total_count"  />
        <result property="readCount"   column="read_count"   />
        <result property="unreadCount" column="unread_count" />
    </resultMap>

    <sql id="selectSysNoticeReadStatusVo">
        select id, notice_id, user_id, read_status, read_time, create_time, update_time
        from sys_notice_read_status
    </sql>

    <select id="selectSysNoticeReadStatusList" parameterType="SysNoticeReadStatus" resultMap="SysNoticeReadStatusResult">
        <include refid="selectSysNoticeReadStatusVo"/>
        <where>
            <if test="noticeId != null">
                AND notice_id = #{noticeId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="readStatus != null and readStatus != ''">
                AND read_status = #{readStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectSysNoticeReadStatusById" parameterType="Long" resultMap="SysNoticeReadStatusResult">
        <include refid="selectSysNoticeReadStatusVo"/>
        where id = #{id}
    </select>

    <select id="selectSysNoticeReadStatusByNoticeAndUser" resultMap="SysNoticeReadStatusResult">
        <include refid="selectSysNoticeReadStatusVo"/>
        where notice_id = #{noticeId} and user_id = #{userId}
    </select>

    <select id="selectNoticeReadStatistics" parameterType="Long" resultMap="NoticeReadStatisticsResult">
        SELECT 
            #{noticeId} as notice_id,
            COUNT(*) as total_count,
            SUM(CASE WHEN read_status = '1' THEN 1 ELSE 0 END) as read_count,
            SUM(CASE WHEN read_status = '0' THEN 1 ELSE 0 END) as unread_count
        FROM sys_notice_read_status 
        WHERE notice_id = #{noticeId}
    </select>

    <insert id="insertSysNoticeReadStatus" parameterType="SysNoticeReadStatus" useGeneratedKeys="true" keyProperty="id">
        insert into sys_notice_read_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="readStatus != null and readStatus != ''">read_status,</if>
            <if test="readTime != null">read_time,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="readStatus != null and readStatus != ''">#{readStatus},</if>
            <if test="readTime != null">#{readTime},</if>
            sysdate()
        </trim>
    </insert>

    <insert id="batchInsertSysNoticeReadStatus" parameterType="java.util.List">
        insert into sys_notice_read_status (notice_id, user_id, read_status, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.noticeId}, #{item.userId}, #{item.readStatus}, sysdate())
        </foreach>
    </insert>

    <update id="updateSysNoticeReadStatus" parameterType="SysNoticeReadStatus">
        update sys_notice_read_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="readStatus != null and readStatus != ''">read_status = #{readStatus},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="markNoticeAsRead">
        update sys_notice_read_status
        set read_status = '1', read_time = sysdate(), update_time = sysdate()
        where notice_id = #{noticeId} and user_id = #{userId}
    </update>

    <update id="batchMarkNoticeAsRead">
        update sys_notice_read_status
        set read_status = '1', read_time = sysdate(), update_time = sysdate()
        where user_id = #{userId} and notice_id in
        <foreach collection="noticeIds" item="noticeId" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </update>

    <insert id="batchInsertNoticeReadStatusForRead" parameterType="java.util.List">
        insert into sys_notice_read_status (notice_id, user_id, read_status, read_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.noticeId}, #{item.userId}, '1', sysdate(), sysdate())
        </foreach>
    </insert>

    <delete id="deleteSysNoticeReadStatusById" parameterType="Long">
        delete from sys_notice_read_status where id = #{id}
    </delete>

    <delete id="deleteSysNoticeReadStatusByIds" parameterType="String">
        delete from sys_notice_read_status where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSysNoticeReadStatusByNoticeId" parameterType="Long">
        delete from sys_notice_read_status where notice_id = #{noticeId}
    </delete>

</mapper>
