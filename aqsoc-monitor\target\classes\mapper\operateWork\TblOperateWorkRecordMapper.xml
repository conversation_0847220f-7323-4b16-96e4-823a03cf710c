<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblOperateWorkRecordMapper">

    <resultMap type="com.ruoyi.monitor2.domain.TblOperateWorkRecord" id="TblOperateWorkRecordResult">
        <result property="id"    column="id"    />
        <result property="workId" column="work_id" />
        <result property="workClass"    column="work_class"    />
        <result property="workName"    column="work_name"    />
        <result property="workType"    column="work_type"    />
        <result property="workTitle"    column="work_title"    />
        <result property="deptId"    column="dept_id" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="fFlowtaskid"    column="f_flowtaskid"    />
        <result property="fFlowid"    column="f_flowid"    />
        <result property="fFlowstate" column="f_flowstate" />
        <result property="fNodeName" column="f_node_name" />
        <result property="createByName" column="create_by_name" />
        <result property="fHandleUser" column="f_handle_user" />
        <result property="nodeProperties" column="node_properties" />
        <result property="updateTime" column="update_time" />
        <result property="files" column="files" />
    </resultMap>

    <sql id="selectTblOperateWorkRecordVo">
        select t1.id,t1.work_id, t1.work_class, t1.work_name, t1.work_type, t1.work_title,t1.dept_id, t1.create_by, t1.create_time,
               t1.f_flowtaskid, t1.f_flowid, t1.f_flowstate,t1.f_node_name,t1.f_handle_user,t1.node_properties,t1.update_time, t1.files,
               su.nick_name as create_by_name
        from tbl_operate_work_record t1
        left join sys_user su on su.user_id = t1.create_by
        left join sys_dept sd on sd.dept_id = su.dept_id
    </sql>

    <select id="selectTblOperateWorkRecordList" parameterType="TblOperateWorkRecord" resultMap="TblOperateWorkRecordResult">
        <include refid="selectTblOperateWorkRecordVo"/>
        <where>
            t1.work_class is not null
            <if test="queryAll == null or queryAll == false">
                <if test="onlySelf == true">
                    and (
                        1 = 2
                        <if test="createBy != null and createBy != ''">
                            OR t1.create_by = #{createBy}
                        </if>
                        <if test="flowHandleUser != null and flowHandleUser != ''">
                            OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                        </if>
                    )
                </if>
                <if test="onlySelf == null or onlySelf == false">
                    and (
                    1 = 2
                    <if test="createBy != null and createBy != ''">
                        OR t1.create_by = #{createBy}
                    </if>
                    <if test="flowHandleUser != null and flowHandleUser != ''">
                        OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                    </if>
                    <if test="handleDeptIds != null and handleDeptIds.size() > 0">
                        OR sd.dept_id in
                        <foreach item="handleDeptId" collection="handleDeptIds" open="(" separator="," close=")">
                            #{handleDeptId}
                        </foreach>
                    </if>
                    )
                </if>
            </if>
            <if test="workClass != null "> and t1.work_class = #{workClass}</if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null "> and t1.work_type = #{workType}</if>
            <if test="workTitle != null  and workTitle != ''"> and t1.work_title like concat('%', #{workTitle}, '%')</if>
            <if test="createByName != null and createByName != ''"> and su.nick_name like concat('%', #{createByName}, '%') </if>
            <if test="fFlowstate != null"> and t1.f_flowstate = #{fFlowstate} </if>
            <if test="queryFlowState != null">
                <if test="queryFlowState == 1">
                    <!-- 待处理 -->
                    and (t1.f_flowstate != 100 and FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
                <if test="queryFlowState == 2">
                    <!-- 已办 -->
                    <!-- and (t1.f_flowstate = 100 or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))-->
                    and (t1.f_flowstate = 100 or t1.f_handle_user IS NULL or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
                <!--<if test="flowHandleUser != null and queryFlowState == null">
                    and t1.f_handle_user = #{flowHandleUser}
                </if>-->
            </if>
            <if test="startTime != null">
                and t1.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and t1.create_time &lt;= #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
            <if test="flowTaskIdList != null and flowTaskIdList.size() > 0">
                and t1.f_flowtaskid in
                <foreach item="flowTaskId" collection="flowTaskIdList" open="(" separator="," close=")">
                    #{flowTaskId}
                </foreach>
            </if>
        </where>
        ORDER BY t1.create_time desc
    </select>

    <select id="selectTblOperateWorkRecordById" parameterType="Long" resultMap="TblOperateWorkRecordResult">
        <include refid="selectTblOperateWorkRecordVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblOperateWorkRecordByIds" parameterType="Long" resultMap="TblOperateWorkRecordResult">
        <include refid="selectTblOperateWorkRecordVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectTblOperateWorkRecordByTaskId" resultType="com.ruoyi.monitor2.domain.TblOperateWorkRecord">
        <include refid="selectTblOperateWorkRecordVo"/>
        where t1.f_flowtaskid = #{fFlowtaskid}
    </select>

    <select id="countOperationalMattersNum" resultType="java.lang.Integer">
        SELECT
        COUNT( 1 )
        FROM
        tbl_operate_work_record t1
        LEFT JOIN sys_dept t2 ON t1.dept_id = t2.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and t1.create_time between #{startTime} and #{endTime}
            </if>
        <if test="deptId != 100">
            and (t2.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},t2.ancestors))
        </if>
        </where>
    </select>

    <select id="countOperateWorkRecordByStatus" resultType="java.lang.Integer">
        select count(1) from tbl_operate_work_record t1
        <where>
            t1.work_class is not null
            <if test="queryFlowState != null">
                <if test="queryFlowState == 1">
                    <!-- 待处理 -->
                    and (t1.f_flowstate != 100 and FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
                <!--<if test="queryFlowState == 2">
                    &lt;!&ndash; 已办 &ndash;&gt;
                    and (t1.f_flowstate = 100 or t1.f_handle_user IS NULL or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>-->
                <if test="queryFlowState == 2">
                    <!-- 已办 -->
                    and (t1.f_flowstate = 100 or t1.f_handle_user IS NULL or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
            </if>
            <if test="flowHandleUser != null and flowHandleUser != 1">
                and t1.f_handle_user = #{flowHandleUser}
            </if>
            <if test="createBy != null">
                and t1.create_by = #{createBy}
            </if>
        </where>
    </select>

    <insert id="insertTblOperateWorkRecord" parameterType="TblOperateWorkRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_operate_work_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workId != null">work_id,</if>
            <if test="workClass != null">work_class,</if>
            <if test="workName != null">work_name,</if>
            <if test="workType != null">work_type,</if>
            <if test="workTitle != null">work_title,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="fFlowtaskid != null">f_flowtaskid,</if>
            <if test="fFlowid != null">f_flowid,</if>
            <if test="fFlowstate != null">f_flowstate,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="files != null and files != ''">files,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workId != null">#{workId},</if>
            <if test="workClass != null">#{workClass},</if>
            <if test="workName != null">#{workName},</if>
            <if test="workType != null">#{workType},</if>
            <if test="workTitle != null">#{workTitle},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="fFlowtaskid != null">#{fFlowtaskid},</if>
            <if test="fFlowid != null">#{fFlowid},</if>
            <if test="fFlowstate != null">#{fFlowstate},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="files != null and files != ''">#{files},</if>
         </trim>
    </insert>

    <update id="updateTblOperateWorkRecord" parameterType="TblOperateWorkRecord">
        update tbl_operate_work_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="workId != null">work_id = #{workId},</if>
            <if test="workClass != null">work_class = #{workClass},</if>
            <if test="workName != null">work_name = #{workName},</if>
            <if test="workType != null">work_type = #{workType},</if>
            <if test="workTitle != null">work_title = #{workTitle},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="fFlowtaskid != null">f_flowtaskid = #{fFlowtaskid},</if>
            <if test="fFlowid != null">f_flowid = #{fFlowid},</if>
            <if test="fFlowstate != null">f_flowstate = #{fFlowstate},</if>
            <if test="fHandleUser != null">f_handle_user = #{fHandleUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="files != null and files != ''">files = #{files},</if>
        </trim>
        where id = #{id}
        <if test="fFlowtaskid != null and fFlowtaskid != ''">
            or f_flowtaskid = #{fFlowtaskid}
        </if>
    </update>

    <delete id="deleteTblOperateWorkRecordById" parameterType="Long">
        delete from tbl_operate_work_record where id = #{id}
    </delete>

    <delete id="deleteTblOperateWorkRecordByIds" parameterType="String">
        delete from tbl_operate_work_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
