{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\UploadFz.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\Generator\\components\\Upload\\UploadFz.vue", "mtime": 1756794279924}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_common", "require", "_Preview", "_interopRequireDefault", "_fileUploader", "_emitter", "dispatch", "emitter", "methods", "_default2", "exports", "default", "name", "components", "Preview", "FileUploader", "props", "value", "type", "Array", "String", "disabled", "Boolean", "showIcon", "showTip", "detailed", "limit", "Number", "accept", "buttonText", "sizeUnit", "pathType", "isAccount", "folder", "fileSize", "tipText", "data", "fileList", "previewVisible", "activeFile", "computed", "acceptText", "txt", "includes", "slice", "watch", "immediate", "handler", "val", "isArray", "handleRemove", "index", "splice", "$emit", "call", "handleClick", "file", "isPreview", "_this", "fileId", "getDownloadUrl", "then", "res", "$download", "export", "url", "handlePreview", "uploadFile", "isTopLimit", "length", "$message", "error", "concat", "$refs", "fileUploader", "openUploader", "fileSuccess", "push", "downloadAll", "_this2", "fileInfo", "i", "len", "fileName", "getPackDownloadUrl", "downloadVo", "downloadName"], "sources": ["src/components/Generator/components/Upload/UploadFz.vue"], "sourcesContent": ["<template>\n  <div class=\"UploadFile-container\">\n    <template v-if=\"!detailed\">\n      <el-button\n        size=\"small\"\n        icon=\"el-icon-upload\"\n        @click=\"uploadFile\"\n        :disabled=\"disabled\"\n      >\n        {{ buttonText }}\n      </el-button>\n      <a\n        type=\"text\"\n        @click=\"downloadAll\"\n        style=\"float: right\"\n        v-if=\"fileList.length\"\n        class=\"el-button el-button--text el-button--small\"\n        ><i class=\"el-icon-download\"></i>全部下载</a\n      >\n    </template>\n    <template v-if=\"fileList.length\">\n      <ul class=\"el-upload-list el-upload-list el-upload-list--text\">\n        <li\n          class=\"el-upload-list__item is-success\"\n          v-for=\"(file, index) in fileList\"\n          :key=\"file.fileId\"\n          :class=\"{ 'el-upload-list__item_detail': detailed }\"\n        >\n          <a\n            class=\"el-upload-list__item-name\"\n            :style=\"{ color: !showIcon ? '#409eff' : '' }\"\n            :title=\"\n              file.name +\n              (file.fileSize ? `（${jnpf.toFileSize(file.fileSize)}）` : '')\n            \"\n            @click=\"handleClick(file,true)\"\n          >\n            <i class=\"el-icon-paperclip\" v-if=\"showIcon\"></i>\n            {{ file.name\n            }}{{ file.fileSize ? `（${jnpf.toFileSize(file.fileSize)}）` : \"\" }}\n          </a>\n          <!-- <i class=\"el-icon-view\" title=\"查看\" @click=\"handlePreview(file)\"></i> -->\n          <i\n            class=\"el-icon-download\"\n            title=\"下载\"\n            @click=\"handleClick(file)\"\n          ></i>\n          <label\n            class=\"el-upload-list__item-status-label\"\n            :class=\"{ disabled: disabled }\"\n          >\n            <i class=\"el-icon-upload-success el-icon-circle-check\"></i>\n          </label>\n          <i\n            class=\"el-icon-close\"\n            title=\"删除\"\n            v-if=\"!disabled\"\n            @click=\"handleRemove(index)\"\n          ></i>\n        </li>\n      </ul>\n    </template>\n    <template>\n      <div class=\"el-upload__tip\" v-if=\"tipText\">\n        {{ tipText }}\n      </div>\n    </template>\n    <fileUploader\n      ref=\"fileUploader\"\n      v-bind=\"$props\"\n      @fileSuccess=\"fileSuccess\"\n    />\n    <Preview :visible.sync=\"previewVisible\" :file=\"activeFile\" />\n  </div>\n</template>\n\n<script>\nimport { getDownloadUrl, getPackDownloadUrl } from \"@/api/lowCode/common\";\nimport Preview from \"./Preview\";\nimport FileUploader from \"./vue-simple-uploader/fileUploader\";\nimport emitter from \"element-ui/src/mixins/emitter\";\nlet {\n  methods: { dispatch },\n} = emitter;\nexport default {\n  name: \"UploadFile\",\n  components: { Preview, FileUploader },\n  props: {\n    value: {\n      type: Array,\n      default: () => [],\n    },\n    type: {\n      type: String,\n      default: \"annex\",\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    showIcon: {\n      type: Boolean,\n      default: true,\n    },\n    showTip: {\n      type: Boolean,\n      default: false,\n    },\n    detailed: {\n      type: Boolean,\n      default: false,\n    },\n    limit: {\n      type: Number,\n      default: 0,\n    },\n    accept: {\n      type: String,\n      default: \"*\",\n    },\n    buttonText: {\n      type: String,\n      default: \"选择文件\",\n    },\n    sizeUnit: {\n      type: String,\n      default: \"MB\",\n    },\n    pathType: {\n      type: String,\n      default: \"defaultPath\",\n    },\n    isAccount: {\n      type: Number,\n      default: 0,\n    },\n    folder: {\n      type: String,\n      default: \"\",\n    },\n    fileSize: {\n      default: 10,\n    },\n    tipText: {\n      type: String,\n      default: \"\",\n    },\n  },\n  data() {\n    return {\n      fileList: [],\n      previewVisible: false,\n      activeFile: {},\n    };\n  },\n  computed: {\n    acceptText() {\n      let txt = \"\";\n      if (this.accept.includes(\"image/*\")) {\n        txt += \"、图片\";\n      }\n      if (this.accept.includes(\"video/*\")) {\n        txt += \"、视频\";\n      }\n      if (this.accept.includes(\"audio/*\")) {\n        txt += \"、音频\";\n      }\n      if (this.accept.includes(\".xls,.xlsx\")) {\n        txt += \"、excel\";\n      }\n      if (this.accept.includes(\".doc,.docx\")) {\n        txt += \"、word\";\n      }\n      if (this.accept.includes(\".pdf\")) {\n        txt += \"、pdf\";\n      }\n      if (this.accept.includes(\".txt\")) {\n        txt += \"、txt\";\n      }\n      return txt.slice(1);\n    },\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler(val) {\n        this.fileList = Array.isArray(val) ? val : [];\n      },\n    },\n  },\n  methods: {\n    handleRemove(index) {\n      this.fileList.splice(index, 1);\n      this.$emit(\"input\", this.fileList);\n      this.$emit(\"change\", this.fileList);\n      dispatch.call(this, \"ElFormItem\", \"el.form.change\", this.fileList);\n    },\n    handleClick(file,isPreview) {\n      // 点击下载文件\n      if (!file.fileId) return;\n      getDownloadUrl(this.type, file.fileId).then((res) => {\n        this.$download.export(\"/proxy\" + res.data.url, file.name,null,file,isPreview);\n      });\n    },\n    handlePreview(file) {\n      this.activeFile = file;\n      this.previewVisible = true;\n    },\n    uploadFile() {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      this.$refs.fileUploader && this.$refs.fileUploader.openUploader();\n    },\n    fileSuccess(data) {\n      const isTopLimit = this.limit ? this.value && this.value.length >= this.limit : false;\n      if (isTopLimit) {\n        this.$message.error(`当前限制最多可以上传${this.limit}个文件`);\n        return false;\n      }\n      this.fileList.push(data);\n      this.$emit(\"input\", this.fileList);\n      this.$emit(\"change\", this.fileList);\n      dispatch.call(this, \"ElFormItem\", \"el.form.change\", this.fileList);\n    },\n    downloadAll() {\n      //下载全部（打包下载）\n      if (!this.fileList.length) return this.$message.error(\"未发现文件\");\n      let fileInfo = [];\n      for (let i = 0, len = this.fileList.length; i < len; i++) {\n        fileInfo.push({\n          fileId: this.fileList[i].fileId,\n          fileName: this.fileList[i].name,\n        });\n      }\n      getPackDownloadUrl(this.type, fileInfo).then((res) => {\n        this.$download.export(\n          \"/proxy\" + res.data.downloadVo.url,\n          res.data.downloadName\n        );\n      });\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.UploadFile-container {\n  position: relative;\n  .el-upload__tip {\n    line-height: 1.2;\n    color: #a5a5a5;\n    margin-top: 5px;\n    word-break: break-all;\n  }\n}\n.el-upload-list__item {\n  &.el-upload-list__item_detail:first-child {\n    margin-top: 5px !important;\n  }\n  .el-upload-list__item-name {\n    margin-right: 70px;\n  }\n  &:hover {\n    .el-upload-list__item-status-label.disabled {\n      display: block !important;\n    }\n  }\n  .el-icon-download {\n    display: inline-block;\n    position: absolute;\n    top: 5px;\n    right: 25px;\n    cursor: pointer;\n    opacity: 0.75;\n    color: #606266;\n  }\n  .el-icon-view {\n    display: inline-block;\n    position: absolute;\n    top: 5px;\n    right: 45px;\n    cursor: pointer;\n    opacity: 0.75;\n    color: #606266;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AA6EA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,QAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IACAK,QAAA,GACAC,gBAAA,CADAC,OAAA,CAAAF,QAAA;AACA,IAAAG,SAAA,GAAAC,OAAA,CAAAC,OAAA,GACA;EACAC,IAAA;EACAC,UAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAR,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAO,IAAA;MACAA,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAU,QAAA;MACAH,IAAA,EAAAI,OAAA;MACAX,OAAA;IACA;IACAY,QAAA;MACAL,IAAA,EAAAI,OAAA;MACAX,OAAA;IACA;IACAa,OAAA;MACAN,IAAA,EAAAI,OAAA;MACAX,OAAA;IACA;IACAc,QAAA;MACAP,IAAA,EAAAI,OAAA;MACAX,OAAA;IACA;IACAe,KAAA;MACAR,IAAA,EAAAS,MAAA;MACAhB,OAAA;IACA;IACAiB,MAAA;MACAV,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAkB,UAAA;MACAX,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAmB,QAAA;MACAZ,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAoB,QAAA;MACAb,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAqB,SAAA;MACAd,IAAA,EAAAS,MAAA;MACAhB,OAAA;IACA;IACAsB,MAAA;MACAf,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;IACAuB,QAAA;MACAvB,OAAA;IACA;IACAwB,OAAA;MACAjB,IAAA,EAAAE,MAAA;MACAT,OAAA;IACA;EACA;EACAyB,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,cAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,GAAA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,SAAAd,MAAA,CAAAe,QAAA;QACAD,GAAA;MACA;MACA,OAAAA,GAAA,CAAAE,KAAA;IACA;EACA;EACAC,KAAA;IACA5B,KAAA;MACA6B,SAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAX,QAAA,GAAAlB,KAAA,CAAA8B,OAAA,CAAAD,GAAA,IAAAA,GAAA;MACA;IACA;EACA;EACAxC,OAAA;IACA0C,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAd,QAAA,CAAAe,MAAA,CAAAD,KAAA;MACA,KAAAE,KAAA,eAAAhB,QAAA;MACA,KAAAgB,KAAA,gBAAAhB,QAAA;MACA/B,QAAA,CAAAgD,IAAA,4CAAAjB,QAAA;IACA;IACAkB,WAAA,WAAAA,YAAAC,IAAA,EAAAC,SAAA;MAAA,IAAAC,KAAA;MACA;MACA,KAAAF,IAAA,CAAAG,MAAA;MACA,IAAAC,sBAAA,OAAA1C,IAAA,EAAAsC,IAAA,CAAAG,MAAA,EAAAE,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAAK,SAAA,CAAAC,MAAA,YAAAF,GAAA,CAAA1B,IAAA,CAAA6B,GAAA,EAAAT,IAAA,CAAA5C,IAAA,QAAA4C,IAAA,EAAAC,SAAA;MACA;IACA;IACAS,aAAA,WAAAA,cAAAV,IAAA;MACA,KAAAjB,UAAA,GAAAiB,IAAA;MACA,KAAAlB,cAAA;IACA;IACA6B,UAAA,WAAAA,WAAA;MACA,IAAAC,UAAA,QAAA1C,KAAA,QAAAT,KAAA,SAAAA,KAAA,CAAAoD,MAAA,SAAA3C,KAAA;MACA,IAAA0C,UAAA;QACA,KAAAE,QAAA,CAAAC,KAAA,gEAAAC,MAAA,MAAA9C,KAAA;QACA;MACA;MACA,KAAA+C,KAAA,CAAAC,YAAA,SAAAD,KAAA,CAAAC,YAAA,CAAAC,YAAA;IACA;IACAC,WAAA,WAAAA,YAAAxC,IAAA;MACA,IAAAgC,UAAA,QAAA1C,KAAA,QAAAT,KAAA,SAAAA,KAAA,CAAAoD,MAAA,SAAA3C,KAAA;MACA,IAAA0C,UAAA;QACA,KAAAE,QAAA,CAAAC,KAAA,gEAAAC,MAAA,MAAA9C,KAAA;QACA;MACA;MACA,KAAAW,QAAA,CAAAwC,IAAA,CAAAzC,IAAA;MACA,KAAAiB,KAAA,eAAAhB,QAAA;MACA,KAAAgB,KAAA,gBAAAhB,QAAA;MACA/B,QAAA,CAAAgD,IAAA,4CAAAjB,QAAA;IACA;IACAyC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA1C,QAAA,CAAAgC,MAAA,cAAAC,QAAA,CAAAC,KAAA;MACA,IAAAS,QAAA;MACA,SAAAC,CAAA,MAAAC,GAAA,QAAA7C,QAAA,CAAAgC,MAAA,EAAAY,CAAA,GAAAC,GAAA,EAAAD,CAAA;QACAD,QAAA,CAAAH,IAAA;UACAlB,MAAA,OAAAtB,QAAA,CAAA4C,CAAA,EAAAtB,MAAA;UACAwB,QAAA,OAAA9C,QAAA,CAAA4C,CAAA,EAAArE;QACA;MACA;MACA,IAAAwE,0BAAA,OAAAlE,IAAA,EAAA8D,QAAA,EAAAnB,IAAA,WAAAC,GAAA;QACAiB,MAAA,CAAAhB,SAAA,CAAAC,MAAA,CACA,WAAAF,GAAA,CAAA1B,IAAA,CAAAiD,UAAA,CAAApB,GAAA,EACAH,GAAA,CAAA1B,IAAA,CAAAkD,YACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}