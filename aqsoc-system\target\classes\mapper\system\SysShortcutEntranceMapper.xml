<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysShortcutEntranceMapper">

    <resultMap type="SysShortcutEntrance" id="SysShortcutEntranceResult">
        <result property="entranceId"    column="entrance_id"    />
        <result property="entranceCategory"    column="entrance_category"    />
        <result property="entranceName"    column="entrance_name"    />
        <result property="menuId"    column="menu_id"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysShortcutEntranceVo">
        select entrance_id, entrance_category, entrance_name, menu_id, icon_url, status, create_by, create_time, update_by, update_time from sys_shortcut_entrance
    </sql>

    <select id="selectSysShortcutEntranceList" parameterType="SysShortcutEntrance" resultMap="SysShortcutEntranceResult">
        <include refid="selectSysShortcutEntranceVo"/>
        <where>
            <if test="entranceCategory != null  and entranceCategory != ''"> and entrance_category = #{entranceCategory}</if>
            <if test="entranceName != null  and entranceName != ''"> and entrance_name like concat('%', #{entranceName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
			</if>
        </where>
    </select>

    <select id="selectSysShortcutEntranceByEntranceId" parameterType="Long" resultMap="SysShortcutEntranceResult">
        <include refid="selectSysShortcutEntranceVo"/>
        where entrance_id = #{entranceId}
    </select>

    <select id="selectSysShortcutEntranceByEntranceIds" parameterType="Long" resultMap="SysShortcutEntranceResult">
        <include refid="selectSysShortcutEntranceVo"/>
        where entrance_id in
        <foreach item="entranceId" collection="array" open="(" separator="," close=")">
            #{entranceId}
        </foreach>
    </select>

    <insert id="insertSysShortcutEntrance" parameterType="SysShortcutEntrance" useGeneratedKeys="true" keyProperty="entranceId">
        insert into sys_shortcut_entrance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entranceCategory != null and entranceCategory != ''">entrance_category,</if>
            <if test="entranceName != null and entranceName != ''">entrance_name,</if>
            <if test="menuId != null">menu_id,</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entranceCategory != null and entranceCategory != ''">#{entranceCategory},</if>
            <if test="entranceName != null and entranceName != ''">#{entranceName},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="iconUrl != null and iconUrl != ''">#{iconUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysShortcutEntrance" parameterType="SysShortcutEntrance">
        update sys_shortcut_entrance
        <trim prefix="SET" suffixOverrides=",">
            <if test="entranceCategory != null and entranceCategory != ''">entrance_category = #{entranceCategory},</if>
            <if test="entranceName != null and entranceName != ''">entrance_name = #{entranceName},</if>
            <if test="menuId != null">menu_id = #{menuId},</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url = #{iconUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where entrance_id = #{entranceId}
    </update>

    <delete id="deleteSysShortcutEntranceByEntranceId" parameterType="Long">
        delete from sys_shortcut_entrance where entrance_id = #{entranceId}
    </delete>

    <delete id="deleteSysShortcutEntranceByEntranceIds" parameterType="String">
        delete from sys_shortcut_entrance where entrance_id in
        <foreach item="entranceId" collection="array" open="(" separator="," close=")">
            #{entranceId}
        </foreach>
    </delete>

    <select id="selectNormalSysShortcutEntranceList" resultMap="SysShortcutEntranceResult">
        <include refid="selectSysShortcutEntranceVo"/>
        where status = '0'
        and not exists (
            select 1 from sys_homepage_entrance
            where sys_homepage_entrance.entrance_id = sys_shortcut_entrance.entrance_id
        )
        order by entrance_category, entrance_id
    </select>

    <select id="countNormalSysShortcutEntrance" resultType="int">
        SELECT COUNT(*) FROM sys_shortcut_entrance WHERE status = '0'
    </select>
</mapper>
