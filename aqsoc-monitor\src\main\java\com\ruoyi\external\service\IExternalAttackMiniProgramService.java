package com.ruoyi.external.service;

import java.util.List;
import java.util.Set;

import com.ruoyi.external.domain.ExternalAttackMiniProgram;
import com.ruoyi.external.model.ExternalAttackMiniProgramExcelForm;

/**
 * 微信小程序Service接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public interface IExternalAttackMiniProgramService
{
    /**
     * 查询微信小程序
     *
     * @param id 微信小程序主键
     * @return 微信小程序
     */
    public ExternalAttackMiniProgram selectExternalAttackMiniProgramById(Long id);

    /**
     * 批量查询微信小程序
     *
     * @param ids 微信小程序主键集合
     * @return 微信小程序集合
     */
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramByIds(Long[] ids);

    /**
     * 查询微信小程序列表
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 微信小程序集合
     */
    public List<ExternalAttackMiniProgram> selectExternalAttackMiniProgramList(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 新增微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    public int insertExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 修改微信小程序
     *
     * @param externalAttackMiniProgram 微信小程序
     * @return 结果
     */
    public int updateExternalAttackMiniProgram(ExternalAttackMiniProgram externalAttackMiniProgram);

    /**
     * 删除微信小程序信息
     *
     * @param id 微信小程序主键
     * @return 结果
     */
    public int deleteExternalAttackMiniProgramById(Long id);

    /**
     * 批量删除微信小程序
     *
     * @param ids 需要删除的微信小程序主键集合
     * @return 结果
     */
    public int deleteExternalAttackMiniProgramByIds(Long[] ids);

    String importExternalAttackMiniProgram(List<ExternalAttackMiniProgramExcelForm> miniProgramExcelFormList, boolean updateSupport, String operName);

    List<ExternalAttackMiniProgram> selectByMiniProgramAppIds(Set<String> appIds);

    int countNum();

    void deleteByEntryTypeAndAppId(Set<String> uniqueKeys, Long deviceConfigId);

}
