# 外部攻击面同步重构任务

## 任务概述
将SyncExternalAttackServiceImpl类中的外部攻击面同步功能从单一配置获取改为从tbl_device_config表批量获取多个配置，支持多设备配置管理。

## 重构目标
- **主要改造目标**：SyncExternalAttackServiceImpl.syncExternalAttackData()方法
- **改造逻辑**：单一配置请求 → 批量配置获取 → 循环处理每个配置 → 分别调用第三方接口
- **参考模式**：PullHostIntrusionAttackEvent.java的实现模式

## 执行计划

### 阶段1：数据库表结构修改 ✅
1. 为三个表添加device_config_id字段：
   - `ALTER TABLE external_attack_app ADD COLUMN device_config_id BIGINT`
   - `ALTER TABLE external_attack_mini_program ADD COLUMN device_config_id BIGINT`
   - `ALTER TABLE external_attack_official_account ADD COLUMN device_config_id BIGINT`

### 阶段2：实体类修改 ✅
1. 修改ExternalAttackApp.java添加deviceConfigId字段
2. 修改ExternalAttackMiniProgram.java添加deviceConfigId字段
3. 修改ExternalAttackOfficialAccount.java添加deviceConfigId字段

### 阶段3：Service层新增方法 ✅
1. 创建ExternalAttackConfig配置类
2. 在ITblDeviceConfigService接口中新增getExternalAttackConfig方法
3. 在TblDeviceConfigServiceImpl中实现getExternalAttackConfig方法

### 阶段4：Mapper文件修改 ✅
1. 修改三个Mapper.xml文件支持device_config_id字段：
   - 更新resultMap映射
   - 更新SQL查询语句
   - 更新插入语句
   - 新增deleteByEntryTypeAndDeviceConfigId删除方法
2. 修改三个Mapper.java接口添加新的删除方法
3. 修改三个Service接口和实现类添加新的删除方法

### 阶段5：核心业务逻辑重构 ✅
1. 重构SyncExternalAttackServiceImpl.syncExternalAttackData()方法：
   - 从单一配置获取改为批量设备配置获取
   - 循环处理每个设备配置
   - 为每个配置单独调用第三方接口
   - 设置deviceConfigId到实体对象
2. 修改相关同步方法支持deviceConfigId参数
3. 移除ExternalAttackSyncTask中的开关判断逻辑

## 技术实现细节

### 配置获取逻辑
```java
// 获取所有启用的设备配置
TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
queryDeviceConfig.setStatus(1);
List<TblDeviceConfig> deviceConfigList = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);

// 循环处理每个设备配置
for (TblDeviceConfig deviceConfig : deviceConfigList) {
    ExternalAttackConfig externalAttackConfig = deviceConfigService.getExternalAttackConfig(deviceConfig);
    // 处理配置...
}
```

### 配置数据结构
```json
{
  "name": "非凡外部攻击面接口配置",
  "key": "synchronize.feifan.externalAttack", 
  "value": {
    "enable": true,
    "url": "https://ip2.oksec.net:4569/api/v1",
    "x-api-key": "Z0FBQUFBQm5FT0NMZ0lXbUN4WTlEV0MwLXkwdUpnRnBPeDQyNmpOUkx0UG9PN3luaFRsYVdPTmNlaGdCM3JYV25JZU16NkVnTF90ZGtZUmdIaEtuTWdGM2NaZkhaVUthb2xLLUhEa09XYXZEYndFLVpSekdLNzhwU0ROUm96RWIzTDB0akdGLW9NSnE=@89357a00-3042-11ee-ab06-33ea3f1fe190",
    "ids": "89357a00-3042-11ee-ab06-33ea3f1fe190"
  }
}
```

### 删除逻辑改进
- 原来：deleteByEntryTypeAndAppId（基于应用ID删除）
- 现在：deleteByEntryTypeAndDeviceConfigId（基于设备配置ID删除）
- 优势：支持多设备配置的数据隔离

## 验证要求
1. 确保重构后的代码能够正确处理多个配置
2. 验证每个配置的enable字段控制是否生效
3. 确保异常情况下不会影响其他配置的处理
4. 验证数据库中的device_config_id字段正确设置
5. 确保向后兼容性，现有功能不受影响

## 完成状态
- [x] 数据库表结构修改
- [x] 实体类修改
- [x] Service层新增方法
- [x] Mapper文件修改
- [x] 核心业务逻辑重构
- [x] ExternalAttackSyncTask调整

## 注意事项
1. 所有修改都保持向后兼容性
2. 异常处理确保单个配置失败不影响其他配置
3. 日志记录包含设备配置ID便于问题排查
4. 删除逻辑基于设备配置ID实现数据隔离
